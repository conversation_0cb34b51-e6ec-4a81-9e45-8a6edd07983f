﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>System.Collections.NonGeneric</name>
  </assembly>
  <members>
    <member name="T:System.Collections.CaseInsensitiveComparer">
      <summary>Compares two objects for equivalence, ignoring the case of strings.</summary>
    </member>
    <member name="M:System.Collections.CaseInsensitiveComparer.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Collections.CaseInsensitiveComparer" /> class using the <see cref="P:System.Threading.Thread.CurrentCulture" /> of the current thread.</summary>
    </member>
    <member name="M:System.Collections.CaseInsensitiveComparer.#ctor(System.Globalization.CultureInfo)">
      <summary>Initializes a new instance of the <see cref="T:System.Collections.CaseInsensitiveComparer" /> class using the specified <see cref="T:System.Globalization.CultureInfo" />.</summary>
      <param name="culture">The <see cref="T:System.Globalization.CultureInfo" /> to use for the new <see cref="T:System.Collections.CaseInsensitiveComparer" />.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="culture" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Collections.CaseInsensitiveComparer.Compare(System.Object,System.Object)">
      <summary>Performs a case-insensitive comparison of two objects of the same type and returns a value indicating whether one is less than, equal to, or greater than the other.</summary>
      <param name="a">The first object to compare.</param>
      <param name="b">The second object to compare.</param>
      <returns>A signed integer that indicates the relative values of <paramref name="a" /> and <paramref name="b" />, as shown in the following table.
  Value  
  
  Meaning  
  
  Less than zero  
  
 <paramref name="a" /> is less than <paramref name="b" />, with casing ignored.  
  
  Zero  
  
 <paramref name="a" /> equals <paramref name="b" />, with casing ignored.  
  
  Greater than zero  
  
 <paramref name="a" /> is greater than <paramref name="b" />, with casing ignored.</returns>
      <exception cref="T:System.ArgumentException">Neither <paramref name="a" /> nor <paramref name="b" /> implements the <see cref="T:System.IComparable" /> interface.
-or-
<paramref name="a" /> and <paramref name="b" /> are of different types.</exception>
    </member>
    <member name="P:System.Collections.CaseInsensitiveComparer.Default">
      <summary>Gets an instance of <see cref="T:System.Collections.CaseInsensitiveComparer" /> that is associated with the <see cref="P:System.Threading.Thread.CurrentCulture" /> of the current thread and that is always available.</summary>
      <returns>An instance of <see cref="T:System.Collections.CaseInsensitiveComparer" /> that is associated with the <see cref="P:System.Threading.Thread.CurrentCulture" /> of the current thread.</returns>
    </member>
    <member name="P:System.Collections.CaseInsensitiveComparer.DefaultInvariant">
      <summary>Gets an instance of <see cref="T:System.Collections.CaseInsensitiveComparer" /> that is associated with <see cref="P:System.Globalization.CultureInfo.InvariantCulture" /> and that is always available.</summary>
      <returns>An instance of <see cref="T:System.Collections.CaseInsensitiveComparer" /> that is associated with <see cref="P:System.Globalization.CultureInfo.InvariantCulture" />.</returns>
    </member>
    <member name="T:System.Collections.CaseInsensitiveHashCodeProvider">
      <summary>Supplies a hash code for an object, using a hashing algorithm that ignores the case of strings.</summary>
    </member>
    <member name="M:System.Collections.CaseInsensitiveHashCodeProvider.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Collections.CaseInsensitiveHashCodeProvider" /> class using the <see cref="P:System.Threading.Thread.CurrentCulture" /> of the current thread.</summary>
    </member>
    <member name="M:System.Collections.CaseInsensitiveHashCodeProvider.#ctor(System.Globalization.CultureInfo)">
      <summary>Initializes a new instance of the <see cref="T:System.Collections.CaseInsensitiveHashCodeProvider" /> class using the specified <see cref="T:System.Globalization.CultureInfo" />.</summary>
      <param name="culture">The <see cref="T:System.Globalization.CultureInfo" /> to use for the new <see cref="T:System.Collections.CaseInsensitiveHashCodeProvider" />.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="culture" /> is <see langword="null" />.</exception>
    </member>
    <member name="P:System.Collections.CaseInsensitiveHashCodeProvider.Default">
      <summary>Gets an instance of <see cref="T:System.Collections.CaseInsensitiveHashCodeProvider" /> that is associated with the <see cref="P:System.Threading.Thread.CurrentCulture" /> of the current thread and that is always available.</summary>
      <returns>An instance of <see cref="T:System.Collections.CaseInsensitiveHashCodeProvider" /> that is associated with the <see cref="P:System.Threading.Thread.CurrentCulture" /> of the current thread.</returns>
    </member>
    <member name="P:System.Collections.CaseInsensitiveHashCodeProvider.DefaultInvariant">
      <summary>Gets an instance of <see cref="T:System.Collections.CaseInsensitiveHashCodeProvider" /> that is associated with <see cref="P:System.Globalization.CultureInfo.InvariantCulture" /> and that is always available.</summary>
      <returns>An instance of <see cref="T:System.Collections.CaseInsensitiveHashCodeProvider" /> that is associated with <see cref="P:System.Globalization.CultureInfo.InvariantCulture" />.</returns>
    </member>
    <member name="M:System.Collections.CaseInsensitiveHashCodeProvider.GetHashCode(System.Object)">
      <summary>Returns a hash code for the given object, using a hashing algorithm that ignores the case of strings.</summary>
      <param name="obj">The <see cref="T:System.Object" /> for which a hash code is to be returned.</param>
      <returns>A hash code for the given object, using a hashing algorithm that ignores the case of strings.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="obj" /> is <see langword="null" />.</exception>
    </member>
    <member name="T:System.Collections.CollectionBase">
      <summary>Provides the <see langword="abstract" /> base class for a strongly typed collection.</summary>
    </member>
    <member name="M:System.Collections.CollectionBase.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Collections.CollectionBase" /> class with the default initial capacity.</summary>
    </member>
    <member name="M:System.Collections.CollectionBase.#ctor(System.Int32)">
      <summary>Initializes a new instance of the <see cref="T:System.Collections.CollectionBase" /> class with the specified capacity.</summary>
      <param name="capacity">The number of elements that the new list can initially store.</param>
    </member>
    <member name="P:System.Collections.CollectionBase.Capacity">
      <summary>Gets or sets the number of elements that the <see cref="T:System.Collections.CollectionBase" /> can contain.</summary>
      <returns>The number of elements that the <see cref="T:System.Collections.CollectionBase" /> can contain.</returns>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <see cref="P:System.Collections.CollectionBase.Capacity" /> is set to a value that is less than <see cref="P:System.Collections.CollectionBase.Count" />.</exception>
      <exception cref="T:System.OutOfMemoryException">There is not enough memory available on the system.</exception>
    </member>
    <member name="M:System.Collections.CollectionBase.Clear">
      <summary>Removes all objects from the <see cref="T:System.Collections.CollectionBase" /> instance. This method cannot be overridden.</summary>
    </member>
    <member name="P:System.Collections.CollectionBase.Count">
      <summary>Gets the number of elements contained in the <see cref="T:System.Collections.CollectionBase" /> instance. This property cannot be overridden.</summary>
      <returns>The number of elements contained in the <see cref="T:System.Collections.CollectionBase" /> instance.
Retrieving the value of this property is an O(1) operation.</returns>
    </member>
    <member name="M:System.Collections.CollectionBase.GetEnumerator">
      <summary>Returns an enumerator that iterates through the <see cref="T:System.Collections.CollectionBase" /> instance.</summary>
      <returns>An <see cref="T:System.Collections.IEnumerator" /> for the <see cref="T:System.Collections.CollectionBase" /> instance.</returns>
    </member>
    <member name="P:System.Collections.CollectionBase.InnerList">
      <summary>Gets an <see cref="T:System.Collections.ArrayList" /> containing the list of elements in the <see cref="T:System.Collections.CollectionBase" /> instance.</summary>
      <returns>An <see cref="T:System.Collections.ArrayList" /> representing the <see cref="T:System.Collections.CollectionBase" /> instance itself.
Retrieving the value of this property is an O(1) operation.</returns>
    </member>
    <member name="P:System.Collections.CollectionBase.List">
      <summary>Gets an <see cref="T:System.Collections.IList" /> containing the list of elements in the <see cref="T:System.Collections.CollectionBase" /> instance.</summary>
      <returns>An <see cref="T:System.Collections.IList" /> representing the <see cref="T:System.Collections.CollectionBase" /> instance itself.</returns>
    </member>
    <member name="M:System.Collections.CollectionBase.OnClear">
      <summary>Performs additional custom processes when clearing the contents of the <see cref="T:System.Collections.CollectionBase" /> instance.</summary>
    </member>
    <member name="M:System.Collections.CollectionBase.OnClearComplete">
      <summary>Performs additional custom processes after clearing the contents of the <see cref="T:System.Collections.CollectionBase" /> instance.</summary>
    </member>
    <member name="M:System.Collections.CollectionBase.OnInsert(System.Int32,System.Object)">
      <summary>Performs additional custom processes before inserting a new element into the <see cref="T:System.Collections.CollectionBase" /> instance.</summary>
      <param name="index">The zero-based index at which to insert <paramref name="value" />.</param>
      <param name="value">The new value of the element at <paramref name="index" />.</param>
    </member>
    <member name="M:System.Collections.CollectionBase.OnInsertComplete(System.Int32,System.Object)">
      <summary>Performs additional custom processes after inserting a new element into the <see cref="T:System.Collections.CollectionBase" /> instance.</summary>
      <param name="index">The zero-based index at which to insert <paramref name="value" />.</param>
      <param name="value">The new value of the element at <paramref name="index" />.</param>
    </member>
    <member name="M:System.Collections.CollectionBase.OnRemove(System.Int32,System.Object)">
      <summary>Performs additional custom processes when removing an element from the <see cref="T:System.Collections.CollectionBase" /> instance.</summary>
      <param name="index">The zero-based index at which <paramref name="value" /> can be found.</param>
      <param name="value">The value of the element to remove from <paramref name="index" />.</param>
    </member>
    <member name="M:System.Collections.CollectionBase.OnRemoveComplete(System.Int32,System.Object)">
      <summary>Performs additional custom processes after removing an element from the <see cref="T:System.Collections.CollectionBase" /> instance.</summary>
      <param name="index">The zero-based index at which <paramref name="value" /> can be found.</param>
      <param name="value">The value of the element to remove from <paramref name="index" />.</param>
    </member>
    <member name="M:System.Collections.CollectionBase.OnSet(System.Int32,System.Object,System.Object)">
      <summary>Performs additional custom processes before setting a value in the <see cref="T:System.Collections.CollectionBase" /> instance.</summary>
      <param name="index">The zero-based index at which <paramref name="oldValue" /> can be found.</param>
      <param name="oldValue">The value to replace with <paramref name="newValue" />.</param>
      <param name="newValue">The new value of the element at <paramref name="index" />.</param>
    </member>
    <member name="M:System.Collections.CollectionBase.OnSetComplete(System.Int32,System.Object,System.Object)">
      <summary>Performs additional custom processes after setting a value in the <see cref="T:System.Collections.CollectionBase" /> instance.</summary>
      <param name="index">The zero-based index at which <paramref name="oldValue" /> can be found.</param>
      <param name="oldValue">The value to replace with <paramref name="newValue" />.</param>
      <param name="newValue">The new value of the element at <paramref name="index" />.</param>
    </member>
    <member name="M:System.Collections.CollectionBase.OnValidate(System.Object)">
      <summary>Performs additional custom processes when validating a value.</summary>
      <param name="value">The object to validate.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Collections.CollectionBase.RemoveAt(System.Int32)">
      <summary>Removes the element at the specified index of the <see cref="T:System.Collections.CollectionBase" /> instance. This method is not overridable.</summary>
      <param name="index">The zero-based index of the element to remove.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> is less than zero.
-or-
<paramref name="index" /> is equal to or greater than <see cref="P:System.Collections.CollectionBase.Count" />.</exception>
    </member>
    <member name="M:System.Collections.CollectionBase.System#Collections#ICollection#CopyTo(System.Array,System.Int32)">
      <summary>Copies the entire <see cref="T:System.Collections.CollectionBase" /> to a compatible one-dimensional <see cref="T:System.Array" />, starting at the specified index of the target array.</summary>
      <param name="array">The one-dimensional <see cref="T:System.Array" /> that is the destination of the elements copied from <see cref="T:System.Collections.CollectionBase" />. The <see cref="T:System.Array" /> must have zero-based indexing.</param>
      <param name="index">The zero-based index in <paramref name="array" /> at which copying begins.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> is less than zero.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="array" /> is multidimensional.
-or-
The number of elements in the source <see cref="T:System.Collections.CollectionBase" /> is greater than the available space from <paramref name="index" /> to the end of the destination <paramref name="array" />.</exception>
      <exception cref="T:System.InvalidCastException">The type of the source <see cref="T:System.Collections.CollectionBase" /> cannot be cast automatically to the type of the destination <paramref name="array" />.</exception>
    </member>
    <member name="P:System.Collections.CollectionBase.System#Collections#ICollection#IsSynchronized">
      <summary>Gets a value indicating whether access to the <see cref="T:System.Collections.CollectionBase" /> is synchronized (thread safe).</summary>
      <returns>
        <see langword="true" /> if access to the <see cref="T:System.Collections.CollectionBase" /> is synchronized (thread safe); otherwise, <see langword="false" />. The default is <see langword="false" />.</returns>
    </member>
    <member name="P:System.Collections.CollectionBase.System#Collections#ICollection#SyncRoot">
      <summary>Gets an object that can be used to synchronize access to the <see cref="T:System.Collections.CollectionBase" />.</summary>
      <returns>An object that can be used to synchronize access to the <see cref="T:System.Collections.CollectionBase" />.</returns>
    </member>
    <member name="M:System.Collections.CollectionBase.System#Collections#IList#Add(System.Object)">
      <summary>Adds an object to the end of the <see cref="T:System.Collections.CollectionBase" />.</summary>
      <param name="value">The <see cref="T:System.Object" /> to be added to the end of the <see cref="T:System.Collections.CollectionBase" />.</param>
      <returns>The <see cref="T:System.Collections.CollectionBase" /> index at which the <paramref name="value" /> has been added.</returns>
      <exception cref="T:System.NotSupportedException">The <see cref="T:System.Collections.CollectionBase" /> is read-only.
-or-
The <see cref="T:System.Collections.CollectionBase" /> has a fixed size.</exception>
    </member>
    <member name="M:System.Collections.CollectionBase.System#Collections#IList#Contains(System.Object)">
      <summary>Determines whether the <see cref="T:System.Collections.CollectionBase" /> contains a specific element.</summary>
      <param name="value">The <see cref="T:System.Object" /> to locate in the <see cref="T:System.Collections.CollectionBase" />.</param>
      <returns>
        <see langword="true" /> if the <see cref="T:System.Collections.CollectionBase" /> contains the specified <paramref name="value" />; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Collections.CollectionBase.System#Collections#IList#IndexOf(System.Object)">
      <summary>Searches for the specified <see cref="T:System.Object" /> and returns the zero-based index of the first occurrence within the entire <see cref="T:System.Collections.CollectionBase" />.</summary>
      <param name="value">The <see cref="T:System.Object" /> to locate in the <see cref="T:System.Collections.CollectionBase" />.</param>
      <returns>The zero-based index of the first occurrence of <paramref name="value" /> within the entire <see cref="T:System.Collections.CollectionBase" />, if found; otherwise, -1.</returns>
    </member>
    <member name="M:System.Collections.CollectionBase.System#Collections#IList#Insert(System.Int32,System.Object)">
      <summary>Inserts an element into the <see cref="T:System.Collections.CollectionBase" /> at the specified index.</summary>
      <param name="index">The zero-based index at which <paramref name="value" /> should be inserted.</param>
      <param name="value">The <see cref="T:System.Object" /> to insert.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> is less than zero.
-or-
<paramref name="index" /> is greater than <see cref="P:System.Collections.CollectionBase.Count" />.</exception>
      <exception cref="T:System.NotSupportedException">The <see cref="T:System.Collections.CollectionBase" /> is read-only.
-or-
The <see cref="T:System.Collections.CollectionBase" /> has a fixed size.</exception>
    </member>
    <member name="P:System.Collections.CollectionBase.System#Collections#IList#IsFixedSize">
      <summary>Gets a value indicating whether the <see cref="T:System.Collections.CollectionBase" /> has a fixed size.</summary>
      <returns>
        <see langword="true" /> if the <see cref="T:System.Collections.CollectionBase" /> has a fixed size; otherwise, <see langword="false" />. The default is <see langword="false" />.</returns>
    </member>
    <member name="P:System.Collections.CollectionBase.System#Collections#IList#IsReadOnly">
      <summary>Gets a value indicating whether the <see cref="T:System.Collections.CollectionBase" /> is read-only.</summary>
      <returns>
        <see langword="true" /> if the <see cref="T:System.Collections.CollectionBase" /> is read-only; otherwise, <see langword="false" />. The default is <see langword="false" />.</returns>
    </member>
    <member name="P:System.Collections.CollectionBase.System#Collections#IList#Item(System.Int32)">
      <summary>Gets or sets the element at the specified index.</summary>
      <param name="index">The zero-based index of the element to get or set.</param>
      <returns>The element at the specified index.</returns>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> is less than zero.
-or-
<paramref name="index" /> is equal to or greater than <see cref="P:System.Collections.CollectionBase.Count" />.</exception>
    </member>
    <member name="M:System.Collections.CollectionBase.System#Collections#IList#Remove(System.Object)">
      <summary>Removes the first occurrence of a specific object from the <see cref="T:System.Collections.CollectionBase" />.</summary>
      <param name="value">The <see cref="T:System.Object" /> to remove from the <see cref="T:System.Collections.CollectionBase" />.</param>
      <exception cref="T:System.ArgumentException">The <paramref name="value" /> parameter was not found in the <see cref="T:System.Collections.CollectionBase" /> object.</exception>
      <exception cref="T:System.NotSupportedException">The <see cref="T:System.Collections.CollectionBase" /> is read-only.
-or-
The <see cref="T:System.Collections.CollectionBase" /> has a fixed size.</exception>
    </member>
    <member name="T:System.Collections.DictionaryBase">
      <summary>Provides the <see langword="abstract" /> base class for a strongly typed collection of key/value pairs.</summary>
    </member>
    <member name="M:System.Collections.DictionaryBase.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Collections.DictionaryBase" /> class.</summary>
    </member>
    <member name="M:System.Collections.DictionaryBase.Clear">
      <summary>Clears the contents of the <see cref="T:System.Collections.DictionaryBase" /> instance.</summary>
    </member>
    <member name="M:System.Collections.DictionaryBase.CopyTo(System.Array,System.Int32)">
      <summary>Copies the <see cref="T:System.Collections.DictionaryBase" /> elements to a one-dimensional <see cref="T:System.Array" /> at the specified index.</summary>
      <param name="array">The one-dimensional <see cref="T:System.Array" /> that is the destination of the <see cref="T:System.Collections.DictionaryEntry" /> objects copied from the <see cref="T:System.Collections.DictionaryBase" /> instance. The <see cref="T:System.Array" /> must have zero-based indexing.</param>
      <param name="index">The zero-based index in <paramref name="array" /> at which copying begins.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> is less than zero.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="array" /> is multidimensional.
-or-
The number of elements in the source <see cref="T:System.Collections.DictionaryBase" /> is greater than the available space from <paramref name="index" /> to the end of the destination <paramref name="array" />.</exception>
      <exception cref="T:System.InvalidCastException">The type of the source <see cref="T:System.Collections.DictionaryBase" /> cannot be cast automatically to the type of the destination <paramref name="array" />.</exception>
    </member>
    <member name="P:System.Collections.DictionaryBase.Count">
      <summary>Gets the number of elements contained in the <see cref="T:System.Collections.DictionaryBase" /> instance.</summary>
      <returns>The number of elements contained in the <see cref="T:System.Collections.DictionaryBase" /> instance.</returns>
    </member>
    <member name="P:System.Collections.DictionaryBase.Dictionary">
      <summary>Gets the list of elements contained in the <see cref="T:System.Collections.DictionaryBase" /> instance.</summary>
      <returns>An <see cref="T:System.Collections.IDictionary" /> representing the <see cref="T:System.Collections.DictionaryBase" /> instance itself.</returns>
    </member>
    <member name="M:System.Collections.DictionaryBase.GetEnumerator">
      <summary>Returns an <see cref="T:System.Collections.IDictionaryEnumerator" /> that iterates through the <see cref="T:System.Collections.DictionaryBase" /> instance.</summary>
      <returns>An <see cref="T:System.Collections.IDictionaryEnumerator" /> for the <see cref="T:System.Collections.DictionaryBase" /> instance.</returns>
    </member>
    <member name="P:System.Collections.DictionaryBase.InnerHashtable">
      <summary>Gets the list of elements contained in the <see cref="T:System.Collections.DictionaryBase" /> instance.</summary>
      <returns>A <see cref="T:System.Collections.Hashtable" /> representing the <see cref="T:System.Collections.DictionaryBase" /> instance itself.</returns>
    </member>
    <member name="M:System.Collections.DictionaryBase.OnClear">
      <summary>Performs additional custom processes before clearing the contents of the <see cref="T:System.Collections.DictionaryBase" /> instance.</summary>
    </member>
    <member name="M:System.Collections.DictionaryBase.OnClearComplete">
      <summary>Performs additional custom processes after clearing the contents of the <see cref="T:System.Collections.DictionaryBase" /> instance.</summary>
    </member>
    <member name="M:System.Collections.DictionaryBase.OnGet(System.Object,System.Object)">
      <summary>Gets the element with the specified key and value in the <see cref="T:System.Collections.DictionaryBase" /> instance.</summary>
      <param name="key">The key of the element to get.</param>
      <param name="currentValue">The current value of the element associated with <paramref name="key" />.</param>
      <returns>An <see cref="T:System.Object" /> containing the element with the specified key and value.</returns>
    </member>
    <member name="M:System.Collections.DictionaryBase.OnInsert(System.Object,System.Object)">
      <summary>Performs additional custom processes before inserting a new element into the <see cref="T:System.Collections.DictionaryBase" /> instance.</summary>
      <param name="key">The key of the element to insert.</param>
      <param name="value">The value of the element to insert.</param>
    </member>
    <member name="M:System.Collections.DictionaryBase.OnInsertComplete(System.Object,System.Object)">
      <summary>Performs additional custom processes after inserting a new element into the <see cref="T:System.Collections.DictionaryBase" /> instance.</summary>
      <param name="key">The key of the element to insert.</param>
      <param name="value">The value of the element to insert.</param>
    </member>
    <member name="M:System.Collections.DictionaryBase.OnRemove(System.Object,System.Object)">
      <summary>Performs additional custom processes before removing an element from the <see cref="T:System.Collections.DictionaryBase" /> instance.</summary>
      <param name="key">The key of the element to remove.</param>
      <param name="value">The value of the element to remove.</param>
    </member>
    <member name="M:System.Collections.DictionaryBase.OnRemoveComplete(System.Object,System.Object)">
      <summary>Performs additional custom processes after removing an element from the <see cref="T:System.Collections.DictionaryBase" /> instance.</summary>
      <param name="key">The key of the element to remove.</param>
      <param name="value">The value of the element to remove.</param>
    </member>
    <member name="M:System.Collections.DictionaryBase.OnSet(System.Object,System.Object,System.Object)">
      <summary>Performs additional custom processes before setting a value in the <see cref="T:System.Collections.DictionaryBase" /> instance.</summary>
      <param name="key">The key of the element to locate.</param>
      <param name="oldValue">The old value of the element associated with <paramref name="key" />.</param>
      <param name="newValue">The new value of the element associated with <paramref name="key" />.</param>
    </member>
    <member name="M:System.Collections.DictionaryBase.OnSetComplete(System.Object,System.Object,System.Object)">
      <summary>Performs additional custom processes after setting a value in the <see cref="T:System.Collections.DictionaryBase" /> instance.</summary>
      <param name="key">The key of the element to locate.</param>
      <param name="oldValue">The old value of the element associated with <paramref name="key" />.</param>
      <param name="newValue">The new value of the element associated with <paramref name="key" />.</param>
    </member>
    <member name="M:System.Collections.DictionaryBase.OnValidate(System.Object,System.Object)">
      <summary>Performs additional custom processes when validating the element with the specified key and value.</summary>
      <param name="key">The key of the element to validate.</param>
      <param name="value">The value of the element to validate.</param>
    </member>
    <member name="P:System.Collections.DictionaryBase.System#Collections#ICollection#IsSynchronized">
      <summary>Gets a value indicating whether access to a <see cref="T:System.Collections.DictionaryBase" /> object is synchronized (thread safe).</summary>
      <returns>
        <see langword="true" /> if access to the <see cref="T:System.Collections.DictionaryBase" /> object is synchronized (thread safe); otherwise, <see langword="false" />. The default is <see langword="false" />.</returns>
    </member>
    <member name="P:System.Collections.DictionaryBase.System#Collections#ICollection#SyncRoot">
      <summary>Gets an object that can be used to synchronize access to a <see cref="T:System.Collections.DictionaryBase" /> object.</summary>
      <returns>An object that can be used to synchronize access to the <see cref="T:System.Collections.DictionaryBase" /> object.</returns>
    </member>
    <member name="M:System.Collections.DictionaryBase.System#Collections#IDictionary#Add(System.Object,System.Object)">
      <summary>Adds an element with the specified key and value into the <see cref="T:System.Collections.DictionaryBase" />.</summary>
      <param name="key">The key of the element to add.</param>
      <param name="value">The value of the element to add.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="key" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentException">An element with the same key already exists in the <see cref="T:System.Collections.DictionaryBase" />.</exception>
      <exception cref="T:System.NotSupportedException">The <see cref="T:System.Collections.DictionaryBase" /> is read-only.
-or-
The <see cref="T:System.Collections.DictionaryBase" /> has a fixed size.</exception>
    </member>
    <member name="M:System.Collections.DictionaryBase.System#Collections#IDictionary#Contains(System.Object)">
      <summary>Determines whether the <see cref="T:System.Collections.DictionaryBase" /> contains a specific key.</summary>
      <param name="key">The key to locate in the <see cref="T:System.Collections.DictionaryBase" />.</param>
      <returns>
        <see langword="true" /> if the <see cref="T:System.Collections.DictionaryBase" /> contains an element with the specified key; otherwise, <see langword="false" />.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="key" /> is <see langword="null" />.</exception>
    </member>
    <member name="P:System.Collections.DictionaryBase.System#Collections#IDictionary#IsFixedSize">
      <summary>Gets a value indicating whether a <see cref="T:System.Collections.DictionaryBase" /> object has a fixed size.</summary>
      <returns>
        <see langword="true" /> if the <see cref="T:System.Collections.DictionaryBase" /> object has a fixed size; otherwise, <see langword="false" />. The default is <see langword="false" />.</returns>
    </member>
    <member name="P:System.Collections.DictionaryBase.System#Collections#IDictionary#IsReadOnly">
      <summary>Gets a value indicating whether a <see cref="T:System.Collections.DictionaryBase" /> object is read-only.</summary>
      <returns>
        <see langword="true" /> if the <see cref="T:System.Collections.DictionaryBase" /> object is read-only; otherwise, <see langword="false" />. The default is <see langword="false" />.</returns>
    </member>
    <member name="P:System.Collections.DictionaryBase.System#Collections#IDictionary#Item(System.Object)">
      <summary>Gets or sets the value associated with the specified key.</summary>
      <param name="key">The key whose value to get or set.</param>
      <returns>The value associated with the specified key. If the specified key is not found, attempting to get it returns <see langword="null" />, and attempting to set it creates a new element using the specified key.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="key" /> is <see langword="null" />.</exception>
      <exception cref="T:System.NotSupportedException">The property is set and the <see cref="T:System.Collections.DictionaryBase" /> is read-only.
-or-
The property is set, <paramref name="key" /> does not exist in the collection, and the <see cref="T:System.Collections.DictionaryBase" /> has a fixed size.</exception>
    </member>
    <member name="P:System.Collections.DictionaryBase.System#Collections#IDictionary#Keys">
      <summary>Gets an <see cref="T:System.Collections.ICollection" /> object containing the keys in the <see cref="T:System.Collections.DictionaryBase" /> object.</summary>
      <returns>An <see cref="T:System.Collections.ICollection" /> object containing the keys in the <see cref="T:System.Collections.DictionaryBase" /> object.</returns>
    </member>
    <member name="M:System.Collections.DictionaryBase.System#Collections#IDictionary#Remove(System.Object)">
      <summary>Removes the element with the specified key from the <see cref="T:System.Collections.DictionaryBase" />.</summary>
      <param name="key">The key of the element to remove.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="key" /> is <see langword="null" />.</exception>
      <exception cref="T:System.NotSupportedException">The <see cref="T:System.Collections.DictionaryBase" /> is read-only.
-or-
The <see cref="T:System.Collections.DictionaryBase" /> has a fixed size.</exception>
    </member>
    <member name="P:System.Collections.DictionaryBase.System#Collections#IDictionary#Values">
      <summary>Gets an <see cref="T:System.Collections.ICollection" /> object containing the values in the <see cref="T:System.Collections.DictionaryBase" /> object.</summary>
      <returns>An <see cref="T:System.Collections.ICollection" /> object containing the values in the <see cref="T:System.Collections.DictionaryBase" /> object.</returns>
    </member>
    <member name="M:System.Collections.DictionaryBase.System#Collections#IEnumerable#GetEnumerator">
      <summary>Returns an <see cref="T:System.Collections.IEnumerator" /> that iterates through the <see cref="T:System.Collections.DictionaryBase" />.</summary>
      <returns>An <see cref="T:System.Collections.IEnumerator" /> for the <see cref="T:System.Collections.DictionaryBase" />.</returns>
    </member>
    <member name="T:System.Collections.Queue">
      <summary>Represents a first-in, first-out collection of objects.</summary>
    </member>
    <member name="M:System.Collections.Queue.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Collections.Queue" /> class that is empty, has the default initial capacity, and uses the default growth factor.</summary>
    </member>
    <member name="M:System.Collections.Queue.#ctor(System.Collections.ICollection)">
      <summary>Initializes a new instance of the <see cref="T:System.Collections.Queue" /> class that contains elements copied from the specified collection, has the same initial capacity as the number of elements copied, and uses the default growth factor.</summary>
      <param name="col">The <see cref="T:System.Collections.ICollection" /> to copy elements from.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="col" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Collections.Queue.#ctor(System.Int32)">
      <summary>Initializes a new instance of the <see cref="T:System.Collections.Queue" /> class that is empty, has the specified initial capacity, and uses the default growth factor.</summary>
      <param name="capacity">The initial number of elements that the <see cref="T:System.Collections.Queue" /> can contain.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="capacity" /> is less than zero.</exception>
    </member>
    <member name="M:System.Collections.Queue.#ctor(System.Int32,System.Single)">
      <summary>Initializes a new instance of the <see cref="T:System.Collections.Queue" /> class that is empty, has the specified initial capacity, and uses the specified growth factor.</summary>
      <param name="capacity">The initial number of elements that the <see cref="T:System.Collections.Queue" /> can contain.</param>
      <param name="growFactor">The factor by which the capacity of the <see cref="T:System.Collections.Queue" /> is expanded.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="capacity" /> is less than zero.
-or-
<paramref name="growFactor" /> is less than 1.0 or greater than 10.0.</exception>
    </member>
    <member name="M:System.Collections.Queue.Clear">
      <summary>Removes all objects from the <see cref="T:System.Collections.Queue" />.</summary>
    </member>
    <member name="M:System.Collections.Queue.Clone">
      <summary>Creates a shallow copy of the <see cref="T:System.Collections.Queue" />.</summary>
      <returns>A shallow copy of the <see cref="T:System.Collections.Queue" />.</returns>
    </member>
    <member name="M:System.Collections.Queue.Contains(System.Object)">
      <summary>Determines whether an element is in the <see cref="T:System.Collections.Queue" />.</summary>
      <param name="obj">The <see cref="T:System.Object" /> to locate in the <see cref="T:System.Collections.Queue" />. The value can be <see langword="null" />.</param>
      <returns>
        <see langword="true" /> if <paramref name="obj" /> is found in the <see cref="T:System.Collections.Queue" />; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Collections.Queue.CopyTo(System.Array,System.Int32)">
      <summary>Copies the <see cref="T:System.Collections.Queue" /> elements to an existing one-dimensional <see cref="T:System.Array" />, starting at the specified array index.</summary>
      <param name="array">The one-dimensional <see cref="T:System.Array" /> that is the destination of the elements copied from <see cref="T:System.Collections.Queue" />. The <see cref="T:System.Array" /> must have zero-based indexing.</param>
      <param name="index">The zero-based index in <paramref name="array" /> at which copying begins.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> is less than zero.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="array" /> is multidimensional.
-or-
The number of elements in the source <see cref="T:System.Collections.Queue" /> is greater than the available space from <paramref name="index" /> to the end of the destination <paramref name="array" />.</exception>
      <exception cref="T:System.ArrayTypeMismatchException">The type of the source <see cref="T:System.Collections.Queue" /> cannot be cast automatically to the type of the destination <paramref name="array" />.</exception>
    </member>
    <member name="P:System.Collections.Queue.Count">
      <summary>Gets the number of elements contained in the <see cref="T:System.Collections.Queue" />.</summary>
      <returns>The number of elements contained in the <see cref="T:System.Collections.Queue" />.</returns>
    </member>
    <member name="M:System.Collections.Queue.Dequeue">
      <summary>Removes and returns the object at the beginning of the <see cref="T:System.Collections.Queue" />.</summary>
      <returns>The object that is removed from the beginning of the <see cref="T:System.Collections.Queue" />.</returns>
      <exception cref="T:System.InvalidOperationException">The <see cref="T:System.Collections.Queue" /> is empty.</exception>
    </member>
    <member name="M:System.Collections.Queue.Enqueue(System.Object)">
      <summary>Adds an object to the end of the <see cref="T:System.Collections.Queue" />.</summary>
      <param name="obj">The object to add to the <see cref="T:System.Collections.Queue" />. The value can be <see langword="null" />.</param>
    </member>
    <member name="M:System.Collections.Queue.GetEnumerator">
      <summary>Returns an enumerator that iterates through the <see cref="T:System.Collections.Queue" />.</summary>
      <returns>An <see cref="T:System.Collections.IEnumerator" /> for the <see cref="T:System.Collections.Queue" />.</returns>
    </member>
    <member name="P:System.Collections.Queue.IsSynchronized">
      <summary>Gets a value indicating whether access to the <see cref="T:System.Collections.Queue" /> is synchronized (thread safe).</summary>
      <returns>
        <see langword="true" /> if access to the <see cref="T:System.Collections.Queue" /> is synchronized (thread safe); otherwise, <see langword="false" />. The default is <see langword="false" />.</returns>
    </member>
    <member name="M:System.Collections.Queue.Peek">
      <summary>Returns the object at the beginning of the <see cref="T:System.Collections.Queue" /> without removing it.</summary>
      <returns>The object at the beginning of the <see cref="T:System.Collections.Queue" />.</returns>
      <exception cref="T:System.InvalidOperationException">The <see cref="T:System.Collections.Queue" /> is empty.</exception>
    </member>
    <member name="M:System.Collections.Queue.Synchronized(System.Collections.Queue)">
      <summary>Returns a new <see cref="T:System.Collections.Queue" /> that wraps the original queue, and is thread safe.</summary>
      <param name="queue">The <see cref="T:System.Collections.Queue" /> to synchronize.</param>
      <returns>A <see cref="T:System.Collections.Queue" /> wrapper that is synchronized (thread safe).</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="queue" /> is <see langword="null" />.</exception>
    </member>
    <member name="P:System.Collections.Queue.SyncRoot">
      <summary>Gets an object that can be used to synchronize access to the <see cref="T:System.Collections.Queue" />.</summary>
      <returns>An object that can be used to synchronize access to the <see cref="T:System.Collections.Queue" />.</returns>
    </member>
    <member name="M:System.Collections.Queue.ToArray">
      <summary>Copies the <see cref="T:System.Collections.Queue" /> elements to a new array.</summary>
      <returns>A new array containing elements copied from the <see cref="T:System.Collections.Queue" />.</returns>
    </member>
    <member name="M:System.Collections.Queue.TrimToSize">
      <summary>Sets the capacity to the actual number of elements in the <see cref="T:System.Collections.Queue" />.</summary>
      <exception cref="T:System.NotSupportedException">The <see cref="T:System.Collections.Queue" /> is read-only.</exception>
    </member>
    <member name="T:System.Collections.ReadOnlyCollectionBase">
      <summary>Provides the <see langword="abstract" /> base class for a strongly typed non-generic read-only collection.</summary>
    </member>
    <member name="M:System.Collections.ReadOnlyCollectionBase.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Collections.ReadOnlyCollectionBase" /> class.</summary>
    </member>
    <member name="P:System.Collections.ReadOnlyCollectionBase.Count">
      <summary>Gets the number of elements contained in the <see cref="T:System.Collections.ReadOnlyCollectionBase" /> instance.</summary>
      <returns>The number of elements contained in the <see cref="T:System.Collections.ReadOnlyCollectionBase" /> instance.
Retrieving the value of this property is an O(1) operation.</returns>
    </member>
    <member name="M:System.Collections.ReadOnlyCollectionBase.GetEnumerator">
      <summary>Returns an enumerator that iterates through the <see cref="T:System.Collections.ReadOnlyCollectionBase" /> instance.</summary>
      <returns>An <see cref="T:System.Collections.IEnumerator" /> for the <see cref="T:System.Collections.ReadOnlyCollectionBase" /> instance.</returns>
    </member>
    <member name="P:System.Collections.ReadOnlyCollectionBase.InnerList">
      <summary>Gets the list of elements contained in the <see cref="T:System.Collections.ReadOnlyCollectionBase" /> instance.</summary>
      <returns>An <see cref="T:System.Collections.ArrayList" /> representing the <see cref="T:System.Collections.ReadOnlyCollectionBase" /> instance itself.</returns>
    </member>
    <member name="M:System.Collections.ReadOnlyCollectionBase.System#Collections#ICollection#CopyTo(System.Array,System.Int32)">
      <summary>Copies the entire <see cref="T:System.Collections.ReadOnlyCollectionBase" /> to a compatible one-dimensional <see cref="T:System.Array" />, starting at the specified index of the target array.</summary>
      <param name="array">The one-dimensional <see cref="T:System.Array" /> that is the destination of the elements copied from <see cref="T:System.Collections.ReadOnlyCollectionBase" />. The <see cref="T:System.Array" /> must have zero-based indexing.</param>
      <param name="index">The zero-based index in <paramref name="array" /> at which copying begins.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> is less than zero.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="array" /> is multidimensional.
-or-
The number of elements in the source <see cref="T:System.Collections.ReadOnlyCollectionBase" /> is greater than the available space from <paramref name="index" /> to the end of the destination <paramref name="array" />.</exception>
      <exception cref="T:System.InvalidCastException">The type of the source <see cref="T:System.Collections.ReadOnlyCollectionBase" /> cannot be cast automatically to the type of the destination <paramref name="array" />.</exception>
    </member>
    <member name="P:System.Collections.ReadOnlyCollectionBase.System#Collections#ICollection#IsSynchronized">
      <summary>Gets a value indicating whether access to a <see cref="T:System.Collections.ReadOnlyCollectionBase" /> object is synchronized (thread safe).</summary>
      <returns>
        <see langword="true" /> if access to the <see cref="T:System.Collections.ReadOnlyCollectionBase" /> object is synchronized (thread safe); otherwise, <see langword="false" />. The default is <see langword="false" />.</returns>
    </member>
    <member name="P:System.Collections.ReadOnlyCollectionBase.System#Collections#ICollection#SyncRoot">
      <summary>Gets an object that can be used to synchronize access to a <see cref="T:System.Collections.ReadOnlyCollectionBase" /> object.</summary>
      <returns>An object that can be used to synchronize access to the <see cref="T:System.Collections.ReadOnlyCollectionBase" /> object.</returns>
    </member>
    <member name="T:System.Collections.SortedList">
      <summary>Represents a collection of key/value pairs that are sorted by the keys and are accessible by key and by index.</summary>
    </member>
    <member name="M:System.Collections.SortedList.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Collections.SortedList" /> class that is empty, has the default initial capacity, and is sorted according to the <see cref="T:System.IComparable" /> interface implemented by each key added to the <see cref="T:System.Collections.SortedList" /> object.</summary>
    </member>
    <member name="M:System.Collections.SortedList.#ctor(System.Collections.IComparer)">
      <summary>Initializes a new instance of the <see cref="T:System.Collections.SortedList" /> class that is empty, has the default initial capacity, and is sorted according to the specified <see cref="T:System.Collections.IComparer" /> interface.</summary>
      <param name="comparer">The <see cref="T:System.Collections.IComparer" /> implementation to use when comparing keys.
-or-
<see langword="null" /> to use the <see cref="T:System.IComparable" /> implementation of each key.</param>
    </member>
    <member name="M:System.Collections.SortedList.#ctor(System.Collections.IComparer,System.Int32)">
      <summary>Initializes a new instance of the <see cref="T:System.Collections.SortedList" /> class that is empty, has the specified initial capacity, and is sorted according to the specified <see cref="T:System.Collections.IComparer" /> interface.</summary>
      <param name="comparer">The <see cref="T:System.Collections.IComparer" /> implementation to use when comparing keys.
-or-
<see langword="null" /> to use the <see cref="T:System.IComparable" /> implementation of each key.</param>
      <param name="capacity">The initial number of elements that the <see cref="T:System.Collections.SortedList" /> object can contain.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="capacity" /> is less than zero.</exception>
      <exception cref="T:System.OutOfMemoryException">There is not enough available memory to create a <see cref="T:System.Collections.SortedList" /> object with the specified <paramref name="capacity" />.</exception>
    </member>
    <member name="M:System.Collections.SortedList.#ctor(System.Collections.IDictionary)">
      <summary>Initializes a new instance of the <see cref="T:System.Collections.SortedList" /> class that contains elements copied from the specified dictionary, has the same initial capacity as the number of elements copied, and is sorted according to the <see cref="T:System.IComparable" /> interface implemented by each key.</summary>
      <param name="d">The <see cref="T:System.Collections.IDictionary" /> implementation to copy to a new <see cref="T:System.Collections.SortedList" /> object.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="d" /> is <see langword="null" />.</exception>
      <exception cref="T:System.InvalidCastException">One or more elements in <paramref name="d" /> do not implement the <see cref="T:System.IComparable" /> interface.</exception>
    </member>
    <member name="M:System.Collections.SortedList.#ctor(System.Collections.IDictionary,System.Collections.IComparer)">
      <summary>Initializes a new instance of the <see cref="T:System.Collections.SortedList" /> class that contains elements copied from the specified dictionary, has the same initial capacity as the number of elements copied, and is sorted according to the specified <see cref="T:System.Collections.IComparer" /> interface.</summary>
      <param name="d">The <see cref="T:System.Collections.IDictionary" /> implementation to copy to a new <see cref="T:System.Collections.SortedList" /> object.</param>
      <param name="comparer">The <see cref="T:System.Collections.IComparer" /> implementation to use when comparing keys.
-or-
<see langword="null" /> to use the <see cref="T:System.IComparable" /> implementation of each key.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="d" /> is <see langword="null" />.</exception>
      <exception cref="T:System.InvalidCastException">
        <paramref name="comparer" /> is <see langword="null" />, and one or more elements in <paramref name="d" /> do not implement the <see cref="T:System.IComparable" /> interface.</exception>
    </member>
    <member name="M:System.Collections.SortedList.#ctor(System.Int32)">
      <summary>Initializes a new instance of the <see cref="T:System.Collections.SortedList" /> class that is empty, has the specified initial capacity, and is sorted according to the <see cref="T:System.IComparable" /> interface implemented by each key added to the <see cref="T:System.Collections.SortedList" /> object.</summary>
      <param name="initialCapacity">The initial number of elements that the <see cref="T:System.Collections.SortedList" /> object can contain.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="initialCapacity" /> is less than zero.</exception>
      <exception cref="T:System.OutOfMemoryException">There is not enough available memory to create a <see cref="T:System.Collections.SortedList" /> object with the specified <paramref name="initialCapacity" />.</exception>
    </member>
    <member name="M:System.Collections.SortedList.Add(System.Object,System.Object)">
      <summary>Adds an element with the specified key and value to a <see cref="T:System.Collections.SortedList" /> object.</summary>
      <param name="key">The key of the element to add.</param>
      <param name="value">The value of the element to add. The value can be <see langword="null" />.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="key" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentException">An element with the specified <paramref name="key" /> already exists in the <see cref="T:System.Collections.SortedList" /> object.
-or-
The <see cref="T:System.Collections.SortedList" /> is set to use the <see cref="T:System.IComparable" /> interface, and <paramref name="key" /> does not implement the <see cref="T:System.IComparable" /> interface.</exception>
      <exception cref="T:System.NotSupportedException">The <see cref="T:System.Collections.SortedList" /> is read-only.
-or-
The <see cref="T:System.Collections.SortedList" /> has a fixed size.</exception>
      <exception cref="T:System.OutOfMemoryException">There is not enough available memory to add the element to the <see cref="T:System.Collections.SortedList" />.</exception>
      <exception cref="T:System.InvalidOperationException">The comparer throws an exception.</exception>
    </member>
    <member name="P:System.Collections.SortedList.Capacity">
      <summary>Gets or sets the capacity of a <see cref="T:System.Collections.SortedList" /> object.</summary>
      <returns>The number of elements that the <see cref="T:System.Collections.SortedList" /> object can contain.</returns>
      <exception cref="T:System.ArgumentOutOfRangeException">The value assigned is less than the current number of elements in the <see cref="T:System.Collections.SortedList" /> object.</exception>
      <exception cref="T:System.OutOfMemoryException">There is not enough memory available on the system.</exception>
    </member>
    <member name="M:System.Collections.SortedList.Clear">
      <summary>Removes all elements from a <see cref="T:System.Collections.SortedList" /> object.</summary>
      <exception cref="T:System.NotSupportedException">The <see cref="T:System.Collections.SortedList" /> object is read-only.
-or-
The <see cref="T:System.Collections.SortedList" /> has a fixed size.</exception>
    </member>
    <member name="M:System.Collections.SortedList.Clone">
      <summary>Creates a shallow copy of a <see cref="T:System.Collections.SortedList" /> object.</summary>
      <returns>A shallow copy of the <see cref="T:System.Collections.SortedList" /> object.</returns>
    </member>
    <member name="M:System.Collections.SortedList.Contains(System.Object)">
      <summary>Determines whether a <see cref="T:System.Collections.SortedList" /> object contains a specific key.</summary>
      <param name="key">The key to locate in the <see cref="T:System.Collections.SortedList" /> object.</param>
      <returns>
        <see langword="true" /> if the <see cref="T:System.Collections.SortedList" /> object contains an element with the specified <paramref name="key" />; otherwise, <see langword="false" />.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="key" /> is <see langword="null" />.</exception>
      <exception cref="T:System.InvalidOperationException">The comparer throws an exception.</exception>
    </member>
    <member name="M:System.Collections.SortedList.ContainsKey(System.Object)">
      <summary>Determines whether a <see cref="T:System.Collections.SortedList" /> object contains a specific key.</summary>
      <param name="key">The key to locate in the <see cref="T:System.Collections.SortedList" /> object.</param>
      <returns>
        <see langword="true" /> if the <see cref="T:System.Collections.SortedList" /> object contains an element with the specified <paramref name="key" />; otherwise, <see langword="false" />.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="key" /> is <see langword="null" />.</exception>
      <exception cref="T:System.InvalidOperationException">The comparer throws an exception.</exception>
    </member>
    <member name="M:System.Collections.SortedList.ContainsValue(System.Object)">
      <summary>Determines whether a <see cref="T:System.Collections.SortedList" /> object contains a specific value.</summary>
      <param name="value">The value to locate in the <see cref="T:System.Collections.SortedList" /> object. The value can be <see langword="null" />.</param>
      <returns>
        <see langword="true" /> if the <see cref="T:System.Collections.SortedList" /> object contains an element with the specified <paramref name="value" />; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Collections.SortedList.CopyTo(System.Array,System.Int32)">
      <summary>Copies <see cref="T:System.Collections.SortedList" /> elements to a one-dimensional <see cref="T:System.Array" /> object, starting at the specified index in the array.</summary>
      <param name="array">The one-dimensional <see cref="T:System.Array" /> object that is the destination of the <see cref="T:System.Collections.DictionaryEntry" /> objects copied from <see cref="T:System.Collections.SortedList" />. The <see cref="T:System.Array" /> must have zero-based indexing.</param>
      <param name="arrayIndex">The zero-based index in <paramref name="array" /> at which copying begins.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="arrayIndex" /> is less than zero.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="array" /> is multidimensional.
-or-
The number of elements in the source <see cref="T:System.Collections.SortedList" /> object is greater than the available space from <paramref name="arrayIndex" /> to the end of the destination <paramref name="array" />.</exception>
      <exception cref="T:System.InvalidCastException">The type of the source <see cref="T:System.Collections.SortedList" /> cannot be cast automatically to the type of the destination <paramref name="array" />.</exception>
    </member>
    <member name="P:System.Collections.SortedList.Count">
      <summary>Gets the number of elements contained in a <see cref="T:System.Collections.SortedList" /> object.</summary>
      <returns>The number of elements contained in the <see cref="T:System.Collections.SortedList" /> object.</returns>
    </member>
    <member name="M:System.Collections.SortedList.GetByIndex(System.Int32)">
      <summary>Gets the value at the specified index of a <see cref="T:System.Collections.SortedList" /> object.</summary>
      <param name="index">The zero-based index of the value to get.</param>
      <returns>The value at the specified index of the <see cref="T:System.Collections.SortedList" /> object.</returns>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> is outside the range of valid indexes for the <see cref="T:System.Collections.SortedList" /> object.</exception>
    </member>
    <member name="M:System.Collections.SortedList.GetEnumerator">
      <summary>Returns an <see cref="T:System.Collections.IDictionaryEnumerator" /> object that iterates through a <see cref="T:System.Collections.SortedList" /> object.</summary>
      <returns>An <see cref="T:System.Collections.IDictionaryEnumerator" /> object for the <see cref="T:System.Collections.SortedList" /> object.</returns>
    </member>
    <member name="M:System.Collections.SortedList.GetKey(System.Int32)">
      <summary>Gets the key at the specified index of a <see cref="T:System.Collections.SortedList" /> object.</summary>
      <param name="index">The zero-based index of the key to get.</param>
      <returns>The key at the specified index of the <see cref="T:System.Collections.SortedList" /> object.</returns>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> is outside the range of valid indexes for the <see cref="T:System.Collections.SortedList" /> object.</exception>
    </member>
    <member name="M:System.Collections.SortedList.GetKeyList">
      <summary>Gets the keys in a <see cref="T:System.Collections.SortedList" /> object.</summary>
      <returns>An <see cref="T:System.Collections.IList" /> object containing the keys in the <see cref="T:System.Collections.SortedList" /> object.</returns>
    </member>
    <member name="M:System.Collections.SortedList.GetValueList">
      <summary>Gets the values in a <see cref="T:System.Collections.SortedList" /> object.</summary>
      <returns>An <see cref="T:System.Collections.IList" /> object containing the values in the <see cref="T:System.Collections.SortedList" /> object.</returns>
    </member>
    <member name="M:System.Collections.SortedList.IndexOfKey(System.Object)">
      <summary>Returns the zero-based index of the specified key in a <see cref="T:System.Collections.SortedList" /> object.</summary>
      <param name="key">The key to locate in the <see cref="T:System.Collections.SortedList" /> object.</param>
      <returns>The zero-based index of the <paramref name="key" /> parameter, if <paramref name="key" /> is found in the <see cref="T:System.Collections.SortedList" /> object; otherwise, -1.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="key" /> is <see langword="null" />.</exception>
      <exception cref="T:System.InvalidOperationException">The comparer throws an exception.</exception>
    </member>
    <member name="M:System.Collections.SortedList.IndexOfValue(System.Object)">
      <summary>Returns the zero-based index of the first occurrence of the specified value in a <see cref="T:System.Collections.SortedList" /> object.</summary>
      <param name="value">The value to locate in the <see cref="T:System.Collections.SortedList" /> object. The value can be <see langword="null" />.</param>
      <returns>The zero-based index of the first occurrence of the <paramref name="value" /> parameter, if <paramref name="value" /> is found in the <see cref="T:System.Collections.SortedList" /> object; otherwise, -1.</returns>
    </member>
    <member name="P:System.Collections.SortedList.IsFixedSize">
      <summary>Gets a value indicating whether a <see cref="T:System.Collections.SortedList" /> object has a fixed size.</summary>
      <returns>
        <see langword="true" /> if the <see cref="T:System.Collections.SortedList" /> object has a fixed size; otherwise, <see langword="false" />. The default is <see langword="false" />.</returns>
    </member>
    <member name="P:System.Collections.SortedList.IsReadOnly">
      <summary>Gets a value indicating whether a <see cref="T:System.Collections.SortedList" /> object is read-only.</summary>
      <returns>
        <see langword="true" /> if the <see cref="T:System.Collections.SortedList" /> object is read-only; otherwise, <see langword="false" />. The default is <see langword="false" />.</returns>
    </member>
    <member name="P:System.Collections.SortedList.IsSynchronized">
      <summary>Gets a value indicating whether access to a <see cref="T:System.Collections.SortedList" /> object is synchronized (thread safe).</summary>
      <returns>
        <see langword="true" /> if access to the <see cref="T:System.Collections.SortedList" /> object is synchronized (thread safe); otherwise, <see langword="false" />. The default is <see langword="false" />.</returns>
    </member>
    <member name="P:System.Collections.SortedList.Item(System.Object)">
      <summary>Gets or sets the value associated with a specific key in a <see cref="T:System.Collections.SortedList" /> object.</summary>
      <param name="key">The key associated with the value to get or set.</param>
      <returns>The value associated with the <paramref name="key" /> parameter in the <see cref="T:System.Collections.SortedList" /> object, if <paramref name="key" /> is found; otherwise, <see langword="null" />.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="key" /> is <see langword="null" />.</exception>
      <exception cref="T:System.NotSupportedException">The property is set and the <see cref="T:System.Collections.SortedList" /> object is read-only.
-or-
The property is set, <paramref name="key" /> does not exist in the collection, and the <see cref="T:System.Collections.SortedList" /> has a fixed size.</exception>
      <exception cref="T:System.OutOfMemoryException">There is not enough available memory to add the element to the <see cref="T:System.Collections.SortedList" />.</exception>
      <exception cref="T:System.InvalidOperationException">The comparer throws an exception.</exception>
    </member>
    <member name="P:System.Collections.SortedList.Keys">
      <summary>Gets the keys in a <see cref="T:System.Collections.SortedList" /> object.</summary>
      <returns>An <see cref="T:System.Collections.ICollection" /> object containing the keys in the <see cref="T:System.Collections.SortedList" /> object.</returns>
    </member>
    <member name="M:System.Collections.SortedList.Remove(System.Object)">
      <summary>Removes the element with the specified key from a <see cref="T:System.Collections.SortedList" /> object.</summary>
      <param name="key">The key of the element to remove.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="key" /> is <see langword="null" />.</exception>
      <exception cref="T:System.NotSupportedException">The <see cref="T:System.Collections.SortedList" /> object is read-only.
-or-
The <see cref="T:System.Collections.SortedList" /> has a fixed size.</exception>
    </member>
    <member name="M:System.Collections.SortedList.RemoveAt(System.Int32)">
      <summary>Removes the element at the specified index of a <see cref="T:System.Collections.SortedList" /> object.</summary>
      <param name="index">The zero-based index of the element to remove.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> is outside the range of valid indexes for the <see cref="T:System.Collections.SortedList" /> object.</exception>
      <exception cref="T:System.NotSupportedException">The <see cref="T:System.Collections.SortedList" /> is read-only.
-or-
The <see cref="T:System.Collections.SortedList" /> has a fixed size.</exception>
    </member>
    <member name="M:System.Collections.SortedList.SetByIndex(System.Int32,System.Object)">
      <summary>Replaces the value at a specific index in a <see cref="T:System.Collections.SortedList" /> object.</summary>
      <param name="index">The zero-based index at which to save <paramref name="value" />.</param>
      <param name="value">The <see cref="T:System.Object" /> to save into the <see cref="T:System.Collections.SortedList" /> object. The value can be <see langword="null" />.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> is outside the range of valid indexes for the <see cref="T:System.Collections.SortedList" /> object.</exception>
    </member>
    <member name="M:System.Collections.SortedList.Synchronized(System.Collections.SortedList)">
      <summary>Returns a synchronized (thread-safe) wrapper for a <see cref="T:System.Collections.SortedList" /> object.</summary>
      <param name="list">The <see cref="T:System.Collections.SortedList" /> object to synchronize.</param>
      <returns>A synchronized (thread-safe) wrapper for the <see cref="T:System.Collections.SortedList" /> object.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="list" /> is <see langword="null" />.</exception>
    </member>
    <member name="P:System.Collections.SortedList.SyncRoot">
      <summary>Gets an object that can be used to synchronize access to a <see cref="T:System.Collections.SortedList" /> object.</summary>
      <returns>An object that can be used to synchronize access to the <see cref="T:System.Collections.SortedList" /> object.</returns>
    </member>
    <member name="M:System.Collections.SortedList.System#Collections#IEnumerable#GetEnumerator">
      <summary>Returns an <see cref="T:System.Collections.IEnumerator" /> that iterates through the <see cref="T:System.Collections.SortedList" />.</summary>
      <returns>An <see cref="T:System.Collections.IEnumerator" /> for the <see cref="T:System.Collections.SortedList" />.</returns>
    </member>
    <member name="M:System.Collections.SortedList.TrimToSize">
      <summary>Sets the capacity to the actual number of elements in a <see cref="T:System.Collections.SortedList" /> object.</summary>
      <exception cref="T:System.NotSupportedException">The <see cref="T:System.Collections.SortedList" /> object is read-only.
-or-
The <see cref="T:System.Collections.SortedList" /> has a fixed size.</exception>
    </member>
    <member name="P:System.Collections.SortedList.Values">
      <summary>Gets the values in a <see cref="T:System.Collections.SortedList" /> object.</summary>
      <returns>An <see cref="T:System.Collections.ICollection" /> object containing the values in the <see cref="T:System.Collections.SortedList" /> object.</returns>
    </member>
    <member name="T:System.Collections.Specialized.CollectionsUtil">
      <summary>Creates collections that ignore the case in strings.</summary>
    </member>
    <member name="M:System.Collections.Specialized.CollectionsUtil.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Collections.Specialized.CollectionsUtil" /> class.</summary>
    </member>
    <member name="M:System.Collections.Specialized.CollectionsUtil.CreateCaseInsensitiveHashtable">
      <summary>Creates a new case-insensitive instance of the <see cref="T:System.Collections.Hashtable" /> class with the default initial capacity.</summary>
      <returns>A new case-insensitive instance of the <see cref="T:System.Collections.Hashtable" /> class with the default initial capacity.</returns>
    </member>
    <member name="M:System.Collections.Specialized.CollectionsUtil.CreateCaseInsensitiveHashtable(System.Collections.IDictionary)">
      <summary>Copies the entries from the specified dictionary to a new case-insensitive instance of the <see cref="T:System.Collections.Hashtable" /> class with the same initial capacity as the number of entries copied.</summary>
      <param name="d">The <see cref="T:System.Collections.IDictionary" /> to copy to a new case-insensitive <see cref="T:System.Collections.Hashtable" />.</param>
      <returns>A new case-insensitive instance of the <see cref="T:System.Collections.Hashtable" /> class containing the entries from the specified <see cref="T:System.Collections.IDictionary" />.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="d" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Collections.Specialized.CollectionsUtil.CreateCaseInsensitiveHashtable(System.Int32)">
      <summary>Creates a new case-insensitive instance of the <see cref="T:System.Collections.Hashtable" /> class with the specified initial capacity.</summary>
      <param name="capacity">The approximate number of entries that the <see cref="T:System.Collections.Hashtable" /> can initially contain.</param>
      <returns>A new case-insensitive instance of the <see cref="T:System.Collections.Hashtable" /> class with the specified initial capacity.</returns>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="capacity" /> is less than zero.</exception>
    </member>
    <member name="M:System.Collections.Specialized.CollectionsUtil.CreateCaseInsensitiveSortedList">
      <summary>Creates a new instance of the <see cref="T:System.Collections.SortedList" /> class that ignores the case of strings.</summary>
      <returns>A new instance of the <see cref="T:System.Collections.SortedList" /> class that ignores the case of strings.</returns>
    </member>
    <member name="T:System.Collections.Stack">
      <summary>Represents a simple last-in-first-out (LIFO) non-generic collection of objects.</summary>
    </member>
    <member name="M:System.Collections.Stack.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Collections.Stack" /> class that is empty and has the default initial capacity.</summary>
    </member>
    <member name="M:System.Collections.Stack.#ctor(System.Collections.ICollection)">
      <summary>Initializes a new instance of the <see cref="T:System.Collections.Stack" /> class that contains elements copied from the specified collection and has the same initial capacity as the number of elements copied.</summary>
      <param name="col">The <see cref="T:System.Collections.ICollection" /> to copy elements from.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="col" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Collections.Stack.#ctor(System.Int32)">
      <summary>Initializes a new instance of the <see cref="T:System.Collections.Stack" /> class that is empty and has the specified initial capacity or the default initial capacity, whichever is greater.</summary>
      <param name="initialCapacity">The initial number of elements that the <see cref="T:System.Collections.Stack" /> can contain.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="initialCapacity" /> is less than zero.</exception>
    </member>
    <member name="M:System.Collections.Stack.Clear">
      <summary>Removes all objects from the <see cref="T:System.Collections.Stack" />.</summary>
    </member>
    <member name="M:System.Collections.Stack.Clone">
      <summary>Creates a shallow copy of the <see cref="T:System.Collections.Stack" />.</summary>
      <returns>A shallow copy of the <see cref="T:System.Collections.Stack" />.</returns>
    </member>
    <member name="M:System.Collections.Stack.Contains(System.Object)">
      <summary>Determines whether an element is in the <see cref="T:System.Collections.Stack" />.</summary>
      <param name="obj">The object to locate in the <see cref="T:System.Collections.Stack" />. The value can be <see langword="null" />.</param>
      <returns>
        <see langword="true" />, if <paramref name="obj" /> is found in the <see cref="T:System.Collections.Stack" />; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Collections.Stack.CopyTo(System.Array,System.Int32)">
      <summary>Copies the <see cref="T:System.Collections.Stack" /> to an existing one-dimensional <see cref="T:System.Array" />, starting at the specified array index.</summary>
      <param name="array">The one-dimensional <see cref="T:System.Array" /> that is the destination of the elements copied from <see cref="T:System.Collections.Stack" />. The <see cref="T:System.Array" /> must have zero-based indexing.</param>
      <param name="index">The zero-based index in <paramref name="array" /> at which copying begins.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> is less than zero.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="array" /> is multidimensional.
-or-
The number of elements in the source <see cref="T:System.Collections.Stack" /> is greater than the available space from <paramref name="index" /> to the end of the destination <paramref name="array" />.</exception>
      <exception cref="T:System.InvalidCastException">The type of the source <see cref="T:System.Collections.Stack" /> cannot be cast automatically to the type of the destination <paramref name="array" />.</exception>
    </member>
    <member name="P:System.Collections.Stack.Count">
      <summary>Gets the number of elements contained in the <see cref="T:System.Collections.Stack" />.</summary>
      <returns>The number of elements contained in the <see cref="T:System.Collections.Stack" />.</returns>
    </member>
    <member name="M:System.Collections.Stack.GetEnumerator">
      <summary>Returns an <see cref="T:System.Collections.IEnumerator" /> for the <see cref="T:System.Collections.Stack" />.</summary>
      <returns>An <see cref="T:System.Collections.IEnumerator" /> for the <see cref="T:System.Collections.Stack" />.</returns>
    </member>
    <member name="P:System.Collections.Stack.IsSynchronized">
      <summary>Gets a value indicating whether access to the <see cref="T:System.Collections.Stack" /> is synchronized (thread safe).</summary>
      <returns>
        <see langword="true" />, if access to the <see cref="T:System.Collections.Stack" /> is synchronized (thread safe); otherwise, <see langword="false" />. The default is <see langword="false" />.</returns>
    </member>
    <member name="M:System.Collections.Stack.Peek">
      <summary>Returns the object at the top of the <see cref="T:System.Collections.Stack" /> without removing it.</summary>
      <returns>The <see cref="T:System.Object" /> at the top of the <see cref="T:System.Collections.Stack" />.</returns>
      <exception cref="T:System.InvalidOperationException">The <see cref="T:System.Collections.Stack" /> is empty.</exception>
    </member>
    <member name="M:System.Collections.Stack.Pop">
      <summary>Removes and returns the object at the top of the <see cref="T:System.Collections.Stack" />.</summary>
      <returns>The <see cref="T:System.Object" /> removed from the top of the <see cref="T:System.Collections.Stack" />.</returns>
      <exception cref="T:System.InvalidOperationException">The <see cref="T:System.Collections.Stack" /> is empty.</exception>
    </member>
    <member name="M:System.Collections.Stack.Push(System.Object)">
      <summary>Inserts an object at the top of the <see cref="T:System.Collections.Stack" />.</summary>
      <param name="obj">The <see cref="T:System.Object" /> to push onto the <see cref="T:System.Collections.Stack" />. The value can be <see langword="null" />.</param>
    </member>
    <member name="M:System.Collections.Stack.Synchronized(System.Collections.Stack)">
      <summary>Returns a synchronized (thread safe) wrapper for the <see cref="T:System.Collections.Stack" />.</summary>
      <param name="stack">The <see cref="T:System.Collections.Stack" /> to synchronize.</param>
      <returns>A synchronized wrapper around the <see cref="T:System.Collections.Stack" />.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="stack" /> is <see langword="null" />.</exception>
    </member>
    <member name="P:System.Collections.Stack.SyncRoot">
      <summary>Gets an object that can be used to synchronize access to the <see cref="T:System.Collections.Stack" />.</summary>
      <returns>An <see cref="T:System.Object" /> that can be used to synchronize access to the <see cref="T:System.Collections.Stack" />.</returns>
    </member>
    <member name="M:System.Collections.Stack.ToArray">
      <summary>Copies the <see cref="T:System.Collections.Stack" /> to a new array.</summary>
      <returns>A new array containing copies of the elements of the <see cref="T:System.Collections.Stack" />.</returns>
    </member>
  </members>
</doc>