﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>System.Diagnostics.TraceSource</name>
  </assembly>
  <members>
    <member name="T:System.Diagnostics.BooleanSwitch">
      <summary>Provides a simple on/off switch that controls debugging and tracing output.</summary>
    </member>
    <member name="M:System.Diagnostics.BooleanSwitch.#ctor(System.String,System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Diagnostics.BooleanSwitch" /> class with the specified display name and description.</summary>
      <param name="displayName">The name to display on a user interface.</param>
      <param name="description">The description of the switch.</param>
    </member>
    <member name="M:System.Diagnostics.BooleanSwitch.#ctor(System.String,System.String,System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Diagnostics.BooleanSwitch" /> class with the specified display name, description, and default switch value.</summary>
      <param name="displayName">The name to display on the user interface.</param>
      <param name="description">The description of the switch.</param>
      <param name="defaultSwitchValue">The default value of the switch.</param>
    </member>
    <member name="P:System.Diagnostics.BooleanSwitch.Enabled">
      <summary>Gets or sets a value indicating whether the switch is enabled or disabled.</summary>
      <returns>
        <see langword="true" /> if the switch is enabled; otherwise, <see langword="false" />. The default is <see langword="false" />.</returns>
      <exception cref="T:System.Security.SecurityException">The caller does not have the correct permission.</exception>
    </member>
    <member name="M:System.Diagnostics.BooleanSwitch.OnValueChanged">
      <summary>Determines whether the new value of the <see cref="P:System.Diagnostics.Switch.Value" /> property can be parsed as a Boolean value.</summary>
    </member>
    <member name="T:System.Diagnostics.CorrelationManager">
      <summary>Correlates traces that are part of a logical transaction.</summary>
    </member>
    <member name="P:System.Diagnostics.CorrelationManager.ActivityId">
      <summary>Gets or sets the identity for a global activity.</summary>
      <returns>A <see cref="T:System.Guid" /> structure that identifies the global activity.</returns>
    </member>
    <member name="P:System.Diagnostics.CorrelationManager.LogicalOperationStack">
      <summary>Gets the logical operation stack from the call context.</summary>
      <returns>A <see cref="T:System.Collections.Stack" /> object that represents the logical operation stack for the call context.</returns>
    </member>
    <member name="M:System.Diagnostics.CorrelationManager.StartLogicalOperation">
      <summary>Starts a logical operation on a thread.</summary>
    </member>
    <member name="M:System.Diagnostics.CorrelationManager.StartLogicalOperation(System.Object)">
      <summary>Starts a logical operation with the specified identity on a thread.</summary>
      <param name="operationId">An object identifying the operation.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="operationId" /> parameter is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Diagnostics.CorrelationManager.StopLogicalOperation">
      <summary>Stops the current logical operation.</summary>
      <exception cref="T:System.InvalidOperationException">The <see cref="P:System.Diagnostics.CorrelationManager.LogicalOperationStack" /> property is an empty stack.</exception>
    </member>
    <member name="T:System.Diagnostics.DefaultTraceListener">
      <summary>Provides the default output methods and behavior for tracing.</summary>
    </member>
    <member name="M:System.Diagnostics.DefaultTraceListener.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Diagnostics.DefaultTraceListener" /> class with "Default" as its <see cref="P:System.Diagnostics.TraceListener.Name" /> property value.</summary>
    </member>
    <member name="P:System.Diagnostics.DefaultTraceListener.AssertUiEnabled">
      <summary>Gets or sets a value indicating whether the application is running in user-interface mode.</summary>
      <returns>
        <see langword="true" /> if user-interface mode is enabled; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Diagnostics.DefaultTraceListener.Fail(System.String)">
      <summary>Emits or displays a message and a stack trace for an assertion that always fails.</summary>
      <param name="message">The message to emit or display.</param>
    </member>
    <member name="M:System.Diagnostics.DefaultTraceListener.Fail(System.String,System.String)">
      <summary>Emits or displays detailed messages and a stack trace for an assertion that always fails.</summary>
      <param name="message">The message to emit or display.</param>
      <param name="detailMessage">The detailed message to emit or display.</param>
    </member>
    <member name="P:System.Diagnostics.DefaultTraceListener.LogFileName">
      <summary>Gets or sets the name of a log file to write trace or debug messages to.</summary>
      <returns>The name of a log file to write trace or debug messages to.</returns>
    </member>
    <member name="M:System.Diagnostics.DefaultTraceListener.Write(System.String)">
      <summary>Writes the output to the <see langword="OutputDebugString" /> function and to the <see cref="M:System.Diagnostics.Debugger.Log(System.Int32,System.String,System.String)" /> method.</summary>
      <param name="message">The message to write to <see langword="OutputDebugString" /> and <see cref="M:System.Diagnostics.Debugger.Log(System.Int32,System.String,System.String)" />.</param>
    </member>
    <member name="M:System.Diagnostics.DefaultTraceListener.WriteLine(System.String)">
      <summary>Writes the output to the <see langword="OutputDebugString" /> function and to the <see cref="M:System.Diagnostics.Debugger.Log(System.Int32,System.String,System.String)" /> method, followed by a carriage return and line feed (\r\n).</summary>
      <param name="message">The message to write to <see langword="OutputDebugString" /> and <see cref="M:System.Diagnostics.Debugger.Log(System.Int32,System.String,System.String)" />.</param>
    </member>
    <member name="T:System.Diagnostics.EventTypeFilter">
      <summary>Indicates whether a listener should trace based on the event type.</summary>
    </member>
    <member name="M:System.Diagnostics.EventTypeFilter.#ctor(System.Diagnostics.SourceLevels)">
      <summary>Initializes a new instance of the <see cref="T:System.Diagnostics.EventTypeFilter" /> class.</summary>
      <param name="level">A bitwise combination of the <see cref="T:System.Diagnostics.SourceLevels" /> values that specifies the event type of the messages to trace.</param>
    </member>
    <member name="P:System.Diagnostics.EventTypeFilter.EventType">
      <summary>Gets or sets the event type of the messages to trace.</summary>
      <returns>A bitwise combination of the <see cref="T:System.Diagnostics.SourceLevels" /> values.</returns>
    </member>
    <member name="M:System.Diagnostics.EventTypeFilter.ShouldTrace(System.Diagnostics.TraceEventCache,System.String,System.Diagnostics.TraceEventType,System.Int32,System.String,System.Object[],System.Object,System.Object[])">
      <summary>Determines whether the trace listener should trace the event.</summary>
      <param name="cache">A <see cref="T:System.Diagnostics.TraceEventCache" /> that represents the information cache for the trace event.</param>
      <param name="source">The name of the source.</param>
      <param name="eventType">One of the <see cref="T:System.Diagnostics.TraceEventType" /> values.</param>
      <param name="id">A trace identifier number.</param>
      <param name="formatOrMessage">The format to use for writing an array of arguments, or a message to write.</param>
      <param name="args">An array of argument objects.</param>
      <param name="data1">A trace data object.</param>
      <param name="data">An array of trace data objects.</param>
      <returns>
        <see langword="true" /> if the trace should be produced; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="T:System.Diagnostics.SourceFilter">
      <summary>Indicates whether a listener should trace a message based on the source of a trace.</summary>
    </member>
    <member name="M:System.Diagnostics.SourceFilter.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Diagnostics.SourceFilter" /> class, specifying the name of the trace source.</summary>
      <param name="source">The name of the trace source.</param>
    </member>
    <member name="M:System.Diagnostics.SourceFilter.ShouldTrace(System.Diagnostics.TraceEventCache,System.String,System.Diagnostics.TraceEventType,System.Int32,System.String,System.Object[],System.Object,System.Object[])">
      <summary>Determines whether the trace listener should trace the event.</summary>
      <param name="cache">An object that represents the information cache for the trace event.</param>
      <param name="source">The name of the source.</param>
      <param name="eventType">One of the enumeration values that identifies the event type.</param>
      <param name="id">A trace identifier number.</param>
      <param name="formatOrMessage">The format to use for writing an array of arguments or a message to write.</param>
      <param name="args">An array of argument objects.</param>
      <param name="data1">A trace data object.</param>
      <param name="data">An array of trace data objects.</param>
      <returns>
        <see langword="true" /> if the trace should be produced; otherwise, <see langword="false" />.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> is <see langword="null" />.</exception>
    </member>
    <member name="P:System.Diagnostics.SourceFilter.Source">
      <summary>Gets or sets the name of the trace source.</summary>
      <returns>The name of the trace source.</returns>
      <exception cref="T:System.ArgumentNullException">The value is <see langword="null" />.</exception>
    </member>
    <member name="T:System.Diagnostics.SourceLevels">
      <summary>Specifies the levels of trace messages filtered by the source switch and event type filter.</summary>
    </member>
    <member name="F:System.Diagnostics.SourceLevels.ActivityTracing">
      <summary>Allows the <see cref="F:System.Diagnostics.TraceEventType.Stop" />, <see cref="F:System.Diagnostics.TraceEventType.Start" />, <see cref="F:System.Diagnostics.TraceEventType.Suspend" />, <see cref="F:System.Diagnostics.TraceEventType.Transfer" />, and <see cref="F:System.Diagnostics.TraceEventType.Resume" /> events through.</summary>
    </member>
    <member name="F:System.Diagnostics.SourceLevels.All">
      <summary>Allows all events through.</summary>
    </member>
    <member name="F:System.Diagnostics.SourceLevels.Critical">
      <summary>Allows only <see cref="F:System.Diagnostics.TraceEventType.Critical" /> events through.</summary>
    </member>
    <member name="F:System.Diagnostics.SourceLevels.Error">
      <summary>Allows <see cref="F:System.Diagnostics.TraceEventType.Critical" /> and <see cref="F:System.Diagnostics.TraceEventType.Error" /> events through.</summary>
    </member>
    <member name="F:System.Diagnostics.SourceLevels.Information">
      <summary>Allows <see cref="F:System.Diagnostics.TraceEventType.Critical" />, <see cref="F:System.Diagnostics.TraceEventType.Error" />, <see cref="F:System.Diagnostics.TraceEventType.Warning" />, and <see cref="F:System.Diagnostics.TraceEventType.Information" /> events through.</summary>
    </member>
    <member name="F:System.Diagnostics.SourceLevels.Off">
      <summary>Does not allow any events through.</summary>
    </member>
    <member name="F:System.Diagnostics.SourceLevels.Verbose">
      <summary>Allows <see cref="F:System.Diagnostics.TraceEventType.Critical" />, <see cref="F:System.Diagnostics.TraceEventType.Error" />, <see cref="F:System.Diagnostics.TraceEventType.Warning" />, <see cref="F:System.Diagnostics.TraceEventType.Information" />, and <see cref="F:System.Diagnostics.TraceEventType.Verbose" /> events through.</summary>
    </member>
    <member name="F:System.Diagnostics.SourceLevels.Warning">
      <summary>Allows <see cref="F:System.Diagnostics.TraceEventType.Critical" />, <see cref="F:System.Diagnostics.TraceEventType.Error" />, and <see cref="F:System.Diagnostics.TraceEventType.Warning" /> events through.</summary>
    </member>
    <member name="T:System.Diagnostics.SourceSwitch">
      <summary>Provides a multilevel switch to control tracing and debug output without recompiling your code.</summary>
    </member>
    <member name="M:System.Diagnostics.SourceSwitch.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Diagnostics.SourceSwitch" /> class, specifying the name of the source.</summary>
      <param name="name">The name of the source.</param>
    </member>
    <member name="M:System.Diagnostics.SourceSwitch.#ctor(System.String,System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Diagnostics.SourceSwitch" /> class, specifying the display name and the default value for the source switch.</summary>
      <param name="displayName">The name of the source switch.</param>
      <param name="defaultSwitchValue">The default value for the switch.</param>
    </member>
    <member name="P:System.Diagnostics.SourceSwitch.Level">
      <summary>Gets or sets the level of the switch.</summary>
      <returns>One of the <see cref="T:System.Diagnostics.SourceLevels" /> values that represents the event level of the switch.</returns>
    </member>
    <member name="M:System.Diagnostics.SourceSwitch.OnValueChanged">
      <summary>Invoked when the value of the <see cref="P:System.Diagnostics.Switch.Value" /> property changes.</summary>
      <exception cref="T:System.ArgumentException">The new value of <see cref="P:System.Diagnostics.Switch.Value" /> is not one of the <see cref="T:System.Diagnostics.SourceLevels" /> values.</exception>
    </member>
    <member name="M:System.Diagnostics.SourceSwitch.ShouldTrace(System.Diagnostics.TraceEventType)">
      <summary>Determines if trace listeners should be called, based on the trace event type.</summary>
      <param name="eventType">One of the <see cref="T:System.Diagnostics.TraceEventType" /> values.</param>
      <returns>
        <see langword="true" /> if the trace listeners should be called; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="T:System.Diagnostics.Switch">
      <summary>Provides an abstract base class to create new debugging and tracing switches.</summary>
    </member>
    <member name="M:System.Diagnostics.Switch.#ctor(System.String,System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Diagnostics.Switch" /> class.</summary>
      <param name="displayName">The name of the switch.</param>
      <param name="description">The description for the switch.</param>
    </member>
    <member name="M:System.Diagnostics.Switch.#ctor(System.String,System.String,System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Diagnostics.Switch" /> class, specifying the display name, description, and default value for the switch.</summary>
      <param name="displayName">The name of the switch.</param>
      <param name="description">The description of the switch.</param>
      <param name="defaultSwitchValue">The default value for the switch.</param>
    </member>
    <member name="P:System.Diagnostics.Switch.Attributes">
      <summary>Gets the custom switch attributes defined in the application configuration file.</summary>
      <returns>A <see cref="T:System.Collections.Specialized.StringDictionary" /> containing the case-insensitive custom attributes for the trace switch.</returns>
    </member>
    <member name="P:System.Diagnostics.Switch.Description">
      <summary>Gets a description of the switch.</summary>
      <returns>The description of the switch. The default value is an empty string ("").</returns>
    </member>
    <member name="P:System.Diagnostics.Switch.DisplayName">
      <summary>Gets a name used to identify the switch.</summary>
      <returns>The name used to identify the switch. The default value is an empty string ("").</returns>
    </member>
    <member name="M:System.Diagnostics.Switch.GetSupportedAttributes">
      <summary>Gets the custom attributes supported by the switch.</summary>
      <returns>A string array that contains the names of the custom attributes supported by the switch, or <see langword="null" /> if there no custom attributes are supported.</returns>
    </member>
    <member name="M:System.Diagnostics.Switch.OnSwitchSettingChanged">
      <summary>Invoked when the <see cref="P:System.Diagnostics.Switch.SwitchSetting" /> property is changed.</summary>
    </member>
    <member name="M:System.Diagnostics.Switch.OnValueChanged">
      <summary>Invoked when the <see cref="P:System.Diagnostics.Switch.Value" /> property is changed.</summary>
    </member>
    <member name="P:System.Diagnostics.Switch.SwitchSetting">
      <summary>Gets or sets the current setting for this switch.</summary>
      <returns>The current setting for this switch. The default is zero.</returns>
    </member>
    <member name="P:System.Diagnostics.Switch.Value">
      <summary>Gets or sets the value of the switch.</summary>
      <returns>A string representing the value of the switch.</returns>
      <exception cref="T:System.Configuration.ConfigurationErrorsException">The value is <see langword="null" />.
-or-
The value does not consist solely of an optional negative sign followed by a sequence of digits ranging from 0 to 9.
-or-
The value represents a number less than <see cref="F:System.Int32.MinValue" /> or greater than <see cref="F:System.Int32.MaxValue" />.</exception>
    </member>
    <member name="T:System.Diagnostics.SwitchAttribute">
      <summary>Identifies a switch used in an assembly, class, or member.</summary>
    </member>
    <member name="M:System.Diagnostics.SwitchAttribute.#ctor(System.String,System.Type)">
      <summary>Initializes a new instance of the <see cref="T:System.Diagnostics.SwitchAttribute" /> class, specifying the name and the type of the switch.</summary>
      <param name="switchName">The display name of the switch.</param>
      <param name="switchType">The type of the switch.</param>
    </member>
    <member name="M:System.Diagnostics.SwitchAttribute.GetAll(System.Reflection.Assembly)">
      <summary>Returns all switch attributes for the specified assembly.</summary>
      <param name="assembly">The assembly to check for switch attributes.</param>
      <returns>An array that contains all the switch attributes for the assembly.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="assembly" /> is <see langword="null" />.</exception>
    </member>
    <member name="P:System.Diagnostics.SwitchAttribute.SwitchDescription">
      <summary>Gets or sets the description of the switch.</summary>
      <returns>The description of the switch.</returns>
    </member>
    <member name="P:System.Diagnostics.SwitchAttribute.SwitchName">
      <summary>Gets or sets the display name of the switch.</summary>
      <returns>The display name of the switch.</returns>
      <exception cref="T:System.ArgumentNullException">
        <see cref="P:System.Diagnostics.SwitchAttribute.SwitchName" /> is set to <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentException">
        <see cref="P:System.Diagnostics.SwitchAttribute.SwitchName" /> is set to an empty string.</exception>
    </member>
    <member name="P:System.Diagnostics.SwitchAttribute.SwitchType">
      <summary>Gets or sets the type of the switch.</summary>
      <returns>The type of the switch.</returns>
      <exception cref="T:System.ArgumentNullException">
        <see cref="P:System.Diagnostics.SwitchAttribute.SwitchType" /> is set to <see langword="null" />.</exception>
    </member>
    <member name="T:System.Diagnostics.SwitchLevelAttribute">
      <summary>Identifies the level type for a switch.</summary>
    </member>
    <member name="M:System.Diagnostics.SwitchLevelAttribute.#ctor(System.Type)">
      <summary>Initializes a new instance of the <see cref="T:System.Diagnostics.SwitchLevelAttribute" /> class, specifying the type that determines whether a trace should be written.</summary>
      <param name="switchLevelType">The <see cref="T:System.Type" /> that determines whether a trace should be written.</param>
    </member>
    <member name="P:System.Diagnostics.SwitchLevelAttribute.SwitchLevelType">
      <summary>Gets or sets the type that determines whether a trace should be written.</summary>
      <returns>The <see cref="T:System.Type" /> that determines whether a trace should be written.</returns>
      <exception cref="T:System.ArgumentNullException">The set operation failed because the value is <see langword="null" />.</exception>
    </member>
    <member name="T:System.Diagnostics.Trace">
      <summary>Provides a set of methods and properties that help you trace the execution of your code. This class cannot be inherited.</summary>
    </member>
    <member name="M:System.Diagnostics.Trace.Assert(System.Boolean)">
      <summary>Checks for a condition; if the condition is <see langword="false" />, displays a message box that shows the call stack.</summary>
      <param name="condition">The conditional expression to evaluate. If the condition is <see langword="true" />, a failure message is not sent and the message box is not displayed.</param>
    </member>
    <member name="M:System.Diagnostics.Trace.Assert(System.Boolean,System.String)">
      <summary>Checks for a condition; if the condition is <see langword="false" />, outputs a specified message and displays a message box that shows the call stack.</summary>
      <param name="condition">The conditional expression to evaluate. If the condition is <see langword="true" />, the specified message is not sent and the message box is not displayed.</param>
      <param name="message">The message to send to the <see cref="P:System.Diagnostics.Trace.Listeners" /> collection.</param>
    </member>
    <member name="M:System.Diagnostics.Trace.Assert(System.Boolean,System.String,System.String)">
      <summary>Checks for a condition; if the condition is <see langword="false" />, outputs two specified messages and displays a message box that shows the call stack.</summary>
      <param name="condition">The conditional expression to evaluate. If the condition is <see langword="true" />, the specified messages are not sent and the message box is not displayed.</param>
      <param name="message">The message to send to the <see cref="P:System.Diagnostics.Trace.Listeners" /> collection.</param>
      <param name="detailMessage">The detailed message to send to the <see cref="P:System.Diagnostics.Trace.Listeners" /> collection.</param>
    </member>
    <member name="P:System.Diagnostics.Trace.AutoFlush">
      <summary>Gets or sets whether <see cref="M:System.Diagnostics.Trace.Flush" /> should be called on the <see cref="P:System.Diagnostics.Trace.Listeners" /> after every write.</summary>
      <returns>
        <see langword="true" /> if <see cref="M:System.Diagnostics.Trace.Flush" /> is called on the <see cref="P:System.Diagnostics.Trace.Listeners" /> after every write; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Diagnostics.Trace.Close">
      <summary>Flushes the output buffer, and then closes the <see cref="P:System.Diagnostics.Trace.Listeners" />.</summary>
    </member>
    <member name="P:System.Diagnostics.Trace.CorrelationManager">
      <summary>Gets the correlation manager for the thread for this trace.</summary>
      <returns>The <see cref="T:System.Diagnostics.CorrelationManager" /> object associated with the thread for this trace.</returns>
    </member>
    <member name="M:System.Diagnostics.Trace.Fail(System.String)">
      <summary>Emits the specified error message.</summary>
      <param name="message">A message to emit.</param>
    </member>
    <member name="M:System.Diagnostics.Trace.Fail(System.String,System.String)">
      <summary>Emits an error message, and a detailed error message.</summary>
      <param name="message">A message to emit.</param>
      <param name="detailMessage">A detailed message to emit.</param>
    </member>
    <member name="M:System.Diagnostics.Trace.Flush">
      <summary>Flushes the output buffer, and causes buffered data to be written to the <see cref="P:System.Diagnostics.Trace.Listeners" />.</summary>
    </member>
    <member name="M:System.Diagnostics.Trace.Indent">
      <summary>Increases the current <see cref="P:System.Diagnostics.Trace.IndentLevel" /> by one.</summary>
    </member>
    <member name="P:System.Diagnostics.Trace.IndentLevel">
      <summary>Gets or sets the indent level.</summary>
      <returns>The indent level. The default is zero.</returns>
    </member>
    <member name="P:System.Diagnostics.Trace.IndentSize">
      <summary>Gets or sets the number of spaces in an indent.</summary>
      <returns>The number of spaces in an indent. The default is four.</returns>
    </member>
    <member name="P:System.Diagnostics.Trace.Listeners">
      <summary>Gets the collection of listeners that is monitoring the trace output.</summary>
      <returns>A <see cref="T:System.Diagnostics.TraceListenerCollection" /> that represents a collection of type <see cref="T:System.Diagnostics.TraceListener" /> monitoring the trace output.</returns>
    </member>
    <member name="M:System.Diagnostics.Trace.Refresh">
      <summary>Refreshes the trace configuration data.</summary>
    </member>
    <member name="M:System.Diagnostics.Trace.TraceError(System.String)">
      <summary>Writes an error message to the trace listeners in the <see cref="P:System.Diagnostics.Trace.Listeners" /> collection using the specified message.</summary>
      <param name="message">The informative message to write.</param>
    </member>
    <member name="M:System.Diagnostics.Trace.TraceError(System.String,System.Object[])">
      <summary>Writes an error message to the trace listeners in the <see cref="P:System.Diagnostics.Trace.Listeners" /> collection using the specified array of objects and formatting information.</summary>
      <param name="format">A format string that contains zero or more format items, which correspond to objects in the <paramref name="args" /> array.</param>
      <param name="args">An <see langword="object" /> array containing zero or more objects to format.</param>
    </member>
    <member name="M:System.Diagnostics.Trace.TraceInformation(System.String)">
      <summary>Writes an informational message to the trace listeners in the <see cref="P:System.Diagnostics.Trace.Listeners" /> collection using the specified message.</summary>
      <param name="message">The informative message to write.</param>
    </member>
    <member name="M:System.Diagnostics.Trace.TraceInformation(System.String,System.Object[])">
      <summary>Writes an informational message to the trace listeners in the <see cref="P:System.Diagnostics.Trace.Listeners" /> collection using the specified array of objects and formatting information.</summary>
      <param name="format">A format string that contains zero or more format items, which correspond to objects in the <paramref name="args" /> array.</param>
      <param name="args">An <see langword="object" /> array containing zero or more objects to format.</param>
    </member>
    <member name="M:System.Diagnostics.Trace.TraceWarning(System.String)">
      <summary>Writes a warning message to the trace listeners in the <see cref="P:System.Diagnostics.Trace.Listeners" /> collection using the specified message.</summary>
      <param name="message">The informative message to write.</param>
    </member>
    <member name="M:System.Diagnostics.Trace.TraceWarning(System.String,System.Object[])">
      <summary>Writes a warning message to the trace listeners in the <see cref="P:System.Diagnostics.Trace.Listeners" /> collection using the specified array of objects and formatting information.</summary>
      <param name="format">A format string that contains zero or more format items, which correspond to objects in the <paramref name="args" /> array.</param>
      <param name="args">An <see langword="object" /> array containing zero or more objects to format.</param>
    </member>
    <member name="M:System.Diagnostics.Trace.Unindent">
      <summary>Decreases the current <see cref="P:System.Diagnostics.Trace.IndentLevel" /> by one.</summary>
    </member>
    <member name="P:System.Diagnostics.Trace.UseGlobalLock">
      <summary>Gets or sets a value indicating whether the global lock should be used.</summary>
      <returns>
        <see langword="true" /> if the global lock is to be used; otherwise, <see langword="false" />. The default is <see langword="true" />.</returns>
    </member>
    <member name="M:System.Diagnostics.Trace.Write(System.Object)">
      <summary>Writes the value of the object's <see cref="M:System.Object.ToString" /> method to the trace listeners in the <see cref="P:System.Diagnostics.Trace.Listeners" /> collection.</summary>
      <param name="value">An <see cref="T:System.Object" /> whose name is sent to the <see cref="P:System.Diagnostics.Trace.Listeners" />.</param>
    </member>
    <member name="M:System.Diagnostics.Trace.Write(System.Object,System.String)">
      <summary>Writes a category name and the value of the object's <see cref="M:System.Object.ToString" /> method to the trace listeners in the <see cref="P:System.Diagnostics.Trace.Listeners" /> collection.</summary>
      <param name="value">An <see cref="T:System.Object" /> name is sent to the <see cref="P:System.Diagnostics.Trace.Listeners" />.</param>
      <param name="category">A category name used to organize the output.</param>
    </member>
    <member name="M:System.Diagnostics.Trace.Write(System.String)">
      <summary>Writes a message to the trace listeners in the <see cref="P:System.Diagnostics.Trace.Listeners" /> collection.</summary>
      <param name="message">A message to write.</param>
    </member>
    <member name="M:System.Diagnostics.Trace.Write(System.String,System.String)">
      <summary>Writes a category name and a message to the trace listeners in the <see cref="P:System.Diagnostics.Trace.Listeners" /> collection.</summary>
      <param name="message">A message to write.</param>
      <param name="category">A category name used to organize the output.</param>
    </member>
    <member name="M:System.Diagnostics.Trace.WriteIf(System.Boolean,System.Object)">
      <summary>Writes the value of the object's <see cref="M:System.Object.ToString" /> method to the trace listeners in the <see cref="P:System.Diagnostics.Trace.Listeners" /> collection if a condition is <see langword="true" />.</summary>
      <param name="condition">
        <see langword="true" /> to cause a message to be written; otherwise, <see langword="false" />.</param>
      <param name="value">An <see cref="T:System.Object" /> whose name is sent to the <see cref="P:System.Diagnostics.Trace.Listeners" />.</param>
    </member>
    <member name="M:System.Diagnostics.Trace.WriteIf(System.Boolean,System.Object,System.String)">
      <summary>Writes a category name and the value of the object's <see cref="M:System.Object.ToString" /> method to the trace listeners in the <see cref="P:System.Diagnostics.Trace.Listeners" /> collection if a condition is <see langword="true" />.</summary>
      <param name="condition">
        <see langword="true" /> to cause a message to be written; otherwise, <see langword="false" />.</param>
      <param name="value">An <see cref="T:System.Object" /> whose name is sent to the <see cref="P:System.Diagnostics.Trace.Listeners" />.</param>
      <param name="category">A category name used to organize the output.</param>
    </member>
    <member name="M:System.Diagnostics.Trace.WriteIf(System.Boolean,System.String)">
      <summary>Writes a message to the trace listeners in the <see cref="P:System.Diagnostics.Trace.Listeners" /> collection if a condition is <see langword="true" />.</summary>
      <param name="condition">
        <see langword="true" /> to cause a message to be written; otherwise, <see langword="false" />.</param>
      <param name="message">A message to write.</param>
    </member>
    <member name="M:System.Diagnostics.Trace.WriteIf(System.Boolean,System.String,System.String)">
      <summary>Writes a category name and message to the trace listeners in the <see cref="P:System.Diagnostics.Trace.Listeners" /> collection if a condition is <see langword="true" />.</summary>
      <param name="condition">
        <see langword="true" /> to cause a message to be written; otherwise, <see langword="false" />.</param>
      <param name="message">A message to write.</param>
      <param name="category">A category name used to organize the output.</param>
    </member>
    <member name="M:System.Diagnostics.Trace.WriteLine(System.Object)">
      <summary>Writes the value of the object's <see cref="M:System.Object.ToString" /> method to the trace listeners in the <see cref="P:System.Diagnostics.Trace.Listeners" /> collection.</summary>
      <param name="value">An <see cref="T:System.Object" /> whose name is sent to the <see cref="P:System.Diagnostics.Trace.Listeners" />.</param>
    </member>
    <member name="M:System.Diagnostics.Trace.WriteLine(System.Object,System.String)">
      <summary>Writes a category name and the value of the object's <see cref="M:System.Object.ToString" /> method to the trace listeners in the <see cref="P:System.Diagnostics.Trace.Listeners" /> collection.</summary>
      <param name="value">An <see cref="T:System.Object" /> whose name is sent to the <see cref="P:System.Diagnostics.Trace.Listeners" />.</param>
      <param name="category">A category name used to organize the output.</param>
    </member>
    <member name="M:System.Diagnostics.Trace.WriteLine(System.String)">
      <summary>Writes a message to the trace listeners in the <see cref="P:System.Diagnostics.Trace.Listeners" /> collection.</summary>
      <param name="message">A message to write.</param>
    </member>
    <member name="M:System.Diagnostics.Trace.WriteLine(System.String,System.String)">
      <summary>Writes a category name and message to the trace listeners in the <see cref="P:System.Diagnostics.Trace.Listeners" /> collection.</summary>
      <param name="message">A message to write.</param>
      <param name="category">A category name used to organize the output.</param>
    </member>
    <member name="M:System.Diagnostics.Trace.WriteLineIf(System.Boolean,System.Object)">
      <summary>Writes the value of the object's <see cref="M:System.Object.ToString" /> method to the trace listeners in the <see cref="P:System.Diagnostics.Trace.Listeners" /> collection if a condition is <see langword="true" />.</summary>
      <param name="condition">
        <see langword="true" /> to cause a message to be written; otherwise, <see langword="false" />.</param>
      <param name="value">An <see cref="T:System.Object" /> whose name is sent to the <see cref="P:System.Diagnostics.Trace.Listeners" />.</param>
    </member>
    <member name="M:System.Diagnostics.Trace.WriteLineIf(System.Boolean,System.Object,System.String)">
      <summary>Writes a category name and the value of the object's <see cref="M:System.Object.ToString" /> method to the trace listeners in the <see cref="P:System.Diagnostics.Trace.Listeners" /> collection if a condition is <see langword="true" />.</summary>
      <param name="condition">
        <see langword="true" /> to cause a message to be written; otherwise, <see langword="false" />.</param>
      <param name="value">An <see cref="T:System.Object" /> whose name is sent to the <see cref="P:System.Diagnostics.Trace.Listeners" />.</param>
      <param name="category">A category name used to organize the output.</param>
    </member>
    <member name="M:System.Diagnostics.Trace.WriteLineIf(System.Boolean,System.String)">
      <summary>Writes a message to the trace listeners in the <see cref="P:System.Diagnostics.Trace.Listeners" /> collection if a condition is <see langword="true" />.</summary>
      <param name="condition">
        <see langword="true" /> to cause a message to be written; otherwise, <see langword="false" />.</param>
      <param name="message">A message to write.</param>
    </member>
    <member name="M:System.Diagnostics.Trace.WriteLineIf(System.Boolean,System.String,System.String)">
      <summary>Writes a category name and message to the trace listeners in the <see cref="P:System.Diagnostics.Trace.Listeners" /> collection if a condition is <see langword="true" />.</summary>
      <param name="condition">
        <see langword="true" /> to cause a message to be written; otherwise, <see langword="false" />.</param>
      <param name="message">A message to write.</param>
      <param name="category">A category name used to organize the output.</param>
    </member>
    <member name="T:System.Diagnostics.TraceEventCache">
      <summary>Provides trace event data specific to a thread and a process.</summary>
    </member>
    <member name="M:System.Diagnostics.TraceEventCache.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Diagnostics.TraceEventCache" /> class.</summary>
    </member>
    <member name="P:System.Diagnostics.TraceEventCache.Callstack">
      <summary>Gets the call stack for the current thread.</summary>
      <returns>A string containing stack trace information. This value can be an empty string ("").</returns>
    </member>
    <member name="P:System.Diagnostics.TraceEventCache.DateTime">
      <summary>Gets the date and time at which the event trace occurred.</summary>
      <returns>A <see cref="T:System.DateTime" /> structure whose value is a date and time expressed in Coordinated Universal Time (UTC).</returns>
    </member>
    <member name="P:System.Diagnostics.TraceEventCache.LogicalOperationStack">
      <summary>Gets the correlation data, contained in a stack.</summary>
      <returns>A <see cref="T:System.Collections.Stack" /> containing correlation data.</returns>
    </member>
    <member name="P:System.Diagnostics.TraceEventCache.ProcessId">
      <summary>Gets the unique identifier of the current process.</summary>
      <returns>The system-generated unique identifier of the current process.</returns>
    </member>
    <member name="P:System.Diagnostics.TraceEventCache.ThreadId">
      <summary>Gets a unique identifier for the current managed thread.</summary>
      <returns>A string that represents a unique integer identifier for this managed thread.</returns>
    </member>
    <member name="P:System.Diagnostics.TraceEventCache.Timestamp">
      <summary>Gets the current number of ticks in the timer mechanism.</summary>
      <returns>The tick counter value of the underlying timer mechanism.</returns>
    </member>
    <member name="T:System.Diagnostics.TraceEventType">
      <summary>Identifies the type of event that has caused the trace.</summary>
    </member>
    <member name="F:System.Diagnostics.TraceEventType.Critical">
      <summary>Fatal error or application crash.</summary>
    </member>
    <member name="F:System.Diagnostics.TraceEventType.Error">
      <summary>Recoverable error.</summary>
    </member>
    <member name="F:System.Diagnostics.TraceEventType.Information">
      <summary>Informational message.</summary>
    </member>
    <member name="F:System.Diagnostics.TraceEventType.Resume">
      <summary>Resumption of a logical operation.</summary>
    </member>
    <member name="F:System.Diagnostics.TraceEventType.Start">
      <summary>Starting of a logical operation.</summary>
    </member>
    <member name="F:System.Diagnostics.TraceEventType.Stop">
      <summary>Stopping of a logical operation.</summary>
    </member>
    <member name="F:System.Diagnostics.TraceEventType.Suspend">
      <summary>Suspension of a logical operation.</summary>
    </member>
    <member name="F:System.Diagnostics.TraceEventType.Transfer">
      <summary>Changing of correlation identity.</summary>
    </member>
    <member name="F:System.Diagnostics.TraceEventType.Verbose">
      <summary>Debugging trace.</summary>
    </member>
    <member name="F:System.Diagnostics.TraceEventType.Warning">
      <summary>Noncritical problem.</summary>
    </member>
    <member name="T:System.Diagnostics.TraceFilter">
      <summary>Provides the base class for trace filter implementations.</summary>
    </member>
    <member name="M:System.Diagnostics.TraceFilter.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Diagnostics.TraceFilter" /> class.</summary>
    </member>
    <member name="M:System.Diagnostics.TraceFilter.ShouldTrace(System.Diagnostics.TraceEventCache,System.String,System.Diagnostics.TraceEventType,System.Int32,System.String,System.Object[],System.Object,System.Object[])">
      <summary>When overridden in a derived class, determines whether the trace listener should trace the event.</summary>
      <param name="cache">The <see cref="T:System.Diagnostics.TraceEventCache" /> that contains information for the trace event.</param>
      <param name="source">The name of the source.</param>
      <param name="eventType">One of the <see cref="T:System.Diagnostics.TraceEventType" /> values specifying the type of event that has caused the trace.</param>
      <param name="id">A trace identifier number.</param>
      <param name="formatOrMessage">Either the format to use for writing an array of arguments specified by the <paramref name="args" /> parameter, or a message to write.</param>
      <param name="args">An array of argument objects.</param>
      <param name="data1">A trace data object.</param>
      <param name="data">An array of trace data objects.</param>
      <returns>
        <see langword="true" /> to trace the specified event; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="T:System.Diagnostics.TraceLevel">
      <summary>Specifies what messages to output for the <see cref="T:System.Diagnostics.Debug" />, <see cref="T:System.Diagnostics.Trace" /> and <see cref="T:System.Diagnostics.TraceSwitch" /> classes.</summary>
    </member>
    <member name="F:System.Diagnostics.TraceLevel.Error">
      <summary>Output error-handling messages.</summary>
    </member>
    <member name="F:System.Diagnostics.TraceLevel.Info">
      <summary>Output informational messages, warnings, and error-handling messages.</summary>
    </member>
    <member name="F:System.Diagnostics.TraceLevel.Off">
      <summary>Output no tracing and debugging messages.</summary>
    </member>
    <member name="F:System.Diagnostics.TraceLevel.Verbose">
      <summary>Output all debugging and tracing messages.</summary>
    </member>
    <member name="F:System.Diagnostics.TraceLevel.Warning">
      <summary>Output warnings and error-handling messages.</summary>
    </member>
    <member name="T:System.Diagnostics.TraceListener">
      <summary>Provides the <see langword="abstract" /> base class for the listeners who monitor trace and debug output.</summary>
    </member>
    <member name="M:System.Diagnostics.TraceListener.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Diagnostics.TraceListener" /> class.</summary>
    </member>
    <member name="M:System.Diagnostics.TraceListener.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Diagnostics.TraceListener" /> class using the specified name as the listener.</summary>
      <param name="name">The name of the <see cref="T:System.Diagnostics.TraceListener" />.</param>
    </member>
    <member name="P:System.Diagnostics.TraceListener.Attributes">
      <summary>Gets the custom trace listener attributes defined in the application configuration file.</summary>
      <returns>A <see cref="T:System.Collections.Specialized.StringDictionary" /> containing the custom attributes for the trace listener.</returns>
    </member>
    <member name="M:System.Diagnostics.TraceListener.Close">
      <summary>When overridden in a derived class, closes the output stream so it no longer receives tracing or debugging output.</summary>
    </member>
    <member name="M:System.Diagnostics.TraceListener.Dispose">
      <summary>Releases all resources used by the <see cref="T:System.Diagnostics.TraceListener" />.</summary>
    </member>
    <member name="M:System.Diagnostics.TraceListener.Dispose(System.Boolean)">
      <summary>Releases the unmanaged resources used by the <see cref="T:System.Diagnostics.TraceListener" /> and optionally releases the managed resources.</summary>
      <param name="disposing">
        <see langword="true" /> to release both managed and unmanaged resources; <see langword="false" /> to release only unmanaged resources.</param>
    </member>
    <member name="M:System.Diagnostics.TraceListener.Fail(System.String)">
      <summary>Emits an error message to the listener you create when you implement the <see cref="T:System.Diagnostics.TraceListener" /> class.</summary>
      <param name="message">A message to emit.</param>
    </member>
    <member name="M:System.Diagnostics.TraceListener.Fail(System.String,System.String)">
      <summary>Emits an error message and a detailed error message to the listener you create when you implement the <see cref="T:System.Diagnostics.TraceListener" /> class.</summary>
      <param name="message">A message to emit.</param>
      <param name="detailMessage">A detailed message to emit.</param>
    </member>
    <member name="P:System.Diagnostics.TraceListener.Filter">
      <summary>Gets or sets the trace filter for the trace listener.</summary>
      <returns>An object derived from the <see cref="T:System.Diagnostics.TraceFilter" /> base class.</returns>
    </member>
    <member name="M:System.Diagnostics.TraceListener.Flush">
      <summary>When overridden in a derived class, flushes the output buffer.</summary>
    </member>
    <member name="M:System.Diagnostics.TraceListener.GetSupportedAttributes">
      <summary>Gets the custom attributes supported by the trace listener.</summary>
      <returns>A string array naming the custom attributes supported by the trace listener, or <see langword="null" /> if there are no custom attributes.</returns>
    </member>
    <member name="P:System.Diagnostics.TraceListener.IndentLevel">
      <summary>Gets or sets the indent level.</summary>
      <returns>The indent level. The default is zero.</returns>
    </member>
    <member name="P:System.Diagnostics.TraceListener.IndentSize">
      <summary>Gets or sets the number of spaces in an indent.</summary>
      <returns>The number of spaces in an indent. The default is four spaces.</returns>
      <exception cref="T:System.ArgumentOutOfRangeException">Set operation failed because the value is less than zero.</exception>
    </member>
    <member name="P:System.Diagnostics.TraceListener.IsThreadSafe">
      <summary>Gets a value indicating whether the trace listener is thread safe.</summary>
      <returns>
        <see langword="true" /> if the trace listener is thread safe; otherwise, <see langword="false" />. The default is <see langword="false" />.</returns>
    </member>
    <member name="P:System.Diagnostics.TraceListener.Name">
      <summary>Gets or sets a name for this <see cref="T:System.Diagnostics.TraceListener" />.</summary>
      <returns>A name for this <see cref="T:System.Diagnostics.TraceListener" />. The default is an empty string ("").</returns>
    </member>
    <member name="P:System.Diagnostics.TraceListener.NeedIndent">
      <summary>Gets or sets a value indicating whether to indent the output.</summary>
      <returns>
        <see langword="true" /> if the output should be indented; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Diagnostics.TraceListener.TraceData(System.Diagnostics.TraceEventCache,System.String,System.Diagnostics.TraceEventType,System.Int32,System.Object)">
      <summary>Writes trace information, a data object and event information to the listener specific output.</summary>
      <param name="eventCache">A <see cref="T:System.Diagnostics.TraceEventCache" /> object that contains the current process ID, thread ID, and stack trace information.</param>
      <param name="source">A name used to identify the output, typically the name of the application that generated the trace event.</param>
      <param name="eventType">One of the <see cref="T:System.Diagnostics.TraceEventType" /> values specifying the type of event that has caused the trace.</param>
      <param name="id">A numeric identifier for the event.</param>
      <param name="data">The trace data to emit.</param>
    </member>
    <member name="M:System.Diagnostics.TraceListener.TraceData(System.Diagnostics.TraceEventCache,System.String,System.Diagnostics.TraceEventType,System.Int32,System.Object[])">
      <summary>Writes trace information, an array of data objects and event information to the listener specific output.</summary>
      <param name="eventCache">A <see cref="T:System.Diagnostics.TraceEventCache" /> object that contains the current process ID, thread ID, and stack trace information.</param>
      <param name="source">A name used to identify the output, typically the name of the application that generated the trace event.</param>
      <param name="eventType">One of the <see cref="T:System.Diagnostics.TraceEventType" /> values specifying the type of event that has caused the trace.</param>
      <param name="id">A numeric identifier for the event.</param>
      <param name="data">An array of objects to emit as data.</param>
    </member>
    <member name="M:System.Diagnostics.TraceListener.TraceEvent(System.Diagnostics.TraceEventCache,System.String,System.Diagnostics.TraceEventType,System.Int32)">
      <summary>Writes trace and event information to the listener specific output.</summary>
      <param name="eventCache">A <see cref="T:System.Diagnostics.TraceEventCache" /> object that contains the current process ID, thread ID, and stack trace information.</param>
      <param name="source">A name used to identify the output, typically the name of the application that generated the trace event.</param>
      <param name="eventType">One of the <see cref="T:System.Diagnostics.TraceEventType" /> values specifying the type of event that has caused the trace.</param>
      <param name="id">A numeric identifier for the event.</param>
    </member>
    <member name="M:System.Diagnostics.TraceListener.TraceEvent(System.Diagnostics.TraceEventCache,System.String,System.Diagnostics.TraceEventType,System.Int32,System.String)">
      <summary>Writes trace information, a message, and event information to the listener specific output.</summary>
      <param name="eventCache">A <see cref="T:System.Diagnostics.TraceEventCache" /> object that contains the current process ID, thread ID, and stack trace information.</param>
      <param name="source">A name used to identify the output, typically the name of the application that generated the trace event.</param>
      <param name="eventType">One of the <see cref="T:System.Diagnostics.TraceEventType" /> values specifying the type of event that has caused the trace.</param>
      <param name="id">A numeric identifier for the event.</param>
      <param name="message">A message to write.</param>
    </member>
    <member name="M:System.Diagnostics.TraceListener.TraceEvent(System.Diagnostics.TraceEventCache,System.String,System.Diagnostics.TraceEventType,System.Int32,System.String,System.Object[])">
      <summary>Writes trace information, a formatted array of objects and event information to the listener specific output.</summary>
      <param name="eventCache">A <see cref="T:System.Diagnostics.TraceEventCache" /> object that contains the current process ID, thread ID, and stack trace information.</param>
      <param name="source">A name used to identify the output, typically the name of the application that generated the trace event.</param>
      <param name="eventType">One of the <see cref="T:System.Diagnostics.TraceEventType" /> values specifying the type of event that has caused the trace.</param>
      <param name="id">A numeric identifier for the event.</param>
      <param name="format">A format string that contains zero or more format items, which correspond to objects in the <paramref name="args" /> array.</param>
      <param name="args">An <see langword="object" /> array containing zero or more objects to format.</param>
    </member>
    <member name="P:System.Diagnostics.TraceListener.TraceOutputOptions">
      <summary>Gets or sets the trace output options.</summary>
      <returns>A bitwise combination of the enumeration values. The default is <see cref="F:System.Diagnostics.TraceOptions.None" />.</returns>
      <exception cref="T:System.ArgumentOutOfRangeException">Set operation failed because the value is invalid.</exception>
    </member>
    <member name="M:System.Diagnostics.TraceListener.TraceTransfer(System.Diagnostics.TraceEventCache,System.String,System.Int32,System.String,System.Guid)">
      <summary>Writes trace information, a message, a related activity identity and event information to the listener specific output.</summary>
      <param name="eventCache">A <see cref="T:System.Diagnostics.TraceEventCache" /> object that contains the current process ID, thread ID, and stack trace information.</param>
      <param name="source">A name used to identify the output, typically the name of the application that generated the trace event.</param>
      <param name="id">A numeric identifier for the event.</param>
      <param name="message">A message to write.</param>
      <param name="relatedActivityId">A <see cref="T:System.Guid" /> object identifying a related activity.</param>
    </member>
    <member name="M:System.Diagnostics.TraceListener.Write(System.Object)">
      <summary>Writes the value of the object's <see cref="M:System.Object.ToString" /> method to the listener you create when you implement the <see cref="T:System.Diagnostics.TraceListener" /> class.</summary>
      <param name="o">An <see cref="T:System.Object" /> whose fully qualified class name you want to write.</param>
    </member>
    <member name="M:System.Diagnostics.TraceListener.Write(System.Object,System.String)">
      <summary>Writes a category name and the value of the object's <see cref="M:System.Object.ToString" /> method to the listener you create when you implement the <see cref="T:System.Diagnostics.TraceListener" /> class.</summary>
      <param name="o">An <see cref="T:System.Object" /> whose fully qualified class name you want to write.</param>
      <param name="category">A category name used to organize the output.</param>
    </member>
    <member name="M:System.Diagnostics.TraceListener.Write(System.String)">
      <summary>When overridden in a derived class, writes the specified message to the listener you create in the derived class.</summary>
      <param name="message">A message to write.</param>
    </member>
    <member name="M:System.Diagnostics.TraceListener.Write(System.String,System.String)">
      <summary>Writes a category name and a message to the listener you create when you implement the <see cref="T:System.Diagnostics.TraceListener" /> class.</summary>
      <param name="message">A message to write.</param>
      <param name="category">A category name used to organize the output.</param>
    </member>
    <member name="M:System.Diagnostics.TraceListener.WriteIndent">
      <summary>Writes the indent to the listener you create when you implement this class, and resets the <see cref="P:System.Diagnostics.TraceListener.NeedIndent" /> property to <see langword="false" />.</summary>
    </member>
    <member name="M:System.Diagnostics.TraceListener.WriteLine(System.Object)">
      <summary>Writes the value of the object's <see cref="M:System.Object.ToString" /> method to the listener you create when you implement the <see cref="T:System.Diagnostics.TraceListener" /> class, followed by a line terminator.</summary>
      <param name="o">An <see cref="T:System.Object" /> whose fully qualified class name you want to write.</param>
    </member>
    <member name="M:System.Diagnostics.TraceListener.WriteLine(System.Object,System.String)">
      <summary>Writes a category name and the value of the object's <see cref="M:System.Object.ToString" /> method to the listener you create when you implement the <see cref="T:System.Diagnostics.TraceListener" /> class, followed by a line terminator.</summary>
      <param name="o">An <see cref="T:System.Object" /> whose fully qualified class name you want to write.</param>
      <param name="category">A category name used to organize the output.</param>
    </member>
    <member name="M:System.Diagnostics.TraceListener.WriteLine(System.String)">
      <summary>When overridden in a derived class, writes a message to the listener you create in the derived class, followed by a line terminator.</summary>
      <param name="message">A message to write.</param>
    </member>
    <member name="M:System.Diagnostics.TraceListener.WriteLine(System.String,System.String)">
      <summary>Writes a category name and a message to the listener you create when you implement the <see cref="T:System.Diagnostics.TraceListener" /> class, followed by a line terminator.</summary>
      <param name="message">A message to write.</param>
      <param name="category">A category name used to organize the output.</param>
    </member>
    <member name="T:System.Diagnostics.TraceListenerCollection">
      <summary>Provides a thread-safe list of <see cref="T:System.Diagnostics.TraceListener" /> objects.</summary>
    </member>
    <member name="M:System.Diagnostics.TraceListenerCollection.Add(System.Diagnostics.TraceListener)">
      <summary>Adds a <see cref="T:System.Diagnostics.TraceListener" /> to the list.</summary>
      <param name="listener">A <see cref="T:System.Diagnostics.TraceListener" /> to add to the list.</param>
      <returns>The position at which the new listener was inserted.</returns>
    </member>
    <member name="M:System.Diagnostics.TraceListenerCollection.AddRange(System.Diagnostics.TraceListener[])">
      <summary>Adds an array of <see cref="T:System.Diagnostics.TraceListener" /> objects to the list.</summary>
      <param name="value">An array of <see cref="T:System.Diagnostics.TraceListener" /> objects to add to the list.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Diagnostics.TraceListenerCollection.AddRange(System.Diagnostics.TraceListenerCollection)">
      <summary>Adds the contents of another <see cref="T:System.Diagnostics.TraceListenerCollection" /> to the list.</summary>
      <param name="value">Another <see cref="T:System.Diagnostics.TraceListenerCollection" /> whose contents are added to the list.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Diagnostics.TraceListenerCollection.Clear">
      <summary>Clears all the listeners from the list.</summary>
    </member>
    <member name="M:System.Diagnostics.TraceListenerCollection.Contains(System.Diagnostics.TraceListener)">
      <summary>Checks whether the list contains the specified listener.</summary>
      <param name="listener">A <see cref="T:System.Diagnostics.TraceListener" /> to find in the list.</param>
      <returns>
        <see langword="true" /> if the listener is in the list; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Diagnostics.TraceListenerCollection.CopyTo(System.Diagnostics.TraceListener[],System.Int32)">
      <summary>Copies a section of the current <see cref="T:System.Diagnostics.TraceListenerCollection" /> list to the specified array at the specified index.</summary>
      <param name="listeners">An array of type <see cref="T:System.Array" /> to copy the elements into.</param>
      <param name="index">The starting index number in the current list to copy from.</param>
    </member>
    <member name="P:System.Diagnostics.TraceListenerCollection.Count">
      <summary>Gets the number of listeners in the list.</summary>
      <returns>The number of listeners in the list.</returns>
    </member>
    <member name="M:System.Diagnostics.TraceListenerCollection.GetEnumerator">
      <summary>Gets an enumerator for this list.</summary>
      <returns>An enumerator of type <see cref="T:System.Collections.IEnumerator" />.</returns>
    </member>
    <member name="M:System.Diagnostics.TraceListenerCollection.IndexOf(System.Diagnostics.TraceListener)">
      <summary>Gets the index of the specified listener.</summary>
      <param name="listener">A <see cref="T:System.Diagnostics.TraceListener" /> to find in the list.</param>
      <returns>The index of the listener, if it can be found in the list; otherwise, -1.</returns>
    </member>
    <member name="M:System.Diagnostics.TraceListenerCollection.Insert(System.Int32,System.Diagnostics.TraceListener)">
      <summary>Inserts the listener at the specified index.</summary>
      <param name="index">The position in the list to insert the new <see cref="T:System.Diagnostics.TraceListener" />.</param>
      <param name="listener">A <see cref="T:System.Diagnostics.TraceListener" /> to insert in the list.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="index" /> is not a valid index in the list.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="listener" /> is <see langword="null" />.</exception>
    </member>
    <member name="P:System.Diagnostics.TraceListenerCollection.Item(System.Int32)">
      <summary>Gets or sets the <see cref="T:System.Diagnostics.TraceListener" /> at the specified index.</summary>
      <param name="i">The zero-based index of the <see cref="T:System.Diagnostics.TraceListener" /> to get from the list.</param>
      <returns>A <see cref="T:System.Diagnostics.TraceListener" /> with the specified index.</returns>
      <exception cref="T:System.ArgumentNullException">The value is <see langword="null" />.</exception>
    </member>
    <member name="P:System.Diagnostics.TraceListenerCollection.Item(System.String)">
      <summary>Gets the first <see cref="T:System.Diagnostics.TraceListener" /> in the list with the specified name.</summary>
      <param name="name">The name of the <see cref="T:System.Diagnostics.TraceListener" /> to get from the list.</param>
      <returns>The first <see cref="T:System.Diagnostics.TraceListener" /> in the list with the given <see cref="P:System.Diagnostics.TraceListener.Name" />. This item returns <see langword="null" /> if no <see cref="T:System.Diagnostics.TraceListener" /> with the given name can be found.</returns>
    </member>
    <member name="M:System.Diagnostics.TraceListenerCollection.Remove(System.Diagnostics.TraceListener)">
      <summary>Removes from the collection the specified <see cref="T:System.Diagnostics.TraceListener" />.</summary>
      <param name="listener">A <see cref="T:System.Diagnostics.TraceListener" /> to remove from the list.</param>
    </member>
    <member name="M:System.Diagnostics.TraceListenerCollection.Remove(System.String)">
      <summary>Removes from the collection the first <see cref="T:System.Diagnostics.TraceListener" /> with the specified name.</summary>
      <param name="name">The name of the <see cref="T:System.Diagnostics.TraceListener" /> to remove from the list.</param>
    </member>
    <member name="M:System.Diagnostics.TraceListenerCollection.RemoveAt(System.Int32)">
      <summary>Removes from the collection the <see cref="T:System.Diagnostics.TraceListener" /> at the specified index.</summary>
      <param name="index">The zero-based index of the <see cref="T:System.Diagnostics.TraceListener" /> to remove from the list.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="index" /> is not a valid index in the list.</exception>
    </member>
    <member name="M:System.Diagnostics.TraceListenerCollection.System#Collections#ICollection#CopyTo(System.Array,System.Int32)">
      <summary>Copies a section of the current <see cref="T:System.Diagnostics.TraceListenerCollection" /> to the specified array of <see cref="T:System.Diagnostics.TraceListener" /> objects.</summary>
      <param name="array">The one-dimensional array of <see cref="T:System.Diagnostics.TraceListener" /> objects that is the destination of the elements copied from the <see cref="T:System.Diagnostics.TraceListenerCollection" />. The array must have zero-based indexing.</param>
      <param name="index">The zero-based index in <paramref name="array" /> at which copying begins.</param>
    </member>
    <member name="P:System.Diagnostics.TraceListenerCollection.System#Collections#ICollection#IsSynchronized">
      <summary>Gets a value indicating whether access to the <see cref="T:System.Diagnostics.TraceListenerCollection" /> is synchronized (thread safe).</summary>
      <returns>Always <see langword="true" />.</returns>
    </member>
    <member name="P:System.Diagnostics.TraceListenerCollection.System#Collections#ICollection#SyncRoot">
      <summary>Gets an object that can be used to synchronize access to the <see cref="T:System.Diagnostics.TraceListenerCollection" />.</summary>
      <returns>The current <see cref="T:System.Diagnostics.TraceListenerCollection" /> object.</returns>
    </member>
    <member name="M:System.Diagnostics.TraceListenerCollection.System#Collections#IList#Add(System.Object)">
      <summary>Adds a trace listener to the <see cref="T:System.Diagnostics.TraceListenerCollection" />.</summary>
      <param name="value">The object to add to the <see cref="T:System.Diagnostics.TraceListenerCollection" />.</param>
      <returns>The position into which the new trace listener was inserted.</returns>
      <exception cref="T:System.ArgumentException">
        <paramref name="value" /> is <see langword="null" />.
-or-
<paramref name="value" /> is not a <see cref="T:System.Diagnostics.TraceListener" />.</exception>
    </member>
    <member name="M:System.Diagnostics.TraceListenerCollection.System#Collections#IList#Contains(System.Object)">
      <summary>Determines whether the <see cref="T:System.Diagnostics.TraceListenerCollection" /> contains a specific object.</summary>
      <param name="value">The object to locate in the <see cref="T:System.Diagnostics.TraceListenerCollection" />.</param>
      <returns>
        <see langword="true" /> if the <see cref="T:System.Object" /> is found in the <see cref="T:System.Diagnostics.TraceListenerCollection" />; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Diagnostics.TraceListenerCollection.System#Collections#IList#IndexOf(System.Object)">
      <summary>Determines the index of a specific object in the <see cref="T:System.Diagnostics.TraceListenerCollection" />.</summary>
      <param name="value">The object to locate in the <see cref="T:System.Diagnostics.TraceListenerCollection" />.</param>
      <returns>The index of <paramref name="value" /> if found in the <see cref="T:System.Diagnostics.TraceListenerCollection" />; otherwise, -1.</returns>
    </member>
    <member name="M:System.Diagnostics.TraceListenerCollection.System#Collections#IList#Insert(System.Int32,System.Object)">
      <summary>Inserts a <see cref="T:System.Diagnostics.TraceListener" /> object at the specified position in the <see cref="T:System.Diagnostics.TraceListenerCollection" />.</summary>
      <param name="index">The zero-based index at which <paramref name="value" /> should be inserted.</param>
      <param name="value">The <see cref="T:System.Diagnostics.TraceListener" /> object to insert into the <see cref="T:System.Diagnostics.TraceListenerCollection" />.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="value" /> is not a <see cref="T:System.Diagnostics.TraceListener" /> object.</exception>
    </member>
    <member name="P:System.Diagnostics.TraceListenerCollection.System#Collections#IList#IsFixedSize">
      <summary>Gets a value indicating whether the <see cref="T:System.Diagnostics.TraceListenerCollection" /> has a fixed size.</summary>
      <returns>Always <see langword="false" />.</returns>
    </member>
    <member name="P:System.Diagnostics.TraceListenerCollection.System#Collections#IList#IsReadOnly">
      <summary>Gets a value indicating whether the <see cref="T:System.Diagnostics.TraceListenerCollection" /> is read-only.</summary>
      <returns>Always <see langword="false" />.</returns>
    </member>
    <member name="P:System.Diagnostics.TraceListenerCollection.System#Collections#IList#Item(System.Int32)">
      <summary>Gets or sets the <see cref="T:System.Diagnostics.TraceListener" /> at the specified index in the <see cref="T:System.Diagnostics.TraceListenerCollection" />.</summary>
      <param name="index">The zero-based index of the <paramref name="value" /> to get.</param>
      <returns>The <see cref="T:System.Diagnostics.TraceListener" /> at the specified index.</returns>
    </member>
    <member name="M:System.Diagnostics.TraceListenerCollection.System#Collections#IList#Remove(System.Object)">
      <summary>Removes an object from the <see cref="T:System.Diagnostics.TraceListenerCollection" />.</summary>
      <param name="value">The object to remove from the <see cref="T:System.Diagnostics.TraceListenerCollection" />.</param>
    </member>
    <member name="T:System.Diagnostics.TraceOptions">
      <summary>Specifies trace data options to be written to the trace output.</summary>
    </member>
    <member name="F:System.Diagnostics.TraceOptions.Callstack">
      <summary>Write the call stack, which is represented by the return value of the <see cref="P:System.Environment.StackTrace" /> property.</summary>
    </member>
    <member name="F:System.Diagnostics.TraceOptions.DateTime">
      <summary>Write the date and time.</summary>
    </member>
    <member name="F:System.Diagnostics.TraceOptions.LogicalOperationStack">
      <summary>Write the logical operation stack, which is represented by the return value of the <see cref="P:System.Diagnostics.CorrelationManager.LogicalOperationStack" /> property.</summary>
    </member>
    <member name="F:System.Diagnostics.TraceOptions.None">
      <summary>Do not write any elements.</summary>
    </member>
    <member name="F:System.Diagnostics.TraceOptions.ProcessId">
      <summary>Write the process identity, which is represented by the return value of the <see cref="P:System.Diagnostics.Process.Id" /> property.</summary>
    </member>
    <member name="F:System.Diagnostics.TraceOptions.ThreadId">
      <summary>Write the thread identity, which is represented by the return value of the <see cref="P:System.Threading.Thread.ManagedThreadId" /> property for the current thread.</summary>
    </member>
    <member name="F:System.Diagnostics.TraceOptions.Timestamp">
      <summary>Write the timestamp, which is represented by the return value of the <see cref="M:System.Diagnostics.Stopwatch.GetTimestamp" /> method.</summary>
    </member>
    <member name="T:System.Diagnostics.TraceSource">
      <summary>Provides a set of methods and properties that enable applications to trace the execution of code and associate trace messages with their source.</summary>
    </member>
    <member name="M:System.Diagnostics.TraceSource.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Diagnostics.TraceSource" /> class, using the specified name for the source.</summary>
      <param name="name">The name of the source (typically, the name of the application).</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="name" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="name" /> is an empty string ("").</exception>
    </member>
    <member name="M:System.Diagnostics.TraceSource.#ctor(System.String,System.Diagnostics.SourceLevels)">
      <summary>Initializes a new instance of the <see cref="T:System.Diagnostics.TraceSource" /> class, using the specified name for the source and the default source level at which tracing is to occur.</summary>
      <param name="name">The name of the source, typically the name of the application.</param>
      <param name="defaultLevel">A bitwise combination of the enumeration values that specifies the default source level at which to trace.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="name" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="name" /> is an empty string ("").</exception>
    </member>
    <member name="P:System.Diagnostics.TraceSource.Attributes">
      <summary>Gets the custom switch attributes defined in the application configuration file.</summary>
      <returns>A <see cref="T:System.Collections.Specialized.StringDictionary" /> containing the custom attributes for the trace switch.</returns>
    </member>
    <member name="M:System.Diagnostics.TraceSource.Close">
      <summary>Closes all the trace listeners in the trace listener collection.</summary>
    </member>
    <member name="M:System.Diagnostics.TraceSource.Flush">
      <summary>Flushes all the trace listeners in the trace listener collection.</summary>
      <exception cref="T:System.ObjectDisposedException">An attempt was made to trace an event during finalization.</exception>
    </member>
    <member name="M:System.Diagnostics.TraceSource.GetSupportedAttributes">
      <summary>Gets the custom attributes supported by the trace source.</summary>
      <returns>A string array naming the custom attributes supported by the trace source, or <see langword="null" /> if there are no custom attributes.</returns>
    </member>
    <member name="P:System.Diagnostics.TraceSource.Listeners">
      <summary>Gets the collection of trace listeners for the trace source.</summary>
      <returns>A <see cref="T:System.Diagnostics.TraceListenerCollection" /> that contains the active trace listeners associated with the source.</returns>
    </member>
    <member name="P:System.Diagnostics.TraceSource.Name">
      <summary>Gets the name of the trace source.</summary>
      <returns>The name of the trace source.</returns>
    </member>
    <member name="P:System.Diagnostics.TraceSource.Switch">
      <summary>Gets or sets the source switch value.</summary>
      <returns>A <see cref="T:System.Diagnostics.SourceSwitch" /> object representing the source switch value.</returns>
      <exception cref="T:System.ArgumentNullException">
        <see cref="P:System.Diagnostics.TraceSource.Switch" /> is set to <see langword="null" />.</exception>
    </member>
    <member name="M:System.Diagnostics.TraceSource.TraceData(System.Diagnostics.TraceEventType,System.Int32,System.Object)">
      <summary>Writes trace data to the trace listeners in the <see cref="P:System.Diagnostics.TraceSource.Listeners" /> collection using the specified event type, event identifier, and trace data.</summary>
      <param name="eventType">One of the enumeration values that specifies the event type of the trace data.</param>
      <param name="id">A numeric identifier for the event.</param>
      <param name="data">The trace data.</param>
      <exception cref="T:System.ObjectDisposedException">An attempt was made to trace an event during finalization.</exception>
    </member>
    <member name="M:System.Diagnostics.TraceSource.TraceData(System.Diagnostics.TraceEventType,System.Int32,System.Object[])">
      <summary>Writes trace data to the trace listeners in the <see cref="P:System.Diagnostics.TraceSource.Listeners" /> collection using the specified event type, event identifier, and trace data array.</summary>
      <param name="eventType">One of the enumeration values that specifies the event type of the trace data.</param>
      <param name="id">A numeric identifier for the event.</param>
      <param name="data">An object array containing the trace data.</param>
      <exception cref="T:System.ObjectDisposedException">An attempt was made to trace an event during finalization.</exception>
    </member>
    <member name="M:System.Diagnostics.TraceSource.TraceEvent(System.Diagnostics.TraceEventType,System.Int32)">
      <summary>Writes a trace event message to the trace listeners in the <see cref="P:System.Diagnostics.TraceSource.Listeners" /> collection using the specified event type and event identifier.</summary>
      <param name="eventType">One of the enumeration values that specifies the event type of the trace data.</param>
      <param name="id">A numeric identifier for the event.</param>
      <exception cref="T:System.ObjectDisposedException">An attempt was made to trace an event during finalization.</exception>
    </member>
    <member name="M:System.Diagnostics.TraceSource.TraceEvent(System.Diagnostics.TraceEventType,System.Int32,System.String)">
      <summary>Writes a trace event message to the trace listeners in the <see cref="P:System.Diagnostics.TraceSource.Listeners" /> collection using the specified event type, event identifier, and message.</summary>
      <param name="eventType">One of the enumeration values that specifies the event type of the trace data.</param>
      <param name="id">A numeric identifier for the event.</param>
      <param name="message">The trace message to write.</param>
      <exception cref="T:System.ObjectDisposedException">An attempt was made to trace an event during finalization.</exception>
    </member>
    <member name="M:System.Diagnostics.TraceSource.TraceEvent(System.Diagnostics.TraceEventType,System.Int32,System.String,System.Object[])">
      <summary>Writes a trace event to the trace listeners in the <see cref="P:System.Diagnostics.TraceSource.Listeners" /> collection using the specified event type, event identifier, and argument array and format.</summary>
      <param name="eventType">One of the enumeration values that specifies the event type of the trace data.</param>
      <param name="id">A numeric identifier for the event.</param>
      <param name="format">A composite format string that contains text intermixed with zero or more format items, which correspond to objects in the <paramref name="args" /> array.</param>
      <param name="args">An <see langword="object" /> array containing zero or more objects to format.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="format" /> is <see langword="null" />.</exception>
      <exception cref="T:System.FormatException">
        <paramref name="format" /> is invalid.
-or-
The number that indicates an argument to format is less than zero, or greater than or equal to the number of specified objects to format.</exception>
      <exception cref="T:System.ObjectDisposedException">An attempt was made to trace an event during finalization.</exception>
    </member>
    <member name="M:System.Diagnostics.TraceSource.TraceInformation(System.String)">
      <summary>Writes an informational message to the trace listeners in the <see cref="P:System.Diagnostics.TraceSource.Listeners" /> collection using the specified message.</summary>
      <param name="message">The informative message to write.</param>
      <exception cref="T:System.ObjectDisposedException">An attempt was made to trace an event during finalization.</exception>
    </member>
    <member name="M:System.Diagnostics.TraceSource.TraceInformation(System.String,System.Object[])">
      <summary>Writes an informational message to the trace listeners in the <see cref="P:System.Diagnostics.TraceSource.Listeners" /> collection using the specified object array and formatting information.</summary>
      <param name="format">A composite format string that contains text intermixed with zero or more format items, which correspond to objects in the <paramref name="args" /> array.</param>
      <param name="args">An array containing zero or more objects to format.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="format" /> is <see langword="null" />.</exception>
      <exception cref="T:System.FormatException">
        <paramref name="format" /> is invalid.
-or-
The number that indicates an argument to format is less than zero, or greater than or equal to the number of specified objects to format.</exception>
      <exception cref="T:System.ObjectDisposedException">An attempt was made to trace an event during finalization.</exception>
    </member>
    <member name="M:System.Diagnostics.TraceSource.TraceTransfer(System.Int32,System.String,System.Guid)">
      <summary>Writes a trace transfer message to the trace listeners in the <see cref="P:System.Diagnostics.TraceSource.Listeners" /> collection using the specified numeric identifier, message, and related activity identifier.</summary>
      <param name="id">A numeric identifier for the event.</param>
      <param name="message">The trace message to write.</param>
      <param name="relatedActivityId">A structure that identifies the related activity.</param>
    </member>
    <member name="T:System.Diagnostics.TraceSwitch">
      <summary>Provides a multilevel switch to control tracing and debug output without recompiling your code.</summary>
    </member>
    <member name="M:System.Diagnostics.TraceSwitch.#ctor(System.String,System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Diagnostics.TraceSwitch" /> class, using the specified display name and description.</summary>
      <param name="displayName">The name to display on a user interface.</param>
      <param name="description">The description of the switch.</param>
    </member>
    <member name="M:System.Diagnostics.TraceSwitch.#ctor(System.String,System.String,System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Diagnostics.TraceSwitch" /> class, using the specified display name, description, and default value for the switch.</summary>
      <param name="displayName">The name to display on a user interface.</param>
      <param name="description">The description of the switch.</param>
      <param name="defaultSwitchValue">The default value of the switch.</param>
    </member>
    <member name="P:System.Diagnostics.TraceSwitch.Level">
      <summary>Gets or sets the trace level that determines the messages the switch allows.</summary>
      <returns>One of the <see cref="T:System.Diagnostics.TraceLevel" /> values that specifies the level of messages that are allowed by the switch.</returns>
      <exception cref="T:System.ArgumentException">
        <see cref="P:System.Diagnostics.TraceSwitch.Level" /> is set to a value that is not one of the <see cref="T:System.Diagnostics.TraceLevel" /> values.</exception>
    </member>
    <member name="M:System.Diagnostics.TraceSwitch.OnSwitchSettingChanged">
      <summary>Updates and corrects the level for this switch.</summary>
    </member>
    <member name="M:System.Diagnostics.TraceSwitch.OnValueChanged">
      <summary>Sets the <see cref="P:System.Diagnostics.Switch.SwitchSetting" /> property to the integer equivalent of the <see cref="P:System.Diagnostics.Switch.Value" /> property.</summary>
    </member>
    <member name="P:System.Diagnostics.TraceSwitch.TraceError">
      <summary>Gets a value indicating whether the switch allows error-handling messages.</summary>
      <returns>
        <see langword="true" /> if the <see cref="P:System.Diagnostics.TraceSwitch.Level" /> property is set to <see cref="F:System.Diagnostics.TraceLevel.Error" />, <see cref="F:System.Diagnostics.TraceLevel.Warning" />, <see cref="F:System.Diagnostics.TraceLevel.Info" />, or <see cref="F:System.Diagnostics.TraceLevel.Verbose" />; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Diagnostics.TraceSwitch.TraceInfo">
      <summary>Gets a value indicating whether the switch allows informational messages.</summary>
      <returns>
        <see langword="true" /> if the <see cref="P:System.Diagnostics.TraceSwitch.Level" /> property is set to <see cref="F:System.Diagnostics.TraceLevel.Info" /> or <see cref="F:System.Diagnostics.TraceLevel.Verbose" />; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Diagnostics.TraceSwitch.TraceVerbose">
      <summary>Gets a value indicating whether the switch allows all messages.</summary>
      <returns>
        <see langword="true" /> if the <see cref="P:System.Diagnostics.TraceSwitch.Level" /> property is set to <see cref="F:System.Diagnostics.TraceLevel.Verbose" />; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Diagnostics.TraceSwitch.TraceWarning">
      <summary>Gets a value indicating whether the switch allows warning messages.</summary>
      <returns>
        <see langword="true" /> if the <see cref="P:System.Diagnostics.TraceSwitch.Level" /> property is set to <see cref="F:System.Diagnostics.TraceLevel.Warning" />, <see cref="F:System.Diagnostics.TraceLevel.Info" />, or <see cref="F:System.Diagnostics.TraceLevel.Verbose" />; otherwise, <see langword="false" />.</returns>
    </member>
  </members>
</doc>