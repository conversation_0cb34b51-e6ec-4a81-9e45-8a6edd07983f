﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>System.Net.ServicePoint</name>
  </assembly>
  <members>
    <member name="T:System.Net.BindIPEndPoint">
      <summary>Represents the method that specifies a local Internet Protocol address and port number for a <see cref="T:System.Net.ServicePoint" />.</summary>
      <param name="servicePoint">The <see cref="T:System.Net.ServicePoint" /> associated with the connection to be created.</param>
      <param name="remoteEndPoint">The remote <see cref="T:System.Net.IPEndPoint" /> that specifies the remote host.</param>
      <param name="retryCount">The number of times this delegate was called for a specified connection.</param>
      <returns>The local <see cref="T:System.Net.IPEndPoint" /> to which the <see cref="T:System.Net.ServicePoint" /> is bound.</returns>
    </member>
    <member name="T:System.Net.SecurityProtocolType">
      <summary>Specifies the security protocols that are supported by the Schannel security package.</summary>
    </member>
    <member name="F:System.Net.SecurityProtocolType.Ssl3">
      <summary>Specifies the Secure Socket Layer (SSL) 3.0 security protocol. SSL 3.0 has been superseded by the Transport Layer Security (TLS) protocol and is provided for backward compatibility only.</summary>
    </member>
    <member name="F:System.Net.SecurityProtocolType.SystemDefault">
      <summary>Allows the operating system to choose the best protocol to use, and to block protocols that are not secure. Unless your app has a specific reason not to, you should use this value.</summary>
    </member>
    <member name="F:System.Net.SecurityProtocolType.Tls">
      <summary>Specifies the Transport Layer Security (TLS) 1.0 security protocol. The TLS 1.0 protocol is defined in IETF RFC 2246.</summary>
    </member>
    <member name="F:System.Net.SecurityProtocolType.Tls11">
      <summary>Specifies the Transport Layer Security (TLS) 1.1 security protocol. The TLS 1.1 protocol is defined in IETF RFC 4346. On Windows systems, this value is supported starting with Windows 7.</summary>
    </member>
    <member name="F:System.Net.SecurityProtocolType.Tls12">
      <summary>Specifies the Transport Layer Security (TLS) 1.2 security protocol. The TLS 1.2 protocol is defined in IETF RFC 5246. On Windows systems, this value is supported starting with Windows 7.</summary>
    </member>
    <member name="F:System.Net.SecurityProtocolType.Tls13">
      <summary>Specifies the TLS 1.3 security protocol. The TLS protocol is defined in IETF RFC 8446.</summary>
    </member>
    <member name="T:System.Net.ServicePoint">
      <summary>Provides connection management for HTTP connections.</summary>
    </member>
    <member name="P:System.Net.ServicePoint.Address">
      <summary>Gets the Uniform Resource Identifier (URI) of the server that this <see cref="T:System.Net.ServicePoint" /> object connects to.</summary>
      <returns>An instance of the <see cref="T:System.Uri" /> class that contains the URI of the Internet server that this <see cref="T:System.Net.ServicePoint" /> object connects to.</returns>
      <exception cref="T:System.NotSupportedException">The <see cref="T:System.Net.ServicePoint" /> is in host mode.</exception>
    </member>
    <member name="P:System.Net.ServicePoint.BindIPEndPointDelegate">
      <summary>Specifies the delegate to associate a local <see cref="T:System.Net.IPEndPoint" /> with a <see cref="T:System.Net.ServicePoint" />.</summary>
      <returns>A delegate that forces a <see cref="T:System.Net.ServicePoint" /> to use a particular local Internet Protocol (IP) address and port number. The default value is <see langword="null" />.</returns>
    </member>
    <member name="P:System.Net.ServicePoint.Certificate">
      <summary>Gets the certificate received for this <see cref="T:System.Net.ServicePoint" /> object.</summary>
      <returns>An instance of the <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" /> class that contains the security certificate received for this <see cref="T:System.Net.ServicePoint" /> object.</returns>
    </member>
    <member name="P:System.Net.ServicePoint.ClientCertificate">
      <summary>Gets the last client certificate sent to the server.</summary>
      <returns>An <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" /> object that contains the public values of the last client certificate sent to the server.</returns>
    </member>
    <member name="M:System.Net.ServicePoint.CloseConnectionGroup(System.String)">
      <summary>Removes the specified connection group from this <see cref="T:System.Net.ServicePoint" /> object.</summary>
      <param name="connectionGroupName">The name of the connection group that contains the connections to close and remove from this service point.</param>
      <returns>A <see cref="T:System.Boolean" /> value that indicates whether the connection group was closed.</returns>
    </member>
    <member name="P:System.Net.ServicePoint.ConnectionLeaseTimeout">
      <summary>Gets or sets the number of milliseconds after which an active <see cref="T:System.Net.ServicePoint" /> connection is closed.</summary>
      <returns>A <see cref="T:System.Int32" /> that specifies the number of milliseconds that an active <see cref="T:System.Net.ServicePoint" /> connection remains open. The default is -1, which allows an active <see cref="T:System.Net.ServicePoint" /> connection to stay connected indefinitely. Set this property to 0 to force <see cref="T:System.Net.ServicePoint" /> connections to close after servicing a request.</returns>
      <exception cref="T:System.ArgumentOutOfRangeException">The value specified for a set operation is a negative number less than -1.</exception>
    </member>
    <member name="P:System.Net.ServicePoint.ConnectionLimit">
      <summary>Gets or sets the maximum number of connections allowed on this <see cref="T:System.Net.ServicePoint" /> object.</summary>
      <returns>The maximum number of connections allowed on this <see cref="T:System.Net.ServicePoint" /> object.</returns>
      <exception cref="T:System.ArgumentOutOfRangeException">The connection limit is equal to or less than 0.</exception>
    </member>
    <member name="P:System.Net.ServicePoint.ConnectionName">
      <summary>Gets the connection name.</summary>
      <returns>A <see cref="T:System.String" /> that represents the connection name.</returns>
    </member>
    <member name="P:System.Net.ServicePoint.CurrentConnections">
      <summary>Gets the number of open connections associated with this <see cref="T:System.Net.ServicePoint" /> object.</summary>
      <returns>The number of open connections associated with this <see cref="T:System.Net.ServicePoint" /> object.</returns>
    </member>
    <member name="P:System.Net.ServicePoint.Expect100Continue">
      <summary>Gets or sets a <see cref="T:System.Boolean" /> value that determines whether 100-Continue behavior is used.</summary>
      <returns>
        <see langword="true" /> to expect 100-Continue responses for <see langword="POST" /> requests; otherwise, <see langword="false" />. The default value is <see langword="true" />.</returns>
    </member>
    <member name="P:System.Net.ServicePoint.IdleSince">
      <summary>Gets the date and time that the <see cref="T:System.Net.ServicePoint" /> object was last connected to a host.</summary>
      <returns>A <see cref="T:System.DateTime" /> object that contains the date and time at which the <see cref="T:System.Net.ServicePoint" /> object was last connected.</returns>
    </member>
    <member name="P:System.Net.ServicePoint.MaxIdleTime">
      <summary>Gets or sets the amount of time a connection associated with the <see cref="T:System.Net.ServicePoint" /> object can remain idle before the connection is closed.</summary>
      <returns>The length of time, in milliseconds, that a connection associated with the <see cref="T:System.Net.ServicePoint" /> object can remain idle before it is closed and reused for another connection.</returns>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <see cref="P:System.Net.ServicePoint.MaxIdleTime" /> is set to less than <see cref="F:System.Threading.Timeout.Infinite" /> or greater than <see cref="F:System.Int32.MaxValue" />.</exception>
    </member>
    <member name="P:System.Net.ServicePoint.ProtocolVersion">
      <summary>Gets the version of the HTTP protocol that the <see cref="T:System.Net.ServicePoint" /> object uses.</summary>
      <returns>A <see cref="T:System.Version" /> object that contains the HTTP protocol version that the <see cref="T:System.Net.ServicePoint" /> object uses.</returns>
    </member>
    <member name="P:System.Net.ServicePoint.ReceiveBufferSize">
      <summary>Gets or sets the size of the receiving buffer for the socket used by this <see cref="T:System.Net.ServicePoint" />.</summary>
      <returns>A <see cref="T:System.Int32" /> that contains the size, in bytes, of the receive buffer. The default is 8192.</returns>
      <exception cref="T:System.ArgumentOutOfRangeException">The value specified for a set operation is greater than <see cref="F:System.Int32.MaxValue" />.</exception>
    </member>
    <member name="M:System.Net.ServicePoint.SetTcpKeepAlive(System.Boolean,System.Int32,System.Int32)">
      <summary>Enables or disables the keep-alive option on a TCP connection.</summary>
      <param name="enabled">If set to true, then the TCP keep-alive option on a TCP connection will be enabled using the specified <paramref name="keepAliveTime" /> and <paramref name="keepAliveInterval" /> values.
If set to false, then the TCP keep-alive option is disabled and the remaining parameters are ignored.
The default value is false.</param>
      <param name="keepAliveTime">Specifies the timeout, in milliseconds, with no activity until the first keep-alive packet is sent.
The value must be greater than 0.  If a value of less than or equal to zero is passed an <see cref="T:System.ArgumentOutOfRangeException" /> is thrown.</param>
      <param name="keepAliveInterval">Specifies the interval, in milliseconds, between when successive keep-alive packets are sent if no acknowledgement is received.
The value must be greater than 0.  If a value of less than or equal to zero is passed an <see cref="T:System.ArgumentOutOfRangeException" /> is thrown.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">The value specified for <paramref name="keepAliveTime" /> or <paramref name="keepAliveInterval" /> parameter is less than or equal to 0.</exception>
    </member>
    <member name="P:System.Net.ServicePoint.SupportsPipelining">
      <summary>Indicates whether the <see cref="T:System.Net.ServicePoint" /> object supports pipelined connections.</summary>
      <returns>
        <see langword="true" /> if the <see cref="T:System.Net.ServicePoint" /> object supports pipelined connections; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Net.ServicePoint.UseNagleAlgorithm">
      <summary>Gets or sets a <see cref="T:System.Boolean" /> value that determines whether the Nagle algorithm is used on connections managed by this <see cref="T:System.Net.ServicePoint" /> object.</summary>
      <returns>
        <see langword="true" /> to use the Nagle algorithm; otherwise, <see langword="false" />. The default value is <see langword="true" />.</returns>
    </member>
    <member name="T:System.Net.ServicePointManager">
      <summary>Manages the collection of <see cref="T:System.Net.ServicePoint" /> objects.</summary>
    </member>
    <member name="P:System.Net.ServicePointManager.CheckCertificateRevocationList">
      <summary>Gets or sets a <see cref="T:System.Boolean" /> value that indicates whether the certificate is checked against the certificate authority revocation list.</summary>
      <returns>
        <see langword="true" /> if the certificate revocation list is checked; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Net.ServicePointManager.DefaultConnectionLimit">
      <summary>Gets or sets the maximum number of concurrent connections allowed by a <see cref="T:System.Net.ServicePoint" /> object.</summary>
      <returns>The maximum number of concurrent connections allowed by a <see cref="T:System.Net.ServicePoint" /> object. The default connection limit is 10 for ASP.NET hosted applications and 2 for all others. When an app is running as an ASP.NET host, it is not possible to alter the value of this property through the config file if the autoConfig property is set to <see langword="true" />. However, you can change the value programmatically when the autoConfig property is <see langword="true" />. Set your preferred value once, when the AppDomain loads.</returns>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <see cref="P:System.Net.ServicePointManager.DefaultConnectionLimit" /> is less than or equal to 0.</exception>
    </member>
    <member name="F:System.Net.ServicePointManager.DefaultNonPersistentConnectionLimit">
      <summary>The default number of non-persistent connections (4) allowed on a <see cref="T:System.Net.ServicePoint" /> object connected to an HTTP/1.0 or later server. This field is constant but is no longer used in the .NET Framework 2.0.</summary>
    </member>
    <member name="F:System.Net.ServicePointManager.DefaultPersistentConnectionLimit">
      <summary>The default number of persistent connections (2) allowed on a <see cref="T:System.Net.ServicePoint" /> object connected to an HTTP/1.1 or later server. This field is constant and is used to initialize the <see cref="P:System.Net.ServicePointManager.DefaultConnectionLimit" /> property if the value of the <see cref="P:System.Net.ServicePointManager.DefaultConnectionLimit" /> property has not been set either directly or through configuration.</summary>
    </member>
    <member name="P:System.Net.ServicePointManager.DnsRefreshTimeout">
      <summary>Gets or sets a value that indicates how long a Domain Name Service (DNS) resolution is considered valid.</summary>
      <returns>The time-out value, in milliseconds. A value of -1 indicates an infinite time-out period. The default value is 120,000 milliseconds (two minutes).</returns>
    </member>
    <member name="P:System.Net.ServicePointManager.EnableDnsRoundRobin">
      <summary>Gets or sets a value that indicates whether a Domain Name Service (DNS) resolution rotates among the applicable Internet Protocol (IP) addresses.</summary>
      <returns>
        <see langword="false" /> if a DNS resolution always returns the first IP address for a particular host; otherwise <see langword="true" />. The default is <see langword="false" />.</returns>
    </member>
    <member name="P:System.Net.ServicePointManager.EncryptionPolicy">
      <summary>Gets the <see cref="T:System.Net.Security.EncryptionPolicy" /> for this <see cref="T:System.Net.ServicePointManager" /> instance.</summary>
      <returns>The encryption policy to use for this <see cref="T:System.Net.ServicePointManager" /> instance.</returns>
    </member>
    <member name="P:System.Net.ServicePointManager.Expect100Continue">
      <summary>Gets or sets a <see cref="T:System.Boolean" /> value that determines whether 100-Continue behavior is used.</summary>
      <returns>
        <see langword="true" /> to enable 100-Continue behavior. The default value is <see langword="true" />.</returns>
    </member>
    <member name="M:System.Net.ServicePointManager.FindServicePoint(System.String,System.Net.IWebProxy)">
      <summary>Finds an existing <see cref="T:System.Net.ServicePoint" /> object or creates a new <see cref="T:System.Net.ServicePoint" /> object to manage communications with the specified Uniform Resource Identifier (URI).</summary>
      <param name="uriString">The URI of the Internet resource to be contacted.</param>
      <param name="proxy">The proxy data for this request.</param>
      <returns>The <see cref="T:System.Net.ServicePoint" /> object that manages communications for the request.</returns>
      <exception cref="T:System.UriFormatException">The URI specified in <paramref name="uriString" /> is invalid.</exception>
      <exception cref="T:System.InvalidOperationException">The maximum number of <see cref="T:System.Net.ServicePoint" /> objects defined in <see cref="P:System.Net.ServicePointManager.MaxServicePoints" /> has been reached.</exception>
    </member>
    <member name="M:System.Net.ServicePointManager.FindServicePoint(System.Uri)">
      <summary>Finds an existing <see cref="T:System.Net.ServicePoint" /> object or creates a new <see cref="T:System.Net.ServicePoint" /> object to manage communications with the specified <see cref="T:System.Uri" /> object.</summary>
      <param name="address">The <see cref="T:System.Uri" /> object of the Internet resource to contact.</param>
      <returns>The <see cref="T:System.Net.ServicePoint" /> object that manages communications for the request.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="address" /> is <see langword="null" />.</exception>
      <exception cref="T:System.InvalidOperationException">The maximum number of <see cref="T:System.Net.ServicePoint" /> objects defined in <see cref="P:System.Net.ServicePointManager.MaxServicePoints" /> has been reached.</exception>
    </member>
    <member name="M:System.Net.ServicePointManager.FindServicePoint(System.Uri,System.Net.IWebProxy)">
      <summary>Finds an existing <see cref="T:System.Net.ServicePoint" /> object or creates a new <see cref="T:System.Net.ServicePoint" /> object to manage communications with the specified <see cref="T:System.Uri" /> object.</summary>
      <param name="address">A <see cref="T:System.Uri" /> object that contains the address of the Internet resource to contact.</param>
      <param name="proxy">The proxy data for this request.</param>
      <returns>The <see cref="T:System.Net.ServicePoint" /> object that manages communications for the request.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="address" /> is <see langword="null" />.</exception>
      <exception cref="T:System.InvalidOperationException">The maximum number of <see cref="T:System.Net.ServicePoint" /> objects defined in <see cref="P:System.Net.ServicePointManager.MaxServicePoints" /> has been reached.</exception>
    </member>
    <member name="P:System.Net.ServicePointManager.MaxServicePointIdleTime">
      <summary>Gets or sets the maximum idle time of a <see cref="T:System.Net.ServicePoint" /> object.</summary>
      <returns>The maximum idle time, in milliseconds, of a <see cref="T:System.Net.ServicePoint" /> object. The default value is 100,000 milliseconds (100 seconds).</returns>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <see cref="P:System.Net.ServicePointManager.MaxServicePointIdleTime" /> is less than <see cref="F:System.Threading.Timeout.Infinite" /> or greater than <see cref="F:System.Int32.MaxValue" />.</exception>
    </member>
    <member name="P:System.Net.ServicePointManager.MaxServicePoints">
      <summary>Gets or sets the maximum number of <see cref="T:System.Net.ServicePoint" /> objects to maintain at any time.</summary>
      <returns>The maximum number of <see cref="T:System.Net.ServicePoint" /> objects to maintain. The default value is 0, which means there is no limit to the number of <see cref="T:System.Net.ServicePoint" /> objects.</returns>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <see cref="P:System.Net.ServicePointManager.MaxServicePoints" /> is less than 0 or greater than <see cref="F:System.Int32.MaxValue" />.</exception>
    </member>
    <member name="P:System.Net.ServicePointManager.ReusePort">
      <summary>Setting this property value to <see langword="true" /> causes all outbound TCP connections from HttpWebRequest to use the native socket option SO_REUSE_UNICASTPORT on the socket. This causes the underlying outgoing ports to be shared. This is useful for scenarios where a large number of outgoing connections are made in a short time, and the app risks running out of ports.</summary>
      <returns>Returns <see cref="T:System.Boolean" />.</returns>
    </member>
    <member name="P:System.Net.ServicePointManager.SecurityProtocol">
      <summary>Gets or sets the security protocol used by the <see cref="T:System.Net.ServicePoint" /> objects managed by the <see cref="T:System.Net.ServicePointManager" /> object.</summary>
      <returns>One of the values defined in the <see cref="T:System.Net.SecurityProtocolType" /> enumeration.</returns>
      <exception cref="T:System.NotSupportedException">The value specified to set the property is not a valid <see cref="T:System.Net.SecurityProtocolType" /> enumeration value.</exception>
    </member>
    <member name="P:System.Net.ServicePointManager.ServerCertificateValidationCallback">
      <summary>Gets or sets the callback to validate a server certificate.</summary>
      <returns>A <see cref="T:System.Net.Security.RemoteCertificateValidationCallback" />. The default value is <see langword="null" />.</returns>
    </member>
    <member name="M:System.Net.ServicePointManager.SetTcpKeepAlive(System.Boolean,System.Int32,System.Int32)">
      <summary>Enables or disables the keep-alive option on a TCP connection.</summary>
      <param name="enabled">If set to true, then the TCP keep-alive option on a TCP connection will be enabled using the specified <paramref name="keepAliveTime" /> and <paramref name="keepAliveInterval" /> values.
If set to false, then the TCP keep-alive option is disabled and the remaining parameters are ignored.
The default value is false.</param>
      <param name="keepAliveTime">Specifies the timeout, in milliseconds, with no activity until the first keep-alive packet is sent.
The value must be greater than 0.  If a value of less than or equal to zero is passed an <see cref="T:System.ArgumentOutOfRangeException" /> is thrown.</param>
      <param name="keepAliveInterval">Specifies the interval, in milliseconds, between when successive keep-alive packets are sent if no acknowledgement is received.
The value must be greater than 0.  If a value of less than or equal to zero is passed an <see cref="T:System.ArgumentOutOfRangeException" /> is thrown.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">The value specified for <paramref name="keepAliveTime" /> or <paramref name="keepAliveInterval" /> parameter is less than or equal to 0.</exception>
    </member>
    <member name="P:System.Net.ServicePointManager.UseNagleAlgorithm">
      <summary>Determines whether the Nagle algorithm is used by the service points managed by this <see cref="T:System.Net.ServicePointManager" /> object.</summary>
      <returns>
        <see langword="true" /> to use the Nagle algorithm; otherwise, <see langword="false" />. The default value is <see langword="true" />.</returns>
    </member>
  </members>
</doc>