﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>System.Reflection.DispatchProxy</name>
  </assembly>
  <members>
    <member name="T:System.Reflection.DispatchProxy">
      <summary>Provides a mechanism for instantiating proxy objects and handling their method dispatch.</summary>
    </member>
    <member name="M:System.Reflection.DispatchProxy.#ctor" />
    <member name="M:System.Reflection.DispatchProxy.Create``2">
      <summary>Creates an object instance that derives from class <typeparamref name="TProxy" /> and implements interface <typeparamref name="T" />.</summary>
      <typeparam name="T">The interface the proxy should implement.</typeparam>
      <typeparam name="TProxy">The base class to use for the proxy class.</typeparam>
      <returns>An object instance that implements <typeparamref name="T" />.</returns>
      <exception cref="T:System.ArgumentException">
        <typeparamref name="T" /> is a class, or <typeparamref name="TProxy" /> is sealed or does not have a parameterless constructor.</exception>
    </member>
    <member name="M:System.Reflection.DispatchProxy.Invoke(System.Reflection.MethodInfo,System.Object[])">
      <summary>Whenever any method on the generated proxy type is called, this method is invoked to dispatch control.</summary>
      <param name="targetMethod">The method the caller invoked.</param>
      <param name="args">The arguments the caller passed to the method.</param>
      <returns>The object to return to the caller, or <see langword="null" /> for void methods.</returns>
    </member>
  </members>
</doc>