﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>System.Reflection.Emit.ILGeneration</name>
  </assembly>
  <members>
    <member name="T:System.Reflection.Emit.CustomAttributeBuilder">
      <summary>Helps build custom attributes.</summary>
    </member>
    <member name="M:System.Reflection.Emit.CustomAttributeBuilder.#ctor(System.Reflection.ConstructorInfo,System.Object[])">
      <summary>Initializes an instance of the <see langword="CustomAttributeBuilder" /> class given the constructor for the custom attribute and the arguments to the constructor.</summary>
      <param name="con">The constructor for the custom attribute.</param>
      <param name="constructorArgs">The arguments to the constructor of the custom attribute.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="con" /> is static or private.
-or-
The number of supplied arguments does not match the number of parameters of the constructor as required by the calling convention of the constructor.
-or-
The type of supplied argument does not match the type of the parameter declared in the constructor.
-or-
A supplied argument is a reference type other than <see cref="T:System.String" /> or <see cref="T:System.Type" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="con" /> or <paramref name="constructorArgs" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Reflection.Emit.CustomAttributeBuilder.#ctor(System.Reflection.ConstructorInfo,System.Object[],System.Reflection.FieldInfo[],System.Object[])">
      <summary>Initializes an instance of the <see langword="CustomAttributeBuilder" /> class given the constructor for the custom attribute, the arguments to the constructor, and a set of named field/value pairs.</summary>
      <param name="con">The constructor for the custom attribute.</param>
      <param name="constructorArgs">The arguments to the constructor of the custom attribute.</param>
      <param name="namedFields">Named fields of the custom attribute.</param>
      <param name="fieldValues">Values for the named fields of the custom attribute.</param>
      <exception cref="T:System.ArgumentException">The lengths of the <paramref name="namedFields" /> and <paramref name="fieldValues" /> arrays are different.
-or-
<paramref name="con" /> is static or private.
-or-
The number of supplied arguments does not match the number of parameters of the constructor as required by the calling convention of the constructor.
-or-
The type of supplied argument does not match the type of the parameter declared in the constructor.
-or-
The types of the field values do not match the types of the named fields.
-or-
The field does not belong to the same class or base class as the constructor.
-or-
A supplied argument or named field is a reference type other than <see cref="T:System.String" /> or <see cref="T:System.Type" />.</exception>
      <exception cref="T:System.ArgumentNullException">One of the parameters is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Reflection.Emit.CustomAttributeBuilder.#ctor(System.Reflection.ConstructorInfo,System.Object[],System.Reflection.PropertyInfo[],System.Object[])">
      <summary>Initializes an instance of the <see langword="CustomAttributeBuilder" /> class given the constructor for the custom attribute, the arguments to the constructor, and a set of named property or value pairs.</summary>
      <param name="con">The constructor for the custom attribute.</param>
      <param name="constructorArgs">The arguments to the constructor of the custom attribute.</param>
      <param name="namedProperties">Named properties of the custom attribute.</param>
      <param name="propertyValues">Values for the named properties of the custom attribute.</param>
      <exception cref="T:System.ArgumentException">The lengths of the <paramref name="namedProperties" /> and <paramref name="propertyValues" /> arrays are different.
-or-
<paramref name="con" /> is static or private.
-or-
The number of supplied arguments does not match the number of parameters of the constructor as required by the calling convention of the constructor.
-or-
The type of supplied argument does not match the type of the parameter declared in the constructor.
-or-
The types of the property values do not match the types of the named properties.
-or-
A property has no setter method.
-or-
The property does not belong to the same class or base class as the constructor.
-or-
A supplied argument or named property is a reference type other than <see cref="T:System.String" /> or <see cref="T:System.Type" />.</exception>
      <exception cref="T:System.ArgumentNullException">One of the parameters is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Reflection.Emit.CustomAttributeBuilder.#ctor(System.Reflection.ConstructorInfo,System.Object[],System.Reflection.PropertyInfo[],System.Object[],System.Reflection.FieldInfo[],System.Object[])">
      <summary>Initializes an instance of the <see langword="CustomAttributeBuilder" /> class given the constructor for the custom attribute, the arguments to the constructor, a set of named property or value pairs, and a set of named field or value pairs.</summary>
      <param name="con">The constructor for the custom attribute.</param>
      <param name="constructorArgs">The arguments to the constructor of the custom attribute.</param>
      <param name="namedProperties">Named properties of the custom attribute.</param>
      <param name="propertyValues">Values for the named properties of the custom attribute.</param>
      <param name="namedFields">Named fields of the custom attribute.</param>
      <param name="fieldValues">Values for the named fields of the custom attribute.</param>
      <exception cref="T:System.ArgumentException">The lengths of the <paramref name="namedProperties" /> and <paramref name="propertyValues" /> arrays are different.
-or-
The lengths of the <paramref name="namedFields" /> and <paramref name="fieldValues" /> arrays are different.
-or-
<paramref name="con" /> is static or private.
-or-
The number of supplied arguments does not match the number of parameters of the constructor as required by the calling convention of the constructor.
-or-
The type of supplied argument does not match the type of the parameter declared in the constructor.
-or-
The types of the property values do not match the types of the named properties.
-or-
The types of the field values do not match the types of the corresponding field types.
-or-
A property has no setter.
-or-
The property or field does not belong to the same class or base class as the constructor.
-or-
A supplied argument, named property, or named field is a reference type other than <see cref="T:System.String" /> or <see cref="T:System.Type" />.</exception>
      <exception cref="T:System.ArgumentNullException">One of the parameters is <see langword="null" />.</exception>
    </member>
    <member name="T:System.Reflection.Emit.ILGenerator">
      <summary>Generates Microsoft intermediate language (MSIL) instructions.</summary>
    </member>
    <member name="M:System.Reflection.Emit.ILGenerator.BeginCatchBlock(System.Type)">
      <summary>Begins a catch block.</summary>
      <param name="exceptionType">The <see cref="T:System.Type" /> object that represents the exception.</param>
      <exception cref="T:System.ArgumentException">The catch block is within a filtered exception.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="exceptionType" /> is <see langword="null" />, and the exception filter block has not returned a value that indicates that finally blocks should be run until this catch block is located.</exception>
      <exception cref="T:System.NotSupportedException">The Microsoft intermediate language (MSIL) being generated is not currently in an exception block.</exception>
    </member>
    <member name="M:System.Reflection.Emit.ILGenerator.BeginExceptFilterBlock">
      <summary>Begins an exception block for a filtered exception.</summary>
      <exception cref="T:System.NotSupportedException">The Microsoft intermediate language (MSIL) being generated is not currently in an exception block.
-or-
This <see cref="T:System.Reflection.Emit.ILGenerator" /> belongs to a <see cref="T:System.Reflection.Emit.DynamicMethod" />.</exception>
    </member>
    <member name="M:System.Reflection.Emit.ILGenerator.BeginExceptionBlock">
      <summary>Begins an exception block for a non-filtered exception.</summary>
      <returns>The label for the end of the block. This will leave you in the correct place to execute finally blocks or to finish the try.</returns>
    </member>
    <member name="M:System.Reflection.Emit.ILGenerator.BeginFaultBlock">
      <summary>Begins an exception fault block in the Microsoft intermediate language (MSIL) stream.</summary>
      <exception cref="T:System.NotSupportedException">The MSIL being generated is not currently in an exception block.
-or-
This <see cref="T:System.Reflection.Emit.ILGenerator" /> belongs to a <see cref="T:System.Reflection.Emit.DynamicMethod" />.</exception>
    </member>
    <member name="M:System.Reflection.Emit.ILGenerator.BeginFinallyBlock">
      <summary>Begins a finally block in the Microsoft intermediate language (MSIL) instruction stream.</summary>
      <exception cref="T:System.NotSupportedException">The MSIL being generated is not currently in an exception block.</exception>
    </member>
    <member name="M:System.Reflection.Emit.ILGenerator.BeginScope">
      <summary>Begins a lexical scope.</summary>
      <exception cref="T:System.NotSupportedException">This <see cref="T:System.Reflection.Emit.ILGenerator" /> belongs to a <see cref="T:System.Reflection.Emit.DynamicMethod" />.</exception>
    </member>
    <member name="M:System.Reflection.Emit.ILGenerator.DeclareLocal(System.Type)">
      <summary>Declares a local variable of the specified type.</summary>
      <param name="localType">A <see cref="T:System.Type" /> object that represents the type of the local variable.</param>
      <returns>The declared local variable.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="localType" /> is <see langword="null" />.</exception>
      <exception cref="T:System.InvalidOperationException">The containing type has been created by the <see cref="M:System.Reflection.Emit.TypeBuilder.CreateType" /> method.</exception>
    </member>
    <member name="M:System.Reflection.Emit.ILGenerator.DeclareLocal(System.Type,System.Boolean)">
      <summary>Declares a local variable of the specified type, optionally pinning the object referred to by the variable.</summary>
      <param name="localType">A <see cref="T:System.Type" /> object that represents the type of the local variable.</param>
      <param name="pinned">
        <see langword="true" /> to pin the object in memory; otherwise, <see langword="false" />.</param>
      <returns>A <see cref="T:System.Reflection.Emit.LocalBuilder" /> object that represents the local variable.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="localType" /> is <see langword="null" />.</exception>
      <exception cref="T:System.InvalidOperationException">The containing type has been created by the <see cref="M:System.Reflection.Emit.TypeBuilder.CreateType" /> method.
-or-
The method body of the enclosing method has been created by the <see cref="M:System.Reflection.Emit.MethodBuilder.CreateMethodBody(System.Byte[],System.Int32)" /> method.</exception>
      <exception cref="T:System.NotSupportedException">The method with which this <see cref="T:System.Reflection.Emit.ILGenerator" /> is associated is not represented by a <see cref="T:System.Reflection.Emit.MethodBuilder" />.</exception>
    </member>
    <member name="M:System.Reflection.Emit.ILGenerator.DefineLabel">
      <summary>Declares a new label.</summary>
      <returns>A new label that can be used as a token for branching.</returns>
    </member>
    <member name="M:System.Reflection.Emit.ILGenerator.Emit(System.Reflection.Emit.OpCode)">
      <summary>Puts the specified instruction onto the stream of instructions.</summary>
      <param name="opcode">The Microsoft Intermediate Language (MSIL) instruction to be put onto the stream.</param>
    </member>
    <member name="M:System.Reflection.Emit.ILGenerator.Emit(System.Reflection.Emit.OpCode,System.Byte)">
      <summary>Puts the specified instruction and character argument onto the Microsoft intermediate language (MSIL) stream of instructions.</summary>
      <param name="opcode">The MSIL instruction to be put onto the stream.</param>
      <param name="arg">The character argument pushed onto the stream immediately after the instruction.</param>
    </member>
    <member name="M:System.Reflection.Emit.ILGenerator.Emit(System.Reflection.Emit.OpCode,System.Double)">
      <summary>Puts the specified instruction and numerical argument onto the Microsoft intermediate language (MSIL) stream of instructions.</summary>
      <param name="opcode">The MSIL instruction to be put onto the stream. Defined in the <see langword="OpCodes" /> enumeration.</param>
      <param name="arg">The numerical argument pushed onto the stream immediately after the instruction.</param>
    </member>
    <member name="M:System.Reflection.Emit.ILGenerator.Emit(System.Reflection.Emit.OpCode,System.Int16)">
      <summary>Puts the specified instruction and numerical argument onto the Microsoft intermediate language (MSIL) stream of instructions.</summary>
      <param name="opcode">The MSIL instruction to be emitted onto the stream.</param>
      <param name="arg">The <see langword="Int" /> argument pushed onto the stream immediately after the instruction.</param>
    </member>
    <member name="M:System.Reflection.Emit.ILGenerator.Emit(System.Reflection.Emit.OpCode,System.Int32)">
      <summary>Puts the specified instruction and numerical argument onto the Microsoft intermediate language (MSIL) stream of instructions.</summary>
      <param name="opcode">The MSIL instruction to be put onto the stream.</param>
      <param name="arg">The numerical argument pushed onto the stream immediately after the instruction.</param>
    </member>
    <member name="M:System.Reflection.Emit.ILGenerator.Emit(System.Reflection.Emit.OpCode,System.Int64)">
      <summary>Puts the specified instruction and numerical argument onto the Microsoft intermediate language (MSIL) stream of instructions.</summary>
      <param name="opcode">The MSIL instruction to be put onto the stream.</param>
      <param name="arg">The numerical argument pushed onto the stream immediately after the instruction.</param>
    </member>
    <member name="M:System.Reflection.Emit.ILGenerator.Emit(System.Reflection.Emit.OpCode,System.Reflection.ConstructorInfo)">
      <summary>Puts the specified instruction and metadata token for the specified constructor onto the Microsoft intermediate language (MSIL) stream of instructions.</summary>
      <param name="opcode">The MSIL instruction to be emitted onto the stream.</param>
      <param name="con">A <see langword="ConstructorInfo" /> representing a constructor.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="con" /> is <see langword="null" />. This exception is new in the .NET Framework 4.</exception>
    </member>
    <member name="M:System.Reflection.Emit.ILGenerator.Emit(System.Reflection.Emit.OpCode,System.Reflection.Emit.Label)">
      <summary>Puts the specified instruction onto the Microsoft intermediate language (MSIL) stream and leaves space to include a label when fixes are done.</summary>
      <param name="opcode">The MSIL instruction to be emitted onto the stream.</param>
      <param name="label">The label to which to branch from this location.</param>
    </member>
    <member name="M:System.Reflection.Emit.ILGenerator.Emit(System.Reflection.Emit.OpCode,System.Reflection.Emit.Label[])">
      <summary>Puts the specified instruction onto the Microsoft intermediate language (MSIL) stream and leaves space to include a label when fixes are done.</summary>
      <param name="opcode">The MSIL instruction to be emitted onto the stream.</param>
      <param name="labels">The array of label objects to which to branch from this location. All of the labels will be used.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="con" /> is <see langword="null" />. This exception is new in the .NET Framework 4.</exception>
    </member>
    <member name="M:System.Reflection.Emit.ILGenerator.Emit(System.Reflection.Emit.OpCode,System.Reflection.Emit.LocalBuilder)">
      <summary>Puts the specified instruction onto the Microsoft intermediate language (MSIL) stream followed by the index of the given local variable.</summary>
      <param name="opcode">The MSIL instruction to be emitted onto the stream.</param>
      <param name="local">A local variable.</param>
      <exception cref="T:System.ArgumentException">The parent method of the <paramref name="local" /> parameter does not match the method associated with this <see cref="T:System.Reflection.Emit.ILGenerator" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="local" /> is <see langword="null" />.</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="opcode" /> is a single-byte instruction, and <paramref name="local" /> represents a local variable with an index greater than <see langword="Byte.MaxValue" />.</exception>
    </member>
    <member name="M:System.Reflection.Emit.ILGenerator.Emit(System.Reflection.Emit.OpCode,System.Reflection.Emit.SignatureHelper)">
      <summary>Puts the specified instruction and a signature token onto the Microsoft intermediate language (MSIL) stream of instructions.</summary>
      <param name="opcode">The MSIL instruction to be emitted onto the stream.</param>
      <param name="signature">A helper for constructing a signature token.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="signature" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Reflection.Emit.ILGenerator.Emit(System.Reflection.Emit.OpCode,System.Reflection.FieldInfo)">
      <summary>Puts the specified instruction and metadata token for the specified field onto the Microsoft intermediate language (MSIL) stream of instructions.</summary>
      <param name="opcode">The MSIL instruction to be emitted onto the stream.</param>
      <param name="field">A <see langword="FieldInfo" /> representing a field.</param>
    </member>
    <member name="M:System.Reflection.Emit.ILGenerator.Emit(System.Reflection.Emit.OpCode,System.Reflection.MethodInfo)">
      <summary>Puts the specified instruction onto the Microsoft intermediate language (MSIL) stream followed by the metadata token for the given method.</summary>
      <param name="opcode">The MSIL instruction to be emitted onto the stream.</param>
      <param name="meth">A <see langword="MethodInfo" /> representing a method.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="meth" /> is <see langword="null" />.</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="meth" /> is a generic method for which the <see cref="P:System.Reflection.MethodBase.IsGenericMethodDefinition" /> property is <see langword="false" />.</exception>
    </member>
    <member name="M:System.Reflection.Emit.ILGenerator.Emit(System.Reflection.Emit.OpCode,System.SByte)">
      <summary>Puts the specified instruction and character argument onto the Microsoft intermediate language (MSIL) stream of instructions.</summary>
      <param name="opcode">The MSIL instruction to be put onto the stream.</param>
      <param name="arg">The character argument pushed onto the stream immediately after the instruction.</param>
    </member>
    <member name="M:System.Reflection.Emit.ILGenerator.Emit(System.Reflection.Emit.OpCode,System.Single)">
      <summary>Puts the specified instruction and numerical argument onto the Microsoft intermediate language (MSIL) stream of instructions.</summary>
      <param name="opcode">The MSIL instruction to be put onto the stream.</param>
      <param name="arg">The <see langword="Single" /> argument pushed onto the stream immediately after the instruction.</param>
    </member>
    <member name="M:System.Reflection.Emit.ILGenerator.Emit(System.Reflection.Emit.OpCode,System.String)">
      <summary>Puts the specified instruction onto the Microsoft intermediate language (MSIL) stream followed by the metadata token for the given string.</summary>
      <param name="opcode">The MSIL instruction to be emitted onto the stream.</param>
      <param name="str">The <see langword="String" /> to be emitted.</param>
    </member>
    <member name="M:System.Reflection.Emit.ILGenerator.Emit(System.Reflection.Emit.OpCode,System.Type)">
      <summary>Puts the specified instruction onto the Microsoft intermediate language (MSIL) stream followed by the metadata token for the given type.</summary>
      <param name="opcode">The MSIL instruction to be put onto the stream.</param>
      <param name="cls">A <see langword="Type" />.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="cls" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Reflection.Emit.ILGenerator.EmitCall(System.Reflection.Emit.OpCode,System.Reflection.MethodInfo,System.Type[])">
      <summary>Puts a <see langword="call" /> or <see langword="callvirt" /> instruction onto the Microsoft intermediate language (MSIL) stream to call a <see langword="varargs" /> method.</summary>
      <param name="opcode">The MSIL instruction to be emitted onto the stream. Must be <see cref="F:System.Reflection.Emit.OpCodes.Call" />, <see cref="F:System.Reflection.Emit.OpCodes.Callvirt" />, or <see cref="F:System.Reflection.Emit.OpCodes.Newobj" />.</param>
      <param name="methodInfo">The <see langword="varargs" /> method to be called.</param>
      <param name="optionalParameterTypes">The types of the optional arguments if the method is a <see langword="varargs" /> method; otherwise, <see langword="null" />.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="opcode" /> does not specify a method call.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="methodInfo" /> is <see langword="null" />.</exception>
      <exception cref="T:System.InvalidOperationException">The calling convention for the method is not <see langword="varargs" />, but optional parameter types are supplied. This exception is thrown in the .NET Framework versions 1.0 and 1.1, In subsequent versions, no exception is thrown.</exception>
    </member>
    <member name="M:System.Reflection.Emit.ILGenerator.EmitCalli(System.Reflection.Emit.OpCode,System.Reflection.CallingConventions,System.Type,System.Type[],System.Type[])">
      <summary>Puts a <see cref="F:System.Reflection.Emit.OpCodes.Calli" /> instruction onto the Microsoft intermediate language (MSIL) stream, specifying a managed calling convention for the indirect call.</summary>
      <param name="opcode">The MSIL instruction to be emitted onto the stream. Must be <see cref="F:System.Reflection.Emit.OpCodes.Calli" />.</param>
      <param name="callingConvention">The managed calling convention to be used.</param>
      <param name="returnType">The <see cref="T:System.Type" /> of the result.</param>
      <param name="parameterTypes">The types of the required arguments to the instruction.</param>
      <param name="optionalParameterTypes">The types of the optional arguments for <see langword="varargs" /> calls.</param>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="optionalParameterTypes" /> is not <see langword="null" />, but <paramref name="callingConvention" /> does not include the <see cref="F:System.Reflection.CallingConventions.VarArgs" /> flag.</exception>
    </member>
    <member name="M:System.Reflection.Emit.ILGenerator.EmitCalli(System.Reflection.Emit.OpCode,System.Runtime.InteropServices.CallingConvention,System.Type,System.Type[])">
      <summary>Puts a <see cref="F:System.Reflection.Emit.OpCodes.Calli" /> instruction onto the Microsoft intermediate language (MSIL) stream, specifying an unmanaged calling convention for the indirect call.</summary>
      <param name="opcode">The MSIL instruction to be emitted onto the stream. Must be <see cref="F:System.Reflection.Emit.OpCodes.Calli" />.</param>
      <param name="unmanagedCallConv">The unmanaged calling convention to be used.</param>
      <param name="returnType">The <see cref="T:System.Type" /> of the result.</param>
      <param name="parameterTypes">The types of the required arguments to the instruction.</param>
    </member>
    <member name="M:System.Reflection.Emit.ILGenerator.EmitWriteLine(System.Reflection.Emit.LocalBuilder)">
      <summary>Emits the Microsoft intermediate language (MSIL) necessary to call <see cref="Overload:System.Console.WriteLine" /> with the given local variable.</summary>
      <param name="localBuilder">The local variable whose value is to be written to the console.</param>
      <exception cref="T:System.ArgumentException">The type of <paramref name="localBuilder" /> is <see cref="T:System.Reflection.Emit.TypeBuilder" /> or <see cref="T:System.Reflection.Emit.EnumBuilder" />, which are not supported.
-or-
There is no overload of <see cref="Overload:System.Console.WriteLine" /> that accepts the type of <paramref name="localBuilder" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="localBuilder" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Reflection.Emit.ILGenerator.EmitWriteLine(System.Reflection.FieldInfo)">
      <summary>Emits the Microsoft intermediate language (MSIL) necessary to call <see cref="Overload:System.Console.WriteLine" /> with the given field.</summary>
      <param name="fld">The field whose value is to be written to the console.</param>
      <exception cref="T:System.ArgumentException">There is no overload of the <see cref="Overload:System.Console.WriteLine" /> method that accepts the type of the specified field.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="fld" /> is <see langword="null" />.</exception>
      <exception cref="T:System.NotSupportedException">The type of the field is <see cref="T:System.Reflection.Emit.TypeBuilder" /> or <see cref="T:System.Reflection.Emit.EnumBuilder" />, which are not supported.</exception>
    </member>
    <member name="M:System.Reflection.Emit.ILGenerator.EmitWriteLine(System.String)">
      <summary>Emits the Microsoft intermediate language (MSIL) to call <see cref="Overload:System.Console.WriteLine" /> with a string.</summary>
      <param name="value">The string to be printed.</param>
    </member>
    <member name="M:System.Reflection.Emit.ILGenerator.EndExceptionBlock">
      <summary>Ends an exception block.</summary>
      <exception cref="T:System.InvalidOperationException">The end exception block occurs in an unexpected place in the code stream.</exception>
      <exception cref="T:System.NotSupportedException">The Microsoft intermediate language (MSIL) being generated is not currently in an exception block.</exception>
    </member>
    <member name="M:System.Reflection.Emit.ILGenerator.EndScope">
      <summary>Ends a lexical scope.</summary>
      <exception cref="T:System.NotSupportedException">This <see cref="T:System.Reflection.Emit.ILGenerator" /> belongs to a <see cref="T:System.Reflection.Emit.DynamicMethod" />.</exception>
    </member>
    <member name="P:System.Reflection.Emit.ILGenerator.ILOffset">
      <summary>Gets the current offset, in bytes, in the Microsoft intermediate language (MSIL) stream that is being emitted by the <see cref="T:System.Reflection.Emit.ILGenerator" />.</summary>
      <returns>The offset in the MSIL stream at which the next instruction will be emitted.</returns>
    </member>
    <member name="M:System.Reflection.Emit.ILGenerator.MarkLabel(System.Reflection.Emit.Label)">
      <summary>Marks the Microsoft intermediate language (MSIL) stream's current position with the given label.</summary>
      <param name="loc">The label for which to set an index.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="loc" /> represents an invalid index into the label array.
-or-
An index for <paramref name="loc" /> has already been defined.</exception>
    </member>
    <member name="M:System.Reflection.Emit.ILGenerator.ThrowException(System.Type)">
      <summary>Emits an instruction to throw an exception.</summary>
      <param name="excType">The class of the type of exception to throw.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="excType" /> is not the <see cref="T:System.Exception" /> class or a derived class of <see cref="T:System.Exception" />.
-or-
The type does not have a parameterless constructor.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="excType" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Reflection.Emit.ILGenerator.UsingNamespace(System.String)">
      <summary>Specifies the namespace to be used in evaluating locals and watches for the current active lexical scope.</summary>
      <param name="usingNamespace">The namespace to be used in evaluating locals and watches for the current active lexical scope</param>
      <exception cref="T:System.ArgumentException">Length of <paramref name="usingNamespace" /> is zero.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="usingNamespace" /> is <see langword="null" />.</exception>
      <exception cref="T:System.NotSupportedException">This <see cref="T:System.Reflection.Emit.ILGenerator" /> belongs to a <see cref="T:System.Reflection.Emit.DynamicMethod" />.</exception>
    </member>
    <member name="T:System.Reflection.Emit.Label">
      <summary>Represents a label in the instruction stream. <see langword="Label" /> is used in conjunction with the <see cref="T:System.Reflection.Emit.ILGenerator" /> class.</summary>
    </member>
    <member name="M:System.Reflection.Emit.Label.Equals(System.Object)">
      <summary>Checks if the given object is an instance of <see langword="Label" /> and is equal to this instance.</summary>
      <param name="obj">The object to compare with this <see langword="Label" /> instance.</param>
      <returns>
        <see langword="true" /> if <paramref name="obj" /> is an instance of <see langword="Label" /> and is equal to this object; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Reflection.Emit.Label.Equals(System.Reflection.Emit.Label)">
      <summary>Indicates whether the current instance is equal to the specified <see cref="T:System.Reflection.Emit.Label" />.</summary>
      <param name="obj">The <see cref="T:System.Reflection.Emit.Label" /> to compare to the current instance.</param>
      <returns>
        <see langword="true" /> if the value of <paramref name="obj" /> is equal to the value of the current instance; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Reflection.Emit.Label.GetHashCode">
      <summary>Generates a hash code for this instance.</summary>
      <returns>A hash code for this instance.</returns>
    </member>
    <member name="M:System.Reflection.Emit.Label.op_Equality(System.Reflection.Emit.Label,System.Reflection.Emit.Label)">
      <summary>Indicates whether two <see cref="T:System.Reflection.Emit.Label" /> structures are equal.</summary>
      <param name="a">The <see cref="T:System.Reflection.Emit.Label" /> to compare to <paramref name="b" />.</param>
      <param name="b">The <see cref="T:System.Reflection.Emit.Label" /> to compare to <paramref name="a" />.</param>
      <returns>
        <see langword="true" /> if <paramref name="a" /> is equal to <paramref name="b" />; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Reflection.Emit.Label.op_Inequality(System.Reflection.Emit.Label,System.Reflection.Emit.Label)">
      <summary>Indicates whether two <see cref="T:System.Reflection.Emit.Label" /> structures are not equal.</summary>
      <param name="a">The <see cref="T:System.Reflection.Emit.Label" /> to compare to <paramref name="b" />.</param>
      <param name="b">The <see cref="T:System.Reflection.Emit.Label" /> to compare to <paramref name="a" />.</param>
      <returns>
        <see langword="true" /> if <paramref name="a" /> is not equal to <paramref name="b" />; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="T:System.Reflection.Emit.LocalBuilder">
      <summary>Represents a local variable within a method or constructor.</summary>
    </member>
    <member name="P:System.Reflection.Emit.LocalBuilder.IsPinned">
      <summary>Gets a value indicating whether the object referred to by the local variable is pinned in memory.</summary>
      <returns>
        <see langword="true" /> if the object referred to by the local variable is pinned in memory; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Reflection.Emit.LocalBuilder.LocalIndex">
      <summary>Gets the zero-based index of the local variable within the method body.</summary>
      <returns>An integer value that represents the order of declaration of the local variable within the method body.</returns>
    </member>
    <member name="P:System.Reflection.Emit.LocalBuilder.LocalType">
      <summary>Gets the type of the local variable.</summary>
      <returns>The <see cref="T:System.Type" /> of the local variable.</returns>
    </member>
    <member name="T:System.Reflection.Emit.ParameterBuilder">
      <summary>Creates or associates parameter information.</summary>
    </member>
    <member name="P:System.Reflection.Emit.ParameterBuilder.Attributes">
      <summary>Retrieves the attributes for this parameter.</summary>
      <returns>Read-only. Retrieves the attributes for this parameter.</returns>
    </member>
    <member name="P:System.Reflection.Emit.ParameterBuilder.IsIn">
      <summary>Retrieves whether this is an input parameter.</summary>
      <returns>Read-only. Retrieves whether this is an input parameter.</returns>
    </member>
    <member name="P:System.Reflection.Emit.ParameterBuilder.IsOptional">
      <summary>Retrieves whether this parameter is optional.</summary>
      <returns>Read-only. Specifies whether this parameter is optional.</returns>
    </member>
    <member name="P:System.Reflection.Emit.ParameterBuilder.IsOut">
      <summary>Retrieves whether this parameter is an output parameter.</summary>
      <returns>Read-only. Retrieves whether this parameter is an output parameter.</returns>
    </member>
    <member name="P:System.Reflection.Emit.ParameterBuilder.Name">
      <summary>Retrieves the name of this parameter.</summary>
      <returns>Read-only. Retrieves the name of this parameter.</returns>
    </member>
    <member name="P:System.Reflection.Emit.ParameterBuilder.Position">
      <summary>Retrieves the signature position for this parameter.</summary>
      <returns>Read-only. Retrieves the signature position for this parameter.</returns>
    </member>
    <member name="M:System.Reflection.Emit.ParameterBuilder.SetConstant(System.Object)">
      <summary>Sets the default value of the parameter.</summary>
      <param name="defaultValue">The default value of this parameter.</param>
      <exception cref="T:System.ArgumentException">The parameter is not one of the supported types.
-or-
The type of <paramref name="defaultValue" /> does not match the type of the parameter.
-or-
The parameter is of type <see cref="T:System.Object" /> or other reference type, <paramref name="defaultValue" /> is not <see langword="null" />, and the value cannot be assigned to the reference type.</exception>
    </member>
    <member name="M:System.Reflection.Emit.ParameterBuilder.SetCustomAttribute(System.Reflection.ConstructorInfo,System.Byte[])">
      <summary>Set a custom attribute using a specified custom attribute blob.</summary>
      <param name="con">The constructor for the custom attribute.</param>
      <param name="binaryAttribute">A byte blob representing the attributes.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="con" /> or <paramref name="binaryAttribute" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Reflection.Emit.ParameterBuilder.SetCustomAttribute(System.Reflection.Emit.CustomAttributeBuilder)">
      <summary>Set a custom attribute using a custom attribute builder.</summary>
      <param name="customBuilder">An instance of a helper class to define the custom attribute.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="con" /> is <see langword="null" />.</exception>
    </member>
    <member name="T:System.Reflection.Emit.SignatureHelper">
      <summary>Provides methods for building signatures.</summary>
    </member>
    <member name="M:System.Reflection.Emit.SignatureHelper.AddArgument(System.Type)">
      <summary>Adds an argument to the signature.</summary>
      <param name="clsArgument">The type of the argument.</param>
      <exception cref="T:System.ArgumentException">The signature has already been finished.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="clsArgument" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Reflection.Emit.SignatureHelper.AddArgument(System.Type,System.Boolean)">
      <summary>Adds an argument of the specified type to the signature, specifying whether the argument is pinned.</summary>
      <param name="argument">The argument type.</param>
      <param name="pinned">
        <see langword="true" /> if the argument is pinned; otherwise, <see langword="false" />.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="argument" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Reflection.Emit.SignatureHelper.AddArgument(System.Type,System.Type[],System.Type[])">
      <summary>Adds an argument to the signature, with the specified custom modifiers.</summary>
      <param name="argument">The argument type.</param>
      <param name="requiredCustomModifiers">An array of types representing the required custom modifiers for the argument, such as <see cref="T:System.Runtime.CompilerServices.IsConst" /> or <see cref="T:System.Runtime.CompilerServices.IsBoxed" />. If the argument has no required custom modifiers, specify <see langword="null" />.</param>
      <param name="optionalCustomModifiers">An array of types representing the optional custom modifiers for the argument, such as <see cref="T:System.Runtime.CompilerServices.IsConst" /> or <see cref="T:System.Runtime.CompilerServices.IsBoxed" />. If the argument has no optional custom modifiers, specify <see langword="null" />.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="argument" /> is <see langword="null" />.
-or-
An element of <paramref name="requiredCustomModifiers" /> or <paramref name="optionalCustomModifiers" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentException">The signature has already been finished.
-or-
One of the specified custom modifiers is an array type.
-or-
One of the specified custom modifiers is an open generic type. That is, the <see cref="P:System.Type.ContainsGenericParameters" /> property is <see langword="true" /> for the custom modifier.</exception>
    </member>
    <member name="M:System.Reflection.Emit.SignatureHelper.AddArguments(System.Type[],System.Type[][],System.Type[][])">
      <summary>Adds a set of arguments to the signature, with the specified custom modifiers.</summary>
      <param name="arguments">The types of the arguments to be added.</param>
      <param name="requiredCustomModifiers">An array of arrays of types. Each array of types represents the required custom modifiers for the corresponding argument, such as <see cref="T:System.Runtime.CompilerServices.IsConst" /> or <see cref="T:System.Runtime.CompilerServices.IsBoxed" />. If a particular argument has no required custom modifiers, specify <see langword="null" /> instead of an array of types. If none of the arguments have required custom modifiers, specify <see langword="null" /> instead of an array of arrays.</param>
      <param name="optionalCustomModifiers">An array of arrays of types. Each array of types represents the optional custom modifiers for the corresponding argument, such as <see cref="T:System.Runtime.CompilerServices.IsConst" /> or <see cref="T:System.Runtime.CompilerServices.IsBoxed" />. If a particular argument has no optional custom modifiers, specify <see langword="null" /> instead of an array of types. If none of the arguments have optional custom modifiers, specify <see langword="null" /> instead of an array of arrays.</param>
      <exception cref="T:System.ArgumentNullException">An element of <paramref name="arguments" /> is <see langword="null" />.
-or-
One of the specified custom modifiers is <see langword="null" />. (However, <see langword="null" /> can be specified for the array of custom modifiers for any argument.)</exception>
      <exception cref="T:System.ArgumentException">The signature has already been finished.
-or-
One of the specified custom modifiers is an array type.
-or-
One of the specified custom modifiers is an open generic type. That is, the <see cref="P:System.Type.ContainsGenericParameters" /> property is <see langword="true" /> for the custom modifier.
-or-
The size of <paramref name="requiredCustomModifiers" /> or <paramref name="optionalCustomModifiers" /> does not equal the size of <paramref name="arguments" />.</exception>
    </member>
    <member name="M:System.Reflection.Emit.SignatureHelper.AddSentinel">
      <summary>Marks the end of a vararg fixed part. This is only used if the caller is creating a vararg signature call site.</summary>
    </member>
    <member name="M:System.Reflection.Emit.SignatureHelper.Equals(System.Object)">
      <summary>Checks if this instance is equal to the given object.</summary>
      <param name="obj">The object with which this instance should be compared.</param>
      <returns>
        <see langword="true" /> if the given object is a <see langword="SignatureHelper" /> and represents the same signature; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Reflection.Emit.SignatureHelper.GetFieldSigHelper(System.Reflection.Module)">
      <summary>Returns a signature helper for a field.</summary>
      <param name="mod">The dynamic module that contains the field for which the <see langword="SignatureHelper" /> is requested.</param>
      <returns>The <see langword="SignatureHelper" /> object for a field.</returns>
    </member>
    <member name="M:System.Reflection.Emit.SignatureHelper.GetHashCode">
      <summary>Creates and returns a hash code for this instance.</summary>
      <returns>The hash code based on the name.</returns>
    </member>
    <member name="M:System.Reflection.Emit.SignatureHelper.GetLocalVarSigHelper">
      <summary>Returns a signature helper for a local variable.</summary>
      <returns>A <see cref="T:System.Reflection.Emit.SignatureHelper" /> for a local variable.</returns>
    </member>
    <member name="M:System.Reflection.Emit.SignatureHelper.GetLocalVarSigHelper(System.Reflection.Module)">
      <summary>Returns a signature helper for a local variable.</summary>
      <param name="mod">The dynamic module that contains the local variable for which the <see langword="SignatureHelper" /> is requested.</param>
      <returns>The <see langword="SignatureHelper" /> object for a local variable.</returns>
    </member>
    <member name="M:System.Reflection.Emit.SignatureHelper.GetMethodSigHelper(System.Reflection.CallingConventions,System.Type)">
      <summary>Returns a signature helper for a method given the method's calling convention and return type.</summary>
      <param name="callingConvention">The calling convention of the method.</param>
      <param name="returnType">The return type of the method, or <see langword="null" /> for a void return type (<see langword="Sub" /> procedure in Visual Basic).</param>
      <returns>The <see langword="SignatureHelper" /> object for a method.</returns>
    </member>
    <member name="M:System.Reflection.Emit.SignatureHelper.GetMethodSigHelper(System.Reflection.Module,System.Reflection.CallingConventions,System.Type)">
      <summary>Returns a signature helper for a method given the method's module, calling convention, and return type.</summary>
      <param name="mod">The <see cref="T:System.Reflection.Emit.ModuleBuilder" /> that contains the method for which the <see langword="SignatureHelper" /> is requested.</param>
      <param name="callingConvention">The calling convention of the method.</param>
      <param name="returnType">The return type of the method, or <see langword="null" /> for a void return type (<see langword="Sub" /> procedure in Visual Basic).</param>
      <returns>The <see langword="SignatureHelper" /> object for a method.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="mod" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="mod" /> is not a <see cref="T:System.Reflection.Emit.ModuleBuilder" />.</exception>
    </member>
    <member name="M:System.Reflection.Emit.SignatureHelper.GetMethodSigHelper(System.Reflection.Module,System.Type,System.Type[])">
      <summary>Returns a signature helper for a method with a standard calling convention, given the method's module, return type, and argument types.</summary>
      <param name="mod">The <see cref="T:System.Reflection.Emit.ModuleBuilder" /> that contains the method for which the <see langword="SignatureHelper" /> is requested.</param>
      <param name="returnType">The return type of the method, or <see langword="null" /> for a void return type (<see langword="Sub" /> procedure in Visual Basic).</param>
      <param name="parameterTypes">The types of the arguments of the method, or <see langword="null" /> if the method has no arguments.</param>
      <returns>The <see langword="SignatureHelper" /> object for a method.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="mod" /> is <see langword="null" />.
-or-
An element of <paramref name="parameterTypes" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="mod" /> is not a <see cref="T:System.Reflection.Emit.ModuleBuilder" />.</exception>
    </member>
    <member name="M:System.Reflection.Emit.SignatureHelper.GetPropertySigHelper(System.Reflection.Module,System.Reflection.CallingConventions,System.Type,System.Type[],System.Type[],System.Type[],System.Type[][],System.Type[][])">
      <summary>Returns a signature helper for a property, given the dynamic module that contains the property, the calling convention, the property type, the property arguments, and custom modifiers for the return type and arguments.</summary>
      <param name="mod">The <see cref="T:System.Reflection.Emit.ModuleBuilder" /> that contains the property for which the <see cref="T:System.Reflection.Emit.SignatureHelper" /> is requested.</param>
      <param name="callingConvention">The calling convention of the property accessors.</param>
      <param name="returnType">The property type.</param>
      <param name="requiredReturnTypeCustomModifiers">An array of types representing the required custom modifiers for the return type, such as <see cref="T:System.Runtime.CompilerServices.IsConst" /> or <see cref="T:System.Runtime.CompilerServices.IsBoxed" />. If the return type has no required custom modifiers, specify <see langword="null" />.</param>
      <param name="optionalReturnTypeCustomModifiers">An array of types representing the optional custom modifiers for the return type, such as <see cref="T:System.Runtime.CompilerServices.IsConst" /> or <see cref="T:System.Runtime.CompilerServices.IsBoxed" />. If the return type has no optional custom modifiers, specify <see langword="null" />.</param>
      <param name="parameterTypes">The types of the property's arguments, or <see langword="null" /> if the property has no arguments.</param>
      <param name="requiredParameterTypeCustomModifiers">An array of arrays of types. Each array of types represents the required custom modifiers for the corresponding argument of the property. If a particular argument has no required custom modifiers, specify <see langword="null" /> instead of an array of types. If the property has no arguments, or if none of the arguments have required custom modifiers, specify <see langword="null" /> instead of an array of arrays.</param>
      <param name="optionalParameterTypeCustomModifiers">An array of arrays of types. Each array of types represents the optional custom modifiers for the corresponding argument of the property. If a particular argument has no optional custom modifiers, specify <see langword="null" /> instead of an array of types. If the property has no arguments, or if none of the arguments have optional custom modifiers, specify <see langword="null" /> instead of an array of arrays.</param>
      <returns>A <see cref="T:System.Reflection.Emit.SignatureHelper" /> object for a property.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="mod" /> is <see langword="null" />.
-or-
An element of <paramref name="parameterTypes" /> is <see langword="null" />.
-or-
One of the specified custom modifiers is <see langword="null" />. (However, <see langword="null" /> can be specified for the array of custom modifiers for any argument.)</exception>
      <exception cref="T:System.ArgumentException">The signature has already been finished.
-or-
<paramref name="mod" /> is not a <see cref="T:System.Reflection.Emit.ModuleBuilder" />.
-or-
One of the specified custom modifiers is an array type.
-or-
One of the specified custom modifiers is an open generic type. That is, the <see cref="P:System.Type.ContainsGenericParameters" /> property is <see langword="true" /> for the custom modifier.
-or-
The size of <paramref name="requiredParameterTypeCustomModifiers" /> or <paramref name="optionalParameterTypeCustomModifiers" /> does not equal the size of <paramref name="parameterTypes" />.</exception>
    </member>
    <member name="M:System.Reflection.Emit.SignatureHelper.GetPropertySigHelper(System.Reflection.Module,System.Type,System.Type[])">
      <summary>Returns a signature helper for a property, given the dynamic module that contains the property, the property type, and the property arguments.</summary>
      <param name="mod">The <see cref="T:System.Reflection.Emit.ModuleBuilder" /> that contains the property for which the <see cref="T:System.Reflection.Emit.SignatureHelper" /> is requested.</param>
      <param name="returnType">The property type.</param>
      <param name="parameterTypes">The argument types, or <see langword="null" /> if the property has no arguments.</param>
      <returns>A <see cref="T:System.Reflection.Emit.SignatureHelper" /> object for a property.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="mod" /> is <see langword="null" />.
-or-
An element of <paramref name="parameterTypes" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="mod" /> is not a <see cref="T:System.Reflection.Emit.ModuleBuilder" />.</exception>
    </member>
    <member name="M:System.Reflection.Emit.SignatureHelper.GetPropertySigHelper(System.Reflection.Module,System.Type,System.Type[],System.Type[],System.Type[],System.Type[][],System.Type[][])">
      <summary>Returns a signature helper for a property, given the dynamic module that contains the property, the property type, the property arguments, and custom modifiers for the return type and arguments.</summary>
      <param name="mod">The <see cref="T:System.Reflection.Emit.ModuleBuilder" /> that contains the property for which the <see cref="T:System.Reflection.Emit.SignatureHelper" /> is requested.</param>
      <param name="returnType">The property type.</param>
      <param name="requiredReturnTypeCustomModifiers">An array of types representing the required custom modifiers for the return type, such as <see cref="T:System.Runtime.CompilerServices.IsConst" /> or <see cref="T:System.Runtime.CompilerServices.IsBoxed" />. If the return type has no required custom modifiers, specify <see langword="null" />.</param>
      <param name="optionalReturnTypeCustomModifiers">An array of types representing the optional custom modifiers for the return type, such as <see cref="T:System.Runtime.CompilerServices.IsConst" /> or <see cref="T:System.Runtime.CompilerServices.IsBoxed" />. If the return type has no optional custom modifiers, specify <see langword="null" />.</param>
      <param name="parameterTypes">The types of the property's arguments, or <see langword="null" /> if the property has no arguments.</param>
      <param name="requiredParameterTypeCustomModifiers">An array of arrays of types. Each array of types represents the required custom modifiers for the corresponding argument of the property. If a particular argument has no required custom modifiers, specify <see langword="null" /> instead of an array of types. If the property has no arguments, or if none of the arguments have required custom modifiers, specify <see langword="null" /> instead of an array of arrays.</param>
      <param name="optionalParameterTypeCustomModifiers">An array of arrays of types. Each array of types represents the optional custom modifiers for the corresponding argument of the property. If a particular argument has no optional custom modifiers, specify <see langword="null" /> instead of an array of types. If the property has no arguments, or if none of the arguments have optional custom modifiers, specify <see langword="null" /> instead of an array of arrays.</param>
      <returns>A <see cref="T:System.Reflection.Emit.SignatureHelper" /> object for a property.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="mod" /> is <see langword="null" />.
-or-
An element of <paramref name="parameterTypes" /> is <see langword="null" />.
-or-
One of the specified custom modifiers is <see langword="null" />. (However, <see langword="null" /> can be specified for the array of custom modifiers for any argument.)</exception>
      <exception cref="T:System.ArgumentException">The signature has already been finished.
-or-
<paramref name="mod" /> is not a <see cref="T:System.Reflection.Emit.ModuleBuilder" />.
-or-
One of the specified custom modifiers is an array type.
-or-
One of the specified custom modifiers is an open generic type. That is, the <see cref="P:System.Type.ContainsGenericParameters" /> property is <see langword="true" /> for the custom modifier.
-or-
The size of <paramref name="requiredParameterTypeCustomModifiers" /> or <paramref name="optionalParameterTypeCustomModifiers" /> does not equal the size of <paramref name="parameterTypes" />.</exception>
    </member>
    <member name="M:System.Reflection.Emit.SignatureHelper.GetSignature">
      <summary>Adds the end token to the signature and marks the signature as finished, so no further tokens can be added.</summary>
      <returns>A byte array made up of the full signature.</returns>
    </member>
    <member name="M:System.Reflection.Emit.SignatureHelper.ToString">
      <summary>Returns a string representing the signature arguments.</summary>
      <returns>A string representing the arguments of this signature.</returns>
    </member>
  </members>
</doc>