﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>System.Reflection.Emit</name>
  </assembly>
  <members>
    <member name="T:System.Reflection.Emit.AssemblyBuilder">
      <summary>Defines and represents a dynamic assembly.</summary>
    </member>
    <member name="P:System.Reflection.Emit.AssemblyBuilder.CodeBase">
      <summary>Gets the location of the assembly, as specified originally (such as in an <see cref="T:System.Reflection.AssemblyName" /> object).</summary>
      <returns>The location of the assembly, as specified originally.</returns>
      <exception cref="T:System.NotSupportedException">This method is not currently supported.</exception>
      <exception cref="T:System.Security.SecurityException">The caller does not have the required permission.</exception>
    </member>
    <member name="M:System.Reflection.Emit.AssemblyBuilder.DefineDynamicAssembly(System.Reflection.AssemblyName,System.Reflection.Emit.AssemblyBuilderAccess)">
      <summary>Defines a dynamic assembly that has the specified name and access rights.</summary>
      <param name="name">The name of the assembly.</param>
      <param name="access">The access rights of the assembly.</param>
      <returns>An object that represents the new assembly.</returns>
    </member>
    <member name="M:System.Reflection.Emit.AssemblyBuilder.DefineDynamicAssembly(System.Reflection.AssemblyName,System.Reflection.Emit.AssemblyBuilderAccess,System.Collections.Generic.IEnumerable{System.Reflection.Emit.CustomAttributeBuilder})">
      <summary>Defines a new assembly that has the specified name, access rights, and attributes.</summary>
      <param name="name">The name of the assembly.</param>
      <param name="access">The access rights of the assembly.</param>
      <param name="assemblyAttributes">A collection that contains the attributes of the assembly.</param>
      <returns>An object that represents the new assembly.</returns>
    </member>
    <member name="M:System.Reflection.Emit.AssemblyBuilder.DefineDynamicModule(System.String)">
      <summary>Defines a named transient dynamic module in this assembly.</summary>
      <param name="name">The name of the dynamic module.</param>
      <returns>A <see cref="T:System.Reflection.Emit.ModuleBuilder" /> representing the defined dynamic module.</returns>
      <exception cref="T:System.ArgumentException">
        <paramref name="name" /> begins with white space.
-or-
The length of <paramref name="name" /> is zero.
-or-
The length of <paramref name="name" /> is greater than the system-defined maximum length.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="name" /> is <see langword="null" />.</exception>
      <exception cref="T:System.Security.SecurityException">The caller does not have the required permission.</exception>
      <exception cref="T:System.ExecutionEngineException">The assembly for default symbol writer cannot be loaded.
-or-
The type that implements the default symbol writer interface cannot be found.</exception>
    </member>
    <member name="P:System.Reflection.Emit.AssemblyBuilder.EntryPoint">
      <summary>Returns the entry point of this assembly.</summary>
      <returns>The entry point of this assembly.</returns>
      <exception cref="T:System.Security.SecurityException">The caller does not have the required permission.</exception>
    </member>
    <member name="M:System.Reflection.Emit.AssemblyBuilder.Equals(System.Object)">
      <summary>Returns a value that indicates whether this instance is equal to the specified object.</summary>
      <param name="obj">An object to compare with this instance, or <see langword="null" />.</param>
      <returns>
        <see langword="true" /> if <paramref name="obj" /> equals the type and value of this instance; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Reflection.Emit.AssemblyBuilder.FullName">
      <summary>Gets the display name of the current dynamic assembly.</summary>
      <returns>The display name of the dynamic assembly.</returns>
    </member>
    <member name="M:System.Reflection.Emit.AssemblyBuilder.GetCustomAttributes(System.Boolean)">
      <summary>Returns all the custom attributes that have been applied to the current <see cref="T:System.Reflection.Emit.AssemblyBuilder" />.</summary>
      <param name="inherit">This argument is ignored for objects of this type.</param>
      <returns>An array that contains the custom attributes; the array is empty if there are no attributes.</returns>
    </member>
    <member name="M:System.Reflection.Emit.AssemblyBuilder.GetCustomAttributes(System.Type,System.Boolean)">
      <summary>Returns all the custom attributes that have been applied to the current <see cref="T:System.Reflection.Emit.AssemblyBuilder" />, and that derive from a specified attribute type.</summary>
      <param name="attributeType">The base type from which attributes derive.</param>
      <param name="inherit">This argument is ignored for objects of this type.</param>
      <returns>An array that contains the custom attributes that are derived at any level from <paramref name="attributeType" />; the array is empty if there are no such attributes.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="attributeType" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="attributeType" /> is not a <see cref="T:System.Type" /> object supplied by the runtime. For example, <paramref name="attributeType" /> is a <see cref="T:System.Reflection.Emit.TypeBuilder" /> object.</exception>
    </member>
    <member name="M:System.Reflection.Emit.AssemblyBuilder.GetCustomAttributesData">
      <summary>Returns <see cref="T:System.Reflection.CustomAttributeData" /> objects that contain information about the attributes that have been applied to the current <see cref="T:System.Reflection.Emit.AssemblyBuilder" />.</summary>
      <returns>A generic list of <see cref="T:System.Reflection.CustomAttributeData" /> objects representing data about the attributes that have been applied to the current module.</returns>
    </member>
    <member name="M:System.Reflection.Emit.AssemblyBuilder.GetDynamicModule(System.String)">
      <summary>Returns the dynamic module with the specified name.</summary>
      <param name="name">The name of the requested dynamic module.</param>
      <returns>A ModuleBuilder object representing the requested dynamic module.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="name" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentException">The length of <paramref name="name" /> is zero.</exception>
      <exception cref="T:System.Security.SecurityException">The caller does not have the required permission.</exception>
    </member>
    <member name="M:System.Reflection.Emit.AssemblyBuilder.GetExportedTypes">
      <summary>Gets the exported types defined in this assembly.</summary>
      <returns>An array of <see cref="T:System.Type" /> containing the exported types defined in this assembly.</returns>
      <exception cref="T:System.NotSupportedException">This method is not implemented.</exception>
      <exception cref="T:System.Security.SecurityException">The caller does not have the required permission.</exception>
    </member>
    <member name="M:System.Reflection.Emit.AssemblyBuilder.GetFile(System.String)">
      <summary>Gets a <see cref="T:System.IO.FileStream" /> for the specified file in the file table of the manifest of this assembly.</summary>
      <param name="name">The name of the specified file.</param>
      <returns>A <see cref="T:System.IO.FileStream" /> for the specified file, or <see langword="null" />, if the file is not found.</returns>
      <exception cref="T:System.NotSupportedException">This method is not currently supported.</exception>
      <exception cref="T:System.Security.SecurityException">The caller does not have the required permission.</exception>
    </member>
    <member name="M:System.Reflection.Emit.AssemblyBuilder.GetFiles(System.Boolean)">
      <summary>Gets the files in the file table of an assembly manifest, specifying whether to include resource modules.</summary>
      <param name="getResourceModules">
        <see langword="true" /> to include resource modules; otherwise, <see langword="false" />.</param>
      <returns>An array of <see cref="T:System.IO.FileStream" /> objects.</returns>
      <exception cref="T:System.NotSupportedException">This method is not currently supported.</exception>
      <exception cref="T:System.Security.SecurityException">The caller does not have the required permission.</exception>
    </member>
    <member name="M:System.Reflection.Emit.AssemblyBuilder.GetHashCode">
      <summary>Returns the hash code for this instance.</summary>
      <returns>A 32-bit signed integer hash code.</returns>
    </member>
    <member name="M:System.Reflection.Emit.AssemblyBuilder.GetLoadedModules(System.Boolean)">
      <summary>Returns all the loaded modules that are part of this assembly, and optionally includes resource modules.</summary>
      <param name="getResourceModules">
        <see langword="true" /> to include resource modules; otherwise, <see langword="false" />.</param>
      <returns>The loaded modules that are part of this assembly.</returns>
    </member>
    <member name="M:System.Reflection.Emit.AssemblyBuilder.GetManifestResourceInfo(System.String)">
      <summary>Returns information about how the given resource has been persisted.</summary>
      <param name="resourceName">The name of the resource.</param>
      <returns>
        <see cref="T:System.Reflection.ManifestResourceInfo" /> populated with information about the resource's topology, or <see langword="null" /> if the resource is not found.</returns>
      <exception cref="T:System.NotSupportedException">This method is not currently supported.</exception>
      <exception cref="T:System.Security.SecurityException">The caller does not have the required permission.</exception>
    </member>
    <member name="M:System.Reflection.Emit.AssemblyBuilder.GetManifestResourceNames">
      <summary>Loads the specified manifest resource from this assembly.</summary>
      <returns>An array of type <see langword="String" /> containing the names of all the resources.</returns>
      <exception cref="T:System.NotSupportedException">This method is not supported on a dynamic assembly. To get the manifest resource names, use <see cref="M:System.Reflection.Assembly.GetManifestResourceNames" />.</exception>
      <exception cref="T:System.Security.SecurityException">The caller does not have the required permission.</exception>
    </member>
    <member name="M:System.Reflection.Emit.AssemblyBuilder.GetManifestResourceStream(System.String)">
      <summary>Loads the specified manifest resource from this assembly.</summary>
      <param name="name">The name of the manifest resource being requested.</param>
      <returns>A <see cref="T:System.IO.Stream" /> representing this manifest resource.</returns>
      <exception cref="T:System.NotSupportedException">This method is not currently supported.</exception>
      <exception cref="T:System.Security.SecurityException">The caller does not have the required permission.</exception>
    </member>
    <member name="M:System.Reflection.Emit.AssemblyBuilder.GetManifestResourceStream(System.Type,System.String)">
      <summary>Loads the specified manifest resource, scoped by the namespace of the specified type, from this assembly.</summary>
      <param name="type">The type whose namespace is used to scope the manifest resource name.</param>
      <param name="name">The name of the manifest resource being requested.</param>
      <returns>A <see cref="T:System.IO.Stream" /> representing this manifest resource.</returns>
      <exception cref="T:System.NotSupportedException">This method is not currently supported.</exception>
      <exception cref="T:System.Security.SecurityException">The caller does not have the required permission.</exception>
    </member>
    <member name="M:System.Reflection.Emit.AssemblyBuilder.GetModule(System.String)">
      <summary>Gets the specified module in this assembly.</summary>
      <param name="name">The name of the requested module.</param>
      <returns>The module being requested, or <see langword="null" /> if the module is not found.</returns>
    </member>
    <member name="M:System.Reflection.Emit.AssemblyBuilder.GetModules(System.Boolean)">
      <summary>Gets all the modules that are part of this assembly, and optionally includes resource modules.</summary>
      <param name="getResourceModules">
        <see langword="true" /> to include resource modules; otherwise, <see langword="false" />.</param>
      <returns>The modules that are part of this assembly.</returns>
    </member>
    <member name="M:System.Reflection.Emit.AssemblyBuilder.GetName(System.Boolean)">
      <summary>Gets the <see cref="T:System.Reflection.AssemblyName" /> that was specified when the current dynamic assembly was created, and sets the code base as specified.</summary>
      <param name="copiedName">
        <see langword="true" /> to set the code base to the location of the assembly after it is shadow-copied; <see langword="false" /> to set the code base to the original location.</param>
      <returns>The name of the dynamic assembly.</returns>
    </member>
    <member name="M:System.Reflection.Emit.AssemblyBuilder.GetReferencedAssemblies">
      <summary>Gets an incomplete list of <see cref="T:System.Reflection.AssemblyName" /> objects for the assemblies that are referenced by this <see cref="T:System.Reflection.Emit.AssemblyBuilder" />.</summary>
      <returns>An array of assembly names for the referenced assemblies. This array is not a complete list.</returns>
    </member>
    <member name="M:System.Reflection.Emit.AssemblyBuilder.GetSatelliteAssembly(System.Globalization.CultureInfo)">
      <summary>Gets the satellite assembly for the specified culture.</summary>
      <param name="culture">The specified culture.</param>
      <returns>The specified satellite assembly.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="culture" /> is <see langword="null" />.</exception>
      <exception cref="T:System.IO.FileNotFoundException">The assembly cannot be found.</exception>
      <exception cref="T:System.IO.FileLoadException">The satellite assembly with a matching file name was found, but the <see langword="CultureInfo" /> did not match the one specified.</exception>
      <exception cref="T:System.BadImageFormatException">The satellite assembly is not a valid assembly.</exception>
    </member>
    <member name="M:System.Reflection.Emit.AssemblyBuilder.GetSatelliteAssembly(System.Globalization.CultureInfo,System.Version)">
      <summary>Gets the specified version of the satellite assembly for the specified culture.</summary>
      <param name="culture">The specified culture.</param>
      <param name="version">The version of the satellite assembly.</param>
      <returns>The specified satellite assembly.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="culture" /> is <see langword="null" />.</exception>
      <exception cref="T:System.IO.FileLoadException">The satellite assembly with a matching file name was found, but the <see langword="CultureInfo" /> or the version did not match the one specified.</exception>
      <exception cref="T:System.IO.FileNotFoundException">The assembly cannot be found.</exception>
      <exception cref="T:System.BadImageFormatException">The satellite assembly is not a valid assembly.</exception>
    </member>
    <member name="M:System.Reflection.Emit.AssemblyBuilder.GetType(System.String,System.Boolean,System.Boolean)">
      <summary>Gets the specified type from the types that have been defined and created in the current <see cref="T:System.Reflection.Emit.AssemblyBuilder" />.</summary>
      <param name="name">The name of the type to search for.</param>
      <param name="throwOnError">
        <see langword="true" /> to throw an exception if the type is not found; otherwise, <see langword="false" />.</param>
      <param name="ignoreCase">
        <see langword="true" /> to ignore the case of the type name when searching; otherwise, <see langword="false" />.</param>
      <returns>The specified type, or <see langword="null" /> if the type is not found or has not been created yet.</returns>
    </member>
    <member name="P:System.Reflection.Emit.AssemblyBuilder.GlobalAssemblyCache">
      <summary>Gets a value that indicates whether the assembly was loaded from the global assembly cache.</summary>
      <returns>Always <see langword="false" />.</returns>
    </member>
    <member name="P:System.Reflection.Emit.AssemblyBuilder.HostContext">
      <summary>Gets the host context where the dynamic assembly is being created.</summary>
      <returns>A value that indicates the host context where the dynamic assembly is being created.</returns>
    </member>
    <member name="P:System.Reflection.Emit.AssemblyBuilder.ImageRuntimeVersion">
      <summary>Gets the version of the common language runtime that will be saved in the file containing the manifest.</summary>
      <returns>A string representing the common language runtime version.</returns>
      <exception cref="T:System.Security.SecurityException">The caller does not have the required permission.</exception>
    </member>
    <member name="M:System.Reflection.Emit.AssemblyBuilder.IsDefined(System.Type,System.Boolean)">
      <summary>Returns a value that indicates whether one or more instances of the specified attribute type is applied to this member.</summary>
      <param name="attributeType">The type of attribute to test for.</param>
      <param name="inherit">This argument is ignored for objects of this type.</param>
      <returns>
        <see langword="true" /> if one or more instances of <paramref name="attributeType" /> is applied to this dynamic assembly; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Reflection.Emit.AssemblyBuilder.IsDynamic">
      <summary>Gets a value that indicates that the current assembly is a dynamic assembly.</summary>
      <returns>Always <see langword="true" />.</returns>
    </member>
    <member name="P:System.Reflection.Emit.AssemblyBuilder.Location">
      <summary>Gets the location, in codebase format, of the loaded file that contains the manifest if it is not shadow-copied.</summary>
      <returns>The location of the loaded file that contains the manifest. If the loaded file has been shadow-copied, the <see langword="Location" /> is that of the file before being shadow-copied.</returns>
      <exception cref="T:System.NotSupportedException">This method is not currently supported.</exception>
      <exception cref="T:System.Security.SecurityException">The caller does not have the required permission.</exception>
    </member>
    <member name="P:System.Reflection.Emit.AssemblyBuilder.ManifestModule">
      <summary>Gets the module in the current <see cref="T:System.Reflection.Emit.AssemblyBuilder" /> that contains the assembly manifest.</summary>
      <returns>The manifest module.</returns>
    </member>
    <member name="P:System.Reflection.Emit.AssemblyBuilder.ReflectionOnly">
      <summary>Gets a value indicating whether the dynamic assembly is in the reflection-only context.</summary>
      <returns>
        <see langword="true" /> if the dynamic assembly is in the reflection-only context; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Reflection.Emit.AssemblyBuilder.SetCustomAttribute(System.Reflection.ConstructorInfo,System.Byte[])">
      <summary>Set a custom attribute on this assembly using a specified custom attribute blob.</summary>
      <param name="con">The constructor for the custom attribute.</param>
      <param name="binaryAttribute">A byte blob representing the attributes.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="con" /> or <paramref name="binaryAttribute" /> is <see langword="null" />.</exception>
      <exception cref="T:System.Security.SecurityException">The caller does not have the required permission.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="con" /> is not a <see langword="RuntimeConstructorInfo" /> object.</exception>
    </member>
    <member name="M:System.Reflection.Emit.AssemblyBuilder.SetCustomAttribute(System.Reflection.Emit.CustomAttributeBuilder)">
      <summary>Set a custom attribute on this assembly using a custom attribute builder.</summary>
      <param name="customBuilder">An instance of a helper class to define the custom attribute.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="con" /> is <see langword="null" />.</exception>
      <exception cref="T:System.Security.SecurityException">The caller does not have the required permission.</exception>
    </member>
    <member name="T:System.Reflection.Emit.AssemblyBuilderAccess">
      <summary>Defines the access modes for a dynamic assembly.</summary>
    </member>
    <member name="F:System.Reflection.Emit.AssemblyBuilderAccess.Run">
      <summary>The dynamic assembly can be executed, but not saved.</summary>
    </member>
    <member name="F:System.Reflection.Emit.AssemblyBuilderAccess.RunAndCollect">
      <summary>The dynamic assembly will be automatically unloaded and its memory reclaimed, when it's no longer accessible.</summary>
    </member>
    <member name="T:System.Reflection.Emit.ConstructorBuilder">
      <summary>Defines and represents a constructor of a dynamic class.</summary>
    </member>
    <member name="P:System.Reflection.Emit.ConstructorBuilder.Attributes">
      <summary>Gets the attributes for this constructor.</summary>
      <returns>The attributes for this constructor.</returns>
    </member>
    <member name="P:System.Reflection.Emit.ConstructorBuilder.CallingConvention">
      <summary>Gets a <see cref="T:System.Reflection.CallingConventions" /> value that depends on whether the declaring type is generic.</summary>
      <returns>
        <see cref="F:System.Reflection.CallingConventions.HasThis" /> if the declaring type is generic; otherwise, <see cref="F:System.Reflection.CallingConventions.Standard" />.</returns>
    </member>
    <member name="P:System.Reflection.Emit.ConstructorBuilder.DeclaringType">
      <summary>Gets a reference to the <see cref="T:System.Type" /> object for the type that declares this member.</summary>
      <returns>The type that declares this member.</returns>
    </member>
    <member name="M:System.Reflection.Emit.ConstructorBuilder.DefineParameter(System.Int32,System.Reflection.ParameterAttributes,System.String)">
      <summary>Defines a parameter of this constructor.</summary>
      <param name="iSequence">The position of the parameter in the parameter list. Parameters are indexed beginning with the number 1 for the first parameter.</param>
      <param name="attributes">The attributes of the parameter.</param>
      <param name="strParamName">The name of the parameter. The name can be the null string.</param>
      <returns>An object that represents the new parameter of this constructor.</returns>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="iSequence" /> is less than 0 (zero), or it is greater than the number of parameters of the constructor.</exception>
      <exception cref="T:System.InvalidOperationException">The containing type has been created using <see cref="M:System.Reflection.Emit.TypeBuilder.CreateType" />.</exception>
    </member>
    <member name="M:System.Reflection.Emit.ConstructorBuilder.GetCustomAttributes(System.Boolean)">
      <summary>Returns all the custom attributes defined for this constructor.</summary>
      <param name="inherit">Controls inheritance of custom attributes from base classes. This parameter is ignored.</param>
      <returns>An array of objects representing all the custom attributes of the constructor represented by this <see cref="T:System.Reflection.Emit.ConstructorBuilder" /> instance.</returns>
      <exception cref="T:System.NotSupportedException">This method is not currently supported.</exception>
    </member>
    <member name="M:System.Reflection.Emit.ConstructorBuilder.GetCustomAttributes(System.Type,System.Boolean)">
      <summary>Returns the custom attributes identified by the given type.</summary>
      <param name="attributeType">The custom attribute type.</param>
      <param name="inherit">Controls inheritance of custom attributes from base classes. This parameter is ignored.</param>
      <returns>An object array that represents the attributes of this constructor.</returns>
      <exception cref="T:System.NotSupportedException">This method is not currently supported.</exception>
    </member>
    <member name="M:System.Reflection.Emit.ConstructorBuilder.GetILGenerator">
      <summary>Gets an <see cref="T:System.Reflection.Emit.ILGenerator" /> for this constructor.</summary>
      <returns>An <see cref="T:System.Reflection.Emit.ILGenerator" /> object for this constructor.</returns>
      <exception cref="T:System.InvalidOperationException">The constructor is a parameterless constructor.
-or-
The constructor has <see cref="T:System.Reflection.MethodAttributes" /> or <see cref="T:System.Reflection.MethodImplAttributes" /> flags indicating that it should not have a method body.</exception>
    </member>
    <member name="M:System.Reflection.Emit.ConstructorBuilder.GetILGenerator(System.Int32)">
      <summary>Gets an <see cref="T:System.Reflection.Emit.ILGenerator" /> object, with the specified MSIL stream size, that can be used to build a method body for this constructor.</summary>
      <param name="streamSize">The size of the MSIL stream, in bytes.</param>
      <returns>An <see cref="T:System.Reflection.Emit.ILGenerator" /> for this constructor.</returns>
      <exception cref="T:System.InvalidOperationException">The constructor is a parameterless constructor.
-or-
The constructor has <see cref="T:System.Reflection.MethodAttributes" /> or <see cref="T:System.Reflection.MethodImplAttributes" /> flags indicating that it should not have a method body.</exception>
    </member>
    <member name="M:System.Reflection.Emit.ConstructorBuilder.GetMethodImplementationFlags">
      <summary>Returns the method implementation flags for this constructor.</summary>
      <returns>The method implementation flags for this constructor.</returns>
    </member>
    <member name="M:System.Reflection.Emit.ConstructorBuilder.GetParameters">
      <summary>Returns the parameters of this constructor.</summary>
      <returns>An array that represents the parameters of this constructor.</returns>
      <exception cref="T:System.InvalidOperationException">
        <see cref="M:System.Reflection.Emit.TypeBuilder.CreateType" /> has not been called on this constructor's type, in the .NET Framework versions 1.0 and 1.1.</exception>
      <exception cref="T:System.NotSupportedException">
        <see cref="M:System.Reflection.Emit.TypeBuilder.CreateType" /> has not been called on this constructor's type, in the .NET Framework version 2.0.</exception>
    </member>
    <member name="P:System.Reflection.Emit.ConstructorBuilder.InitLocals">
      <summary>Gets or sets whether the local variables in this constructor should be zero-initialized.</summary>
      <returns>Read/write. Gets or sets whether the local variables in this constructor should be zero-initialized.</returns>
    </member>
    <member name="M:System.Reflection.Emit.ConstructorBuilder.Invoke(System.Object,System.Reflection.BindingFlags,System.Reflection.Binder,System.Object[],System.Globalization.CultureInfo)">
      <summary>Dynamically invokes the constructor reflected by this instance with the specified arguments, under the constraints of the specified <see langword="Binder" />.</summary>
      <param name="obj">The object that needs to be reinitialized.</param>
      <param name="invokeAttr">One of the <see langword="BindingFlags" /> values that specifies the type of binding that is desired.</param>
      <param name="binder">A <see langword="Binder" /> that defines a set of properties and enables the binding, coercion of argument types, and invocation of members using reflection. If <paramref name="binder" /> is <see langword="null" />, then Binder.DefaultBinding is used.</param>
      <param name="parameters">An argument list. This is an array of arguments with the same number, order, and type as the parameters of the constructor to be invoked. If there are no parameters, this should be a null reference (<see langword="Nothing" /> in Visual Basic).</param>
      <param name="culture">A <see cref="T:System.Globalization.CultureInfo" /> used to govern the coercion of types. If this is null, the <see cref="T:System.Globalization.CultureInfo" /> for the current thread is used.</param>
      <returns>An instance of the class associated with the constructor.</returns>
      <exception cref="T:System.NotSupportedException">This method is not currently supported. You can retrieve the constructor using <see cref="M:System.Type.GetConstructor(System.Reflection.BindingFlags,System.Reflection.Binder,System.Reflection.CallingConventions,System.Type[],System.Reflection.ParameterModifier[])" /> and call <see cref="M:System.Reflection.ConstructorInfo.Invoke(System.Reflection.BindingFlags,System.Reflection.Binder,System.Object[],System.Globalization.CultureInfo)" /> on the returned <see cref="T:System.Reflection.ConstructorInfo" />.</exception>
    </member>
    <member name="M:System.Reflection.Emit.ConstructorBuilder.Invoke(System.Reflection.BindingFlags,System.Reflection.Binder,System.Object[],System.Globalization.CultureInfo)">
      <summary>Dynamically invokes the constructor represented by this instance on the given object, passing along the specified parameters, and under the constraints of the given binder.</summary>
      <param name="invokeAttr">This must be a bit flag from <see cref="T:System.Reflection.BindingFlags" />, such as InvokeMethod, NonPublic, and so on.</param>
      <param name="binder">An object that enables the binding, coercion of argument types, invocation of members, and retrieval of <see langword="MemberInfo" /> objects using reflection. If binder is <see langword="null" />, the default binder is used. See <see cref="T:System.Reflection.Binder" />.</param>
      <param name="parameters">An argument list. This is an array of arguments with the same number, order, and type as the parameters of the constructor to be invoked. If there are no parameters this should be <see langword="null" />.</param>
      <param name="culture">An instance of <see cref="T:System.Globalization.CultureInfo" /> used to govern the coercion of types. If this is null, the <see cref="T:System.Globalization.CultureInfo" /> for the current thread is used. (For example, this is necessary to convert a <see cref="T:System.String" /> that represents 1000 to a <see cref="T:System.Double" /> value, since 1000 is represented differently by different cultures.)</param>
      <returns>The value returned by the invoked constructor.</returns>
      <exception cref="T:System.NotSupportedException">This method is not currently supported. You can retrieve the constructor using <see cref="M:System.Type.GetConstructor(System.Reflection.BindingFlags,System.Reflection.Binder,System.Reflection.CallingConventions,System.Type[],System.Reflection.ParameterModifier[])" /> and call <see cref="M:System.Reflection.ConstructorInfo.Invoke(System.Reflection.BindingFlags,System.Reflection.Binder,System.Object[],System.Globalization.CultureInfo)" /> on the returned <see cref="T:System.Reflection.ConstructorInfo" />.</exception>
    </member>
    <member name="M:System.Reflection.Emit.ConstructorBuilder.IsDefined(System.Type,System.Boolean)">
      <summary>Checks if the specified custom attribute type is defined.</summary>
      <param name="attributeType">A custom attribute type.</param>
      <param name="inherit">Controls inheritance of custom attributes from base classes. This parameter is ignored.</param>
      <returns>
        <see langword="true" /> if the specified custom attribute type is defined; otherwise, <see langword="false" />.</returns>
      <exception cref="T:System.NotSupportedException">This method is not currently supported. You can retrieve the constructor using <see cref="M:System.Type.GetConstructor(System.Reflection.BindingFlags,System.Reflection.Binder,System.Reflection.CallingConventions,System.Type[],System.Reflection.ParameterModifier[])" /> and call <see cref="M:System.Reflection.MemberInfo.IsDefined(System.Type,System.Boolean)" /> on the returned <see cref="T:System.Reflection.ConstructorInfo" />.</exception>
    </member>
    <member name="P:System.Reflection.Emit.ConstructorBuilder.MethodHandle">
      <summary>Gets the internal handle for the method. Use this handle to access the underlying metadata handle.</summary>
      <returns>The internal handle for the method. Use this handle to access the underlying metadata handle.</returns>
      <exception cref="T:System.NotSupportedException">This property is not supported on this class.</exception>
    </member>
    <member name="P:System.Reflection.Emit.ConstructorBuilder.Module">
      <summary>Gets the dynamic module in which this constructor is defined.</summary>
      <returns>A <see cref="T:System.Reflection.Module" /> object that represents the dynamic module in which this constructor is defined.</returns>
    </member>
    <member name="P:System.Reflection.Emit.ConstructorBuilder.Name">
      <summary>Retrieves the name of this constructor.</summary>
      <returns>The name of this constructor.</returns>
    </member>
    <member name="P:System.Reflection.Emit.ConstructorBuilder.ReflectedType">
      <summary>Holds a reference to the <see cref="T:System.Type" /> object from which this object was obtained.</summary>
      <returns>The <see langword="Type" /> object from which this object was obtained.</returns>
    </member>
    <member name="M:System.Reflection.Emit.ConstructorBuilder.SetCustomAttribute(System.Reflection.ConstructorInfo,System.Byte[])">
      <summary>Set a custom attribute using a specified custom attribute blob.</summary>
      <param name="con">The constructor for the custom attribute.</param>
      <param name="binaryAttribute">A byte blob representing the attributes.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="con" /> or <paramref name="binaryAttribute" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Reflection.Emit.ConstructorBuilder.SetCustomAttribute(System.Reflection.Emit.CustomAttributeBuilder)">
      <summary>Set a custom attribute using a custom attribute builder.</summary>
      <param name="customBuilder">An instance of a helper class to define the custom attribute.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="customBuilder" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Reflection.Emit.ConstructorBuilder.SetImplementationFlags(System.Reflection.MethodImplAttributes)">
      <summary>Sets the method implementation flags for this constructor.</summary>
      <param name="attributes">The method implementation flags.</param>
      <exception cref="T:System.InvalidOperationException">The containing type has been created using <see cref="M:System.Reflection.Emit.TypeBuilder.CreateType" />.</exception>
    </member>
    <member name="M:System.Reflection.Emit.ConstructorBuilder.ToString">
      <summary>Returns this <see cref="T:System.Reflection.Emit.ConstructorBuilder" /> instance as a <see cref="T:System.String" />.</summary>
      <returns>A string containing the name, attributes, and exceptions of this constructor, followed by the current Microsoft intermediate language (MSIL) stream.</returns>
    </member>
    <member name="T:System.Reflection.Emit.EnumBuilder">
      <summary>Describes and represents an enumeration type.</summary>
    </member>
    <member name="P:System.Reflection.Emit.EnumBuilder.Assembly">
      <summary>Retrieves the dynamic assembly that contains this enum definition.</summary>
      <returns>Read-only. The dynamic assembly that contains this enum definition.</returns>
    </member>
    <member name="P:System.Reflection.Emit.EnumBuilder.AssemblyQualifiedName">
      <summary>Returns the full path of this enum qualified by the display name of the parent assembly.</summary>
      <returns>Read-only. The full path of this enum qualified by the display name of the parent assembly.</returns>
    </member>
    <member name="P:System.Reflection.Emit.EnumBuilder.BaseType">
      <summary>Returns the parent <see cref="T:System.Type" /> of this type which is always <see cref="T:System.Enum" />.</summary>
      <returns>Read-only. The parent <see cref="T:System.Type" /> of this type.</returns>
    </member>
    <member name="M:System.Reflection.Emit.EnumBuilder.CreateTypeInfo">
      <summary>Gets a <see cref="T:System.Reflection.TypeInfo" /> object that represents this enumeration.</summary>
      <returns>An object that represents this enumeration.</returns>
    </member>
    <member name="P:System.Reflection.Emit.EnumBuilder.DeclaringType">
      <summary>Returns the type that declared this <see cref="T:System.Reflection.Emit.EnumBuilder" />.</summary>
      <returns>Read-only. The type that declared this <see cref="T:System.Reflection.Emit.EnumBuilder" />.</returns>
    </member>
    <member name="M:System.Reflection.Emit.EnumBuilder.DefineLiteral(System.String,System.Object)">
      <summary>Defines the named static field in an enumeration type with the specified constant value.</summary>
      <param name="literalName">The name of the static field.</param>
      <param name="literalValue">The constant value of the literal.</param>
      <returns>The defined field.</returns>
    </member>
    <member name="P:System.Reflection.Emit.EnumBuilder.FullName">
      <summary>Returns the full path of this enum.</summary>
      <returns>Read-only. The full path of this enum.</returns>
    </member>
    <member name="M:System.Reflection.Emit.EnumBuilder.GetConstructors(System.Reflection.BindingFlags)">
      <summary>Returns an array of <see cref="T:System.Reflection.ConstructorInfo" /> objects representing the public and non-public constructors defined for this class, as specified.</summary>
      <param name="bindingAttr">This must be a bit flag from <see cref="T:System.Reflection.BindingFlags" /> : <see langword="InvokeMethod" />, <see langword="NonPublic" />, and so on.</param>
      <returns>Returns an array of <see cref="T:System.Reflection.ConstructorInfo" /> objects representing the specified constructors defined for this class. If no constructors are defined, an empty array is returned.</returns>
      <exception cref="T:System.NotSupportedException">This method is not currently supported in types that are not complete.</exception>
    </member>
    <member name="M:System.Reflection.Emit.EnumBuilder.GetCustomAttributes(System.Boolean)">
      <summary>Returns all the custom attributes defined for this constructor.</summary>
      <param name="inherit">Specifies whether to search this member's inheritance chain to find the attributes.</param>
      <returns>Returns an array of objects representing all the custom attributes of the constructor represented by this <see cref="T:System.Reflection.Emit.ConstructorBuilder" /> instance.</returns>
      <exception cref="T:System.NotSupportedException">This method is not currently supported in types that are not complete.</exception>
    </member>
    <member name="M:System.Reflection.Emit.EnumBuilder.GetCustomAttributes(System.Type,System.Boolean)">
      <summary>Returns the custom attributes identified by the given type.</summary>
      <param name="attributeType">The <see langword="Type" /> object to which the custom attributes are applied.</param>
      <param name="inherit">Specifies whether to search this member's inheritance chain to find the attributes.</param>
      <returns>Returns an array of objects representing the attributes of this constructor that are of <see cref="T:System.Type" /><paramref name="attributeType" />.</returns>
      <exception cref="T:System.NotSupportedException">This method is not currently supported in types that are not complete.</exception>
    </member>
    <member name="M:System.Reflection.Emit.EnumBuilder.GetElementType">
      <summary>Calling this method always throws <see cref="T:System.NotSupportedException" />.</summary>
      <returns>This method is not supported. No value is returned.</returns>
      <exception cref="T:System.NotSupportedException">This method is not currently supported.</exception>
    </member>
    <member name="M:System.Reflection.Emit.EnumBuilder.GetEnumUnderlyingType">
      <summary>Returns the underlying integer type of the current enumeration, which is set when the enumeration builder is defined.</summary>
      <returns>The underlying type.</returns>
    </member>
    <member name="M:System.Reflection.Emit.EnumBuilder.GetEvent(System.String,System.Reflection.BindingFlags)">
      <summary>Returns the event with the specified name.</summary>
      <param name="name">The name of the event to get.</param>
      <param name="bindingAttr">This invocation attribute. This must be a bit flag from <see cref="T:System.Reflection.BindingFlags" /> : <see langword="InvokeMethod" />, <see langword="NonPublic" />, and so on.</param>
      <returns>Returns an <see cref="T:System.Reflection.EventInfo" /> object representing the event declared or inherited by this type with the specified name. If there are no matches, <see langword="null" /> is returned.</returns>
      <exception cref="T:System.NotSupportedException">This method is not currently supported in types that are not complete.</exception>
    </member>
    <member name="M:System.Reflection.Emit.EnumBuilder.GetEvents">
      <summary>Returns the events for the public events declared or inherited by this type.</summary>
      <returns>Returns an array of <see cref="T:System.Reflection.EventInfo" /> objects representing the public events declared or inherited by this type. An empty array is returned if there are no public events.</returns>
      <exception cref="T:System.NotSupportedException">This method is not currently supported in types that are not complete.</exception>
    </member>
    <member name="M:System.Reflection.Emit.EnumBuilder.GetEvents(System.Reflection.BindingFlags)">
      <summary>Returns the public and non-public events that are declared by this type.</summary>
      <param name="bindingAttr">This must be a bit flag from <see cref="T:System.Reflection.BindingFlags" />, such as <see langword="InvokeMethod" />, <see langword="NonPublic" />, and so on.</param>
      <returns>Returns an array of <see cref="T:System.Reflection.EventInfo" /> objects representing the public and non-public events declared or inherited by this type. An empty array is returned if there are no events, as specified.</returns>
      <exception cref="T:System.NotSupportedException">This method is not currently supported in types that are not complete.</exception>
    </member>
    <member name="M:System.Reflection.Emit.EnumBuilder.GetField(System.String,System.Reflection.BindingFlags)">
      <summary>Returns the field specified by the given name.</summary>
      <param name="name">The name of the field to get.</param>
      <param name="bindingAttr">This must be a bit flag from <see cref="T:System.Reflection.BindingFlags" /> : <see langword="InvokeMethod" />, <see langword="NonPublic" />, and so on.</param>
      <returns>Returns the <see cref="T:System.Reflection.FieldInfo" /> object representing the field declared or inherited by this type with the specified name and public or non-public modifier. If there are no matches, then null is returned.</returns>
      <exception cref="T:System.NotSupportedException">This method is not currently supported in types that are not complete.</exception>
    </member>
    <member name="M:System.Reflection.Emit.EnumBuilder.GetFields(System.Reflection.BindingFlags)">
      <summary>Returns the public and non-public fields that are declared by this type.</summary>
      <param name="bindingAttr">This must be a bit flag from <see cref="T:System.Reflection.BindingFlags" />, such as InvokeMethod, NonPublic, and so on.</param>
      <returns>Returns an array of <see cref="T:System.Reflection.FieldInfo" /> objects representing the public and non-public fields declared or inherited by this type. An empty array is returned if there are no fields, as specified.</returns>
      <exception cref="T:System.NotSupportedException">This method is not currently supported in types that are not complete.</exception>
    </member>
    <member name="M:System.Reflection.Emit.EnumBuilder.GetInterface(System.String,System.Boolean)">
      <summary>Returns the interface implemented (directly or indirectly) by this type, with the specified fully-qualified name.</summary>
      <param name="name">The name of the interface.</param>
      <param name="ignoreCase">If <see langword="true" />, the search is case-insensitive. If <see langword="false" />, the search is case-sensitive.</param>
      <returns>Returns a <see cref="T:System.Type" /> object representing the implemented interface. Returns null if no interface matching name is found.</returns>
      <exception cref="T:System.NotSupportedException">This method is not currently supported in types that are not complete.</exception>
    </member>
    <member name="M:System.Reflection.Emit.EnumBuilder.GetInterfaceMap(System.Type)">
      <summary>Returns an interface mapping for the interface requested.</summary>
      <param name="interfaceType">The type of the interface for which the interface mapping is to be retrieved.</param>
      <returns>The requested interface mapping.</returns>
      <exception cref="T:System.ArgumentException">The type does not implement the interface.</exception>
    </member>
    <member name="M:System.Reflection.Emit.EnumBuilder.GetInterfaces">
      <summary>Returns an array of all the interfaces implemented on this a class and its base classes.</summary>
      <returns>Returns an array of <see cref="T:System.Type" /> objects representing the implemented interfaces. If none are defined, an empty array is returned.</returns>
    </member>
    <member name="M:System.Reflection.Emit.EnumBuilder.GetMember(System.String,System.Reflection.MemberTypes,System.Reflection.BindingFlags)">
      <summary>Returns all members with the specified name, type, and binding that are declared or inherited by this type.</summary>
      <param name="name">The name of the member.</param>
      <param name="type">The type of member that is to be returned.</param>
      <param name="bindingAttr">This must be a bit flag from <see cref="T:System.Reflection.BindingFlags" /> : <see langword="InvokeMethod" />, <see langword="NonPublic" />, and so on.</param>
      <returns>Returns an array of <see cref="T:System.Reflection.MemberInfo" /> objects representing the public and non-public members defined on this type if <paramref name="nonPublic" /> is used; otherwise, only the public members are returned.</returns>
      <exception cref="T:System.NotSupportedException">This method is not currently supported in types that are not complete.</exception>
    </member>
    <member name="M:System.Reflection.Emit.EnumBuilder.GetMembers(System.Reflection.BindingFlags)">
      <summary>Returns the specified members declared or inherited by this type,.</summary>
      <param name="bindingAttr">This must be a bit flag from <see cref="T:System.Reflection.BindingFlags" /> : <see langword="InvokeMethod" />, <see langword="NonPublic" />, and so on.</param>
      <returns>Returns an array of <see cref="T:System.Reflection.MemberInfo" /> objects representing the public and non-public members declared or inherited by this type. An empty array is returned if there are no matching members.</returns>
      <exception cref="T:System.NotSupportedException">This method is not currently supported in types that are not complete.</exception>
    </member>
    <member name="M:System.Reflection.Emit.EnumBuilder.GetMethods(System.Reflection.BindingFlags)">
      <summary>Returns all the public and non-public methods declared or inherited by this type, as specified.</summary>
      <param name="bindingAttr">This must be a bit flag from <see cref="T:System.Reflection.BindingFlags" />, such as <see langword="InvokeMethod" />, <see langword="NonPublic" />, and so on.</param>
      <returns>Returns an array of <see cref="T:System.Reflection.MethodInfo" /> objects representing the public and non-public methods defined on this type if <paramref name="nonPublic" /> is used; otherwise, only the public methods are returned.</returns>
      <exception cref="T:System.NotSupportedException">This method is not currently supported in types that are not complete.</exception>
    </member>
    <member name="M:System.Reflection.Emit.EnumBuilder.GetNestedType(System.String,System.Reflection.BindingFlags)">
      <summary>Returns the specified nested type that is declared by this type.</summary>
      <param name="name">The <see cref="T:System.String" /> containing the name of the nested type to get.</param>
      <param name="bindingAttr">A bitmask comprised of one or more <see cref="T:System.Reflection.BindingFlags" /> that specify how the search is conducted.
-or-
Zero, to conduct a case-sensitive search for public methods.</param>
      <returns>A <see cref="T:System.Type" /> object representing the nested type that matches the specified requirements, if found; otherwise, <see langword="null" />.</returns>
      <exception cref="T:System.NotSupportedException">This method is not currently supported in types that are not complete.</exception>
    </member>
    <member name="M:System.Reflection.Emit.EnumBuilder.GetNestedTypes(System.Reflection.BindingFlags)">
      <summary>Returns the public and non-public nested types that are declared or inherited by this type.</summary>
      <param name="bindingAttr">This must be a bit flag from <see cref="T:System.Reflection.BindingFlags" />, such as <see langword="InvokeMethod" />, <see langword="NonPublic" />, and so on.</param>
      <returns>An array of <see cref="T:System.Type" /> objects representing all the types nested within the current <see cref="T:System.Type" /> that match the specified binding constraints.
An empty array of type <see cref="T:System.Type" />, if no types are nested within the current <see cref="T:System.Type" />, or if none of the nested types match the binding constraints.</returns>
      <exception cref="T:System.NotSupportedException">This method is not currently supported in types that are not complete.</exception>
    </member>
    <member name="M:System.Reflection.Emit.EnumBuilder.GetProperties(System.Reflection.BindingFlags)">
      <summary>Returns all the public and non-public properties declared or inherited by this type, as specified.</summary>
      <param name="bindingAttr">This invocation attribute. This must be a bit flag from <see cref="T:System.Reflection.BindingFlags" /> : <see langword="InvokeMethod" />, <see langword="NonPublic" />, and so on.</param>
      <returns>Returns an array of <see cref="T:System.Reflection.PropertyInfo" /> objects representing the public and non-public properties defined on this type if <paramref name="nonPublic" /> is used; otherwise, only the public properties are returned.</returns>
      <exception cref="T:System.NotSupportedException">This method is not currently supported in types that are not complete.</exception>
    </member>
    <member name="P:System.Reflection.Emit.EnumBuilder.GUID">
      <summary>Returns the GUID of this enum.</summary>
      <returns>Read-only. The GUID of this enum.</returns>
      <exception cref="T:System.NotSupportedException">This method is not currently supported in types that are not complete.</exception>
    </member>
    <member name="M:System.Reflection.Emit.EnumBuilder.InvokeMember(System.String,System.Reflection.BindingFlags,System.Reflection.Binder,System.Object,System.Object[],System.Reflection.ParameterModifier[],System.Globalization.CultureInfo,System.String[])">
      <summary>Invokes the specified member. The method that is to be invoked must be accessible and provide the most specific match with the specified argument list, under the constraints of the specified binder and invocation attributes.</summary>
      <param name="name">The name of the member to invoke. This can be a constructor, method, property, or field. A suitable invocation attribute must be specified. Note that it is possible to invoke the default member of a class by passing an empty string as the name of the member.</param>
      <param name="invokeAttr">The invocation attribute. This must be a bit flag from <see langword="BindingFlags" />.</param>
      <param name="binder">An object that enables the binding, coercion of argument types, invocation of members, and retrieval of <see langword="MemberInfo" /> objects using reflection. If binder is <see langword="null" />, the default binder is used. See <see cref="T:System.Reflection.Binder" />.</param>
      <param name="target">The object on which to invoke the specified member. If the member is static, this parameter is ignored.</param>
      <param name="args">An argument list. This is an array of objects that contains the number, order, and type of the parameters of the member to be invoked. If there are no parameters this should be null.</param>
      <param name="modifiers">An array of the same length as <paramref name="args" /> with elements that represent the attributes associated with the arguments of the member to be invoked. A parameter has attributes associated with it in the metadata. They are used by various interoperability services. See the metadata specs for details such as this.</param>
      <param name="culture">An instance of <see langword="CultureInfo" /> used to govern the coercion of types. If this is null, the <see langword="CultureInfo" /> for the current thread is used. (Note that this is necessary to, for example, convert a string that represents 1000 to a double value, since 1000 is represented differently by different cultures.)</param>
      <param name="namedParameters">Each parameter in the <paramref name="namedParameters" /> array gets the value in the corresponding element in the <paramref name="args" /> array. If the length of <paramref name="args" /> is greater than the length of <paramref name="namedParameters" />, the remaining argument values are passed in order.</param>
      <returns>Returns the return value of the invoked member.</returns>
      <exception cref="T:System.NotSupportedException">This method is not currently supported in types that are not complete.</exception>
    </member>
    <member name="P:System.Reflection.Emit.EnumBuilder.IsByRefLike" />
    <member name="P:System.Reflection.Emit.EnumBuilder.IsConstructedGenericType">
      <summary>Gets a value that indicates whether this object represents a constructed generic type.</summary>
      <returns>
        <see langword="true" /> if this object represents a constructed generic type; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Reflection.Emit.EnumBuilder.IsDefined(System.Type,System.Boolean)">
      <summary>Checks if the specified custom attribute type is defined.</summary>
      <param name="attributeType">The <see langword="Type" /> object to which the custom attributes are applied.</param>
      <param name="inherit">Specifies whether to search this member's inheritance chain to find the attributes.</param>
      <returns>
        <see langword="true" /> if one or more instance of <paramref name="attributeType" /> is defined on this member; otherwise, <see langword="false" />.</returns>
      <exception cref="T:System.NotSupportedException">This method is not currently supported in types that are not complete.</exception>
    </member>
    <member name="P:System.Reflection.Emit.EnumBuilder.IsSZArray" />
    <member name="P:System.Reflection.Emit.EnumBuilder.IsTypeDefinition" />
    <member name="P:System.Reflection.Emit.EnumBuilder.IsVariableBoundArray" />
    <member name="M:System.Reflection.Emit.EnumBuilder.MakeArrayType">
      <summary>Returns a <see cref="T:System.Type" /> object representing a one-dimensional array of the current type, with a lower bound of zero.</summary>
      <returns>A <see cref="T:System.Type" /> object representing a one-dimensional array of the current type, with a lower bound of zero.</returns>
    </member>
    <member name="M:System.Reflection.Emit.EnumBuilder.MakeArrayType(System.Int32)">
      <summary>Returns a <see cref="T:System.Type" /> object representing an array of the current type, with the specified number of dimensions.</summary>
      <param name="rank">The number of dimensions for the array. This number must be less than or equal to 32.</param>
      <returns>An object representing an array of the current type, with the specified number of dimensions.</returns>
      <exception cref="T:System.IndexOutOfRangeException">
        <paramref name="rank" /> is less than 1.</exception>
    </member>
    <member name="M:System.Reflection.Emit.EnumBuilder.MakeByRefType">
      <summary>Returns a <see cref="T:System.Type" /> object that represents the current type when passed as a ref parameter (ByRef parameter in Visual Basic).</summary>
      <returns>A <see cref="T:System.Type" /> object that represents the current type when passed as a ref parameter (ByRef parameter in Visual Basic).</returns>
    </member>
    <member name="M:System.Reflection.Emit.EnumBuilder.MakePointerType">
      <summary>Returns a <see cref="T:System.Type" /> object that represents a pointer to the current type.</summary>
      <returns>A <see cref="T:System.Type" /> object that represents a pointer to the current type.</returns>
    </member>
    <member name="P:System.Reflection.Emit.EnumBuilder.Module">
      <summary>Retrieves the dynamic module that contains this <see cref="T:System.Reflection.Emit.EnumBuilder" /> definition.</summary>
      <returns>Read-only. The dynamic module that contains this <see cref="T:System.Reflection.Emit.EnumBuilder" /> definition.</returns>
    </member>
    <member name="P:System.Reflection.Emit.EnumBuilder.Name">
      <summary>Returns the name of this enum.</summary>
      <returns>Read-only. The name of this enum.</returns>
    </member>
    <member name="P:System.Reflection.Emit.EnumBuilder.Namespace">
      <summary>Returns the namespace of this enum.</summary>
      <returns>Read-only. The namespace of this enum.</returns>
    </member>
    <member name="P:System.Reflection.Emit.EnumBuilder.ReflectedType">
      <summary>Returns the type that was used to obtain this <see cref="T:System.Reflection.Emit.EnumBuilder" />.</summary>
      <returns>Read-only. The type that was used to obtain this <see cref="T:System.Reflection.Emit.EnumBuilder" />.</returns>
    </member>
    <member name="M:System.Reflection.Emit.EnumBuilder.SetCustomAttribute(System.Reflection.ConstructorInfo,System.Byte[])">
      <summary>Sets a custom attribute using a specified custom attribute blob.</summary>
      <param name="con">The constructor for the custom attribute.</param>
      <param name="binaryAttribute">A byte blob representing the attributes.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="con" /> or <paramref name="binaryAttribute" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Reflection.Emit.EnumBuilder.SetCustomAttribute(System.Reflection.Emit.CustomAttributeBuilder)">
      <summary>Sets a custom attribute using a custom attribute builder.</summary>
      <param name="customBuilder">An instance of a helper class to define the custom attribute.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="con" /> is <see langword="null" />.</exception>
    </member>
    <member name="P:System.Reflection.Emit.EnumBuilder.TypeHandle">
      <summary>Retrieves the internal handle for this enum.</summary>
      <returns>Read-only. The internal handle for this enum.</returns>
      <exception cref="T:System.NotSupportedException">This property is not currently supported.</exception>
    </member>
    <member name="P:System.Reflection.Emit.EnumBuilder.UnderlyingField">
      <summary>Returns the underlying field for this enum.</summary>
      <returns>Read-only. The underlying field for this enum.</returns>
    </member>
    <member name="P:System.Reflection.Emit.EnumBuilder.UnderlyingSystemType">
      <summary>Returns the underlying system type for this enum.</summary>
      <returns>Read-only. Returns the underlying system type.</returns>
    </member>
    <member name="T:System.Reflection.Emit.EventBuilder">
      <summary>Defines events for a class.</summary>
    </member>
    <member name="M:System.Reflection.Emit.EventBuilder.AddOtherMethod(System.Reflection.Emit.MethodBuilder)">
      <summary>Adds one of the "other" methods associated with this event. "Other" methods are methods other than the "on" and "raise" methods associated with an event. This function can be called many times to add as many "other" methods.</summary>
      <param name="mdBuilder">A <see langword="MethodBuilder" /> object that represents the other method.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="mdBuilder" /> is <see langword="null" />.</exception>
      <exception cref="T:System.InvalidOperationException">
        <see cref="M:System.Reflection.Emit.TypeBuilder.CreateType" /> has been called on the enclosing type.</exception>
    </member>
    <member name="M:System.Reflection.Emit.EventBuilder.SetAddOnMethod(System.Reflection.Emit.MethodBuilder)">
      <summary>Sets the method used to subscribe to this event.</summary>
      <param name="mdBuilder">A <see langword="MethodBuilder" /> object that represents the method used to subscribe to this event.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="mdBuilder" /> is <see langword="null" />.</exception>
      <exception cref="T:System.InvalidOperationException">
        <see cref="M:System.Reflection.Emit.TypeBuilder.CreateType" /> has been called on the enclosing type.</exception>
    </member>
    <member name="M:System.Reflection.Emit.EventBuilder.SetCustomAttribute(System.Reflection.ConstructorInfo,System.Byte[])">
      <summary>Set a custom attribute using a specified custom attribute blob.</summary>
      <param name="con">The constructor for the custom attribute.</param>
      <param name="binaryAttribute">A byte blob representing the attributes.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="con" /> or <paramref name="binaryAttribute" /> is <see langword="null" />.</exception>
      <exception cref="T:System.InvalidOperationException">
        <see cref="M:System.Reflection.Emit.TypeBuilder.CreateType" /> has been called on the enclosing type.</exception>
    </member>
    <member name="M:System.Reflection.Emit.EventBuilder.SetCustomAttribute(System.Reflection.Emit.CustomAttributeBuilder)">
      <summary>Sets a custom attribute using a custom attribute builder.</summary>
      <param name="customBuilder">An instance of a helper class to describe the custom attribute.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="con" /> is <see langword="null" />.</exception>
      <exception cref="T:System.InvalidOperationException">
        <see cref="M:System.Reflection.Emit.TypeBuilder.CreateType" /> has been called on the enclosing type.</exception>
    </member>
    <member name="M:System.Reflection.Emit.EventBuilder.SetRaiseMethod(System.Reflection.Emit.MethodBuilder)">
      <summary>Sets the method used to raise this event.</summary>
      <param name="mdBuilder">A <see langword="MethodBuilder" /> object that represents the method used to raise this event.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="mdBuilder" /> is <see langword="null" />.</exception>
      <exception cref="T:System.InvalidOperationException">
        <see cref="M:System.Reflection.Emit.TypeBuilder.CreateType" /> has been called on the enclosing type.</exception>
    </member>
    <member name="M:System.Reflection.Emit.EventBuilder.SetRemoveOnMethod(System.Reflection.Emit.MethodBuilder)">
      <summary>Sets the method used to unsubscribe to this event.</summary>
      <param name="mdBuilder">A <see langword="MethodBuilder" /> object that represents the method used to unsubscribe to this event.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="mdBuilder" /> is <see langword="null" />.</exception>
      <exception cref="T:System.InvalidOperationException">
        <see cref="M:System.Reflection.Emit.TypeBuilder.CreateType" /> has been called on the enclosing type.</exception>
    </member>
    <member name="T:System.Reflection.Emit.FieldBuilder">
      <summary>Defines and represents a field. This class cannot be inherited.</summary>
    </member>
    <member name="P:System.Reflection.Emit.FieldBuilder.Attributes">
      <summary>Indicates the attributes of this field. This property is read-only.</summary>
      <returns>The attributes of this field.</returns>
    </member>
    <member name="P:System.Reflection.Emit.FieldBuilder.DeclaringType">
      <summary>Indicates a reference to the <see cref="T:System.Type" /> object for the type that declares this field. This property is read-only.</summary>
      <returns>A reference to the <see cref="T:System.Type" /> object for the type that declares this field.</returns>
    </member>
    <member name="P:System.Reflection.Emit.FieldBuilder.FieldHandle">
      <summary>Indicates the internal metadata handle for this field. This property is read-only.</summary>
      <returns>The internal metadata handle for this field.</returns>
      <exception cref="T:System.NotSupportedException">This method is not supported.</exception>
    </member>
    <member name="P:System.Reflection.Emit.FieldBuilder.FieldType">
      <summary>Indicates the <see cref="T:System.Type" /> object that represents the type of this field. This property is read-only.</summary>
      <returns>The <see cref="T:System.Type" /> object that represents the type of this field.</returns>
    </member>
    <member name="M:System.Reflection.Emit.FieldBuilder.GetCustomAttributes(System.Boolean)">
      <summary>Returns all the custom attributes defined for this field.</summary>
      <param name="inherit">Controls inheritance of custom attributes from base classes.</param>
      <returns>An array of type <see cref="T:System.Object" /> representing all the custom attributes of the constructor represented by this <see cref="T:System.Reflection.Emit.FieldBuilder" /> instance.</returns>
      <exception cref="T:System.NotSupportedException">This method is not supported.</exception>
    </member>
    <member name="M:System.Reflection.Emit.FieldBuilder.GetCustomAttributes(System.Type,System.Boolean)">
      <summary>Returns all the custom attributes defined for this field identified by the given type.</summary>
      <param name="attributeType">The custom attribute type.</param>
      <param name="inherit">Controls inheritance of custom attributes from base classes.</param>
      <returns>An array of type <see cref="T:System.Object" /> representing all the custom attributes of the constructor represented by this <see cref="T:System.Reflection.Emit.FieldBuilder" /> instance.</returns>
      <exception cref="T:System.NotSupportedException">This method is not supported.</exception>
    </member>
    <member name="M:System.Reflection.Emit.FieldBuilder.GetValue(System.Object)">
      <summary>Retrieves the value of the field supported by the given object.</summary>
      <param name="obj">The object on which to access the field.</param>
      <returns>An <see cref="T:System.Object" /> containing the value of the field reflected by this instance.</returns>
      <exception cref="T:System.NotSupportedException">This method is not supported.</exception>
    </member>
    <member name="M:System.Reflection.Emit.FieldBuilder.IsDefined(System.Type,System.Boolean)">
      <summary>Indicates whether an attribute having the specified type is defined on a field.</summary>
      <param name="attributeType">The type of the attribute.</param>
      <param name="inherit">Controls inheritance of custom attributes from base classes.</param>
      <returns>
        <see langword="true" /> if one or more instance of <paramref name="attributeType" /> is defined on this field; otherwise, <see langword="false" />.</returns>
      <exception cref="T:System.NotSupportedException">This method is not currently supported. Retrieve the field using <see cref="M:System.Type.GetField(System.String,System.Reflection.BindingFlags)" /> and call <see cref="M:System.Reflection.MemberInfo.IsDefined(System.Type,System.Boolean)" /> on the returned <see cref="T:System.Reflection.FieldInfo" />.</exception>
    </member>
    <member name="P:System.Reflection.Emit.FieldBuilder.Module">
      <summary>Gets the module in which the type that contains this field is being defined.</summary>
      <returns>A <see cref="T:System.Reflection.Module" /> that represents the dynamic module in which this field is being defined.</returns>
    </member>
    <member name="P:System.Reflection.Emit.FieldBuilder.Name">
      <summary>Indicates the name of this field. This property is read-only.</summary>
      <returns>A <see cref="T:System.String" /> containing the name of this field.</returns>
    </member>
    <member name="P:System.Reflection.Emit.FieldBuilder.ReflectedType">
      <summary>Indicates the reference to the <see cref="T:System.Type" /> object from which this object was obtained. This property is read-only.</summary>
      <returns>A reference to the <see cref="T:System.Type" /> object from which this instance was obtained.</returns>
    </member>
    <member name="M:System.Reflection.Emit.FieldBuilder.SetConstant(System.Object)">
      <summary>Sets the default value of this field.</summary>
      <param name="defaultValue">The new default value for this field.</param>
      <exception cref="T:System.InvalidOperationException">The containing type has been created using <see cref="M:System.Reflection.Emit.TypeBuilder.CreateType" />.</exception>
      <exception cref="T:System.ArgumentException">The field is not one of the supported types.
-or-
The type of <paramref name="defaultValue" /> does not match the type of the field.
-or-
The field is of type <see cref="T:System.Object" /> or other reference type, <paramref name="defaultValue" /> is not <see langword="null" />, and the value cannot be assigned to the reference type.</exception>
    </member>
    <member name="M:System.Reflection.Emit.FieldBuilder.SetCustomAttribute(System.Reflection.ConstructorInfo,System.Byte[])">
      <summary>Sets a custom attribute using a specified custom attribute blob.</summary>
      <param name="con">The constructor for the custom attribute.</param>
      <param name="binaryAttribute">A byte blob representing the attributes.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="con" /> or <paramref name="binaryAttribute" /> is <see langword="null" />.</exception>
      <exception cref="T:System.InvalidOperationException">The parent type of this field is complete.</exception>
    </member>
    <member name="M:System.Reflection.Emit.FieldBuilder.SetCustomAttribute(System.Reflection.Emit.CustomAttributeBuilder)">
      <summary>Sets a custom attribute using a custom attribute builder.</summary>
      <param name="customBuilder">An instance of a helper class to define the custom attribute.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="con" /> is <see langword="null" />.</exception>
      <exception cref="T:System.InvalidOperationException">The parent type of this field is complete.</exception>
    </member>
    <member name="M:System.Reflection.Emit.FieldBuilder.SetOffset(System.Int32)">
      <summary>Specifies the field layout.</summary>
      <param name="iOffset">The offset of the field within the type containing this field.</param>
      <exception cref="T:System.InvalidOperationException">The containing type has been created using <see cref="M:System.Reflection.Emit.TypeBuilder.CreateType" />.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="iOffset" /> is less than zero.</exception>
    </member>
    <member name="M:System.Reflection.Emit.FieldBuilder.SetValue(System.Object,System.Object,System.Reflection.BindingFlags,System.Reflection.Binder,System.Globalization.CultureInfo)">
      <summary>Sets the value of the field supported by the given object.</summary>
      <param name="obj">The object on which to access the field.</param>
      <param name="val">The value to assign to the field.</param>
      <param name="invokeAttr">A member of <see langword="IBinder" /> that specifies the type of binding that is desired (for example, IBinder.CreateInstance, IBinder.ExactBinding).</param>
      <param name="binder">A set of properties and enabling for binding, coercion of argument types, and invocation of members using reflection. If binder is null, then IBinder.DefaultBinding is used.</param>
      <param name="culture">The software preferences of a particular culture.</param>
      <exception cref="T:System.NotSupportedException">This method is not supported.</exception>
    </member>
    <member name="T:System.Reflection.Emit.GenericTypeParameterBuilder">
      <summary>Defines and creates generic type parameters for dynamically defined generic types and methods. This class cannot be inherited.</summary>
    </member>
    <member name="P:System.Reflection.Emit.GenericTypeParameterBuilder.Assembly">
      <summary>Gets an <see cref="T:System.Reflection.Assembly" /> object representing the dynamic assembly that contains the generic type definition the current type parameter belongs to.</summary>
      <returns>An <see cref="T:System.Reflection.Assembly" /> object representing the dynamic assembly that contains the generic type definition the current type parameter belongs to.</returns>
    </member>
    <member name="P:System.Reflection.Emit.GenericTypeParameterBuilder.AssemblyQualifiedName">
      <summary>Gets <see langword="null" /> in all cases.</summary>
      <returns>A null reference (<see langword="Nothing" /> in Visual Basic) in all cases.</returns>
    </member>
    <member name="P:System.Reflection.Emit.GenericTypeParameterBuilder.BaseType">
      <summary>Gets the base type constraint of the current generic type parameter.</summary>
      <returns>A <see cref="T:System.Type" /> object that represents the base type constraint of the generic type parameter, or <see langword="null" /> if the type parameter has no base type constraint.</returns>
    </member>
    <member name="P:System.Reflection.Emit.GenericTypeParameterBuilder.ContainsGenericParameters">
      <summary>Gets <see langword="true" /> in all cases.</summary>
      <returns>
        <see langword="true" /> in all cases.</returns>
    </member>
    <member name="P:System.Reflection.Emit.GenericTypeParameterBuilder.DeclaringMethod">
      <summary>Gets a <see cref="T:System.Reflection.MethodInfo" /> that represents the declaring method, if the current <see cref="T:System.Reflection.Emit.GenericTypeParameterBuilder" /> represents a type parameter of a generic method.</summary>
      <returns>A <see cref="T:System.Reflection.MethodInfo" /> that represents the declaring method, if the current <see cref="T:System.Reflection.Emit.GenericTypeParameterBuilder" /> represents a type parameter of a generic method; otherwise, <see langword="null" />.</returns>
    </member>
    <member name="P:System.Reflection.Emit.GenericTypeParameterBuilder.DeclaringType">
      <summary>Gets the generic type definition or generic method definition to which the generic type parameter belongs.</summary>
      <returns>If the type parameter belongs to a generic type, a <see cref="T:System.Type" /> object representing that generic type; if the type parameter belongs to a generic method, a <see cref="T:System.Type" /> object representing that type that declared that generic method.</returns>
    </member>
    <member name="M:System.Reflection.Emit.GenericTypeParameterBuilder.Equals(System.Object)">
      <summary>Tests whether the given object is an instance of <see langword="EventToken" /> and is equal to the current instance.</summary>
      <param name="o">The object to be compared with the current instance.</param>
      <returns>
        <see langword="true" /> if <paramref name="o" /> is an instance of <see langword="EventToken" /> and equals the current instance; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Reflection.Emit.GenericTypeParameterBuilder.FullName">
      <summary>Gets <see langword="null" /> in all cases.</summary>
      <returns>A null reference (<see langword="Nothing" /> in Visual Basic) in all cases.</returns>
    </member>
    <member name="P:System.Reflection.Emit.GenericTypeParameterBuilder.GenericParameterAttributes">
      <summary>Gets a combination of <see cref="T:System.Reflection.GenericParameterAttributes" /> flags that describe the covariance and special constraints of the current generic type parameter.</summary>
      <returns>A bitwise combination of values that describes the covariance and special constraints of the current generic type parameter.</returns>
    </member>
    <member name="P:System.Reflection.Emit.GenericTypeParameterBuilder.GenericParameterPosition">
      <summary>Gets the position of the type parameter in the type parameter list of the generic type or method that declared the parameter.</summary>
      <returns>The position of the type parameter in the type parameter list of the generic type or method that declared the parameter.</returns>
    </member>
    <member name="M:System.Reflection.Emit.GenericTypeParameterBuilder.GetConstructors(System.Reflection.BindingFlags)">
      <summary>Not supported for incomplete generic type parameters.</summary>
      <param name="bindingAttr">Not supported.</param>
      <returns>Not supported for incomplete generic type parameters.</returns>
      <exception cref="T:System.NotSupportedException">In all cases.</exception>
    </member>
    <member name="M:System.Reflection.Emit.GenericTypeParameterBuilder.GetCustomAttributes(System.Boolean)">
      <summary>Not supported for incomplete generic type parameters.</summary>
      <param name="inherit">Specifies whether to search this member's inheritance chain to find the attributes.</param>
      <returns>Not supported for incomplete generic type parameters.</returns>
      <exception cref="T:System.NotSupportedException">In all cases.</exception>
    </member>
    <member name="M:System.Reflection.Emit.GenericTypeParameterBuilder.GetCustomAttributes(System.Type,System.Boolean)">
      <summary>Not supported for incomplete generic type parameters.</summary>
      <param name="attributeType">The type of attribute to search for. Only attributes that are assignable to this type are returned.</param>
      <param name="inherit">Specifies whether to search this member's inheritance chain to find the attributes.</param>
      <returns>Not supported for incomplete generic type parameters.</returns>
      <exception cref="T:System.NotSupportedException">In all cases.</exception>
    </member>
    <member name="M:System.Reflection.Emit.GenericTypeParameterBuilder.GetElementType">
      <summary>Throws a <see cref="T:System.NotSupportedException" /> in all cases.</summary>
      <returns>The type referred to by the current array type, pointer type, or <see langword="ByRef" /> type; or <see langword="null" /> if the current type is not an array type, is not a pointer type, and is not passed by reference.</returns>
      <exception cref="T:System.NotSupportedException">In all cases.</exception>
    </member>
    <member name="M:System.Reflection.Emit.GenericTypeParameterBuilder.GetEvent(System.String,System.Reflection.BindingFlags)">
      <summary>Not supported for incomplete generic type parameters.</summary>
      <param name="name">Not supported.</param>
      <param name="bindingAttr">Not supported.</param>
      <returns>Not supported for incomplete generic type parameters.</returns>
      <exception cref="T:System.NotSupportedException">In all cases.</exception>
    </member>
    <member name="M:System.Reflection.Emit.GenericTypeParameterBuilder.GetEvents">
      <summary>Not supported for incomplete generic type parameters.</summary>
      <returns>Not supported for incomplete generic type parameters.</returns>
      <exception cref="T:System.NotSupportedException">In all cases.</exception>
    </member>
    <member name="M:System.Reflection.Emit.GenericTypeParameterBuilder.GetEvents(System.Reflection.BindingFlags)">
      <summary>Not supported for incomplete generic type parameters.</summary>
      <param name="bindingAttr">Not supported.</param>
      <returns>Not supported for incomplete generic type parameters.</returns>
      <exception cref="T:System.NotSupportedException">In all cases.</exception>
    </member>
    <member name="M:System.Reflection.Emit.GenericTypeParameterBuilder.GetField(System.String,System.Reflection.BindingFlags)">
      <summary>Not supported for incomplete generic type parameters.</summary>
      <param name="name">Not supported.</param>
      <param name="bindingAttr">Not supported.</param>
      <returns>Not supported for incomplete generic type parameters.</returns>
      <exception cref="T:System.NotSupportedException">In all cases.</exception>
    </member>
    <member name="M:System.Reflection.Emit.GenericTypeParameterBuilder.GetFields(System.Reflection.BindingFlags)">
      <summary>Not supported for incomplete generic type parameters.</summary>
      <param name="bindingAttr">Not supported.</param>
      <returns>Not supported for incomplete generic type parameters.</returns>
      <exception cref="T:System.NotSupportedException">In all cases.</exception>
    </member>
    <member name="M:System.Reflection.Emit.GenericTypeParameterBuilder.GetGenericArguments">
      <summary>Not valid for generic type parameters.</summary>
      <returns>Not valid for generic type parameters.</returns>
      <exception cref="T:System.InvalidOperationException">In all cases.</exception>
    </member>
    <member name="M:System.Reflection.Emit.GenericTypeParameterBuilder.GetGenericTypeDefinition">
      <summary>Not valid for generic type parameters.</summary>
      <returns>Not valid for generic type parameters.</returns>
      <exception cref="T:System.InvalidOperationException">In all cases.</exception>
    </member>
    <member name="M:System.Reflection.Emit.GenericTypeParameterBuilder.GetHashCode">
      <summary>Returns a 32-bit integer hash code for the current instance.</summary>
      <returns>A 32-bit integer hash code.</returns>
    </member>
    <member name="M:System.Reflection.Emit.GenericTypeParameterBuilder.GetInterface(System.String,System.Boolean)">
      <summary>Not supported for incomplete generic type parameters.</summary>
      <param name="name">The name of the interface.</param>
      <param name="ignoreCase">
        <see langword="true" /> to search without regard for case; <see langword="false" /> to make a case-sensitive search.</param>
      <returns>Not supported for incomplete generic type parameters.</returns>
      <exception cref="T:System.NotSupportedException">In all cases.</exception>
    </member>
    <member name="M:System.Reflection.Emit.GenericTypeParameterBuilder.GetInterfaceMap(System.Type)">
      <summary>Not supported for incomplete generic type parameters.</summary>
      <param name="interfaceType">A <see cref="T:System.Type" /> object that represents the interface type for which the mapping is to be retrieved.</param>
      <returns>Not supported for incomplete generic type parameters.</returns>
      <exception cref="T:System.NotSupportedException">In all cases.</exception>
    </member>
    <member name="M:System.Reflection.Emit.GenericTypeParameterBuilder.GetInterfaces">
      <summary>Not supported for incomplete generic type parameters.</summary>
      <returns>Not supported for incomplete generic type parameters.</returns>
      <exception cref="T:System.NotSupportedException">In all cases.</exception>
    </member>
    <member name="M:System.Reflection.Emit.GenericTypeParameterBuilder.GetMember(System.String,System.Reflection.MemberTypes,System.Reflection.BindingFlags)">
      <summary>Not supported for incomplete generic type parameters.</summary>
      <param name="name">Not supported.</param>
      <param name="type">Not supported.</param>
      <param name="bindingAttr">Not supported.</param>
      <returns>Not supported for incomplete generic type parameters.</returns>
      <exception cref="T:System.NotSupportedException">In all cases.</exception>
    </member>
    <member name="M:System.Reflection.Emit.GenericTypeParameterBuilder.GetMembers(System.Reflection.BindingFlags)">
      <summary>Not supported for incomplete generic type parameters.</summary>
      <param name="bindingAttr">Not supported.</param>
      <returns>Not supported for incomplete generic type parameters.</returns>
      <exception cref="T:System.NotSupportedException">In all cases.</exception>
    </member>
    <member name="M:System.Reflection.Emit.GenericTypeParameterBuilder.GetMethods(System.Reflection.BindingFlags)">
      <summary>Not supported for incomplete generic type parameters.</summary>
      <param name="bindingAttr">Not supported.</param>
      <returns>Not supported for incomplete generic type parameters.</returns>
      <exception cref="T:System.NotSupportedException">In all cases.</exception>
    </member>
    <member name="M:System.Reflection.Emit.GenericTypeParameterBuilder.GetNestedType(System.String,System.Reflection.BindingFlags)">
      <summary>Not supported for incomplete generic type parameters.</summary>
      <param name="name">Not supported.</param>
      <param name="bindingAttr">Not supported.</param>
      <returns>Not supported for incomplete generic type parameters.</returns>
      <exception cref="T:System.NotSupportedException">In all cases.</exception>
    </member>
    <member name="M:System.Reflection.Emit.GenericTypeParameterBuilder.GetNestedTypes(System.Reflection.BindingFlags)">
      <summary>Not supported for incomplete generic type parameters.</summary>
      <param name="bindingAttr">Not supported.</param>
      <returns>Not supported for incomplete generic type parameters.</returns>
      <exception cref="T:System.NotSupportedException">In all cases.</exception>
    </member>
    <member name="M:System.Reflection.Emit.GenericTypeParameterBuilder.GetProperties(System.Reflection.BindingFlags)">
      <summary>Not supported for incomplete generic type parameters.</summary>
      <param name="bindingAttr">Not supported.</param>
      <returns>Not supported for incomplete generic type parameters.</returns>
      <exception cref="T:System.NotSupportedException">In all cases.</exception>
    </member>
    <member name="P:System.Reflection.Emit.GenericTypeParameterBuilder.GUID">
      <summary>Not supported for incomplete generic type parameters.</summary>
      <returns>Not supported for incomplete generic type parameters.</returns>
      <exception cref="T:System.NotSupportedException">In all cases.</exception>
    </member>
    <member name="M:System.Reflection.Emit.GenericTypeParameterBuilder.InvokeMember(System.String,System.Reflection.BindingFlags,System.Reflection.Binder,System.Object,System.Object[],System.Reflection.ParameterModifier[],System.Globalization.CultureInfo,System.String[])">
      <summary>Not supported for incomplete generic type parameters.</summary>
      <param name="name">Not supported.</param>
      <param name="invokeAttr">Not supported.</param>
      <param name="binder">Not supported.</param>
      <param name="target">Not supported.</param>
      <param name="args">Not supported.</param>
      <param name="modifiers">Not supported.</param>
      <param name="culture">Not supported.</param>
      <param name="namedParameters">Not supported.</param>
      <returns>Not supported for incomplete generic type parameters.</returns>
      <exception cref="T:System.NotSupportedException">In all cases.</exception>
    </member>
    <member name="M:System.Reflection.Emit.GenericTypeParameterBuilder.IsAssignableFrom(System.Type)">
      <summary>Throws a <see cref="T:System.NotSupportedException" /> exception in all cases.</summary>
      <param name="c">The object to test.</param>
      <returns>Throws a <see cref="T:System.NotSupportedException" /> exception in all cases.</returns>
      <exception cref="T:System.NotSupportedException">In all cases.</exception>
    </member>
    <member name="P:System.Reflection.Emit.GenericTypeParameterBuilder.IsByRefLike" />
    <member name="P:System.Reflection.Emit.GenericTypeParameterBuilder.IsConstructedGenericType">
      <summary>Gets a value that indicates whether this object represents a constructed generic type.</summary>
      <returns>
        <see langword="true" /> if this object represents a constructed generic type; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Reflection.Emit.GenericTypeParameterBuilder.IsDefined(System.Type,System.Boolean)">
      <summary>Not supported for incomplete generic type parameters.</summary>
      <param name="attributeType">Not supported.</param>
      <param name="inherit">Not supported.</param>
      <returns>Not supported for incomplete generic type parameters.</returns>
      <exception cref="T:System.NotSupportedException">In all cases.</exception>
    </member>
    <member name="P:System.Reflection.Emit.GenericTypeParameterBuilder.IsGenericParameter">
      <summary>Gets <see langword="true" /> in all cases.</summary>
      <returns>
        <see langword="true" /> in all cases.</returns>
    </member>
    <member name="P:System.Reflection.Emit.GenericTypeParameterBuilder.IsGenericType">
      <summary>Returns <see langword="false" /> in all cases.</summary>
      <returns>
        <see langword="false" /> in all cases.</returns>
    </member>
    <member name="P:System.Reflection.Emit.GenericTypeParameterBuilder.IsGenericTypeDefinition">
      <summary>Gets <see langword="false" /> in all cases.</summary>
      <returns>
        <see langword="false" /> in all cases.</returns>
    </member>
    <member name="M:System.Reflection.Emit.GenericTypeParameterBuilder.IsSubclassOf(System.Type)">
      <summary>Not supported for incomplete generic type parameters.</summary>
      <param name="c">Not supported.</param>
      <returns>Not supported for incomplete generic type parameters.</returns>
      <exception cref="T:System.NotSupportedException">In all cases.</exception>
    </member>
    <member name="P:System.Reflection.Emit.GenericTypeParameterBuilder.IsSZArray" />
    <member name="P:System.Reflection.Emit.GenericTypeParameterBuilder.IsTypeDefinition" />
    <member name="P:System.Reflection.Emit.GenericTypeParameterBuilder.IsVariableBoundArray" />
    <member name="M:System.Reflection.Emit.GenericTypeParameterBuilder.MakeArrayType">
      <summary>Returns the type of a one-dimensional array whose element type is the generic type parameter.</summary>
      <returns>A <see cref="T:System.Type" /> object that represents the type of a one-dimensional array whose element type is the generic type parameter.</returns>
    </member>
    <member name="M:System.Reflection.Emit.GenericTypeParameterBuilder.MakeArrayType(System.Int32)">
      <summary>Returns the type of an array whose element type is the generic type parameter, with the specified number of dimensions.</summary>
      <param name="rank">The number of dimensions for the array.</param>
      <returns>A <see cref="T:System.Type" /> object that represents the type of an array whose element type is the generic type parameter, with the specified number of dimensions.</returns>
      <exception cref="T:System.IndexOutOfRangeException">
        <paramref name="rank" /> is not a valid number of dimensions. For example, its value is less than 1.</exception>
    </member>
    <member name="M:System.Reflection.Emit.GenericTypeParameterBuilder.MakeByRefType">
      <summary>Returns a <see cref="T:System.Type" /> object that represents the current generic type parameter when passed as a reference parameter.</summary>
      <returns>A <see cref="T:System.Type" /> object that represents the current generic type parameter when passed as a reference parameter.</returns>
    </member>
    <member name="M:System.Reflection.Emit.GenericTypeParameterBuilder.MakeGenericType(System.Type[])">
      <summary>Not valid for incomplete generic type parameters.</summary>
      <param name="typeArguments">An array of type arguments.</param>
      <returns>This method is invalid for incomplete generic type parameters.</returns>
      <exception cref="T:System.InvalidOperationException">In all cases.</exception>
    </member>
    <member name="M:System.Reflection.Emit.GenericTypeParameterBuilder.MakePointerType">
      <summary>Returns a <see cref="T:System.Type" /> object that represents a pointer to the current generic type parameter.</summary>
      <returns>A <see cref="T:System.Type" /> object that represents a pointer to the current generic type parameter.</returns>
    </member>
    <member name="P:System.Reflection.Emit.GenericTypeParameterBuilder.Module">
      <summary>Gets the dynamic module that contains the generic type parameter.</summary>
      <returns>A <see cref="T:System.Reflection.Module" /> object that represents the dynamic module that contains the generic type parameter.</returns>
    </member>
    <member name="P:System.Reflection.Emit.GenericTypeParameterBuilder.Name">
      <summary>Gets the name of the generic type parameter.</summary>
      <returns>The name of the generic type parameter.</returns>
    </member>
    <member name="P:System.Reflection.Emit.GenericTypeParameterBuilder.Namespace">
      <summary>Gets <see langword="null" /> in all cases.</summary>
      <returns>A null reference (<see langword="Nothing" /> in Visual Basic) in all cases.</returns>
    </member>
    <member name="P:System.Reflection.Emit.GenericTypeParameterBuilder.ReflectedType">
      <summary>Gets the <see cref="T:System.Type" /> object that was used to obtain the <see cref="T:System.Reflection.Emit.GenericTypeParameterBuilder" />.</summary>
      <returns>The <see cref="T:System.Type" /> object that was used to obtain the <see cref="T:System.Reflection.Emit.GenericTypeParameterBuilder" />.</returns>
    </member>
    <member name="M:System.Reflection.Emit.GenericTypeParameterBuilder.SetBaseTypeConstraint(System.Type)">
      <summary>Sets the base type that a type must inherit in order to be substituted for the type parameter.</summary>
      <param name="baseTypeConstraint">The <see cref="T:System.Type" /> that must be inherited by any type that is to be substituted for the type parameter.</param>
    </member>
    <member name="M:System.Reflection.Emit.GenericTypeParameterBuilder.SetCustomAttribute(System.Reflection.ConstructorInfo,System.Byte[])">
      <summary>Sets a custom attribute using a specified custom attribute blob.</summary>
      <param name="con">The constructor for the custom attribute.</param>
      <param name="binaryAttribute">A byte blob representing the attribute.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="con" /> is <see langword="null" />.
-or-
<paramref name="binaryAttribute" /> is a null reference.</exception>
    </member>
    <member name="M:System.Reflection.Emit.GenericTypeParameterBuilder.SetCustomAttribute(System.Reflection.Emit.CustomAttributeBuilder)">
      <summary>Set a custom attribute using a custom attribute builder.</summary>
      <param name="customBuilder">An instance of a helper class that defines the custom attribute.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="customBuilder" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Reflection.Emit.GenericTypeParameterBuilder.SetGenericParameterAttributes(System.Reflection.GenericParameterAttributes)">
      <summary>Sets the variance characteristics and special constraints of the generic parameter, such as the parameterless constructor constraint.</summary>
      <param name="genericParameterAttributes">A bitwise combination of <see cref="T:System.Reflection.GenericParameterAttributes" /> values that represent the variance characteristics and special constraints of the generic type parameter.</param>
    </member>
    <member name="M:System.Reflection.Emit.GenericTypeParameterBuilder.SetInterfaceConstraints(System.Type[])">
      <summary>Sets the interfaces a type must implement in order to be substituted for the type parameter.</summary>
      <param name="interfaceConstraints">An array of <see cref="T:System.Type" /> objects that represent the interfaces a type must implement in order to be substituted for the type parameter.</param>
    </member>
    <member name="M:System.Reflection.Emit.GenericTypeParameterBuilder.ToString">
      <summary>Returns a string representation of the current generic type parameter.</summary>
      <returns>A string that contains the name of the generic type parameter.</returns>
    </member>
    <member name="P:System.Reflection.Emit.GenericTypeParameterBuilder.TypeHandle">
      <summary>Not supported for incomplete generic type parameters.</summary>
      <returns>Not supported for incomplete generic type parameters.</returns>
      <exception cref="T:System.NotSupportedException">In all cases.</exception>
    </member>
    <member name="P:System.Reflection.Emit.GenericTypeParameterBuilder.UnderlyingSystemType">
      <summary>Gets the current generic type parameter.</summary>
      <returns>The current <see cref="T:System.Reflection.Emit.GenericTypeParameterBuilder" /> object.</returns>
    </member>
    <member name="T:System.Reflection.Emit.MethodBuilder">
      <summary>Defines and represents a method (or constructor) on a dynamic class.</summary>
    </member>
    <member name="P:System.Reflection.Emit.MethodBuilder.Attributes">
      <summary>Retrieves the attributes for this method.</summary>
      <returns>Read-only. Retrieves the <see langword="MethodAttributes" /> for this method.</returns>
    </member>
    <member name="P:System.Reflection.Emit.MethodBuilder.CallingConvention">
      <summary>Returns the calling convention of the method.</summary>
      <returns>Read-only. The calling convention of the method.</returns>
    </member>
    <member name="P:System.Reflection.Emit.MethodBuilder.ContainsGenericParameters">
      <summary>Not supported for this type.</summary>
      <returns>Not supported.</returns>
      <exception cref="T:System.NotSupportedException">The invoked method is not supported in the base class.</exception>
    </member>
    <member name="P:System.Reflection.Emit.MethodBuilder.DeclaringType">
      <summary>Returns the type that declares this method.</summary>
      <returns>Read-only. The type that declares this method.</returns>
    </member>
    <member name="M:System.Reflection.Emit.MethodBuilder.DefineGenericParameters(System.String[])">
      <summary>Sets the number of generic type parameters for the current method, specifies their names, and returns an array of <see cref="T:System.Reflection.Emit.GenericTypeParameterBuilder" /> objects that can be used to define their constraints.</summary>
      <param name="names">An array of strings that represent the names of the generic type parameters.</param>
      <returns>An array of <see cref="T:System.Reflection.Emit.GenericTypeParameterBuilder" /> objects representing the type parameters of the generic method.</returns>
      <exception cref="T:System.InvalidOperationException">Generic type parameters have already been defined for this method.
-or-
The method has been completed already.
-or-
The <see cref="M:System.Reflection.Emit.MethodBuilder.SetImplementationFlags(System.Reflection.MethodImplAttributes)" /> method has been called for the current method.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="names" /> is <see langword="null" />.
-or-
An element of <paramref name="names" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="names" /> is an empty array.</exception>
    </member>
    <member name="M:System.Reflection.Emit.MethodBuilder.DefineParameter(System.Int32,System.Reflection.ParameterAttributes,System.String)">
      <summary>Sets the parameter attributes and the name of a parameter of this method, or of the return value of this method. Returns a ParameterBuilder that can be used to apply custom attributes.</summary>
      <param name="position">The position of the parameter in the parameter list. Parameters are indexed beginning with the number 1 for the first parameter; the number 0 represents the return value of the method.</param>
      <param name="attributes">The parameter attributes of the parameter.</param>
      <param name="strParamName">The name of the parameter. The name can be the null string.</param>
      <returns>Returns a <see langword="ParameterBuilder" /> object that represents a parameter of this method or the return value of this method.</returns>
      <exception cref="T:System.ArgumentOutOfRangeException">The method has no parameters.
-or-
<paramref name="position" /> is less than zero.
-or-
<paramref name="position" /> is greater than the number of the method's parameters.</exception>
      <exception cref="T:System.InvalidOperationException">The containing type was previously created using <see cref="M:System.Reflection.Emit.TypeBuilder.CreateType" />.
-or-
For the current method, the <see cref="P:System.Reflection.Emit.MethodBuilder.IsGenericMethod" /> property is <see langword="true" />, but the <see cref="P:System.Reflection.Emit.MethodBuilder.IsGenericMethodDefinition" /> property is <see langword="false" />.</exception>
    </member>
    <member name="M:System.Reflection.Emit.MethodBuilder.Equals(System.Object)">
      <summary>Determines whether the given object is equal to this instance.</summary>
      <param name="obj">The object to compare with this <see langword="MethodBuilder" /> instance.</param>
      <returns>
        <see langword="true" /> if <paramref name="obj" /> is an instance of <see langword="MethodBuilder" /> and is equal to this object; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Reflection.Emit.MethodBuilder.GetBaseDefinition">
      <summary>Return the base implementation for a method.</summary>
      <returns>The base implementation of this method.</returns>
    </member>
    <member name="M:System.Reflection.Emit.MethodBuilder.GetCustomAttributes(System.Boolean)">
      <summary>Returns all the custom attributes defined for this method.</summary>
      <param name="inherit">Specifies whether to search this member's inheritance chain to find the custom attributes.</param>
      <returns>Returns an array of objects representing all the custom attributes of this method.</returns>
      <exception cref="T:System.NotSupportedException">This method is not currently supported. Retrieve the method using <see cref="M:System.Type.GetMethod(System.String,System.Reflection.BindingFlags,System.Reflection.Binder,System.Reflection.CallingConventions,System.Type[],System.Reflection.ParameterModifier[])" /> and call <see cref="M:System.Reflection.MemberInfo.GetCustomAttributes(System.Boolean)" /> on the returned <see cref="T:System.Reflection.MethodInfo" />.</exception>
    </member>
    <member name="M:System.Reflection.Emit.MethodBuilder.GetCustomAttributes(System.Type,System.Boolean)">
      <summary>Returns the custom attributes identified by the given type.</summary>
      <param name="attributeType">The custom attribute type.</param>
      <param name="inherit">Specifies whether to search this member's inheritance chain to find the custom attributes.</param>
      <returns>Returns an array of objects representing the attributes of this method that are of type <paramref name="attributeType" />.</returns>
      <exception cref="T:System.NotSupportedException">This method is not currently supported. Retrieve the method using <see cref="M:System.Type.GetMethod(System.String,System.Reflection.BindingFlags,System.Reflection.Binder,System.Reflection.CallingConventions,System.Type[],System.Reflection.ParameterModifier[])" /> and call <see cref="M:System.Reflection.MemberInfo.GetCustomAttributes(System.Boolean)" /> on the returned <see cref="T:System.Reflection.MethodInfo" />.</exception>
    </member>
    <member name="M:System.Reflection.Emit.MethodBuilder.GetGenericArguments">
      <summary>Returns an array of <see cref="T:System.Reflection.Emit.GenericTypeParameterBuilder" /> objects that represent the type parameters of the method, if it is generic.</summary>
      <returns>An array of <see cref="T:System.Reflection.Emit.GenericTypeParameterBuilder" /> objects representing the type parameters, if the method is generic, or <see langword="null" /> if the method is not generic.</returns>
    </member>
    <member name="M:System.Reflection.Emit.MethodBuilder.GetGenericMethodDefinition">
      <summary>Returns this method.</summary>
      <returns>The current instance of <see cref="T:System.Reflection.Emit.MethodBuilder" />.</returns>
      <exception cref="T:System.InvalidOperationException">The current method is not generic. That is, the <see cref="P:System.Reflection.Emit.MethodBuilder.IsGenericMethod" /> property returns <see langword="false" />.</exception>
    </member>
    <member name="M:System.Reflection.Emit.MethodBuilder.GetHashCode">
      <summary>Gets the hash code for this method.</summary>
      <returns>The hash code for this method.</returns>
    </member>
    <member name="M:System.Reflection.Emit.MethodBuilder.GetILGenerator">
      <summary>Returns an <see langword="ILGenerator" /> for this method with a default Microsoft intermediate language (MSIL) stream size of 64 bytes.</summary>
      <returns>Returns an <see langword="ILGenerator" /> object for this method.</returns>
      <exception cref="T:System.InvalidOperationException">The method should not have a body because of its <see cref="T:System.Reflection.MethodAttributes" /> or <see cref="T:System.Reflection.MethodImplAttributes" /> flags, for example because it has the <see cref="F:System.Reflection.MethodAttributes.PinvokeImpl" /> flag.
-or-
The method is a generic method, but not a generic method definition. That is, the <see cref="P:System.Reflection.Emit.MethodBuilder.IsGenericMethod" /> property is <see langword="true" />, but the <see cref="P:System.Reflection.Emit.MethodBuilder.IsGenericMethodDefinition" /> property is <see langword="false" />.</exception>
    </member>
    <member name="M:System.Reflection.Emit.MethodBuilder.GetILGenerator(System.Int32)">
      <summary>Returns an <see langword="ILGenerator" /> for this method with the specified Microsoft intermediate language (MSIL) stream size.</summary>
      <param name="size">The size of the MSIL stream, in bytes.</param>
      <returns>Returns an <see langword="ILGenerator" /> object for this method.</returns>
      <exception cref="T:System.InvalidOperationException">The method should not have a body because of its <see cref="T:System.Reflection.MethodAttributes" /> or <see cref="T:System.Reflection.MethodImplAttributes" /> flags, for example because it has the <see cref="F:System.Reflection.MethodAttributes.PinvokeImpl" /> flag.
-or-
The method is a generic method, but not a generic method definition. That is, the <see cref="P:System.Reflection.Emit.MethodBuilder.IsGenericMethod" /> property is <see langword="true" />, but the <see cref="P:System.Reflection.Emit.MethodBuilder.IsGenericMethodDefinition" /> property is <see langword="false" />.</exception>
    </member>
    <member name="M:System.Reflection.Emit.MethodBuilder.GetMethodImplementationFlags">
      <summary>Returns the implementation flags for the method.</summary>
      <returns>Returns the implementation flags for the method.</returns>
    </member>
    <member name="M:System.Reflection.Emit.MethodBuilder.GetParameters">
      <summary>Returns the parameters of this method.</summary>
      <returns>An array of <see langword="ParameterInfo" /> objects that represent the parameters of the method.</returns>
      <exception cref="T:System.NotSupportedException">This method is not currently supported. Retrieve the method using <see cref="M:System.Type.GetMethod(System.String,System.Reflection.BindingFlags,System.Reflection.Binder,System.Reflection.CallingConventions,System.Type[],System.Reflection.ParameterModifier[])" /> and call <see langword="GetParameters" /> on the returned <see cref="T:System.Reflection.MethodInfo" />.</exception>
    </member>
    <member name="P:System.Reflection.Emit.MethodBuilder.InitLocals">
      <summary>Gets or sets a Boolean value that specifies whether the local variables in this method are zero initialized. The default value of this property is <see langword="true" />.</summary>
      <returns>
        <see langword="true" /> if the local variables in this method should be zero initialized; otherwise <see langword="false" />.</returns>
      <exception cref="T:System.InvalidOperationException">For the current method, the <see cref="P:System.Reflection.Emit.MethodBuilder.IsGenericMethod" /> property is <see langword="true" />, but the <see cref="P:System.Reflection.Emit.MethodBuilder.IsGenericMethodDefinition" /> property is <see langword="false" />. (Get or set.)</exception>
    </member>
    <member name="M:System.Reflection.Emit.MethodBuilder.Invoke(System.Object,System.Reflection.BindingFlags,System.Reflection.Binder,System.Object[],System.Globalization.CultureInfo)">
      <summary>Dynamically invokes the method reflected by this instance on the given object, passing along the specified parameters, and under the constraints of the given binder.</summary>
      <param name="obj">The object on which to invoke the specified method. If the method is static, this parameter is ignored.</param>
      <param name="invokeAttr">This must be a bit flag from <see cref="T:System.Reflection.BindingFlags" /> : <see langword="InvokeMethod" />, <see langword="NonPublic" />, and so on.</param>
      <param name="binder">An object that enables the binding, coercion of argument types, invocation of members, and retrieval of MemberInfo objects via reflection. If binder is <see langword="null" />, the default binder is used. For more details, see <see cref="T:System.Reflection.Binder" />.</param>
      <param name="parameters">An argument list. This is an array of arguments with the same number, order, and type as the parameters of the method to be invoked. If there are no parameters this should be <see langword="null" />.</param>
      <param name="culture">An instance of <see cref="T:System.Globalization.CultureInfo" /> used to govern the coercion of types. If this is null, the <see cref="T:System.Globalization.CultureInfo" /> for the current thread is used. (Note that this is necessary to, for example, convert a <see cref="T:System.String" /> that represents 1000 to a <see cref="T:System.Double" /> value, since 1000 is represented differently by different cultures.)</param>
      <returns>Returns an object containing the return value of the invoked method.</returns>
      <exception cref="T:System.NotSupportedException">This method is not currently supported. Retrieve the method using <see cref="M:System.Type.GetMethod(System.String,System.Reflection.BindingFlags,System.Reflection.Binder,System.Reflection.CallingConventions,System.Type[],System.Reflection.ParameterModifier[])" /> and call <see cref="M:System.Type.InvokeMember(System.String,System.Reflection.BindingFlags,System.Reflection.Binder,System.Object,System.Object[],System.Reflection.ParameterModifier[],System.Globalization.CultureInfo,System.String[])" /> on the returned <see cref="T:System.Reflection.MethodInfo" />.</exception>
    </member>
    <member name="P:System.Reflection.Emit.MethodBuilder.IsConstructedGenericMethod" />
    <member name="M:System.Reflection.Emit.MethodBuilder.IsDefined(System.Type,System.Boolean)">
      <summary>Checks if the specified custom attribute type is defined.</summary>
      <param name="attributeType">The custom attribute type.</param>
      <param name="inherit">Specifies whether to search this member's inheritance chain to find the custom attributes.</param>
      <returns>
        <see langword="true" /> if the specified custom attribute type is defined; otherwise, <see langword="false" />.</returns>
      <exception cref="T:System.NotSupportedException">This method is not currently supported. Retrieve the method using <see cref="M:System.Type.GetMethod(System.String,System.Reflection.BindingFlags,System.Reflection.Binder,System.Reflection.CallingConventions,System.Type[],System.Reflection.ParameterModifier[])" /> and call <see cref="M:System.Reflection.MemberInfo.IsDefined(System.Type,System.Boolean)" /> on the returned <see cref="T:System.Reflection.MethodInfo" />.</exception>
    </member>
    <member name="P:System.Reflection.Emit.MethodBuilder.IsGenericMethod">
      <summary>Gets a value indicating whether the method is a generic method.</summary>
      <returns>
        <see langword="true" /> if the method is generic; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Reflection.Emit.MethodBuilder.IsGenericMethodDefinition">
      <summary>Gets a value indicating whether the current <see cref="T:System.Reflection.Emit.MethodBuilder" /> object represents the definition of a generic method.</summary>
      <returns>
        <see langword="true" /> if the current <see cref="T:System.Reflection.Emit.MethodBuilder" /> object represents the definition of a generic method; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Reflection.Emit.MethodBuilder.IsSecurityCritical">
      <summary>Throws a <see cref="T:System.NotSupportedException" /> in all cases.</summary>
      <returns>Throws a <see cref="T:System.NotSupportedException" /> in all cases.</returns>
      <exception cref="T:System.NotSupportedException">In all cases. This property is not supported in dynamic assemblies.</exception>
    </member>
    <member name="P:System.Reflection.Emit.MethodBuilder.IsSecuritySafeCritical">
      <summary>Throws a <see cref="T:System.NotSupportedException" /> in all cases.</summary>
      <returns>Throws a <see cref="T:System.NotSupportedException" /> in all cases.</returns>
      <exception cref="T:System.NotSupportedException">In all cases. This property is not supported in dynamic assemblies.</exception>
    </member>
    <member name="P:System.Reflection.Emit.MethodBuilder.IsSecurityTransparent">
      <summary>Throws a <see cref="T:System.NotSupportedException" /> in all cases.</summary>
      <returns>Throws a <see cref="T:System.NotSupportedException" /> in all cases.</returns>
      <exception cref="T:System.NotSupportedException">In all cases. This property is not supported in dynamic assemblies.</exception>
    </member>
    <member name="M:System.Reflection.Emit.MethodBuilder.MakeGenericMethod(System.Type[])">
      <summary>Returns a generic method constructed from the current generic method definition using the specified generic type arguments.</summary>
      <param name="typeArguments">An array of <see cref="T:System.Type" /> objects that represent the type arguments for the generic method.</param>
      <returns>A <see cref="T:System.Reflection.MethodInfo" /> representing the generic method constructed from the current generic method definition using the specified generic type arguments.</returns>
    </member>
    <member name="P:System.Reflection.Emit.MethodBuilder.MethodHandle">
      <summary>Retrieves the internal handle for the method. Use this handle to access the underlying metadata handle.</summary>
      <returns>Read-only. The internal handle for the method. Use this handle to access the underlying metadata handle.</returns>
      <exception cref="T:System.NotSupportedException">This method is not currently supported. Retrieve the method using <see cref="M:System.Type.GetMethod(System.String,System.Reflection.BindingFlags,System.Reflection.Binder,System.Reflection.CallingConventions,System.Type[],System.Reflection.ParameterModifier[])" /> and call <see cref="P:System.Reflection.MethodBase.MethodHandle" /> on the returned <see cref="T:System.Reflection.MethodInfo" />.</exception>
    </member>
    <member name="P:System.Reflection.Emit.MethodBuilder.Module">
      <summary>Gets the module in which the current method is being defined.</summary>
      <returns>The <see cref="T:System.Reflection.Module" /> in which the member represented by the current <see cref="T:System.Reflection.MemberInfo" /> is being defined.</returns>
    </member>
    <member name="P:System.Reflection.Emit.MethodBuilder.Name">
      <summary>Retrieves the name of this method.</summary>
      <returns>Read-only. Retrieves a string containing the simple name of this method.</returns>
    </member>
    <member name="P:System.Reflection.Emit.MethodBuilder.ReflectedType">
      <summary>Retrieves the class that was used in reflection to obtain this object.</summary>
      <returns>Read-only. The type used to obtain this method.</returns>
    </member>
    <member name="P:System.Reflection.Emit.MethodBuilder.ReturnParameter">
      <summary>Gets a <see cref="T:System.Reflection.ParameterInfo" /> object that contains information about the return type of the method, such as whether the return type has custom modifiers.</summary>
      <returns>A <see cref="T:System.Reflection.ParameterInfo" /> object that contains information about the return type.</returns>
      <exception cref="T:System.InvalidOperationException">The declaring type has not been created.</exception>
    </member>
    <member name="P:System.Reflection.Emit.MethodBuilder.ReturnType">
      <summary>Gets the return type of the method represented by this <see cref="T:System.Reflection.Emit.MethodBuilder" />.</summary>
      <returns>The return type of the method.</returns>
    </member>
    <member name="P:System.Reflection.Emit.MethodBuilder.ReturnTypeCustomAttributes">
      <summary>Returns the custom attributes of the method's return type.</summary>
      <returns>Read-only. The custom attributes of the method's return type.</returns>
    </member>
    <member name="M:System.Reflection.Emit.MethodBuilder.SetCustomAttribute(System.Reflection.ConstructorInfo,System.Byte[])">
      <summary>Sets a custom attribute using a specified custom attribute blob.</summary>
      <param name="con">The constructor for the custom attribute.</param>
      <param name="binaryAttribute">A byte blob representing the attributes.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="con" /> or <paramref name="binaryAttribute" /> is <see langword="null" />.</exception>
      <exception cref="T:System.InvalidOperationException">For the current method, the <see cref="P:System.Reflection.Emit.MethodBuilder.IsGenericMethod" /> property is <see langword="true" />, but the <see cref="P:System.Reflection.Emit.MethodBuilder.IsGenericMethodDefinition" /> property is <see langword="false" />.</exception>
    </member>
    <member name="M:System.Reflection.Emit.MethodBuilder.SetCustomAttribute(System.Reflection.Emit.CustomAttributeBuilder)">
      <summary>Sets a custom attribute using a custom attribute builder.</summary>
      <param name="customBuilder">An instance of a helper class to describe the custom attribute.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="customBuilder" /> is <see langword="null" />.</exception>
      <exception cref="T:System.InvalidOperationException">For the current method, the <see cref="P:System.Reflection.Emit.MethodBuilder.IsGenericMethod" /> property is <see langword="true" />, but the <see cref="P:System.Reflection.Emit.MethodBuilder.IsGenericMethodDefinition" /> property is <see langword="false" />.</exception>
    </member>
    <member name="M:System.Reflection.Emit.MethodBuilder.SetImplementationFlags(System.Reflection.MethodImplAttributes)">
      <summary>Sets the implementation flags for this method.</summary>
      <param name="attributes">The implementation flags to set.</param>
      <exception cref="T:System.InvalidOperationException">The containing type was previously created using <see cref="M:System.Reflection.Emit.TypeBuilder.CreateType" />.
-or-
For the current method, the <see cref="P:System.Reflection.Emit.MethodBuilder.IsGenericMethod" /> property is <see langword="true" />, but the <see cref="P:System.Reflection.Emit.MethodBuilder.IsGenericMethodDefinition" /> property is <see langword="false" />.</exception>
    </member>
    <member name="M:System.Reflection.Emit.MethodBuilder.SetParameters(System.Type[])">
      <summary>Sets the number and types of parameters for a method.</summary>
      <param name="parameterTypes">An array of <see cref="T:System.Type" /> objects representing the parameter types.</param>
      <exception cref="T:System.InvalidOperationException">The current method is generic, but is not a generic method definition. That is, the <see cref="P:System.Reflection.Emit.MethodBuilder.IsGenericMethod" /> property is <see langword="true" />, but the <see cref="P:System.Reflection.Emit.MethodBuilder.IsGenericMethodDefinition" /> property is <see langword="false" />.</exception>
    </member>
    <member name="M:System.Reflection.Emit.MethodBuilder.SetReturnType(System.Type)">
      <summary>Sets the return type of the method.</summary>
      <param name="returnType">A <see cref="T:System.Type" /> object that represents the return type of the method.</param>
      <exception cref="T:System.InvalidOperationException">The current method is generic, but is not a generic method definition. That is, the <see cref="P:System.Reflection.Emit.MethodBuilder.IsGenericMethod" /> property is <see langword="true" />, but the <see cref="P:System.Reflection.Emit.MethodBuilder.IsGenericMethodDefinition" /> property is <see langword="false" />.</exception>
    </member>
    <member name="M:System.Reflection.Emit.MethodBuilder.SetSignature(System.Type,System.Type[],System.Type[],System.Type[],System.Type[][],System.Type[][])">
      <summary>Sets the method signature, including the return type, the parameter types, and the required and optional custom modifiers of the return type and parameter types.</summary>
      <param name="returnType">The return type of the method.</param>
      <param name="returnTypeRequiredCustomModifiers">An array of types representing the required custom modifiers, such as <see cref="T:System.Runtime.CompilerServices.IsConst" />, for the return type of the method. If the return type has no required custom modifiers, specify <see langword="null" />.</param>
      <param name="returnTypeOptionalCustomModifiers">An array of types representing the optional custom modifiers, such as <see cref="T:System.Runtime.CompilerServices.IsConst" />, for the return type of the method. If the return type has no optional custom modifiers, specify <see langword="null" />.</param>
      <param name="parameterTypes">The types of the parameters of the method.</param>
      <param name="parameterTypeRequiredCustomModifiers">An array of arrays of types. Each array of types represents the required custom modifiers for the corresponding parameter, such as <see cref="T:System.Runtime.CompilerServices.IsConst" />. If a particular parameter has no required custom modifiers, specify <see langword="null" /> instead of an array of types. If none of the parameters have required custom modifiers, specify <see langword="null" /> instead of an array of arrays.</param>
      <param name="parameterTypeOptionalCustomModifiers">An array of arrays of types. Each array of types represents the optional custom modifiers for the corresponding parameter, such as <see cref="T:System.Runtime.CompilerServices.IsConst" />. If a particular parameter has no optional custom modifiers, specify <see langword="null" /> instead of an array of types. If none of the parameters have optional custom modifiers, specify <see langword="null" /> instead of an array of arrays.</param>
      <exception cref="T:System.InvalidOperationException">The current method is generic, but is not a generic method definition. That is, the <see cref="P:System.Reflection.Emit.MethodBuilder.IsGenericMethod" /> property is <see langword="true" />, but the <see cref="P:System.Reflection.Emit.MethodBuilder.IsGenericMethodDefinition" /> property is <see langword="false" />.</exception>
    </member>
    <member name="M:System.Reflection.Emit.MethodBuilder.ToString">
      <summary>Returns this <see langword="MethodBuilder" /> instance as a string.</summary>
      <returns>Returns a string containing the name, attributes, method signature, exceptions, and local signature of this method followed by the current Microsoft intermediate language (MSIL) stream.</returns>
    </member>
    <member name="T:System.Reflection.Emit.ModuleBuilder">
      <summary>Defines and represents a module in a dynamic assembly.</summary>
    </member>
    <member name="P:System.Reflection.Emit.ModuleBuilder.Assembly">
      <summary>Gets the dynamic assembly that defined this instance of <see cref="T:System.Reflection.Emit.ModuleBuilder" />.</summary>
      <returns>The dynamic assembly that defined the current dynamic module.</returns>
    </member>
    <member name="M:System.Reflection.Emit.ModuleBuilder.CreateGlobalFunctions">
      <summary>Completes the global function definitions and global data definitions for this dynamic module.</summary>
      <exception cref="T:System.InvalidOperationException">This method was called previously.</exception>
    </member>
    <member name="M:System.Reflection.Emit.ModuleBuilder.DefineEnum(System.String,System.Reflection.TypeAttributes,System.Type)">
      <summary>Defines an enumeration type that is a value type with a single non-static field called <paramref name="value__" /> of the specified type.</summary>
      <param name="name">The full path of the enumeration type. <paramref name="name" /> cannot contain embedded nulls.</param>
      <param name="visibility">The type attributes for the enumeration. The attributes are any bits defined by <see cref="F:System.Reflection.TypeAttributes.VisibilityMask" />.</param>
      <param name="underlyingType">The underlying type for the enumeration. This must be a built-in integer type.</param>
      <returns>The defined enumeration.</returns>
      <exception cref="T:System.ArgumentException">Attributes other than visibility attributes are provided.
-or-
An enumeration with the given name exists in the parent assembly of this module.
-or-
The visibility attributes do not match the scope of the enumeration. For example, <see cref="F:System.Reflection.TypeAttributes.NestedPublic" /> is specified for <paramref name="visibility" />, but the enumeration is not a nested type.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="name" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Reflection.Emit.ModuleBuilder.DefineGlobalMethod(System.String,System.Reflection.MethodAttributes,System.Reflection.CallingConventions,System.Type,System.Type[])">
      <summary>Defines a global method with the specified name, attributes, calling convention, return type, and parameter types.</summary>
      <param name="name">The name of the method. <paramref name="name" /> cannot contain embedded nulls.</param>
      <param name="attributes">The attributes of the method. <paramref name="attributes" /> must include <see cref="F:System.Reflection.MethodAttributes.Static" />.</param>
      <param name="callingConvention">The calling convention for the method.</param>
      <param name="returnType">The return type of the method.</param>
      <param name="parameterTypes">The types of the method's parameters.</param>
      <returns>The defined global method.</returns>
      <exception cref="T:System.ArgumentException">The method is not static. That is, <paramref name="attributes" /> does not include <see cref="F:System.Reflection.MethodAttributes.Static" />.
-or-
An element in the <see cref="T:System.Type" /> array is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="name" /> is <see langword="null" />.</exception>
      <exception cref="T:System.InvalidOperationException">
        <see cref="M:System.Reflection.Emit.ModuleBuilder.CreateGlobalFunctions" /> has been previously called.</exception>
    </member>
    <member name="M:System.Reflection.Emit.ModuleBuilder.DefineGlobalMethod(System.String,System.Reflection.MethodAttributes,System.Reflection.CallingConventions,System.Type,System.Type[],System.Type[],System.Type[],System.Type[][],System.Type[][])">
      <summary>Defines a global method with the specified name, attributes, calling convention, return type, custom modifiers for the return type, parameter types, and custom modifiers for the parameter types.</summary>
      <param name="name">The name of the method. <paramref name="name" /> cannot contain embedded null characters.</param>
      <param name="attributes">The attributes of the method. <paramref name="attributes" /> must include <see cref="F:System.Reflection.MethodAttributes.Static" />.</param>
      <param name="callingConvention">The calling convention for the method.</param>
      <param name="returnType">The return type of the method.</param>
      <param name="requiredReturnTypeCustomModifiers">An array of types representing the required custom modifiers for the return type, such as <see cref="T:System.Runtime.CompilerServices.IsConst" /> or <see cref="T:System.Runtime.CompilerServices.IsBoxed" />. If the return type has no required custom modifiers, specify <see langword="null" />.</param>
      <param name="optionalReturnTypeCustomModifiers">An array of types representing the optional custom modifiers for the return type, such as <see cref="T:System.Runtime.CompilerServices.IsConst" /> or <see cref="T:System.Runtime.CompilerServices.IsBoxed" />. If the return type has no optional custom modifiers, specify <see langword="null" />.</param>
      <param name="parameterTypes">The types of the method's parameters.</param>
      <param name="requiredParameterTypeCustomModifiers">An array of arrays of types. Each array of types represents the required custom modifiers for the corresponding parameter of the global method. If a particular argument has no required custom modifiers, specify <see langword="null" /> instead of an array of types. If the global method has no arguments, or if none of the arguments have required custom modifiers, specify <see langword="null" /> instead of an array of arrays.</param>
      <param name="optionalParameterTypeCustomModifiers">An array of arrays of types. Each array of types represents the optional custom modifiers for the corresponding parameter. If a particular argument has no optional custom modifiers, specify <see langword="null" /> instead of an array of types. If the global method has no arguments, or if none of the arguments have optional custom modifiers, specify <see langword="null" /> instead of an array of arrays.</param>
      <returns>The defined global method.</returns>
      <exception cref="T:System.ArgumentException">The method is not static. That is, <paramref name="attributes" /> does not include <see cref="F:System.Reflection.MethodAttributes.Static" />.
-or-
An element in the <see cref="T:System.Type" /> array is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="name" /> is <see langword="null" />.</exception>
      <exception cref="T:System.InvalidOperationException">The <see cref="M:System.Reflection.Emit.ModuleBuilder.CreateGlobalFunctions" /> method has been previously called.</exception>
    </member>
    <member name="M:System.Reflection.Emit.ModuleBuilder.DefineGlobalMethod(System.String,System.Reflection.MethodAttributes,System.Type,System.Type[])">
      <summary>Defines a global method with the specified name, attributes, return type, and parameter types.</summary>
      <param name="name">The name of the method. <paramref name="name" /> cannot contain embedded nulls.</param>
      <param name="attributes">The attributes of the method. <paramref name="attributes" /> must include <see cref="F:System.Reflection.MethodAttributes.Static" />.</param>
      <param name="returnType">The return type of the method.</param>
      <param name="parameterTypes">The types of the method's parameters.</param>
      <returns>The defined global method.</returns>
      <exception cref="T:System.ArgumentException">The method is not static. That is, <paramref name="attributes" /> does not include <see cref="F:System.Reflection.MethodAttributes.Static" />.
-or-
The length of <paramref name="name" /> is zero
-or-
An element in the <see cref="T:System.Type" /> array is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="name" /> is <see langword="null" />.</exception>
      <exception cref="T:System.InvalidOperationException">
        <see cref="M:System.Reflection.Emit.ModuleBuilder.CreateGlobalFunctions" /> has been previously called.</exception>
    </member>
    <member name="M:System.Reflection.Emit.ModuleBuilder.DefineInitializedData(System.String,System.Byte[],System.Reflection.FieldAttributes)">
      <summary>Defines an initialized data field in the .sdata section of the portable executable (PE) file.</summary>
      <param name="name">The name used to refer to the data. <paramref name="name" /> cannot contain embedded nulls.</param>
      <param name="data">The binary large object (BLOB) of data.</param>
      <param name="attributes">The attributes for the field. The default is <see langword="Static" />.</param>
      <returns>A field to reference the data.</returns>
      <exception cref="T:System.ArgumentException">The length of <paramref name="name" /> is zero.
-or-
The size of <paramref name="data" /> is less than or equal to zero or greater than or equal to 0x3f0000.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="name" /> or <paramref name="data" /> is <see langword="null" />.</exception>
      <exception cref="T:System.InvalidOperationException">
        <see cref="M:System.Reflection.Emit.ModuleBuilder.CreateGlobalFunctions" /> has been previously called.</exception>
    </member>
    <member name="M:System.Reflection.Emit.ModuleBuilder.DefinePInvokeMethod(System.String,System.String,System.Reflection.MethodAttributes,System.Reflection.CallingConventions,System.Type,System.Type[],System.Runtime.InteropServices.CallingConvention,System.Runtime.InteropServices.CharSet)">
      <summary>Defines a <see langword="PInvoke" /> method with the specified name, the name of the DLL in which the method is defined, the attributes of the method, the calling convention of the method, the return type of the method, the types of the parameters of the method, and the <see langword="PInvoke" /> flags.</summary>
      <param name="name">The name of the <see langword="PInvoke" /> method. <paramref name="name" /> cannot contain embedded nulls.</param>
      <param name="dllName">The name of the DLL in which the <see langword="PInvoke" /> method is defined.</param>
      <param name="attributes">The attributes of the method.</param>
      <param name="callingConvention">The method's calling convention.</param>
      <param name="returnType">The method's return type.</param>
      <param name="parameterTypes">The types of the method's parameters.</param>
      <param name="nativeCallConv">The native calling convention.</param>
      <param name="nativeCharSet">The method's native character set.</param>
      <returns>The defined <see langword="PInvoke" /> method.</returns>
      <exception cref="T:System.ArgumentException">The method is not static or if the containing type is an interface.
-or-
The method is abstract.
-or-
The method was previously defined.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="name" /> or <paramref name="dllName" /> is <see langword="null" />.</exception>
      <exception cref="T:System.InvalidOperationException">The containing type has been previously created using <see cref="M:System.Reflection.Emit.TypeBuilder.CreateType" /></exception>
    </member>
    <member name="M:System.Reflection.Emit.ModuleBuilder.DefinePInvokeMethod(System.String,System.String,System.String,System.Reflection.MethodAttributes,System.Reflection.CallingConventions,System.Type,System.Type[],System.Runtime.InteropServices.CallingConvention,System.Runtime.InteropServices.CharSet)">
      <summary>Defines a <see langword="PInvoke" /> method with the specified name, the name of the DLL in which the method is defined, the attributes of the method, the calling convention of the method, the return type of the method, the types of the parameters of the method, and the <see langword="PInvoke" /> flags.</summary>
      <param name="name">The name of the <see langword="PInvoke" /> method. <paramref name="name" /> cannot contain embedded nulls.</param>
      <param name="dllName">The name of the DLL in which the <see langword="PInvoke" /> method is defined.</param>
      <param name="entryName">The name of the entry point in the DLL.</param>
      <param name="attributes">The attributes of the method.</param>
      <param name="callingConvention">The method's calling convention.</param>
      <param name="returnType">The method's return type.</param>
      <param name="parameterTypes">The types of the method's parameters.</param>
      <param name="nativeCallConv">The native calling convention.</param>
      <param name="nativeCharSet">The method's native character set.</param>
      <returns>The defined <see langword="PInvoke" /> method.</returns>
      <exception cref="T:System.ArgumentException">The method is not static or if the containing type is an interface or if the method is abstract of if the method was previously defined.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="name" /> or <paramref name="dllName" /> is <see langword="null" />.</exception>
      <exception cref="T:System.InvalidOperationException">The containing type has been previously created using <see cref="M:System.Reflection.Emit.TypeBuilder.CreateType" /></exception>
    </member>
    <member name="M:System.Reflection.Emit.ModuleBuilder.DefineType(System.String)">
      <summary>Constructs a <see langword="TypeBuilder" /> for a private type with the specified name in this module.</summary>
      <param name="name">The full path of the type, including the namespace. <paramref name="name" /> cannot contain embedded nulls.</param>
      <returns>A private type with the specified name.</returns>
      <exception cref="T:System.ArgumentException">A type with the given name exists in the parent assembly of this module.
-or-
Nested type attributes are set on a type that is not nested.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="name" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Reflection.Emit.ModuleBuilder.DefineType(System.String,System.Reflection.TypeAttributes)">
      <summary>Constructs a <see langword="TypeBuilder" /> given the type name and the type attributes.</summary>
      <param name="name">The full path of the type. <paramref name="name" /> cannot contain embedded nulls.</param>
      <param name="attr">The attributes of the defined type.</param>
      <returns>A <see langword="TypeBuilder" /> created with all of the requested attributes.</returns>
      <exception cref="T:System.ArgumentException">A type with the given name exists in the parent assembly of this module.
-or-
Nested type attributes are set on a type that is not nested.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="name" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Reflection.Emit.ModuleBuilder.DefineType(System.String,System.Reflection.TypeAttributes,System.Type)">
      <summary>Constructs a <see langword="TypeBuilder" /> given type name, its attributes, and the type that the defined type extends.</summary>
      <param name="name">The full path of the type. <paramref name="name" /> cannot contain embedded nulls.</param>
      <param name="attr">The attribute to be associated with the type.</param>
      <param name="parent">The type that the defined type extends.</param>
      <returns>A <see langword="TypeBuilder" /> created with all of the requested attributes.</returns>
      <exception cref="T:System.ArgumentException">A type with the given name exists in the parent assembly of this module.
-or-
Nested type attributes are set on a type that is not nested.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="name" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Reflection.Emit.ModuleBuilder.DefineType(System.String,System.Reflection.TypeAttributes,System.Type,System.Int32)">
      <summary>Constructs a <see langword="TypeBuilder" /> given the type name, the attributes, the type that the defined type extends, and the total size of the type.</summary>
      <param name="name">The full path of the type. <paramref name="name" /> cannot contain embedded nulls.</param>
      <param name="attr">The attributes of the defined type.</param>
      <param name="parent">The type that the defined type extends.</param>
      <param name="typesize">The total size of the type.</param>
      <returns>A <see langword="TypeBuilder" /> object.</returns>
      <exception cref="T:System.ArgumentException">A type with the given name exists in the parent assembly of this module.
-or-
Nested type attributes are set on a type that is not nested.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="name" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Reflection.Emit.ModuleBuilder.DefineType(System.String,System.Reflection.TypeAttributes,System.Type,System.Reflection.Emit.PackingSize)">
      <summary>Constructs a <see langword="TypeBuilder" /> given the type name, the attributes, the type that the defined type extends, and the packing size of the type.</summary>
      <param name="name">The full path of the type. <paramref name="name" /> cannot contain embedded nulls.</param>
      <param name="attr">The attributes of the defined type.</param>
      <param name="parent">The type that the defined type extends.</param>
      <param name="packsize">The packing size of the type.</param>
      <returns>A <see langword="TypeBuilder" /> object.</returns>
      <exception cref="T:System.ArgumentException">A type with the given name exists in the parent assembly of this module.
-or-
Nested type attributes are set on a type that is not nested.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="name" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Reflection.Emit.ModuleBuilder.DefineType(System.String,System.Reflection.TypeAttributes,System.Type,System.Reflection.Emit.PackingSize,System.Int32)">
      <summary>Constructs a <see langword="TypeBuilder" /> given the type name, attributes, the type that the defined type extends, the packing size of the defined type, and the total size of the defined type.</summary>
      <param name="name">The full path of the type. <paramref name="name" /> cannot contain embedded nulls.</param>
      <param name="attr">The attributes of the defined type.</param>
      <param name="parent">The type that the defined type extends.</param>
      <param name="packingSize">The packing size of the type.</param>
      <param name="typesize">The total size of the type.</param>
      <returns>A <see langword="TypeBuilder" /> created with all of the requested attributes.</returns>
      <exception cref="T:System.ArgumentException">A type with the given name exists in the parent assembly of this module.
-or-
Nested type attributes are set on a type that is not nested.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="name" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Reflection.Emit.ModuleBuilder.DefineType(System.String,System.Reflection.TypeAttributes,System.Type,System.Type[])">
      <summary>Constructs a <see langword="TypeBuilder" /> given the type name, attributes, the type that the defined type extends, and the interfaces that the defined type implements.</summary>
      <param name="name">The full path of the type. <paramref name="name" /> cannot contain embedded nulls.</param>
      <param name="attr">The attributes to be associated with the type.</param>
      <param name="parent">The type that the defined type extends.</param>
      <param name="interfaces">The list of interfaces that the type implements.</param>
      <returns>A <see langword="TypeBuilder" /> created with all of the requested attributes.</returns>
      <exception cref="T:System.ArgumentException">A type with the given name exists in the parent assembly of this module.
-or-
Nested type attributes are set on a type that is not nested.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="name" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Reflection.Emit.ModuleBuilder.DefineUninitializedData(System.String,System.Int32,System.Reflection.FieldAttributes)">
      <summary>Defines an uninitialized data field in the .sdata section of the portable executable (PE) file.</summary>
      <param name="name">The name used to refer to the data. <paramref name="name" /> cannot contain embedded nulls.</param>
      <param name="size">The size of the data field.</param>
      <param name="attributes">The attributes for the field.</param>
      <returns>A field to reference the data.</returns>
      <exception cref="T:System.ArgumentException">The length of <paramref name="name" /> is zero.
-or-
<paramref name="size" /> is less than or equal to zero, or greater than or equal to 0x003f0000.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="name" /> is <see langword="null" />.</exception>
      <exception cref="T:System.InvalidOperationException">
        <see cref="M:System.Reflection.Emit.ModuleBuilder.CreateGlobalFunctions" /> has been previously called.</exception>
    </member>
    <member name="M:System.Reflection.Emit.ModuleBuilder.Equals(System.Object)">
      <summary>Returns a value that indicates whether this instance is equal to the specified object.</summary>
      <param name="obj">An object to compare with this instance, or <see langword="null" />.</param>
      <returns>
        <see langword="true" /> if <paramref name="obj" /> equals the type and value of this instance; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Reflection.Emit.ModuleBuilder.FullyQualifiedName">
      <summary>Gets a <see langword="String" /> representing the fully qualified name and path to this module.</summary>
      <returns>The fully qualified module name.</returns>
    </member>
    <member name="M:System.Reflection.Emit.ModuleBuilder.GetArrayMethod(System.Type,System.String,System.Reflection.CallingConventions,System.Type,System.Type[])">
      <summary>Returns the named method on an array class.</summary>
      <param name="arrayClass">An array class.</param>
      <param name="methodName">The name of a method on the array class.</param>
      <param name="callingConvention">The method's calling convention.</param>
      <param name="returnType">The return type of the method.</param>
      <param name="parameterTypes">The types of the method's parameters.</param>
      <returns>The named method on an array class.</returns>
      <exception cref="T:System.ArgumentException">
        <paramref name="arrayClass" /> is not an array.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="arrayClass" /> or <paramref name="methodName" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Reflection.Emit.ModuleBuilder.GetCustomAttributes(System.Boolean)">
      <summary>Returns all the custom attributes that have been applied to the current <see cref="T:System.Reflection.Emit.ModuleBuilder" />.</summary>
      <param name="inherit">This argument is ignored for objects of this type.</param>
      <returns>An array that contains the custom attributes; the array is empty if there are no attributes.</returns>
    </member>
    <member name="M:System.Reflection.Emit.ModuleBuilder.GetCustomAttributes(System.Type,System.Boolean)">
      <summary>Returns all the custom attributes that have been applied to the current <see cref="T:System.Reflection.Emit.ModuleBuilder" />, and that derive from a specified attribute type.</summary>
      <param name="attributeType">The base type from which attributes derive.</param>
      <param name="inherit">This argument is ignored for objects of this type.</param>
      <returns>An array that contains the custom attributes that are derived, at any level, from <paramref name="attributeType" />; the array is empty if there are no such attributes.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="attributeType" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="attributeType" /> is not a <see cref="T:System.Type" /> object supplied by the runtime. For example, <paramref name="attributeType" /> is a <see cref="T:System.Reflection.Emit.TypeBuilder" /> object.</exception>
    </member>
    <member name="M:System.Reflection.Emit.ModuleBuilder.GetCustomAttributesData">
      <summary>Returns information about the attributes that have been applied to the current <see cref="T:System.Reflection.Emit.ModuleBuilder" />, expressed as <see cref="T:System.Reflection.CustomAttributeData" /> objects.</summary>
      <returns>A generic list of <see cref="T:System.Reflection.CustomAttributeData" /> objects representing data about the attributes that have been applied to the current module.</returns>
    </member>
    <member name="M:System.Reflection.Emit.ModuleBuilder.GetField(System.String,System.Reflection.BindingFlags)">
      <summary>Returns a module-level field, defined in the .sdata region of the portable executable (PE) file, that has the specified name and binding attributes.</summary>
      <param name="name">The field name.</param>
      <param name="bindingAttr">A combination of the <see langword="BindingFlags" /> bit flags used to control the search.</param>
      <returns>A field that has the specified name and binding attributes, or <see langword="null" /> if the field does not exist.</returns>
      <exception cref="T:System.ArgumentNullException">The <paramref name="name" /> parameter is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Reflection.Emit.ModuleBuilder.GetFields(System.Reflection.BindingFlags)">
      <summary>Returns all fields defined in the .sdata region of the portable executable (PE) file that match the specified binding flags.</summary>
      <param name="bindingFlags">A combination of the <see langword="BindingFlags" /> bit flags used to control the search.</param>
      <returns>An array of fields that match the specified flags; the array is empty if no such fields exist.</returns>
      <exception cref="T:System.ArgumentNullException">The <paramref name="name" /> parameter is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Reflection.Emit.ModuleBuilder.GetHashCode">
      <summary>Returns the hash code for this instance.</summary>
      <returns>A 32-bit signed integer hash code.</returns>
    </member>
    <member name="M:System.Reflection.Emit.ModuleBuilder.GetMethods(System.Reflection.BindingFlags)">
      <summary>Returns all the methods that have been defined at the module level for the current <see cref="T:System.Reflection.Emit.ModuleBuilder" />, and that match the specified binding flags.</summary>
      <param name="bindingFlags">A combination of <see langword="BindingFlags" /> bit flags used to control the search.</param>
      <returns>An array that contains all the module-level methods that match <paramref name="bindingFlags" />.</returns>
    </member>
    <member name="M:System.Reflection.Emit.ModuleBuilder.GetPEKind(System.Reflection.PortableExecutableKinds@,System.Reflection.ImageFileMachine@)">
      <summary>Gets a pair of values indicating the nature of the code in a module and the platform targeted by the module.</summary>
      <param name="peKind">When this method returns, a combination of the <see cref="T:System.Reflection.PortableExecutableKinds" /> values indicating the nature of the code in the module.</param>
      <param name="machine">When this method returns, one of the <see cref="T:System.Reflection.ImageFileMachine" /> values indicating the platform targeted by the module.</param>
    </member>
    <member name="M:System.Reflection.Emit.ModuleBuilder.GetType(System.String)">
      <summary>Gets the named type defined in the module.</summary>
      <param name="className">The name of the <see cref="T:System.Type" /> to get.</param>
      <returns>The requested type, if the type is defined in this module; otherwise, <see langword="null" />.</returns>
      <exception cref="T:System.ArgumentException">Length of <paramref name="className" /> is zero or is greater than 1023.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="className" /> is <see langword="null" />.</exception>
      <exception cref="T:System.Security.SecurityException">The requested <see cref="T:System.Type" /> is non-public and the caller does not have <see cref="T:System.Security.Permissions.ReflectionPermission" /> to reflect non-public objects outside the current assembly.</exception>
      <exception cref="T:System.Reflection.TargetInvocationException">A class initializer is invoked and throws an exception.</exception>
      <exception cref="T:System.TypeLoadException">An error is encountered while loading the <see cref="T:System.Type" />.</exception>
    </member>
    <member name="M:System.Reflection.Emit.ModuleBuilder.GetType(System.String,System.Boolean)">
      <summary>Gets the named type defined in the module, optionally ignoring the case of the type name.</summary>
      <param name="className">The name of the <see cref="T:System.Type" /> to get.</param>
      <param name="ignoreCase">If <see langword="true" />, the search is case-insensitive. If <see langword="false" />, the search is case-sensitive.</param>
      <returns>The requested type, if the type is defined in this module; otherwise, <see langword="null" />.</returns>
      <exception cref="T:System.ArgumentException">Length of <paramref name="className" /> is zero or is greater than 1023.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="className" /> is <see langword="null" />.</exception>
      <exception cref="T:System.Security.SecurityException">The requested <see cref="T:System.Type" /> is non-public and the caller does not have <see cref="T:System.Security.Permissions.ReflectionPermission" /> to reflect non-public objects outside the current assembly.</exception>
      <exception cref="T:System.Reflection.TargetInvocationException">A class initializer is invoked and throws an exception.</exception>
    </member>
    <member name="M:System.Reflection.Emit.ModuleBuilder.GetType(System.String,System.Boolean,System.Boolean)">
      <summary>Gets the named type defined in the module, optionally ignoring the case of the type name. Optionally throws an exception if the type is not found.</summary>
      <param name="className">The name of the <see cref="T:System.Type" /> to get.</param>
      <param name="throwOnError">
        <see langword="true" /> to throw an exception if the type cannot be found; <see langword="false" /> to return <see langword="null" />.</param>
      <param name="ignoreCase">If <see langword="true" />, the search is case-insensitive. If <see langword="false" />, the search is case-sensitive.</param>
      <returns>The specified type, if the type is declared in this module; otherwise, <see langword="null" />.</returns>
      <exception cref="T:System.ArgumentException">Length of <paramref name="className" /> is zero or is greater than 1023.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="className" /> is <see langword="null" />.</exception>
      <exception cref="T:System.Security.SecurityException">The requested <see cref="T:System.Type" /> is non-public and the caller does not have <see cref="T:System.Security.Permissions.ReflectionPermission" /> to reflect non-public objects outside the current assembly.</exception>
      <exception cref="T:System.Reflection.TargetInvocationException">A class initializer is invoked and throws an exception.</exception>
      <exception cref="T:System.TypeLoadException">
        <paramref name="throwOnError" /> is <see langword="true" /> and the specified type is not found.</exception>
    </member>
    <member name="M:System.Reflection.Emit.ModuleBuilder.GetTypes">
      <summary>Returns all the classes defined within this module.</summary>
      <returns>An array that contains the types defined within the module that is reflected by this instance.</returns>
      <exception cref="T:System.Reflection.ReflectionTypeLoadException">One or more classes in a module could not be loaded.</exception>
      <exception cref="T:System.Security.SecurityException">The caller does not have the required permission.</exception>
    </member>
    <member name="M:System.Reflection.Emit.ModuleBuilder.IsDefined(System.Type,System.Boolean)">
      <summary>Returns a value that indicates whether the specified attribute type has been applied to this module.</summary>
      <param name="attributeType">The type of custom attribute to test for.</param>
      <param name="inherit">This argument is ignored for objects of this type.</param>
      <returns>
        <see langword="true" /> if one or more instances of <paramref name="attributeType" /> have been applied to this module; otherwise, <see langword="false" />.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="attributeType" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="attributeType" /> is not a <see cref="T:System.Type" /> object supplied by the runtime. For example, <paramref name="attributeType" /> is a <see cref="T:System.Reflection.Emit.TypeBuilder" /> object.</exception>
    </member>
    <member name="M:System.Reflection.Emit.ModuleBuilder.IsResource">
      <summary>Gets a value indicating whether the object is a resource.</summary>
      <returns>
        <see langword="true" /> if the object is a resource; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Reflection.Emit.ModuleBuilder.MDStreamVersion">
      <summary>Gets the metadata stream version.</summary>
      <returns>A 32-bit integer representing the metadata stream version. The high-order two bytes represent the major version number, and the low-order two bytes represent the minor version number.</returns>
    </member>
    <member name="P:System.Reflection.Emit.ModuleBuilder.MetadataToken">
      <summary>Gets a token that identifies the current dynamic module in metadata.</summary>
      <returns>An integer token that identifies the current module in metadata.</returns>
    </member>
    <member name="P:System.Reflection.Emit.ModuleBuilder.ModuleVersionId">
      <summary>Gets a universally unique identifier (UUID) that can be used to distinguish between two versions of a module.</summary>
      <returns>A <see cref="T:System.Guid" /> that can be used to distinguish between two versions of a module.</returns>
    </member>
    <member name="P:System.Reflection.Emit.ModuleBuilder.Name">
      <summary>A string that indicates that this is an in-memory module.</summary>
      <returns>Text that indicates that this is an in-memory module.</returns>
    </member>
    <member name="M:System.Reflection.Emit.ModuleBuilder.ResolveField(System.Int32,System.Type[],System.Type[])">
      <summary>Returns the field identified by the specified metadata token, in the context defined by the specified generic type parameters.</summary>
      <param name="metadataToken">A metadata token that identifies a field in the module.</param>
      <param name="genericTypeArguments">An array of <see cref="T:System.Type" /> objects representing the generic type arguments of the type where the token is in scope, or <see langword="null" /> if that type is not generic.</param>
      <param name="genericMethodArguments">An array of <see cref="T:System.Type" /> objects representing the generic type arguments of the method where the token is in scope, or <see langword="null" /> if that method is not generic.</param>
      <returns>A <see cref="T:System.Reflection.FieldInfo" /> object representing the field that is identified by the specified metadata token.</returns>
      <exception cref="T:System.ArgumentException">
        <paramref name="metadataToken" /> is not a token for a field in the scope of the current module.
-or-
<paramref name="metadataToken" /> identifies a field whose parent <see langword="TypeSpec" /> has a signature containing element type <see langword="var" /> (a type parameter of a generic type) or <see langword="mvar" /> (a type parameter of a generic method), and the necessary generic type arguments were not supplied for either or both of <paramref name="genericTypeArguments" /> and <paramref name="genericMethodArguments" />.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="metadataToken" /> is not a valid token in the scope of the current module.</exception>
    </member>
    <member name="M:System.Reflection.Emit.ModuleBuilder.ResolveMember(System.Int32,System.Type[],System.Type[])">
      <summary>Returns the type or member identified by the specified metadata token, in the context defined by the specified generic type parameters.</summary>
      <param name="metadataToken">A metadata token that identifies a type or member in the module.</param>
      <param name="genericTypeArguments">An array of <see cref="T:System.Type" /> objects representing the generic type arguments of the type where the token is in scope, or <see langword="null" /> if that type is not generic.</param>
      <param name="genericMethodArguments">An array of <see cref="T:System.Type" /> objects representing the generic type arguments of the method where the token is in scope, or <see langword="null" /> if that method is not generic.</param>
      <returns>A <see cref="T:System.Reflection.MemberInfo" /> object representing the type or member that is identified by the specified metadata token.</returns>
      <exception cref="T:System.ArgumentException">
        <paramref name="metadataToken" /> is not a token for a type or member in the scope of the current module.
-or-
<paramref name="metadataToken" /> is a <see langword="MethodSpec" /> or <see langword="TypeSpec" /> whose signature contains element type <see langword="var" /> (a type parameter of a generic type) or <see langword="mvar" /> (a type parameter of a generic method), and the necessary generic type arguments were not supplied for either or both of <paramref name="genericTypeArguments" /> and <paramref name="genericMethodArguments" />.
-or-
<paramref name="metadataToken" /> identifies a property or event.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="metadataToken" /> is not a valid token in the scope of the current module.</exception>
    </member>
    <member name="M:System.Reflection.Emit.ModuleBuilder.ResolveMethod(System.Int32,System.Type[],System.Type[])">
      <summary>Returns the method or constructor identified by the specified metadata token, in the context defined by the specified generic type parameters.</summary>
      <param name="metadataToken">A metadata token that identifies a method or constructor in the module.</param>
      <param name="genericTypeArguments">An array of <see cref="T:System.Type" /> objects representing the generic type arguments of the type where the token is in scope, or <see langword="null" /> if that type is not generic.</param>
      <param name="genericMethodArguments">An array of <see cref="T:System.Type" /> objects representing the generic type arguments of the method where the token is in scope, or <see langword="null" /> if that method is not generic.</param>
      <returns>A <see cref="T:System.Reflection.MethodBase" /> object representing the method that is identified by the specified metadata token.</returns>
      <exception cref="T:System.ArgumentException">
        <paramref name="metadataToken" /> is not a token for a method or constructor in the scope of the current module.
-or-
<paramref name="metadataToken" /> is a <see langword="MethodSpec" /> whose signature contains element type <see langword="var" /> (a type parameter of a generic type) or <see langword="mvar" /> (a type parameter of a generic method), and the necessary generic type arguments were not supplied for either or both of <paramref name="genericTypeArguments" /> and <paramref name="genericMethodArguments" />.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="metadataToken" /> is not a valid token in the scope of the current module.</exception>
    </member>
    <member name="M:System.Reflection.Emit.ModuleBuilder.ResolveSignature(System.Int32)">
      <summary>Returns the signature blob identified by a metadata token.</summary>
      <param name="metadataToken">A metadata token that identifies a signature in the module.</param>
      <returns>An array of bytes representing the signature blob.</returns>
      <exception cref="T:System.ArgumentException">
        <paramref name="metadataToken" /> is not a valid <see langword="MemberRef" />, <see langword="MethodDef" />, <see langword="TypeSpec" />, signature, or <see langword="FieldDef" /> token in the scope of the current module.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="metadataToken" /> is not a valid token in the scope of the current module.</exception>
    </member>
    <member name="M:System.Reflection.Emit.ModuleBuilder.ResolveString(System.Int32)">
      <summary>Returns the string identified by the specified metadata token.</summary>
      <param name="metadataToken">A metadata token that identifies a string in the string heap of the module.</param>
      <returns>A <see cref="T:System.String" /> containing a string value from the metadata string heap.</returns>
      <exception cref="T:System.ArgumentException">
        <paramref name="metadataToken" /> is not a token for a string in the scope of the current module.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="metadataToken" /> is not a valid token in the scope of the current module.</exception>
    </member>
    <member name="M:System.Reflection.Emit.ModuleBuilder.ResolveType(System.Int32,System.Type[],System.Type[])">
      <summary>Returns the type identified by the specified metadata token, in the context defined by the specified generic type parameters.</summary>
      <param name="metadataToken">A metadata token that identifies a type in the module.</param>
      <param name="genericTypeArguments">An array of <see cref="T:System.Type" /> objects representing the generic type arguments of the type where the token is in scope, or <see langword="null" /> if that type is not generic.</param>
      <param name="genericMethodArguments">An array of <see cref="T:System.Type" /> objects representing the generic type arguments of the method where the token is in scope, or <see langword="null" /> if that method is not generic.</param>
      <returns>A <see cref="T:System.Type" /> object representing the type that is identified by the specified metadata token.</returns>
      <exception cref="T:System.ArgumentException">
        <paramref name="metadataToken" /> is not a token for a type in the scope of the current module.
-or-
<paramref name="metadataToken" /> is a <see langword="TypeSpec" /> whose signature contains element type <see langword="var" /> (a type parameter of a generic type) or <see langword="mvar" /> (a type parameter of a generic method), and the necessary generic type arguments were not supplied for either or both of <paramref name="genericTypeArguments" /> and <paramref name="genericMethodArguments" />.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="metadataToken" /> is not a valid token in the scope of the current module.</exception>
    </member>
    <member name="P:System.Reflection.Emit.ModuleBuilder.ScopeName">
      <summary>Gets a string that represents the name of the dynamic module.</summary>
      <returns>The name of the dynamic module.</returns>
    </member>
    <member name="M:System.Reflection.Emit.ModuleBuilder.SetCustomAttribute(System.Reflection.ConstructorInfo,System.Byte[])">
      <summary>Applies a custom attribute to this module by using a specified binary large object (BLOB) that represents the attribute.</summary>
      <param name="con">The constructor for the custom attribute.</param>
      <param name="binaryAttribute">A byte BLOB representing the attribute.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="con" /> or <paramref name="binaryAttribute" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Reflection.Emit.ModuleBuilder.SetCustomAttribute(System.Reflection.Emit.CustomAttributeBuilder)">
      <summary>Applies a custom attribute to this module by using a custom attribute builder.</summary>
      <param name="customBuilder">An instance of a helper class that specifies the custom attribute to apply.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="customBuilder" /> is <see langword="null" />.</exception>
    </member>
    <member name="T:System.Reflection.Emit.PropertyBuilder">
      <summary>Defines the properties for a type.</summary>
    </member>
    <member name="M:System.Reflection.Emit.PropertyBuilder.AddOtherMethod(System.Reflection.Emit.MethodBuilder)">
      <summary>Adds one of the other methods associated with this property.</summary>
      <param name="mdBuilder">A <see langword="MethodBuilder" /> object that represents the other method.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="mdBuilder" /> is <see langword="null" />.</exception>
      <exception cref="T:System.InvalidOperationException">
        <see cref="M:System.Reflection.Emit.TypeBuilder.CreateType" /> has been called on the enclosing type.</exception>
    </member>
    <member name="P:System.Reflection.Emit.PropertyBuilder.Attributes">
      <summary>Gets the attributes for this property.</summary>
      <returns>Attributes of this property.</returns>
    </member>
    <member name="P:System.Reflection.Emit.PropertyBuilder.CanRead">
      <summary>Gets a value indicating whether the property can be read.</summary>
      <returns>
        <see langword="true" /> if this property can be read; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Reflection.Emit.PropertyBuilder.CanWrite">
      <summary>Gets a value indicating whether the property can be written to.</summary>
      <returns>
        <see langword="true" /> if this property can be written to; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Reflection.Emit.PropertyBuilder.DeclaringType">
      <summary>Gets the class that declares this member.</summary>
      <returns>The <see langword="Type" /> object for the class that declares this member.</returns>
    </member>
    <member name="M:System.Reflection.Emit.PropertyBuilder.GetAccessors(System.Boolean)">
      <summary>Returns an array of the public and non-public <see langword="get" /> and <see langword="set" /> accessors on this property.</summary>
      <param name="nonPublic">Indicates whether non-public methods should be returned in the <see langword="MethodInfo" /> array. <see langword="true" /> if non-public methods are to be included; otherwise, <see langword="false" />.</param>
      <returns>An array of type <see langword="MethodInfo" /> containing the matching public or non-public accessors, or an empty array if matching accessors do not exist on this property.</returns>
      <exception cref="T:System.NotSupportedException">This method is not supported.</exception>
    </member>
    <member name="M:System.Reflection.Emit.PropertyBuilder.GetCustomAttributes(System.Boolean)">
      <summary>Returns an array of all the custom attributes for this property.</summary>
      <param name="inherit">If <see langword="true" />, walks up this property's inheritance chain to find the custom attributes</param>
      <returns>An array of all the custom attributes.</returns>
      <exception cref="T:System.NotSupportedException">This method is not supported.</exception>
    </member>
    <member name="M:System.Reflection.Emit.PropertyBuilder.GetCustomAttributes(System.Type,System.Boolean)">
      <summary>Returns an array of custom attributes identified by <see cref="T:System.Type" />.</summary>
      <param name="attributeType">An array of custom attributes identified by type.</param>
      <param name="inherit">If <see langword="true" />, walks up this property's inheritance chain to find the custom attributes.</param>
      <returns>An array of custom attributes defined on this reflected member, or <see langword="null" /> if no attributes are defined on this member.</returns>
      <exception cref="T:System.NotSupportedException">This method is not supported.</exception>
    </member>
    <member name="M:System.Reflection.Emit.PropertyBuilder.GetGetMethod(System.Boolean)">
      <summary>Returns the public and non-public get accessor for this property.</summary>
      <param name="nonPublic">Indicates whether non-public get accessors should be returned. <see langword="true" /> if non-public methods are to be included; otherwise, <see langword="false" />.</param>
      <returns>A <see langword="MethodInfo" /> object representing the get accessor for this property, if <paramref name="nonPublic" /> is <see langword="true" />. Returns <see langword="null" /> if <paramref name="nonPublic" /> is <see langword="false" /> and the get accessor is non-public, or if <paramref name="nonPublic" /> is <see langword="true" /> but no get accessors exist.</returns>
    </member>
    <member name="M:System.Reflection.Emit.PropertyBuilder.GetIndexParameters">
      <summary>Returns an array of all the index parameters for the property.</summary>
      <returns>An array of type <see langword="ParameterInfo" /> containing the parameters for the indexes.</returns>
      <exception cref="T:System.NotSupportedException">This method is not supported.</exception>
    </member>
    <member name="M:System.Reflection.Emit.PropertyBuilder.GetSetMethod(System.Boolean)">
      <summary>Returns the set accessor for this property.</summary>
      <param name="nonPublic">Indicates whether the accessor should be returned if it is non-public. <see langword="true" /> if non-public methods are to be included; otherwise, <see langword="false" />.</param>
      <returns>The property's <see langword="Set" /> method, or <see langword="null" />, as shown in the following table.
  Value  
  
  Condition  
  
  A <see cref="T:System.Reflection.MethodInfo" /> object representing the Set method for this property.  
  
  The set accessor is public.  
  
 <paramref name="nonPublic" /> is true and non-public methods can be returned.  
  
  null  
  
 <paramref name="nonPublic" /> is true, but the property is read-only.  
  
 <paramref name="nonPublic" /> is false and the set accessor is non-public.</returns>
    </member>
    <member name="M:System.Reflection.Emit.PropertyBuilder.GetValue(System.Object,System.Object[])">
      <summary>Gets the value of the indexed property by calling the property's getter method.</summary>
      <param name="obj">The object whose property value will be returned.</param>
      <param name="index">Optional index values for indexed properties. This value should be <see langword="null" /> for non-indexed properties.</param>
      <returns>The value of the specified indexed property.</returns>
      <exception cref="T:System.NotSupportedException">This method is not supported.</exception>
    </member>
    <member name="M:System.Reflection.Emit.PropertyBuilder.GetValue(System.Object,System.Reflection.BindingFlags,System.Reflection.Binder,System.Object[],System.Globalization.CultureInfo)">
      <summary>Gets the value of a property having the specified binding, index, and <see langword="CultureInfo" />.</summary>
      <param name="obj">The object whose property value will be returned.</param>
      <param name="invokeAttr">The invocation attribute. This must be a bit flag from <see langword="BindingFlags" /> : <see langword="InvokeMethod" />, <see langword="CreateInstance" />, <see langword="Static" />, <see langword="GetField" />, <see langword="SetField" />, <see langword="GetProperty" />, or <see langword="SetProperty" />. A suitable invocation attribute must be specified. If a static member is to be invoked, the <see langword="Static" /> flag of <see langword="BindingFlags" /> must be set.</param>
      <param name="binder">An object that enables the binding, coercion of argument types, invocation of members, and retrieval of <see langword="MemberInfo" /> objects using reflection. If <paramref name="binder" /> is <see langword="null" />, the default binder is used.</param>
      <param name="index">Optional index values for indexed properties. This value should be <see langword="null" /> for non-indexed properties.</param>
      <param name="culture">The <see langword="CultureInfo" /> object that represents the culture for which the resource is to be localized. Note that if the resource is not localized for this culture, the <see langword="CultureInfo.Parent" /> method will be called successively in search of a match. If this value is <see langword="null" />, the <see langword="CultureInfo" /> is obtained from the <see langword="CultureInfo.CurrentUICulture" /> property.</param>
      <returns>The property value for <paramref name="obj" />.</returns>
      <exception cref="T:System.NotSupportedException">This method is not supported.</exception>
    </member>
    <member name="M:System.Reflection.Emit.PropertyBuilder.IsDefined(System.Type,System.Boolean)">
      <summary>Indicates whether one or more instance of <paramref name="attributeType" /> is defined on this property.</summary>
      <param name="attributeType">The <see langword="Type" /> object to which the custom attributes are applied.</param>
      <param name="inherit">Specifies whether to walk up this property's inheritance chain to find the custom attributes.</param>
      <returns>
        <see langword="true" /> if one or more instance of <paramref name="attributeType" /> is defined on this property; otherwise <see langword="false" />.</returns>
      <exception cref="T:System.NotSupportedException">This method is not supported.</exception>
    </member>
    <member name="P:System.Reflection.Emit.PropertyBuilder.Module">
      <summary>Gets the module in which the type that declares the current property is being defined.</summary>
      <returns>The <see cref="T:System.Reflection.Module" /> in which the type that declares the current property is defined.</returns>
    </member>
    <member name="P:System.Reflection.Emit.PropertyBuilder.Name">
      <summary>Gets the name of this member.</summary>
      <returns>A <see cref="T:System.String" /> containing the name of this member.</returns>
    </member>
    <member name="P:System.Reflection.Emit.PropertyBuilder.PropertyType">
      <summary>Gets the type of the field of this property.</summary>
      <returns>The type of this property.</returns>
    </member>
    <member name="P:System.Reflection.Emit.PropertyBuilder.ReflectedType">
      <summary>Gets the class object that was used to obtain this instance of <see langword="MemberInfo" />.</summary>
      <returns>The <see langword="Type" /> object through which this <see langword="MemberInfo" /> object was obtained.</returns>
    </member>
    <member name="M:System.Reflection.Emit.PropertyBuilder.SetConstant(System.Object)">
      <summary>Sets the default value of this property.</summary>
      <param name="defaultValue">The default value of this property.</param>
      <exception cref="T:System.InvalidOperationException">
        <see cref="M:System.Reflection.Emit.TypeBuilder.CreateType" /> has been called on the enclosing type.</exception>
      <exception cref="T:System.ArgumentException">The property is not one of the supported types.
-or-
The type of <paramref name="defaultValue" /> does not match the type of the property.
-or-
The property is of type <see cref="T:System.Object" /> or other reference type, <paramref name="defaultValue" /> is not <see langword="null" />, and the value cannot be assigned to the reference type.</exception>
    </member>
    <member name="M:System.Reflection.Emit.PropertyBuilder.SetCustomAttribute(System.Reflection.ConstructorInfo,System.Byte[])">
      <summary>Set a custom attribute using a specified custom attribute blob.</summary>
      <param name="con">The constructor for the custom attribute.</param>
      <param name="binaryAttribute">A byte blob representing the attributes.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="con" /> or <paramref name="binaryAttribute" /> is <see langword="null" />.</exception>
      <exception cref="T:System.InvalidOperationException">
        <see cref="M:System.Reflection.Emit.TypeBuilder.CreateType" /> has been called on the enclosing type.</exception>
    </member>
    <member name="M:System.Reflection.Emit.PropertyBuilder.SetCustomAttribute(System.Reflection.Emit.CustomAttributeBuilder)">
      <summary>Set a custom attribute using a custom attribute builder.</summary>
      <param name="customBuilder">An instance of a helper class to define the custom attribute.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="customBuilder" /> is <see langword="null" />.</exception>
      <exception cref="T:System.InvalidOperationException">if <see cref="M:System.Reflection.Emit.TypeBuilder.CreateType" /> has been called on the enclosing type.</exception>
    </member>
    <member name="M:System.Reflection.Emit.PropertyBuilder.SetGetMethod(System.Reflection.Emit.MethodBuilder)">
      <summary>Sets the method that gets the property value.</summary>
      <param name="mdBuilder">A <see langword="MethodBuilder" /> object that represents the method that gets the property value.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="mdBuilder" /> is <see langword="null" />.</exception>
      <exception cref="T:System.InvalidOperationException">
        <see cref="M:System.Reflection.Emit.TypeBuilder.CreateType" /> has been called on the enclosing type.</exception>
    </member>
    <member name="M:System.Reflection.Emit.PropertyBuilder.SetSetMethod(System.Reflection.Emit.MethodBuilder)">
      <summary>Sets the method that sets the property value.</summary>
      <param name="mdBuilder">A <see langword="MethodBuilder" /> object that represents the method that sets the property value.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="mdBuilder" /> is <see langword="null" />.</exception>
      <exception cref="T:System.InvalidOperationException">
        <see cref="M:System.Reflection.Emit.TypeBuilder.CreateType" /> has been called on the enclosing type.</exception>
    </member>
    <member name="M:System.Reflection.Emit.PropertyBuilder.SetValue(System.Object,System.Object,System.Object[])">
      <summary>Sets the value of the property with optional index values for index properties.</summary>
      <param name="obj">The object whose property value will be set.</param>
      <param name="value">The new value for this property.</param>
      <param name="index">Optional index values for indexed properties. This value should be <see langword="null" /> for non-indexed properties.</param>
      <exception cref="T:System.NotSupportedException">This method is not supported.</exception>
    </member>
    <member name="M:System.Reflection.Emit.PropertyBuilder.SetValue(System.Object,System.Object,System.Reflection.BindingFlags,System.Reflection.Binder,System.Object[],System.Globalization.CultureInfo)">
      <summary>Sets the property value for the given object to the given value.</summary>
      <param name="obj">The object whose property value will be returned.</param>
      <param name="value">The new value for this property.</param>
      <param name="invokeAttr">The invocation attribute. This must be a bit flag from <see langword="BindingFlags" /> : <see langword="InvokeMethod" />, <see langword="CreateInstance" />, <see langword="Static" />, <see langword="GetField" />, <see langword="SetField" />, <see langword="GetProperty" />, or <see langword="SetProperty" />. A suitable invocation attribute must be specified. If a static member is to be invoked, the <see langword="Static" /> flag of <see langword="BindingFlags" /> must be set.</param>
      <param name="binder">An object that enables the binding, coercion of argument types, invocation of members, and retrieval of <see langword="MemberInfo" /> objects using reflection. If <paramref name="binder" /> is <see langword="null" />, the default binder is used.</param>
      <param name="index">Optional index values for indexed properties. This value should be <see langword="null" /> for non-indexed properties.</param>
      <param name="culture">The <see langword="CultureInfo" /> object that represents the culture for which the resource is to be localized. Note that if the resource is not localized for this culture, the <see langword="CultureInfo.Parent" /> method will be called successively in search of a match. If this value is <see langword="null" />, the <see langword="CultureInfo" /> is obtained from the <see langword="CultureInfo.CurrentUICulture" /> property.</param>
      <exception cref="T:System.NotSupportedException">This method is not supported.</exception>
    </member>
    <member name="T:System.Reflection.Emit.TypeBuilder">
      <summary>Defines and creates new instances of classes during run time.</summary>
    </member>
    <member name="M:System.Reflection.Emit.TypeBuilder.AddInterfaceImplementation(System.Type)">
      <summary>Adds an interface that this type implements.</summary>
      <param name="interfaceType">The interface that this type implements.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="interfaceType" /> is <see langword="null" />.</exception>
      <exception cref="T:System.InvalidOperationException">The type was previously created using <see cref="M:System.Reflection.Emit.TypeBuilder.CreateType" />.</exception>
    </member>
    <member name="P:System.Reflection.Emit.TypeBuilder.Assembly">
      <summary>Retrieves the dynamic assembly that contains this type definition.</summary>
      <returns>Read-only. Retrieves the dynamic assembly that contains this type definition.</returns>
    </member>
    <member name="P:System.Reflection.Emit.TypeBuilder.AssemblyQualifiedName">
      <summary>Returns the full name of this type qualified by the display name of the assembly.</summary>
      <returns>Read-only. The full name of this type qualified by the display name of the assembly.</returns>
    </member>
    <member name="P:System.Reflection.Emit.TypeBuilder.BaseType">
      <summary>Retrieves the base type of this type.</summary>
      <returns>Read-only. Retrieves the base type of this type.</returns>
    </member>
    <member name="M:System.Reflection.Emit.TypeBuilder.CreateType">
      <summary>Creates a <see cref="T:System.Type" /> object for the class. After defining fields and methods on the class, <see langword="CreateType" /> is called in order to load its <see langword="Type" /> object.</summary>
      <returns>Returns the new <see cref="T:System.Type" /> object for this class.</returns>
      <exception cref="T:System.InvalidOperationException">The enclosing type has not been created.
-or-
This type is non-abstract and contains an abstract method.
-or-
This type is not an abstract class or an interface and has a method without a method body.</exception>
      <exception cref="T:System.ArgumentException">Bad label content in <see cref="T:System.Reflection.Emit.ILGenerator" />: You have defined a label without calling <see cref="M:System.Reflection.Emit.ILGenerator.MarkLabel(System.Reflection.Emit.Label)" />.</exception>
      <exception cref="T:System.NotSupportedException">The type contains invalid Microsoft intermediate language (MSIL) code.
-or-
The branch target is specified using a 1-byte offset, but the target is at a distance greater than 127 bytes from the branch.</exception>
      <exception cref="T:System.TypeLoadException">The type cannot be loaded. For example, it contains a <see langword="static" /> method that has the calling convention <see cref="F:System.Reflection.CallingConventions.HasThis" />.</exception>
    </member>
    <member name="M:System.Reflection.Emit.TypeBuilder.CreateTypeInfo">
      <summary>Gets a <see cref="T:System.Reflection.TypeInfo" /> object that represents this type.</summary>
      <returns>An object that represents this type.</returns>
    </member>
    <member name="P:System.Reflection.Emit.TypeBuilder.DeclaringMethod">
      <summary>Gets the method that declared the current generic type parameter.</summary>
      <returns>A <see cref="T:System.Reflection.MethodBase" /> that represents the method that declared the current type, if the current type is a generic type parameter; otherwise, <see langword="null" />.</returns>
    </member>
    <member name="P:System.Reflection.Emit.TypeBuilder.DeclaringType">
      <summary>Returns the type that declared this type.</summary>
      <returns>Read-only. The type that declared this type.</returns>
    </member>
    <member name="M:System.Reflection.Emit.TypeBuilder.DefineConstructor(System.Reflection.MethodAttributes,System.Reflection.CallingConventions,System.Type[])">
      <summary>Adds a new constructor to the type, with the given attributes and signature.</summary>
      <param name="attributes">The attributes of the constructor.</param>
      <param name="callingConvention">The calling convention of the constructor.</param>
      <param name="parameterTypes">The parameter types of the constructor.</param>
      <returns>The defined constructor.</returns>
      <exception cref="T:System.InvalidOperationException">The type was previously created using <see cref="M:System.Reflection.Emit.TypeBuilder.CreateType" />.</exception>
    </member>
    <member name="M:System.Reflection.Emit.TypeBuilder.DefineConstructor(System.Reflection.MethodAttributes,System.Reflection.CallingConventions,System.Type[],System.Type[][],System.Type[][])">
      <summary>Adds a new constructor to the type, with the given attributes, signature, and custom modifiers.</summary>
      <param name="attributes">The attributes of the constructor.</param>
      <param name="callingConvention">The calling convention of the constructor.</param>
      <param name="parameterTypes">The parameter types of the constructor.</param>
      <param name="requiredCustomModifiers">An array of arrays of types. Each array of types represents the required custom modifiers for the corresponding parameter, such as <see cref="T:System.Runtime.CompilerServices.IsConst" />. If a particular parameter has no required custom modifiers, specify <see langword="null" /> instead of an array of types. If none of the parameters have required custom modifiers, specify <see langword="null" /> instead of an array of arrays.</param>
      <param name="optionalCustomModifiers">An array of arrays of types. Each array of types represents the optional custom modifiers for the corresponding parameter, such as <see cref="T:System.Runtime.CompilerServices.IsConst" />. If a particular parameter has no optional custom modifiers, specify <see langword="null" /> instead of an array of types. If none of the parameters have optional custom modifiers, specify <see langword="null" /> instead of an array of arrays.</param>
      <returns>The defined constructor.</returns>
      <exception cref="T:System.ArgumentException">The size of <paramref name="requiredCustomModifiers" /> or <paramref name="optionalCustomModifiers" /> does not equal the size of <paramref name="parameterTypes" />.</exception>
      <exception cref="T:System.InvalidOperationException">The type was previously created using <see cref="M:System.Reflection.Emit.TypeBuilder.CreateType" />.
-or-
For the current dynamic type, the <see cref="P:System.Reflection.Emit.TypeBuilder.IsGenericType" /> property is <see langword="true" />, but the <see cref="P:System.Reflection.Emit.TypeBuilder.IsGenericTypeDefinition" /> property is <see langword="false" />.</exception>
    </member>
    <member name="M:System.Reflection.Emit.TypeBuilder.DefineDefaultConstructor(System.Reflection.MethodAttributes)">
      <summary>Defines the parameterless constructor. The constructor defined here will simply call the parameterless constructor of the parent.</summary>
      <param name="attributes">A <see langword="MethodAttributes" /> object representing the attributes to be applied to the constructor.</param>
      <returns>Returns the constructor.</returns>
      <exception cref="T:System.NotSupportedException">The parent type (base type) does not have a parameterless constructor.</exception>
      <exception cref="T:System.InvalidOperationException">The type was previously created using <see cref="M:System.Reflection.Emit.TypeBuilder.CreateType" />.
-or-
For the current dynamic type, the <see cref="P:System.Reflection.Emit.TypeBuilder.IsGenericType" /> property is <see langword="true" />, but the <see cref="P:System.Reflection.Emit.TypeBuilder.IsGenericTypeDefinition" /> property is <see langword="false" />.</exception>
    </member>
    <member name="M:System.Reflection.Emit.TypeBuilder.DefineEvent(System.String,System.Reflection.EventAttributes,System.Type)">
      <summary>Adds a new event to the type, with the given name, attributes and event type.</summary>
      <param name="name">The name of the event. <paramref name="name" /> cannot contain embedded nulls.</param>
      <param name="attributes">The attributes of the event.</param>
      <param name="eventtype">The type of the event.</param>
      <returns>The defined event.</returns>
      <exception cref="T:System.ArgumentException">The length of <paramref name="name" /> is zero.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="name" /> is <see langword="null" />.
-or-
<paramref name="eventtype" /> is <see langword="null" />.</exception>
      <exception cref="T:System.InvalidOperationException">The type was previously created using <see cref="M:System.Reflection.Emit.TypeBuilder.CreateType" />.</exception>
    </member>
    <member name="M:System.Reflection.Emit.TypeBuilder.DefineField(System.String,System.Type,System.Reflection.FieldAttributes)">
      <summary>Adds a new field to the type, with the given name, attributes, and field type.</summary>
      <param name="fieldName">The name of the field. <paramref name="fieldName" /> cannot contain embedded nulls.</param>
      <param name="type">The type of the field</param>
      <param name="attributes">The attributes of the field.</param>
      <returns>The defined field.</returns>
      <exception cref="T:System.ArgumentException">The length of <paramref name="fieldName" /> is zero.
-or-
<paramref name="type" /> is System.Void.
-or-
A total size was specified for the parent class of this field.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="fieldName" /> is <see langword="null" />.</exception>
      <exception cref="T:System.InvalidOperationException">The type was previously created using <see cref="M:System.Reflection.Emit.TypeBuilder.CreateType" />.</exception>
    </member>
    <member name="M:System.Reflection.Emit.TypeBuilder.DefineField(System.String,System.Type,System.Type[],System.Type[],System.Reflection.FieldAttributes)">
      <summary>Adds a new field to the type, with the given name, attributes, field type, and custom modifiers.</summary>
      <param name="fieldName">The name of the field. <paramref name="fieldName" /> cannot contain embedded nulls.</param>
      <param name="type">The type of the field</param>
      <param name="requiredCustomModifiers">An array of types representing the required custom modifiers for the field, such as <see cref="T:Microsoft.VisualC.IsConstModifier" />.</param>
      <param name="optionalCustomModifiers">An array of types representing the optional custom modifiers for the field, such as <see cref="T:Microsoft.VisualC.IsConstModifier" />.</param>
      <param name="attributes">The attributes of the field.</param>
      <returns>The defined field.</returns>
      <exception cref="T:System.ArgumentException">The length of <paramref name="fieldName" /> is zero.
-or-
<paramref name="type" /> is System.Void.
-or-
A total size was specified for the parent class of this field.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="fieldName" /> is <see langword="null" />.</exception>
      <exception cref="T:System.InvalidOperationException">The type was previously created using <see cref="M:System.Reflection.Emit.TypeBuilder.CreateType" />.</exception>
    </member>
    <member name="M:System.Reflection.Emit.TypeBuilder.DefineGenericParameters(System.String[])">
      <summary>Defines the generic type parameters for the current type, specifying their number and their names, and returns an array of <see cref="T:System.Reflection.Emit.GenericTypeParameterBuilder" /> objects that can be used to set their constraints.</summary>
      <param name="names">An array of names for the generic type parameters.</param>
      <returns>An array of <see cref="T:System.Reflection.Emit.GenericTypeParameterBuilder" /> objects that can be used to define the constraints of the generic type parameters for the current type.</returns>
      <exception cref="T:System.InvalidOperationException">Generic type parameters have already been defined for this type.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="names" /> is <see langword="null" />.
-or-
An element of <paramref name="names" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="names" /> is an empty array.</exception>
    </member>
    <member name="M:System.Reflection.Emit.TypeBuilder.DefineInitializedData(System.String,System.Byte[],System.Reflection.FieldAttributes)">
      <summary>Defines initialized data field in the .sdata section of the portable executable (PE) file.</summary>
      <param name="name">The name used to refer to the data. <paramref name="name" /> cannot contain embedded nulls.</param>
      <param name="data">The blob of data.</param>
      <param name="attributes">The attributes for the field.</param>
      <returns>A field to reference the data.</returns>
      <exception cref="T:System.ArgumentException">Length of <paramref name="name" /> is zero.
-or-
The size of the data is less than or equal to zero, or greater than or equal to 0x3f0000.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="name" /> or <paramref name="data" /> is <see langword="null" />.</exception>
      <exception cref="T:System.InvalidOperationException">
        <see cref="M:System.Reflection.Emit.TypeBuilder.CreateType" /> has been previously called.</exception>
    </member>
    <member name="M:System.Reflection.Emit.TypeBuilder.DefineMethod(System.String,System.Reflection.MethodAttributes)">
      <summary>Adds a new method to the type, with the specified name and method attributes.</summary>
      <param name="name">The name of the method. <paramref name="name" /> cannot contain embedded nulls.</param>
      <param name="attributes">The attributes of the method.</param>
      <returns>A <see cref="T:System.Reflection.Emit.MethodBuilder" /> representing the newly defined method.</returns>
      <exception cref="T:System.ArgumentException">The length of <paramref name="name" /> is zero.
-or-
The type of the parent of this method is an interface, and this method is not virtual (<see langword="Overridable" /> in Visual Basic).</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="name" /> is <see langword="null" />.</exception>
      <exception cref="T:System.InvalidOperationException">The type was previously created using <see cref="M:System.Reflection.Emit.TypeBuilder.CreateType" />.
-or-
For the current dynamic type, the <see cref="P:System.Reflection.Emit.TypeBuilder.IsGenericType" /> property is <see langword="true" />, but the <see cref="P:System.Reflection.Emit.TypeBuilder.IsGenericTypeDefinition" /> property is <see langword="false" />.</exception>
    </member>
    <member name="M:System.Reflection.Emit.TypeBuilder.DefineMethod(System.String,System.Reflection.MethodAttributes,System.Reflection.CallingConventions)">
      <summary>Adds a new method to the type, with the specified name, method attributes, and calling convention.</summary>
      <param name="name">The name of the method. <paramref name="name" /> cannot contain embedded nulls.</param>
      <param name="attributes">The attributes of the method.</param>
      <param name="callingConvention">The calling convention of the method.</param>
      <returns>A <see cref="T:System.Reflection.Emit.MethodBuilder" /> representing the newly defined method.</returns>
      <exception cref="T:System.ArgumentException">The length of <paramref name="name" /> is zero.
-or-
The type of the parent of this method is an interface and this method is not virtual (<see langword="Overridable" /> in Visual Basic).</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="name" /> is <see langword="null" />.</exception>
      <exception cref="T:System.InvalidOperationException">The type was previously created using <see cref="M:System.Reflection.Emit.TypeBuilder.CreateType" />.
-or-
For the current dynamic type, the <see cref="P:System.Reflection.Emit.TypeBuilder.IsGenericType" /> property is <see langword="true" />, but the <see cref="P:System.Reflection.Emit.TypeBuilder.IsGenericTypeDefinition" /> property is <see langword="false" />.</exception>
    </member>
    <member name="M:System.Reflection.Emit.TypeBuilder.DefineMethod(System.String,System.Reflection.MethodAttributes,System.Reflection.CallingConventions,System.Type,System.Type[])">
      <summary>Adds a new method to the type, with the specified name, method attributes, calling convention, and method signature.</summary>
      <param name="name">The name of the method. <paramref name="name" /> cannot contain embedded nulls.</param>
      <param name="attributes">The attributes of the method.</param>
      <param name="callingConvention">The calling convention of the method.</param>
      <param name="returnType">The return type of the method.</param>
      <param name="parameterTypes">The types of the parameters of the method.</param>
      <returns>A <see cref="T:System.Reflection.Emit.MethodBuilder" /> representing the newly defined method.</returns>
      <exception cref="T:System.ArgumentException">The length of <paramref name="name" /> is zero.
-or-
The type of the parent of this method is an interface, and this method is not virtual (<see langword="Overridable" /> in Visual Basic).</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="name" /> is <see langword="null" />.</exception>
      <exception cref="T:System.InvalidOperationException">The type was previously created using <see cref="M:System.Reflection.Emit.TypeBuilder.CreateType" />.
-or-
For the current dynamic type, the <see cref="P:System.Reflection.Emit.TypeBuilder.IsGenericType" /> property is <see langword="true" />, but the <see cref="P:System.Reflection.Emit.TypeBuilder.IsGenericTypeDefinition" /> property is <see langword="false" />.</exception>
    </member>
    <member name="M:System.Reflection.Emit.TypeBuilder.DefineMethod(System.String,System.Reflection.MethodAttributes,System.Reflection.CallingConventions,System.Type,System.Type[],System.Type[],System.Type[],System.Type[][],System.Type[][])">
      <summary>Adds a new method to the type, with the specified name, method attributes, calling convention, method signature, and custom modifiers.</summary>
      <param name="name">The name of the method. <paramref name="name" /> cannot contain embedded nulls.</param>
      <param name="attributes">The attributes of the method.</param>
      <param name="callingConvention">The calling convention of the method.</param>
      <param name="returnType">The return type of the method.</param>
      <param name="returnTypeRequiredCustomModifiers">An array of types representing the required custom modifiers, such as <see cref="T:System.Runtime.CompilerServices.IsConst" />, for the return type of the method. If the return type has no required custom modifiers, specify <see langword="null" />.</param>
      <param name="returnTypeOptionalCustomModifiers">An array of types representing the optional custom modifiers, such as <see cref="T:System.Runtime.CompilerServices.IsConst" />, for the return type of the method. If the return type has no optional custom modifiers, specify <see langword="null" />.</param>
      <param name="parameterTypes">The types of the parameters of the method.</param>
      <param name="parameterTypeRequiredCustomModifiers">An array of arrays of types. Each array of types represents the required custom modifiers for the corresponding parameter, such as <see cref="T:System.Runtime.CompilerServices.IsConst" />. If a particular parameter has no required custom modifiers, specify <see langword="null" /> instead of an array of types. If none of the parameters have required custom modifiers, specify <see langword="null" /> instead of an array of arrays.</param>
      <param name="parameterTypeOptionalCustomModifiers">An array of arrays of types. Each array of types represents the optional custom modifiers for the corresponding parameter, such as <see cref="T:System.Runtime.CompilerServices.IsConst" />. If a particular parameter has no optional custom modifiers, specify <see langword="null" /> instead of an array of types. If none of the parameters have optional custom modifiers, specify <see langword="null" /> instead of an array of arrays.</param>
      <returns>A <see cref="T:System.Reflection.Emit.MethodBuilder" /> object representing the newly added method.</returns>
      <exception cref="T:System.ArgumentException">The length of <paramref name="name" /> is zero.
-or-
The type of the parent of this method is an interface, and this method is not virtual (<see langword="Overridable" /> in Visual Basic).
-or-
The size of <paramref name="parameterTypeRequiredCustomModifiers" /> or <paramref name="parameterTypeOptionalCustomModifiers" /> does not equal the size of <paramref name="parameterTypes" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="name" /> is <see langword="null" />.</exception>
      <exception cref="T:System.InvalidOperationException">The type was previously created using <see cref="M:System.Reflection.Emit.TypeBuilder.CreateType" />.
-or-
For the current dynamic type, the <see cref="P:System.Reflection.Emit.TypeBuilder.IsGenericType" /> property is <see langword="true" />, but the <see cref="P:System.Reflection.Emit.TypeBuilder.IsGenericTypeDefinition" /> property is <see langword="false" />.</exception>
    </member>
    <member name="M:System.Reflection.Emit.TypeBuilder.DefineMethod(System.String,System.Reflection.MethodAttributes,System.Type,System.Type[])">
      <summary>Adds a new method to the type, with the specified name, method attributes, and method signature.</summary>
      <param name="name">The name of the method. <paramref name="name" /> cannot contain embedded nulls.</param>
      <param name="attributes">The attributes of the method.</param>
      <param name="returnType">The return type of the method.</param>
      <param name="parameterTypes">The types of the parameters of the method.</param>
      <returns>The defined method.</returns>
      <exception cref="T:System.ArgumentException">The length of <paramref name="name" /> is zero.
-or-
The type of the parent of this method is an interface, and this method is not virtual (<see langword="Overridable" /> in Visual Basic).</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="name" /> is <see langword="null" />.</exception>
      <exception cref="T:System.InvalidOperationException">The type was previously created using <see cref="M:System.Reflection.Emit.TypeBuilder.CreateType" />.
-or-
For the current dynamic type, the <see cref="P:System.Reflection.Emit.TypeBuilder.IsGenericType" /> property is <see langword="true" />, but the <see cref="P:System.Reflection.Emit.TypeBuilder.IsGenericTypeDefinition" /> property is <see langword="false" />.</exception>
    </member>
    <member name="M:System.Reflection.Emit.TypeBuilder.DefineMethodOverride(System.Reflection.MethodInfo,System.Reflection.MethodInfo)">
      <summary>Specifies a given method body that implements a given method declaration, potentially with a different name.</summary>
      <param name="methodInfoBody">The method body to be used. This should be a <see langword="MethodBuilder" /> object.</param>
      <param name="methodInfoDeclaration">The method whose declaration is to be used.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="methodInfoBody" /> does not belong to this class.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="methodInfoBody" /> or <paramref name="methodInfoDeclaration" /> is <see langword="null" />.</exception>
      <exception cref="T:System.InvalidOperationException">The type was previously created using <see cref="M:System.Reflection.Emit.TypeBuilder.CreateType" />.
-or-
The declaring type of <paramref name="methodInfoBody" /> is not the type represented by this <see cref="T:System.Reflection.Emit.TypeBuilder" />.</exception>
    </member>
    <member name="M:System.Reflection.Emit.TypeBuilder.DefineNestedType(System.String)">
      <summary>Defines a nested type, given its name.</summary>
      <param name="name">The short name of the type. <paramref name="name" /> cannot contain embedded nulls.</param>
      <returns>The defined nested type.</returns>
      <exception cref="T:System.ArgumentException">Length of <paramref name="name" /> is zero or greater than 1023.
-or-
This operation would create a type with a duplicate <see cref="P:System.Reflection.Emit.TypeBuilder.FullName" /> in the current assembly.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="name" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Reflection.Emit.TypeBuilder.DefineNestedType(System.String,System.Reflection.TypeAttributes)">
      <summary>Defines a nested type, given its name and attributes.</summary>
      <param name="name">The short name of the type. <paramref name="name" /> cannot contain embedded nulls.</param>
      <param name="attr">The attributes of the type.</param>
      <returns>The defined nested type.</returns>
      <exception cref="T:System.ArgumentException">The nested attribute is not specified.
-or-
This type is sealed.
-or-
This type is an array.
-or-
This type is an interface, but the nested type is not an interface.
-or-
The length of <paramref name="name" /> is zero or greater than 1023.
-or-
This operation would create a type with a duplicate <see cref="P:System.Reflection.Emit.TypeBuilder.FullName" /> in the current assembly.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="name" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Reflection.Emit.TypeBuilder.DefineNestedType(System.String,System.Reflection.TypeAttributes,System.Type)">
      <summary>Defines a nested type, given its name, attributes, and the type that it extends.</summary>
      <param name="name">The short name of the type. <paramref name="name" /> cannot contain embedded nulls.</param>
      <param name="attr">The attributes of the type.</param>
      <param name="parent">The type that the nested type extends.</param>
      <returns>The defined nested type.</returns>
      <exception cref="T:System.ArgumentException">The nested attribute is not specified.
-or-
This type is sealed.
-or-
This type is an array.
-or-
This type is an interface, but the nested type is not an interface.
-or-
The length of <paramref name="name" /> is zero or greater than 1023.
-or-
This operation would create a type with a duplicate <see cref="P:System.Reflection.Emit.TypeBuilder.FullName" /> in the current assembly.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="name" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Reflection.Emit.TypeBuilder.DefineNestedType(System.String,System.Reflection.TypeAttributes,System.Type,System.Int32)">
      <summary>Defines a nested type, given its name, attributes, the total size of the type, and the type that it extends.</summary>
      <param name="name">The short name of the type. <paramref name="name" /> cannot contain embedded nulls.</param>
      <param name="attr">The attributes of the type.</param>
      <param name="parent">The type that the nested type extends.</param>
      <param name="typeSize">The total size of the type.</param>
      <returns>The defined nested type.</returns>
      <exception cref="T:System.ArgumentException">The nested attribute is not specified.
-or-
This type is sealed.
-or-
This type is an array.
-or-
This type is an interface, but the nested type is not an interface.
-or-
The length of <paramref name="name" /> is zero or greater than 1023.
-or-
This operation would create a type with a duplicate <see cref="P:System.Reflection.Emit.TypeBuilder.FullName" /> in the current assembly.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="name" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Reflection.Emit.TypeBuilder.DefineNestedType(System.String,System.Reflection.TypeAttributes,System.Type,System.Reflection.Emit.PackingSize)">
      <summary>Defines a nested type, given its name, attributes, the type that it extends, and the packing size.</summary>
      <param name="name">The short name of the type. <paramref name="name" /> cannot contain embedded nulls.</param>
      <param name="attr">The attributes of the type.</param>
      <param name="parent">The type that the nested type extends.</param>
      <param name="packSize">The packing size of the type.</param>
      <returns>The defined nested type.</returns>
      <exception cref="T:System.ArgumentException">The nested attribute is not specified.
-or-
This type is sealed.
-or-
This type is an array.
-or-
This type is an interface, but the nested type is not an interface.
-or-
The length of <paramref name="name" /> is zero or greater than 1023.
-or-
This operation would create a type with a duplicate <see cref="P:System.Reflection.Emit.TypeBuilder.FullName" /> in the current assembly.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="name" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Reflection.Emit.TypeBuilder.DefineNestedType(System.String,System.Reflection.TypeAttributes,System.Type,System.Reflection.Emit.PackingSize,System.Int32)">
      <summary>Defines a nested type, given its name, attributes, size, and the type that it extends.</summary>
      <param name="name">The short name of the type. <paramref name="name" /> cannot contain embedded null values.</param>
      <param name="attr">The attributes of the type.</param>
      <param name="parent">The type that the nested type extends.</param>
      <param name="packSize">The packing size of the type.</param>
      <param name="typeSize">The total size of the type.</param>
      <returns>The defined nested type.</returns>
    </member>
    <member name="M:System.Reflection.Emit.TypeBuilder.DefineNestedType(System.String,System.Reflection.TypeAttributes,System.Type,System.Type[])">
      <summary>Defines a nested type, given its name, attributes, the type that it extends, and the interfaces that it implements.</summary>
      <param name="name">The short name of the type. <paramref name="name" /> cannot contain embedded nulls.</param>
      <param name="attr">The attributes of the type.</param>
      <param name="parent">The type that the nested type extends.</param>
      <param name="interfaces">The interfaces that the nested type implements.</param>
      <returns>The defined nested type.</returns>
      <exception cref="T:System.ArgumentException">The nested attribute is not specified.
-or-
This type is sealed.
-or-
This type is an array.
-or-
This type is an interface, but the nested type is not an interface.
-or-
The length of <paramref name="name" /> is zero or greater than 1023.
-or-
This operation would create a type with a duplicate <see cref="P:System.Reflection.Emit.TypeBuilder.FullName" /> in the current assembly.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="name" /> is <see langword="null" />.
-or-
An element of the <paramref name="interfaces" /> array is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Reflection.Emit.TypeBuilder.DefinePInvokeMethod(System.String,System.String,System.Reflection.MethodAttributes,System.Reflection.CallingConventions,System.Type,System.Type[],System.Runtime.InteropServices.CallingConvention,System.Runtime.InteropServices.CharSet)">
      <summary>Defines a <see langword="PInvoke" /> method given its name, the name of the DLL in which the method is defined, the attributes of the method, the calling convention of the method, the return type of the method, the types of the parameters of the method, and the <see langword="PInvoke" /> flags.</summary>
      <param name="name">The name of the <see langword="PInvoke" /> method. <paramref name="name" /> cannot contain embedded nulls.</param>
      <param name="dllName">The name of the DLL in which the <see langword="PInvoke" /> method is defined.</param>
      <param name="attributes">The attributes of the method.</param>
      <param name="callingConvention">The method's calling convention.</param>
      <param name="returnType">The method's return type.</param>
      <param name="parameterTypes">The types of the method's parameters.</param>
      <param name="nativeCallConv">The native calling convention.</param>
      <param name="nativeCharSet">The method's native character set.</param>
      <returns>The defined <see langword="PInvoke" /> method.</returns>
      <exception cref="T:System.ArgumentException">The method is not static.
-or-
The parent type is an interface.
-or-
The method is abstract.
-or-
The method was previously defined.
-or-
The length of <paramref name="name" /> or <paramref name="dllName" /> is zero.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="name" /> or <paramref name="dllName" /> is <see langword="null" />.</exception>
      <exception cref="T:System.InvalidOperationException">The containing type has been previously created using <see cref="M:System.Reflection.Emit.TypeBuilder.CreateType" />.</exception>
    </member>
    <member name="M:System.Reflection.Emit.TypeBuilder.DefinePInvokeMethod(System.String,System.String,System.String,System.Reflection.MethodAttributes,System.Reflection.CallingConventions,System.Type,System.Type[],System.Runtime.InteropServices.CallingConvention,System.Runtime.InteropServices.CharSet)">
      <summary>Defines a <see langword="PInvoke" /> method given its name, the name of the DLL in which the method is defined, the name of the entry point, the attributes of the method, the calling convention of the method, the return type of the method, the types of the parameters of the method, and the <see langword="PInvoke" /> flags.</summary>
      <param name="name">The name of the <see langword="PInvoke" /> method. <paramref name="name" /> cannot contain embedded nulls.</param>
      <param name="dllName">The name of the DLL in which the <see langword="PInvoke" /> method is defined.</param>
      <param name="entryName">The name of the entry point in the DLL.</param>
      <param name="attributes">The attributes of the method.</param>
      <param name="callingConvention">The method's calling convention.</param>
      <param name="returnType">The method's return type.</param>
      <param name="parameterTypes">The types of the method's parameters.</param>
      <param name="nativeCallConv">The native calling convention.</param>
      <param name="nativeCharSet">The method's native character set.</param>
      <returns>The defined <see langword="PInvoke" /> method.</returns>
      <exception cref="T:System.ArgumentException">The method is not static.
-or-
The parent type is an interface.
-or-
The method is abstract.
-or-
The method was previously defined.
-or-
The length of <paramref name="name" />, <paramref name="dllName" />, or <paramref name="entryName" /> is zero.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="name" />, <paramref name="dllName" />, or <paramref name="entryName" /> is <see langword="null" />.</exception>
      <exception cref="T:System.InvalidOperationException">The containing type has been previously created using <see cref="M:System.Reflection.Emit.TypeBuilder.CreateType" />.</exception>
    </member>
    <member name="M:System.Reflection.Emit.TypeBuilder.DefinePInvokeMethod(System.String,System.String,System.Type[][],System.Runtime.InteropServices.CallingConvention,System.Runtime.InteropServices.CharSet,System.String,System.Reflection.MethodAttributes,System.Reflection.CallingConventions,System.Type,System.Type[],System.Type[],System.Type[],System.Type[][])">
      <summary>Defines a <see langword="PInvoke" /> method given its name, the name of the DLL in which the method is defined, the name of the entry point, the attributes of the method, the calling convention of the method, the return type of the method, the types of the parameters of the method, the <see langword="PInvoke" /> flags, and custom modifiers for the parameters and return type.</summary>
      <param name="name">The name of the <see langword="PInvoke" /> method. <paramref name="name" /> cannot contain embedded nulls.</param>
      <param name="dllName">The name of the DLL in which the <see langword="PInvoke" /> method is defined.</param>
      <param name="parameterTypeOptionalCustomModifiers">An array of arrays of types. Each array of types represents the optional custom modifiers for the corresponding parameter, such as <see cref="T:System.Runtime.CompilerServices.IsConst" />. If a particular parameter has no optional custom modifiers, specify <see langword="null" /> instead of an array of types. If none of the parameters have optional custom modifiers, specify <see langword="null" /> instead of an array of arrays.</param>
      <param name="nativeCallConv">The native calling convention.</param>
      <param name="nativeCharSet">The method's native character set.</param>
      <param name="entryName">The name of the entry point in the DLL.</param>
      <param name="attributes">The attributes of the method.</param>
      <param name="callingConvention">The method's calling convention.</param>
      <param name="returnType">The method's return type.</param>
      <param name="returnTypeRequiredCustomModifiers">An array of types representing the required custom modifiers, such as <see cref="T:System.Runtime.CompilerServices.IsConst" />, for the return type of the method. If the return type has no required custom modifiers, specify <see langword="null" />.</param>
      <param name="returnTypeOptionalCustomModifiers">An array of types representing the optional custom modifiers, such as <see cref="T:System.Runtime.CompilerServices.IsConst" />, for the return type of the method. If the return type has no optional custom modifiers, specify <see langword="null" />.</param>
      <param name="parameterTypes">The types of the method's parameters.</param>
      <param name="parameterTypeRequiredCustomModifiers">An array of arrays of types. Each array of types represents the required custom modifiers for the corresponding parameter, such as <see cref="T:System.Runtime.CompilerServices.IsConst" />. If a particular parameter has no required custom modifiers, specify <see langword="null" /> instead of an array of types. If none of the parameters have required custom modifiers, specify <see langword="null" /> instead of an array of arrays.</param>
      <returns>A <see cref="T:System.Reflection.Emit.MethodBuilder" /> representing the defined <see langword="PInvoke" /> method.</returns>
      <exception cref="T:System.ArgumentException">The method is not static.
-or-
The parent type is an interface.
-or-
The method is abstract.
-or-
The method was previously defined.
-or-
The length of <paramref name="name" />, <paramref name="dllName" />, or <paramref name="entryName" /> is zero.
-or-
The size of <paramref name="parameterTypeRequiredCustomModifiers" /> or <paramref name="parameterTypeOptionalCustomModifiers" /> does not equal the size of <paramref name="parameterTypes" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="name" />, <paramref name="dllName" />, or <paramref name="entryName" /> is <see langword="null" />.</exception>
      <exception cref="T:System.InvalidOperationException">The type was previously created using <see cref="M:System.Reflection.Emit.TypeBuilder.CreateType" />.
-or-
For the current dynamic type, the <see cref="P:System.Reflection.Emit.TypeBuilder.IsGenericType" /> property is <see langword="true" />, but the <see cref="P:System.Reflection.Emit.TypeBuilder.IsGenericTypeDefinition" /> property is <see langword="false" />.</exception>
    </member>
    <member name="M:System.Reflection.Emit.TypeBuilder.DefineProperty(System.String,System.Reflection.PropertyAttributes,System.Reflection.CallingConventions,System.Type,System.Type[])">
      <summary>Adds a new property to the type, with the given name, attributes, calling convention, and property signature.</summary>
      <param name="name">The name of the property. <paramref name="name" /> cannot contain embedded nulls.</param>
      <param name="attributes">The attributes of the property.</param>
      <param name="callingConvention">The calling convention of the property accessors.</param>
      <param name="returnType">The return type of the property.</param>
      <param name="parameterTypes">The types of the parameters of the property.</param>
      <returns>The defined property.</returns>
      <exception cref="T:System.ArgumentException">The length of <paramref name="name" /> is zero.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="name" /> is <see langword="null" />.
-or-
Any of the elements of the <paramref name="parameterTypes" /> array is <see langword="null" />.</exception>
      <exception cref="T:System.InvalidOperationException">The type was previously created using <see cref="M:System.Reflection.Emit.TypeBuilder.CreateType" />.</exception>
    </member>
    <member name="M:System.Reflection.Emit.TypeBuilder.DefineProperty(System.String,System.Reflection.PropertyAttributes,System.Reflection.CallingConventions,System.Type,System.Type[],System.Type[],System.Type[],System.Type[][],System.Type[][])">
      <summary>Adds a new property to the type, with the given name, calling convention, property signature, and custom modifiers.</summary>
      <param name="name">The name of the property. <paramref name="name" /> cannot contain embedded nulls.</param>
      <param name="attributes">The attributes of the property.</param>
      <param name="callingConvention">The calling convention of the property accessors.</param>
      <param name="returnType">The return type of the property.</param>
      <param name="returnTypeRequiredCustomModifiers">An array of types representing the required custom modifiers, such as <see cref="T:System.Runtime.CompilerServices.IsConst" />, for the return type of the property. If the return type has no required custom modifiers, specify <see langword="null" />.</param>
      <param name="returnTypeOptionalCustomModifiers">An array of types representing the optional custom modifiers, such as <see cref="T:System.Runtime.CompilerServices.IsConst" />, for the return type of the property. If the return type has no optional custom modifiers, specify <see langword="null" />.</param>
      <param name="parameterTypes">The types of the parameters of the property.</param>
      <param name="parameterTypeRequiredCustomModifiers">An array of arrays of types. Each array of types represents the required custom modifiers for the corresponding parameter, such as <see cref="T:System.Runtime.CompilerServices.IsConst" />. If a particular parameter has no required custom modifiers, specify <see langword="null" /> instead of an array of types. If none of the parameters have required custom modifiers, specify <see langword="null" /> instead of an array of arrays.</param>
      <param name="parameterTypeOptionalCustomModifiers">An array of arrays of types. Each array of types represents the optional custom modifiers for the corresponding parameter, such as <see cref="T:System.Runtime.CompilerServices.IsConst" />. If a particular parameter has no optional custom modifiers, specify <see langword="null" /> instead of an array of types. If none of the parameters have optional custom modifiers, specify <see langword="null" /> instead of an array of arrays.</param>
      <returns>The defined property.</returns>
      <exception cref="T:System.ArgumentException">The length of <paramref name="name" /> is zero.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="name" /> is <see langword="null" />.
-or-
Any of the elements of the <paramref name="parameterTypes" /> array is <see langword="null" />.</exception>
      <exception cref="T:System.InvalidOperationException">The type was previously created using <see cref="M:System.Reflection.Emit.TypeBuilder.CreateType" />.</exception>
    </member>
    <member name="M:System.Reflection.Emit.TypeBuilder.DefineProperty(System.String,System.Reflection.PropertyAttributes,System.Type,System.Type[])">
      <summary>Adds a new property to the type, with the given name and property signature.</summary>
      <param name="name">The name of the property. <paramref name="name" /> cannot contain embedded nulls.</param>
      <param name="attributes">The attributes of the property.</param>
      <param name="returnType">The return type of the property.</param>
      <param name="parameterTypes">The types of the parameters of the property.</param>
      <returns>The defined property.</returns>
      <exception cref="T:System.ArgumentException">The length of <paramref name="name" /> is zero.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="name" /> is <see langword="null" />.
-or-
Any of the elements of the <paramref name="parameterTypes" /> array is <see langword="null" />.</exception>
      <exception cref="T:System.InvalidOperationException">The type was previously created using <see cref="M:System.Reflection.Emit.TypeBuilder.CreateType" />.</exception>
    </member>
    <member name="M:System.Reflection.Emit.TypeBuilder.DefineProperty(System.String,System.Reflection.PropertyAttributes,System.Type,System.Type[],System.Type[],System.Type[],System.Type[][],System.Type[][])">
      <summary>Adds a new property to the type, with the given name, property signature, and custom modifiers.</summary>
      <param name="name">The name of the property. <paramref name="name" /> cannot contain embedded nulls.</param>
      <param name="attributes">The attributes of the property.</param>
      <param name="returnType">The return type of the property.</param>
      <param name="returnTypeRequiredCustomModifiers">An array of types representing the required custom modifiers, such as <see cref="T:System.Runtime.CompilerServices.IsConst" />, for the return type of the property. If the return type has no required custom modifiers, specify <see langword="null" />.</param>
      <param name="returnTypeOptionalCustomModifiers">An array of types representing the optional custom modifiers, such as <see cref="T:System.Runtime.CompilerServices.IsConst" />, for the return type of the property. If the return type has no optional custom modifiers, specify <see langword="null" />.</param>
      <param name="parameterTypes">The types of the parameters of the property.</param>
      <param name="parameterTypeRequiredCustomModifiers">An array of arrays of types. Each array of types represents the required custom modifiers for the corresponding parameter, such as <see cref="T:System.Runtime.CompilerServices.IsConst" />. If a particular parameter has no required custom modifiers, specify <see langword="null" /> instead of an array of types. If none of the parameters have required custom modifiers, specify <see langword="null" /> instead of an array of arrays.</param>
      <param name="parameterTypeOptionalCustomModifiers">An array of arrays of types. Each array of types represents the optional custom modifiers for the corresponding parameter, such as <see cref="T:System.Runtime.CompilerServices.IsConst" />. If a particular parameter has no optional custom modifiers, specify <see langword="null" /> instead of an array of types. If none of the parameters have optional custom modifiers, specify <see langword="null" /> instead of an array of arrays.</param>
      <returns>The defined property.</returns>
      <exception cref="T:System.ArgumentException">The length of <paramref name="name" /> is zero.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="name" /> is <see langword="null" />
-or-
Any of the elements of the <paramref name="parameterTypes" /> array is <see langword="null" /></exception>
      <exception cref="T:System.InvalidOperationException">The type was previously created using <see cref="M:System.Reflection.Emit.TypeBuilder.CreateType" />.</exception>
    </member>
    <member name="M:System.Reflection.Emit.TypeBuilder.DefineTypeInitializer">
      <summary>Defines the initializer for this type.</summary>
      <returns>Returns a type initializer.</returns>
      <exception cref="T:System.InvalidOperationException">The containing type has been previously created using <see cref="M:System.Reflection.Emit.TypeBuilder.CreateType" />.</exception>
    </member>
    <member name="M:System.Reflection.Emit.TypeBuilder.DefineUninitializedData(System.String,System.Int32,System.Reflection.FieldAttributes)">
      <summary>Defines an uninitialized data field in the <see langword=".sdata" /> section of the portable executable (PE) file.</summary>
      <param name="name">The name used to refer to the data. <paramref name="name" /> cannot contain embedded nulls.</param>
      <param name="size">The size of the data field.</param>
      <param name="attributes">The attributes for the field.</param>
      <returns>A field to reference the data.</returns>
      <exception cref="T:System.ArgumentException">Length of <paramref name="name" /> is zero.
-or-
<paramref name="size" /> is less than or equal to zero, or greater than or equal to 0x003f0000.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="name" /> is <see langword="null" />.</exception>
      <exception cref="T:System.InvalidOperationException">The type was previously created using <see cref="M:System.Reflection.Emit.TypeBuilder.CreateType" />.</exception>
    </member>
    <member name="P:System.Reflection.Emit.TypeBuilder.FullName">
      <summary>Retrieves the full path of this type.</summary>
      <returns>Read-only. Retrieves the full path of this type.</returns>
    </member>
    <member name="P:System.Reflection.Emit.TypeBuilder.GenericParameterAttributes">
      <summary>Gets a value that indicates the covariance and special constraints of the current generic type parameter.</summary>
      <returns>A bitwise combination of <see cref="T:System.Reflection.GenericParameterAttributes" /> values that describes the covariance and special constraints of the current generic type parameter.</returns>
    </member>
    <member name="P:System.Reflection.Emit.TypeBuilder.GenericParameterPosition">
      <summary>Gets the position of a type parameter in the type parameter list of the generic type that declared the parameter.</summary>
      <returns>If the current <see cref="T:System.Reflection.Emit.TypeBuilder" /> object represents a generic type parameter, the position of the type parameter in the type parameter list of the generic type that declared the parameter; otherwise, undefined.</returns>
    </member>
    <member name="M:System.Reflection.Emit.TypeBuilder.GetConstructor(System.Type,System.Reflection.ConstructorInfo)">
      <summary>Returns the constructor of the specified constructed generic type that corresponds to the specified constructor of the generic type definition.</summary>
      <param name="type">The constructed generic type whose constructor is returned.</param>
      <param name="constructor">A constructor on the generic type definition of <paramref name="type" />, which specifies which constructor of <paramref name="type" /> to return.</param>
      <returns>A <see cref="T:System.Reflection.ConstructorInfo" /> object that represents the constructor of <paramref name="type" /> corresponding to <paramref name="constructor" />, which specifies a constructor belonging to the generic type definition of <paramref name="type" />.</returns>
      <exception cref="T:System.ArgumentException">
        <paramref name="type" /> does not represent a generic type.
-or-
<paramref name="type" /> is not of type <see cref="T:System.Reflection.Emit.TypeBuilder" />.
-or-
The declaring type of <paramref name="constructor" /> is not a generic type definition.
-or-
The declaring type of <paramref name="constructor" /> is not the generic type definition of <paramref name="type" />.</exception>
    </member>
    <member name="M:System.Reflection.Emit.TypeBuilder.GetConstructors(System.Reflection.BindingFlags)">
      <summary>Returns an array of <see cref="T:System.Reflection.ConstructorInfo" /> objects representing the public and non-public constructors defined for this class, as specified.</summary>
      <param name="bindingAttr">This must be a bit flag from <see cref="T:System.Reflection.BindingFlags" /> as in <see langword="InvokeMethod" />, <see langword="NonPublic" />, and so on.</param>
      <returns>Returns an array of <see cref="T:System.Reflection.ConstructorInfo" /> objects representing the specified constructors defined for this class. If no constructors are defined, an empty array is returned.</returns>
      <exception cref="T:System.NotSupportedException">This method is not implemented for incomplete types.</exception>
    </member>
    <member name="M:System.Reflection.Emit.TypeBuilder.GetCustomAttributes(System.Boolean)">
      <summary>Returns all the custom attributes defined for this type.</summary>
      <param name="inherit">Specifies whether to search this member's inheritance chain to find the attributes.</param>
      <returns>Returns an array of objects representing all the custom attributes of this type.</returns>
      <exception cref="T:System.NotSupportedException">This method is not currently supported for incomplete types. Retrieve the type using <see cref="M:System.Type.GetType" /> and call <see cref="M:System.Reflection.MemberInfo.GetCustomAttributes(System.Boolean)" /> on the returned <see cref="T:System.Type" />.</exception>
    </member>
    <member name="M:System.Reflection.Emit.TypeBuilder.GetCustomAttributes(System.Type,System.Boolean)">
      <summary>Returns all the custom attributes of the current type that are assignable to a specified type.</summary>
      <param name="attributeType">The type of attribute to search for. Only attributes that are assignable to this type are returned.</param>
      <param name="inherit">Specifies whether to search this member's inheritance chain to find the attributes.</param>
      <returns>An array of custom attributes defined on the current type.</returns>
      <exception cref="T:System.NotSupportedException">This method is not currently supported for incomplete types. Retrieve the type using <see cref="M:System.Type.GetType" /> and call <see cref="M:System.Reflection.MemberInfo.GetCustomAttributes(System.Boolean)" /> on the returned <see cref="T:System.Type" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="attributeType" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentException">The type must be a type provided by the underlying runtime system.</exception>
    </member>
    <member name="M:System.Reflection.Emit.TypeBuilder.GetElementType">
      <summary>Calling this method always throws <see cref="T:System.NotSupportedException" />.</summary>
      <returns>This method is not supported. No value is returned.</returns>
      <exception cref="T:System.NotSupportedException">This method is not supported.</exception>
    </member>
    <member name="M:System.Reflection.Emit.TypeBuilder.GetEvent(System.String,System.Reflection.BindingFlags)">
      <summary>Returns the event with the specified name.</summary>
      <param name="name">The name of the event to search for.</param>
      <param name="bindingAttr">A bitwise combination of <see cref="T:System.Reflection.BindingFlags" /> values that limits the search.</param>
      <returns>An <see cref="T:System.Reflection.EventInfo" /> object representing the event declared or inherited by this type with the specified name, or <see langword="null" /> if there are no matches.</returns>
      <exception cref="T:System.NotSupportedException">This method is not implemented for incomplete types.</exception>
    </member>
    <member name="M:System.Reflection.Emit.TypeBuilder.GetEvents">
      <summary>Returns the public events declared or inherited by this type.</summary>
      <returns>Returns an array of <see cref="T:System.Reflection.EventInfo" /> objects representing the public events declared or inherited by this type. An empty array is returned if there are no public events.</returns>
      <exception cref="T:System.NotSupportedException">This method is not implemented for incomplete types.</exception>
    </member>
    <member name="M:System.Reflection.Emit.TypeBuilder.GetEvents(System.Reflection.BindingFlags)">
      <summary>Returns the public and non-public events that are declared by this type.</summary>
      <param name="bindingAttr">A bitwise combination of <see cref="T:System.Reflection.BindingFlags" /> values that limits the search.</param>
      <returns>Returns an array of <see cref="T:System.Reflection.EventInfo" /> objects representing the events declared or inherited by this type that match the specified binding flags. An empty array is returned if there are no matching events.</returns>
      <exception cref="T:System.NotSupportedException">This method is not implemented for incomplete types.</exception>
    </member>
    <member name="M:System.Reflection.Emit.TypeBuilder.GetField(System.String,System.Reflection.BindingFlags)">
      <summary>Returns the field specified by the given name.</summary>
      <param name="name">The name of the field to get.</param>
      <param name="bindingAttr">This must be a bit flag from <see cref="T:System.Reflection.BindingFlags" /> as in <see langword="InvokeMethod" />, <see langword="NonPublic" />, and so on.</param>
      <returns>Returns the <see cref="T:System.Reflection.FieldInfo" /> object representing the field declared or inherited by this type with the specified name and public or non-public modifier. If there are no matches then <see langword="null" /> is returned.</returns>
      <exception cref="T:System.NotSupportedException">This method is not implemented for incomplete types.</exception>
    </member>
    <member name="M:System.Reflection.Emit.TypeBuilder.GetField(System.Type,System.Reflection.FieldInfo)">
      <summary>Returns the field of the specified constructed generic type that corresponds to the specified field of the generic type definition.</summary>
      <param name="type">The constructed generic type whose field is returned.</param>
      <param name="field">A field on the generic type definition of <paramref name="type" />, which specifies which field of <paramref name="type" /> to return.</param>
      <returns>A <see cref="T:System.Reflection.FieldInfo" /> object that represents the field of <paramref name="type" /> corresponding to <paramref name="field" />, which specifies a field belonging to the generic type definition of <paramref name="type" />.</returns>
      <exception cref="T:System.ArgumentException">
        <paramref name="type" /> does not represent a generic type.
-or-
<paramref name="type" /> is not of type <see cref="T:System.Reflection.Emit.TypeBuilder" />.
-or-
The declaring type of <paramref name="field" /> is not a generic type definition.
-or-
The declaring type of <paramref name="field" /> is not the generic type definition of <paramref name="type" />.</exception>
    </member>
    <member name="M:System.Reflection.Emit.TypeBuilder.GetFields(System.Reflection.BindingFlags)">
      <summary>Returns the public and non-public fields that are declared by this type.</summary>
      <param name="bindingAttr">This must be a bit flag from <see cref="T:System.Reflection.BindingFlags" /> : <see langword="InvokeMethod" />, <see langword="NonPublic" />, and so on.</param>
      <returns>Returns an array of <see cref="T:System.Reflection.FieldInfo" /> objects representing the public and non-public fields declared or inherited by this type. An empty array is returned if there are no fields, as specified.</returns>
      <exception cref="T:System.NotSupportedException">This method is not implemented for incomplete types.</exception>
    </member>
    <member name="M:System.Reflection.Emit.TypeBuilder.GetGenericArguments">
      <summary>Returns an array of <see cref="T:System.Type" /> objects representing the type arguments of a generic type or the type parameters of a generic type definition.</summary>
      <returns>An array of <see cref="T:System.Type" /> objects. The elements of the array represent the type arguments of a generic type or the type parameters of a generic type definition.</returns>
    </member>
    <member name="M:System.Reflection.Emit.TypeBuilder.GetGenericTypeDefinition">
      <summary>Returns a <see cref="T:System.Type" /> object that represents a generic type definition from which the current type can be obtained.</summary>
      <returns>A <see cref="T:System.Type" /> object representing a generic type definition from which the current type can be obtained.</returns>
      <exception cref="T:System.InvalidOperationException">The current type is not generic. That is, <see cref="P:System.Reflection.Emit.TypeBuilder.IsGenericType" /> returns <see langword="false" />.</exception>
    </member>
    <member name="M:System.Reflection.Emit.TypeBuilder.GetInterface(System.String,System.Boolean)">
      <summary>Returns the interface implemented (directly or indirectly) by this class with the fully qualified name matching the given interface name.</summary>
      <param name="name">The name of the interface.</param>
      <param name="ignoreCase">If <see langword="true" />, the search is case-insensitive. If <see langword="false" />, the search is case-sensitive.</param>
      <returns>Returns a <see cref="T:System.Type" /> object representing the implemented interface. Returns null if no interface matching name is found.</returns>
      <exception cref="T:System.NotSupportedException">This method is not implemented for incomplete types.</exception>
    </member>
    <member name="M:System.Reflection.Emit.TypeBuilder.GetInterfaceMap(System.Type)">
      <summary>Returns an interface mapping for the requested interface.</summary>
      <param name="interfaceType">The <see cref="T:System.Type" /> of the interface for which the mapping is to be retrieved.</param>
      <returns>Returns the requested interface mapping.</returns>
      <exception cref="T:System.NotSupportedException">This method is not implemented for incomplete types.</exception>
    </member>
    <member name="M:System.Reflection.Emit.TypeBuilder.GetInterfaces">
      <summary>Returns an array of all the interfaces implemented on this type and its base types.</summary>
      <returns>Returns an array of <see cref="T:System.Type" /> objects representing the implemented interfaces. If none are defined, an empty array is returned.</returns>
    </member>
    <member name="M:System.Reflection.Emit.TypeBuilder.GetMember(System.String,System.Reflection.MemberTypes,System.Reflection.BindingFlags)">
      <summary>Returns all the public and non-public members declared or inherited by this type, as specified.</summary>
      <param name="name">The name of the member.</param>
      <param name="type">The type of the member to return.</param>
      <param name="bindingAttr">This must be a bit flag from <see cref="T:System.Reflection.BindingFlags" />, as in <see langword="InvokeMethod" />, <see langword="NonPublic" />, and so on.</param>
      <returns>Returns an array of <see cref="T:System.Reflection.MemberInfo" /> objects representing the public and non-public members defined on this type if <paramref name="nonPublic" /> is used; otherwise, only the public members are returned.</returns>
      <exception cref="T:System.NotSupportedException">This method is not implemented for incomplete types.</exception>
    </member>
    <member name="M:System.Reflection.Emit.TypeBuilder.GetMembers(System.Reflection.BindingFlags)">
      <summary>Returns the members for the public and non-public members declared or inherited by this type.</summary>
      <param name="bindingAttr">This must be a bit flag from <see cref="T:System.Reflection.BindingFlags" />, such as <see langword="InvokeMethod" />, <see langword="NonPublic" />, and so on.</param>
      <returns>Returns an array of <see cref="T:System.Reflection.MemberInfo" /> objects representing the public and non-public members declared or inherited by this type. An empty array is returned if there are no matching members.</returns>
      <exception cref="T:System.NotSupportedException">This method is not implemented for incomplete types.</exception>
    </member>
    <member name="M:System.Reflection.Emit.TypeBuilder.GetMethod(System.Type,System.Reflection.MethodInfo)">
      <summary>Returns the method of the specified constructed generic type that corresponds to the specified method of the generic type definition.</summary>
      <param name="type">The constructed generic type whose method is returned.</param>
      <param name="method">A method on the generic type definition of <paramref name="type" />, which specifies which method of <paramref name="type" /> to return.</param>
      <returns>A <see cref="T:System.Reflection.MethodInfo" /> object that represents the method of <paramref name="type" /> corresponding to <paramref name="method" />, which specifies a method belonging to the generic type definition of <paramref name="type" />.</returns>
      <exception cref="T:System.ArgumentException">
        <paramref name="method" /> is a generic method that is not a generic method definition.
-or-
<paramref name="type" /> does not represent a generic type.
-or-
<paramref name="type" /> is not of type <see cref="T:System.Reflection.Emit.TypeBuilder" />.
-or-
The declaring type of <paramref name="method" /> is not a generic type definition.
-or-
The declaring type of <paramref name="method" /> is not the generic type definition of <paramref name="type" />.</exception>
    </member>
    <member name="M:System.Reflection.Emit.TypeBuilder.GetMethods(System.Reflection.BindingFlags)">
      <summary>Returns all the public and non-public methods declared or inherited by this type, as specified.</summary>
      <param name="bindingAttr">This must be a bit flag from <see cref="T:System.Reflection.BindingFlags" /> as in <see langword="InvokeMethod" />, <see langword="NonPublic" />, and so on.</param>
      <returns>Returns an array of <see cref="T:System.Reflection.MethodInfo" /> objects representing the public and non-public methods defined on this type if <paramref name="nonPublic" /> is used; otherwise, only the public methods are returned.</returns>
      <exception cref="T:System.NotSupportedException">This method is not implemented for incomplete types.</exception>
    </member>
    <member name="M:System.Reflection.Emit.TypeBuilder.GetNestedType(System.String,System.Reflection.BindingFlags)">
      <summary>Returns the public and non-public nested types that are declared by this type.</summary>
      <param name="name">The <see cref="T:System.String" /> containing the name of the nested type to get.</param>
      <param name="bindingAttr">A bitmask comprised of one or more <see cref="T:System.Reflection.BindingFlags" /> that specify how the search is conducted.
-or-
Zero, to conduct a case-sensitive search for public methods.</param>
      <returns>A <see cref="T:System.Type" /> object representing the nested type that matches the specified requirements, if found; otherwise, <see langword="null" />.</returns>
      <exception cref="T:System.NotSupportedException">This method is not implemented for incomplete types.</exception>
    </member>
    <member name="M:System.Reflection.Emit.TypeBuilder.GetNestedTypes(System.Reflection.BindingFlags)">
      <summary>Returns the public and non-public nested types that are declared or inherited by this type.</summary>
      <param name="bindingAttr">This must be a bit flag from <see cref="T:System.Reflection.BindingFlags" />, as in <see langword="InvokeMethod" />, <see langword="NonPublic" />, and so on.</param>
      <returns>An array of <see cref="T:System.Type" /> objects representing all the types nested within the current <see cref="T:System.Type" /> that match the specified binding constraints.
An empty array of type <see cref="T:System.Type" />, if no types are nested within the current <see cref="T:System.Type" />, or if none of the nested types match the binding constraints.</returns>
      <exception cref="T:System.NotSupportedException">This method is not implemented for incomplete types.</exception>
    </member>
    <member name="M:System.Reflection.Emit.TypeBuilder.GetProperties(System.Reflection.BindingFlags)">
      <summary>Returns all the public and non-public properties declared or inherited by this type, as specified.</summary>
      <param name="bindingAttr">This invocation attribute. This must be a bit flag from <see cref="T:System.Reflection.BindingFlags" /> : <see langword="InvokeMethod" />, <see langword="NonPublic" />, and so on.</param>
      <returns>Returns an array of <see langword="PropertyInfo" /> objects representing the public and non-public properties defined on this type if <paramref name="nonPublic" /> is used; otherwise, only the public properties are returned.</returns>
      <exception cref="T:System.NotSupportedException">This method is not implemented for incomplete types.</exception>
    </member>
    <member name="P:System.Reflection.Emit.TypeBuilder.GUID">
      <summary>Retrieves the GUID of this type.</summary>
      <returns>Read-only. Retrieves the GUID of this type</returns>
      <exception cref="T:System.NotSupportedException">This method is not currently supported for incomplete types.</exception>
    </member>
    <member name="M:System.Reflection.Emit.TypeBuilder.InvokeMember(System.String,System.Reflection.BindingFlags,System.Reflection.Binder,System.Object,System.Object[],System.Reflection.ParameterModifier[],System.Globalization.CultureInfo,System.String[])">
      <summary>Invokes the specified member. The method that is to be invoked must be accessible and provide the most specific match with the specified argument list, under the constraints of the specified binder and invocation attributes.</summary>
      <param name="name">The name of the member to invoke. This can be a constructor, method, property, or field. A suitable invocation attribute must be specified. Note that it is possible to invoke the default member of a class by passing an empty string as the name of the member.</param>
      <param name="invokeAttr">The invocation attribute. This must be a bit flag from <see langword="BindingFlags" />.</param>
      <param name="binder">An object that enables the binding, coercion of argument types, invocation of members, and retrieval of <see langword="MemberInfo" /> objects using reflection. If binder is <see langword="null" />, the default binder is used. See <see cref="T:System.Reflection.Binder" />.</param>
      <param name="target">The object on which to invoke the specified member. If the member is static, this parameter is ignored.</param>
      <param name="args">An argument list. This is an array of Objects that contains the number, order, and type of the parameters of the member to be invoked. If there are no parameters this should be null.</param>
      <param name="modifiers">An array of the same length as <paramref name="args" /> with elements that represent the attributes associated with the arguments of the member to be invoked. A parameter has attributes associated with it in the metadata. They are used by various interoperability services. See the metadata specs for more details.</param>
      <param name="culture">An instance of <see langword="CultureInfo" /> used to govern the coercion of types. If this is null, the <see langword="CultureInfo" /> for the current thread is used. (Note that this is necessary to, for example, convert a String that represents 1000 to a Double value, since 1000 is represented differently by different cultures.)</param>
      <param name="namedParameters">Each parameter in the <paramref name="namedParameters" /> array gets the value in the corresponding element in the <paramref name="args" /> array. If the length of <paramref name="args" /> is greater than the length of <paramref name="namedParameters" />, the remaining argument values are passed in order.</param>
      <returns>Returns the return value of the invoked member.</returns>
      <exception cref="T:System.NotSupportedException">This method is not currently supported for incomplete types.</exception>
    </member>
    <member name="M:System.Reflection.Emit.TypeBuilder.IsAssignableFrom(System.Type)">
      <summary>Gets a value that indicates whether a specified <see cref="T:System.Type" /> can be assigned to this object.</summary>
      <param name="c">The object to test.</param>
      <returns>
        <see langword="true" /> if the <paramref name="c" /> parameter and the current type represent the same type, or if the current type is in the inheritance hierarchy of <paramref name="c" />, or if the current type is an interface that <paramref name="c" /> supports. <see langword="false" /> if none of these conditions are valid, or if <paramref name="c" /> is <see langword="null" />.</returns>
    </member>
    <member name="P:System.Reflection.Emit.TypeBuilder.IsByRefLike" />
    <member name="P:System.Reflection.Emit.TypeBuilder.IsConstructedGenericType">
      <summary>Gets a value that indicates whether this object represents a constructed generic type.</summary>
      <returns>
        <see langword="true" /> if this object represents a constructed generic type; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Reflection.Emit.TypeBuilder.IsCreated">
      <summary>Returns a value that indicates whether the current dynamic type has been created.</summary>
      <returns>
        <see langword="true" /> if the <see cref="M:System.Reflection.Emit.TypeBuilder.CreateType" /> method has been called; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Reflection.Emit.TypeBuilder.IsDefined(System.Type,System.Boolean)">
      <summary>Determines whether a custom attribute is applied to the current type.</summary>
      <param name="attributeType">The type of attribute to search for. Only attributes that are assignable to this type are returned.</param>
      <param name="inherit">Specifies whether to search this member's inheritance chain to find the attributes.</param>
      <returns>
        <see langword="true" /> if one or more instances of <paramref name="attributeType" />, or an attribute derived from <paramref name="attributeType" />, is defined on this type; otherwise, <see langword="false" />.</returns>
      <exception cref="T:System.NotSupportedException">This method is not currently supported for incomplete types. Retrieve the type using <see cref="M:System.Type.GetType" /> and call <see cref="M:System.Reflection.MemberInfo.IsDefined(System.Type,System.Boolean)" /> on the returned <see cref="T:System.Type" />.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="attributeType" /> is not defined.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="attributeType" /> is <see langword="null" />.</exception>
    </member>
    <member name="P:System.Reflection.Emit.TypeBuilder.IsGenericParameter">
      <summary>Gets a value indicating whether the current type is a generic type parameter.</summary>
      <returns>
        <see langword="true" /> if the current <see cref="T:System.Reflection.Emit.TypeBuilder" /> object represents a generic type parameter; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Reflection.Emit.TypeBuilder.IsGenericType">
      <summary>Gets a value indicating whether the current type is a generic type.</summary>
      <returns>
        <see langword="true" /> if the type represented by the current <see cref="T:System.Reflection.Emit.TypeBuilder" /> object is generic; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Reflection.Emit.TypeBuilder.IsGenericTypeDefinition">
      <summary>Gets a value indicating whether the current <see cref="T:System.Reflection.Emit.TypeBuilder" /> represents a generic type definition from which other generic types can be constructed.</summary>
      <returns>
        <see langword="true" /> if this <see cref="T:System.Reflection.Emit.TypeBuilder" /> object represents a generic type definition; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Reflection.Emit.TypeBuilder.IsSecurityCritical">
      <summary>Gets a value that indicates whether the current type is security-critical or security-safe-critical, and therefore can perform critical operations.</summary>
      <returns>
        <see langword="true" /> if the current type is security-critical or security-safe-critical; <see langword="false" /> if it is transparent.</returns>
      <exception cref="T:System.NotSupportedException">The current dynamic type has not been created by calling the <see cref="M:System.Reflection.Emit.TypeBuilder.CreateType" /> method.</exception>
    </member>
    <member name="P:System.Reflection.Emit.TypeBuilder.IsSecuritySafeCritical">
      <summary>Gets a value that indicates whether the current type is security-safe-critical; that is, whether it can perform critical operations and can be accessed by transparent code.</summary>
      <returns>
        <see langword="true" /> if the current type is security-safe-critical; <see langword="false" /> if it is security-critical or transparent.</returns>
      <exception cref="T:System.NotSupportedException">The current dynamic type has not been created by calling the <see cref="M:System.Reflection.Emit.TypeBuilder.CreateType" /> method.</exception>
    </member>
    <member name="P:System.Reflection.Emit.TypeBuilder.IsSecurityTransparent">
      <summary>Gets a value that indicates whether the current type is transparent, and therefore cannot perform critical operations.</summary>
      <returns>
        <see langword="true" /> if the type is security-transparent; otherwise, <see langword="false" />.</returns>
      <exception cref="T:System.NotSupportedException">The current dynamic type has not been created by calling the <see cref="M:System.Reflection.Emit.TypeBuilder.CreateType" /> method.</exception>
    </member>
    <member name="M:System.Reflection.Emit.TypeBuilder.IsSubclassOf(System.Type)">
      <summary>Determines whether this type is derived from a specified type.</summary>
      <param name="c">A <see cref="T:System.Type" /> that is to be checked.</param>
      <returns>Read-only. Returns <see langword="true" /> if this type is the same as the type <paramref name="c" />, or is a subtype of type <paramref name="c" />; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Reflection.Emit.TypeBuilder.IsSZArray" />
    <member name="P:System.Reflection.Emit.TypeBuilder.IsTypeDefinition" />
    <member name="P:System.Reflection.Emit.TypeBuilder.IsVariableBoundArray" />
    <member name="M:System.Reflection.Emit.TypeBuilder.MakeArrayType">
      <summary>Returns a <see cref="T:System.Type" /> object that represents a one-dimensional array of the current type, with a lower bound of zero.</summary>
      <returns>A <see cref="T:System.Type" /> object representing a one-dimensional array type whose element type is the current type, with a lower bound of zero.</returns>
    </member>
    <member name="M:System.Reflection.Emit.TypeBuilder.MakeArrayType(System.Int32)">
      <summary>Returns a <see cref="T:System.Type" /> object that represents an array of the current type, with the specified number of dimensions.</summary>
      <param name="rank">The number of dimensions for the array.</param>
      <returns>A <see cref="T:System.Type" /> object that represents a one-dimensional array of the current type.</returns>
      <exception cref="T:System.IndexOutOfRangeException">
        <paramref name="rank" /> is not a valid array dimension.</exception>
    </member>
    <member name="M:System.Reflection.Emit.TypeBuilder.MakeByRefType">
      <summary>Returns a <see cref="T:System.Type" /> object that represents the current type when passed as a <see langword="ref" /> parameter (<see langword="ByRef" /> in Visual Basic).</summary>
      <returns>A <see cref="T:System.Type" /> object that represents the current type when passed as a <see langword="ref" /> parameter (<see langword="ByRef" /> in Visual Basic).</returns>
    </member>
    <member name="M:System.Reflection.Emit.TypeBuilder.MakeGenericType(System.Type[])">
      <summary>Substitutes the elements of an array of types for the type parameters of the current generic type definition, and returns the resulting constructed type.</summary>
      <param name="typeArguments">An array of types to be substituted for the type parameters of the current generic type definition.</param>
      <returns>A <see cref="T:System.Type" /> representing the constructed type formed by substituting the elements of <paramref name="typeArguments" /> for the type parameters of the current generic type.</returns>
      <exception cref="T:System.InvalidOperationException">The current type does not represent the definition of a generic type. That is, <see cref="P:System.Reflection.Emit.TypeBuilder.IsGenericTypeDefinition" /> returns <see langword="false" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="typeArguments" /> is <see langword="null" />.
-or-
Any element of <paramref name="typeArguments" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentException">The <see cref="P:System.Type.Module" /> property of any element of <paramref name="typeArguments" /> is <see langword="null" />.
-or-
The <see cref="P:System.Reflection.Module.Assembly" /> property of the module of any element of <paramref name="typeArguments" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Reflection.Emit.TypeBuilder.MakePointerType">
      <summary>Returns a <see cref="T:System.Type" /> object that represents the type of an unmanaged pointer to the current type.</summary>
      <returns>A <see cref="T:System.Type" /> object that represents the type of an unmanaged pointer to the current type.</returns>
    </member>
    <member name="P:System.Reflection.Emit.TypeBuilder.Module">
      <summary>Retrieves the dynamic module that contains this type definition.</summary>
      <returns>Read-only. Retrieves the dynamic module that contains this type definition.</returns>
    </member>
    <member name="P:System.Reflection.Emit.TypeBuilder.Name">
      <summary>Retrieves the name of this type.</summary>
      <returns>Read-only. Retrieves the <see cref="T:System.String" /> name of this type.</returns>
    </member>
    <member name="P:System.Reflection.Emit.TypeBuilder.Namespace">
      <summary>Retrieves the namespace where this <see langword="TypeBuilder" /> is defined.</summary>
      <returns>Read-only. Retrieves the namespace where this <see langword="TypeBuilder" /> is defined.</returns>
    </member>
    <member name="P:System.Reflection.Emit.TypeBuilder.PackingSize">
      <summary>Retrieves the packing size of this type.</summary>
      <returns>Read-only. Retrieves the packing size of this type.</returns>
    </member>
    <member name="P:System.Reflection.Emit.TypeBuilder.ReflectedType">
      <summary>Returns the type that was used to obtain this type.</summary>
      <returns>Read-only. The type that was used to obtain this type.</returns>
    </member>
    <member name="M:System.Reflection.Emit.TypeBuilder.SetCustomAttribute(System.Reflection.ConstructorInfo,System.Byte[])">
      <summary>Sets a custom attribute using a specified custom attribute blob.</summary>
      <param name="con">The constructor for the custom attribute.</param>
      <param name="binaryAttribute">A byte blob representing the attributes.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="con" /> or <paramref name="binaryAttribute" /> is <see langword="null" />.</exception>
      <exception cref="T:System.InvalidOperationException">For the current dynamic type, the <see cref="P:System.Reflection.Emit.TypeBuilder.IsGenericType" /> property is <see langword="true" />, but the <see cref="P:System.Reflection.Emit.TypeBuilder.IsGenericTypeDefinition" /> property is <see langword="false" />.</exception>
    </member>
    <member name="M:System.Reflection.Emit.TypeBuilder.SetCustomAttribute(System.Reflection.Emit.CustomAttributeBuilder)">
      <summary>Set a custom attribute using a custom attribute builder.</summary>
      <param name="customBuilder">An instance of a helper class to define the custom attribute.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="customBuilder" /> is <see langword="null" />.</exception>
      <exception cref="T:System.InvalidOperationException">For the current dynamic type, the <see cref="P:System.Reflection.Emit.TypeBuilder.IsGenericType" /> property is <see langword="true" />, but the <see cref="P:System.Reflection.Emit.TypeBuilder.IsGenericTypeDefinition" /> property is <see langword="false" />.</exception>
    </member>
    <member name="M:System.Reflection.Emit.TypeBuilder.SetParent(System.Type)">
      <summary>Sets the base type of the type currently under construction.</summary>
      <param name="parent">The new base type.</param>
      <exception cref="T:System.InvalidOperationException">The type was previously created using <see cref="M:System.Reflection.Emit.TypeBuilder.CreateType" />.
-or-
<paramref name="parent" /> is <see langword="null" />, and the current instance represents an interface whose attributes do not include <see cref="F:System.Reflection.TypeAttributes.Abstract" />.
-or-
For the current dynamic type, the <see cref="P:System.Reflection.Emit.TypeBuilder.IsGenericType" /> property is <see langword="true" />, but the <see cref="P:System.Reflection.Emit.TypeBuilder.IsGenericTypeDefinition" /> property is <see langword="false" />.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="parent" /> is an interface. This exception condition is new in the .NET Framework version 2.0.</exception>
    </member>
    <member name="P:System.Reflection.Emit.TypeBuilder.Size">
      <summary>Retrieves the total size of a type.</summary>
      <returns>Read-only. Retrieves this type's total size.</returns>
    </member>
    <member name="M:System.Reflection.Emit.TypeBuilder.ToString">
      <summary>Returns the name of the type excluding the namespace.</summary>
      <returns>Read-only. The name of the type excluding the namespace.</returns>
    </member>
    <member name="P:System.Reflection.Emit.TypeBuilder.TypeHandle">
      <summary>Not supported in dynamic modules.</summary>
      <returns>Read-only.</returns>
      <exception cref="T:System.NotSupportedException">Not supported in dynamic modules.</exception>
    </member>
    <member name="P:System.Reflection.Emit.TypeBuilder.UnderlyingSystemType">
      <summary>Returns the underlying system type for this <see langword="TypeBuilder" />.</summary>
      <returns>Read-only. Returns the underlying system type.</returns>
      <exception cref="T:System.InvalidOperationException">This type is an enumeration, but there is no underlying system type.</exception>
    </member>
    <member name="F:System.Reflection.Emit.TypeBuilder.UnspecifiedTypeSize">
      <summary>Represents that total size for the type is not specified.</summary>
    </member>
  </members>
</doc>