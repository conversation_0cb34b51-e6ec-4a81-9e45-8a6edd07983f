﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>System.Reflection.Metadata</name>
  </assembly>
  <members>
    <member name="T:System.Reflection.AssemblyFlags" />
    <member name="F:System.Reflection.AssemblyFlags.ContentTypeMask">
      <summary>Content type masked bits that correspond to values of <see cref="T:System.Reflection.AssemblyContentType" />.</summary>
    </member>
    <member name="F:System.Reflection.AssemblyFlags.DisableJitCompileOptimizer">
      <summary>Just-In-Time (JIT) compiler optimization is disabled for the assembly.</summary>
    </member>
    <member name="F:System.Reflection.AssemblyFlags.EnableJitCompileTracking">
      <summary>Just-In-Time (JIT) compiler tracking is enabled for the assembly.</summary>
    </member>
    <member name="F:System.Reflection.AssemblyFlags.PublicKey">
      <summary>The assembly reference holds the full (unhashed) public key. Not applicable on assembly definition.</summary>
    </member>
    <member name="F:System.Reflection.AssemblyFlags.Retargetable">
      <summary>The implementation of the referenced assembly used at runtime is not expected to match the version seen at compile time.</summary>
    </member>
    <member name="F:System.Reflection.AssemblyFlags.WindowsRuntime">
      <summary>The assembly contains Windows Runtime code.</summary>
    </member>
    <member name="T:System.Reflection.AssemblyHashAlgorithm">
      <summary>Specifies the hash algorithms used for hashing assembly files and for generating the strong name.</summary>
    </member>
    <member name="F:System.Reflection.AssemblyHashAlgorithm.MD5">
      <summary>Retrieves the MD5 message-digest algorithm.</summary>
    </member>
    <member name="F:System.Reflection.AssemblyHashAlgorithm.None">
      <summary>A mask indicating that there is no hash algorithm.</summary>
    </member>
    <member name="F:System.Reflection.AssemblyHashAlgorithm.Sha1">
      <summary>Retrieves a revision of the Secure Hash Algorithm that corrects an unpublished flaw in SHA.</summary>
    </member>
    <member name="F:System.Reflection.AssemblyHashAlgorithm.Sha256">
      <summary>Retrieves a version of the Secure Hash Algorithm with a hash size of 256 bits.</summary>
    </member>
    <member name="F:System.Reflection.AssemblyHashAlgorithm.Sha384">
      <summary>Retrieves a version of the Secure Hash Algorithm with a hash size of 384 bits.</summary>
    </member>
    <member name="F:System.Reflection.AssemblyHashAlgorithm.Sha512">
      <summary>Retrieves a version of the Secure Hash Algorithm with a hash size of 512 bits.</summary>
    </member>
    <member name="T:System.Reflection.DeclarativeSecurityAction">
      <summary>Specifies the security actions that can be performed using declarative security.</summary>
    </member>
    <member name="F:System.Reflection.DeclarativeSecurityAction.Assert">
      <summary>The calling code can access the resource identified by the current permission object, even if callers higher in the stack have not been granted permission to access the resource.</summary>
    </member>
    <member name="F:System.Reflection.DeclarativeSecurityAction.Demand">
      <summary>Check that all callers in the call chain have been granted the specified permission.</summary>
    </member>
    <member name="F:System.Reflection.DeclarativeSecurityAction.Deny">
      <summary>Without further checks refuse Demand for the specified permission.</summary>
    </member>
    <member name="F:System.Reflection.DeclarativeSecurityAction.InheritanceDemand">
      <summary>The derived class inheriting the class or overriding a method is required to have the specified permission.</summary>
    </member>
    <member name="F:System.Reflection.DeclarativeSecurityAction.LinkDemand">
      <summary>Check that the immediate caller has been granted the specified permission.</summary>
    </member>
    <member name="F:System.Reflection.DeclarativeSecurityAction.None">
      <summary>No declarative security action.</summary>
    </member>
    <member name="F:System.Reflection.DeclarativeSecurityAction.PermitOnly">
      <summary>Without further checks, refuse the demand for all permissions other than those specified.</summary>
    </member>
    <member name="F:System.Reflection.DeclarativeSecurityAction.RequestMinimum">
      <summary>Request the minimum permissions required for code to run. This action can only be used within the scope of the assembly.</summary>
    </member>
    <member name="F:System.Reflection.DeclarativeSecurityAction.RequestOptional">
      <summary>Request additional permissions that are optional (not required to run). This request implicitly refuses all other permissions not specifically requested. This action can only be used within the scope of the assembly.</summary>
    </member>
    <member name="F:System.Reflection.DeclarativeSecurityAction.RequestRefuse">
      <summary>Request that permissions that might be misused not be granted to the calling code. This action can only be used within the scope of the assembly.</summary>
    </member>
    <member name="T:System.Reflection.ManifestResourceAttributes" />
    <member name="F:System.Reflection.ManifestResourceAttributes.Private">
      <summary>The resource is not exported from the assembly.</summary>
    </member>
    <member name="F:System.Reflection.ManifestResourceAttributes.Public">
      <summary>The resource is exported from the assembly.</summary>
    </member>
    <member name="F:System.Reflection.ManifestResourceAttributes.VisibilityMask">
      <summary>Masks just the visibility-related attributes.</summary>
    </member>
    <member name="T:System.Reflection.Metadata.ArrayShape">
      <summary>Represents the shape of an array type.</summary>
    </member>
    <member name="M:System.Reflection.Metadata.ArrayShape.#ctor(System.Int32,System.Collections.Immutable.ImmutableArray{System.Int32},System.Collections.Immutable.ImmutableArray{System.Int32})">
      <summary>Initializes a new instance of the <see cref="T:System.Reflection.Metadata.ArrayShape" /> structure.</summary>
      <param name="rank">The number of dimensions in the array.</param>
      <param name="sizes">The size of each dimension.</param>
      <param name="lowerBounds">The lower-bound of each dimension.</param>
    </member>
    <member name="P:System.Reflection.Metadata.ArrayShape.LowerBounds">
      <summary>Gets the lower-bounds of all dimensions. Length may be smaller than rank, in which case the trailing dimensions have unspecified lower bounds.</summary>
      <returns>An array of lower-bounds.</returns>
    </member>
    <member name="P:System.Reflection.Metadata.ArrayShape.Rank">
      <summary>Gets the number of dimensions in the array.</summary>
      <returns>The number of dimensions.</returns>
    </member>
    <member name="P:System.Reflection.Metadata.ArrayShape.Sizes">
      <summary>Gets the sizes of all dimensions.</summary>
      <returns>An array of sizes.</returns>
    </member>
    <member name="T:System.Reflection.Metadata.AssemblyDefinition" />
    <member name="P:System.Reflection.Metadata.AssemblyDefinition.Culture" />
    <member name="P:System.Reflection.Metadata.AssemblyDefinition.Flags" />
    <member name="M:System.Reflection.Metadata.AssemblyDefinition.GetAssemblyName" />
    <member name="M:System.Reflection.Metadata.AssemblyDefinition.GetCustomAttributes" />
    <member name="M:System.Reflection.Metadata.AssemblyDefinition.GetDeclarativeSecurityAttributes" />
    <member name="P:System.Reflection.Metadata.AssemblyDefinition.HashAlgorithm" />
    <member name="P:System.Reflection.Metadata.AssemblyDefinition.Name" />
    <member name="P:System.Reflection.Metadata.AssemblyDefinition.PublicKey" />
    <member name="P:System.Reflection.Metadata.AssemblyDefinition.Version" />
    <member name="T:System.Reflection.Metadata.AssemblyDefinitionHandle" />
    <member name="M:System.Reflection.Metadata.AssemblyDefinitionHandle.Equals(System.Object)">
      <param name="obj" />
    </member>
    <member name="M:System.Reflection.Metadata.AssemblyDefinitionHandle.Equals(System.Reflection.Metadata.AssemblyDefinitionHandle)">
      <param name="other" />
    </member>
    <member name="M:System.Reflection.Metadata.AssemblyDefinitionHandle.GetHashCode" />
    <member name="P:System.Reflection.Metadata.AssemblyDefinitionHandle.IsNil" />
    <member name="M:System.Reflection.Metadata.AssemblyDefinitionHandle.op_Equality(System.Reflection.Metadata.AssemblyDefinitionHandle,System.Reflection.Metadata.AssemblyDefinitionHandle)">
      <param name="left" />
      <param name="right" />
    </member>
    <member name="M:System.Reflection.Metadata.AssemblyDefinitionHandle.op_Explicit(System.Reflection.Metadata.EntityHandle)~System.Reflection.Metadata.AssemblyDefinitionHandle">
      <param name="handle" />
    </member>
    <member name="M:System.Reflection.Metadata.AssemblyDefinitionHandle.op_Explicit(System.Reflection.Metadata.Handle)~System.Reflection.Metadata.AssemblyDefinitionHandle">
      <param name="handle" />
    </member>
    <member name="M:System.Reflection.Metadata.AssemblyDefinitionHandle.op_Implicit(System.Reflection.Metadata.AssemblyDefinitionHandle)~System.Reflection.Metadata.EntityHandle">
      <param name="handle" />
    </member>
    <member name="M:System.Reflection.Metadata.AssemblyDefinitionHandle.op_Implicit(System.Reflection.Metadata.AssemblyDefinitionHandle)~System.Reflection.Metadata.Handle">
      <param name="handle" />
    </member>
    <member name="M:System.Reflection.Metadata.AssemblyDefinitionHandle.op_Inequality(System.Reflection.Metadata.AssemblyDefinitionHandle,System.Reflection.Metadata.AssemblyDefinitionHandle)">
      <param name="left" />
      <param name="right" />
    </member>
    <member name="T:System.Reflection.Metadata.AssemblyFile" />
    <member name="P:System.Reflection.Metadata.AssemblyFile.ContainsMetadata">
      <summary>Gets a value that indicates whether the file contains metadata.</summary>
      <returns>
        <see langword="true" /> if the file contains metadata, <see langword="false" /> otherwise.</returns>
    </member>
    <member name="M:System.Reflection.Metadata.AssemblyFile.GetCustomAttributes" />
    <member name="P:System.Reflection.Metadata.AssemblyFile.HashValue">
      <summary>Gets the hash value of the file content calculated using <see cref="P:System.Reflection.Metadata.AssemblyDefinition.HashAlgorithm" />.</summary>
      <returns>A <see cref="T:System.Reflection.Metadata.BlobHandle" /> instance representing the hash value of the file content.</returns>
    </member>
    <member name="P:System.Reflection.Metadata.AssemblyFile.Name">
      <summary>Gets the file name, including its extension.</summary>
      <returns>A <see cref="T:System.Reflection.Metadata.StringHandle" /> instance representing the file name with its extension.</returns>
    </member>
    <member name="T:System.Reflection.Metadata.AssemblyFileHandle" />
    <member name="M:System.Reflection.Metadata.AssemblyFileHandle.Equals(System.Object)">
      <param name="obj" />
    </member>
    <member name="M:System.Reflection.Metadata.AssemblyFileHandle.Equals(System.Reflection.Metadata.AssemblyFileHandle)">
      <param name="other" />
    </member>
    <member name="M:System.Reflection.Metadata.AssemblyFileHandle.GetHashCode" />
    <member name="P:System.Reflection.Metadata.AssemblyFileHandle.IsNil" />
    <member name="M:System.Reflection.Metadata.AssemblyFileHandle.op_Equality(System.Reflection.Metadata.AssemblyFileHandle,System.Reflection.Metadata.AssemblyFileHandle)">
      <param name="left" />
      <param name="right" />
    </member>
    <member name="M:System.Reflection.Metadata.AssemblyFileHandle.op_Explicit(System.Reflection.Metadata.EntityHandle)~System.Reflection.Metadata.AssemblyFileHandle">
      <param name="handle" />
    </member>
    <member name="M:System.Reflection.Metadata.AssemblyFileHandle.op_Explicit(System.Reflection.Metadata.Handle)~System.Reflection.Metadata.AssemblyFileHandle">
      <param name="handle" />
    </member>
    <member name="M:System.Reflection.Metadata.AssemblyFileHandle.op_Implicit(System.Reflection.Metadata.AssemblyFileHandle)~System.Reflection.Metadata.EntityHandle">
      <param name="handle" />
    </member>
    <member name="M:System.Reflection.Metadata.AssemblyFileHandle.op_Implicit(System.Reflection.Metadata.AssemblyFileHandle)~System.Reflection.Metadata.Handle">
      <param name="handle" />
    </member>
    <member name="M:System.Reflection.Metadata.AssemblyFileHandle.op_Inequality(System.Reflection.Metadata.AssemblyFileHandle,System.Reflection.Metadata.AssemblyFileHandle)">
      <param name="left" />
      <param name="right" />
    </member>
    <member name="T:System.Reflection.Metadata.AssemblyFileHandleCollection">
      <summary>Represents a collection of <see cref="T:System.Reflection.Metadata.AssemblyFileHandle" />.</summary>
    </member>
    <member name="P:System.Reflection.Metadata.AssemblyFileHandleCollection.Count" />
    <member name="T:System.Reflection.Metadata.AssemblyFileHandleCollection.Enumerator" />
    <member name="P:System.Reflection.Metadata.AssemblyFileHandleCollection.Enumerator.Current" />
    <member name="M:System.Reflection.Metadata.AssemblyFileHandleCollection.Enumerator.MoveNext" />
    <member name="P:System.Reflection.Metadata.AssemblyFileHandleCollection.Enumerator.System#Collections#IEnumerator#Current" />
    <member name="M:System.Reflection.Metadata.AssemblyFileHandleCollection.Enumerator.System#Collections#IEnumerator#Reset" />
    <member name="M:System.Reflection.Metadata.AssemblyFileHandleCollection.Enumerator.System#IDisposable#Dispose" />
    <member name="M:System.Reflection.Metadata.AssemblyFileHandleCollection.GetEnumerator" />
    <member name="M:System.Reflection.Metadata.AssemblyFileHandleCollection.System#Collections#Generic#IEnumerable{System#Reflection#Metadata#AssemblyFileHandle}#GetEnumerator" />
    <member name="M:System.Reflection.Metadata.AssemblyFileHandleCollection.System#Collections#IEnumerable#GetEnumerator" />
    <member name="T:System.Reflection.Metadata.AssemblyReference" />
    <member name="P:System.Reflection.Metadata.AssemblyReference.Culture" />
    <member name="P:System.Reflection.Metadata.AssemblyReference.Flags" />
    <member name="M:System.Reflection.Metadata.AssemblyReference.GetAssemblyName" />
    <member name="M:System.Reflection.Metadata.AssemblyReference.GetCustomAttributes" />
    <member name="P:System.Reflection.Metadata.AssemblyReference.HashValue" />
    <member name="P:System.Reflection.Metadata.AssemblyReference.Name" />
    <member name="P:System.Reflection.Metadata.AssemblyReference.PublicKeyOrToken" />
    <member name="P:System.Reflection.Metadata.AssemblyReference.Version" />
    <member name="T:System.Reflection.Metadata.AssemblyReferenceHandle" />
    <member name="M:System.Reflection.Metadata.AssemblyReferenceHandle.Equals(System.Object)">
      <param name="obj" />
    </member>
    <member name="M:System.Reflection.Metadata.AssemblyReferenceHandle.Equals(System.Reflection.Metadata.AssemblyReferenceHandle)">
      <param name="other" />
    </member>
    <member name="M:System.Reflection.Metadata.AssemblyReferenceHandle.GetHashCode" />
    <member name="P:System.Reflection.Metadata.AssemblyReferenceHandle.IsNil" />
    <member name="M:System.Reflection.Metadata.AssemblyReferenceHandle.op_Equality(System.Reflection.Metadata.AssemblyReferenceHandle,System.Reflection.Metadata.AssemblyReferenceHandle)">
      <param name="left" />
      <param name="right" />
    </member>
    <member name="M:System.Reflection.Metadata.AssemblyReferenceHandle.op_Explicit(System.Reflection.Metadata.EntityHandle)~System.Reflection.Metadata.AssemblyReferenceHandle">
      <param name="handle" />
    </member>
    <member name="M:System.Reflection.Metadata.AssemblyReferenceHandle.op_Explicit(System.Reflection.Metadata.Handle)~System.Reflection.Metadata.AssemblyReferenceHandle">
      <param name="handle" />
    </member>
    <member name="M:System.Reflection.Metadata.AssemblyReferenceHandle.op_Implicit(System.Reflection.Metadata.AssemblyReferenceHandle)~System.Reflection.Metadata.EntityHandle">
      <param name="handle" />
    </member>
    <member name="M:System.Reflection.Metadata.AssemblyReferenceHandle.op_Implicit(System.Reflection.Metadata.AssemblyReferenceHandle)~System.Reflection.Metadata.Handle">
      <param name="handle" />
    </member>
    <member name="M:System.Reflection.Metadata.AssemblyReferenceHandle.op_Inequality(System.Reflection.Metadata.AssemblyReferenceHandle,System.Reflection.Metadata.AssemblyReferenceHandle)">
      <param name="left" />
      <param name="right" />
    </member>
    <member name="T:System.Reflection.Metadata.AssemblyReferenceHandleCollection">
      <summary>A collection of assembly references.</summary>
    </member>
    <member name="P:System.Reflection.Metadata.AssemblyReferenceHandleCollection.Count" />
    <member name="T:System.Reflection.Metadata.AssemblyReferenceHandleCollection.Enumerator" />
    <member name="P:System.Reflection.Metadata.AssemblyReferenceHandleCollection.Enumerator.Current" />
    <member name="M:System.Reflection.Metadata.AssemblyReferenceHandleCollection.Enumerator.MoveNext" />
    <member name="P:System.Reflection.Metadata.AssemblyReferenceHandleCollection.Enumerator.System#Collections#IEnumerator#Current" />
    <member name="M:System.Reflection.Metadata.AssemblyReferenceHandleCollection.Enumerator.System#Collections#IEnumerator#Reset" />
    <member name="M:System.Reflection.Metadata.AssemblyReferenceHandleCollection.Enumerator.System#IDisposable#Dispose" />
    <member name="M:System.Reflection.Metadata.AssemblyReferenceHandleCollection.GetEnumerator" />
    <member name="M:System.Reflection.Metadata.AssemblyReferenceHandleCollection.System#Collections#Generic#IEnumerable{System#Reflection#Metadata#AssemblyReferenceHandle}#GetEnumerator" />
    <member name="M:System.Reflection.Metadata.AssemblyReferenceHandleCollection.System#Collections#IEnumerable#GetEnumerator" />
    <member name="T:System.Reflection.Metadata.Blob" />
    <member name="M:System.Reflection.Metadata.Blob.GetBytes" />
    <member name="P:System.Reflection.Metadata.Blob.IsDefault" />
    <member name="P:System.Reflection.Metadata.Blob.Length" />
    <member name="T:System.Reflection.Metadata.BlobBuilder" />
    <member name="M:System.Reflection.Metadata.BlobBuilder.#ctor(System.Int32)">
      <param name="capacity" />
    </member>
    <member name="M:System.Reflection.Metadata.BlobBuilder.Align(System.Int32)">
      <param name="alignment" />
      <exception cref="T:System.InvalidOperationException">The builder is not writable; it has been linked with another one.</exception>
    </member>
    <member name="M:System.Reflection.Metadata.BlobBuilder.AllocateChunk(System.Int32)">
      <param name="minimalSize" />
    </member>
    <member name="T:System.Reflection.Metadata.BlobBuilder.Blobs" />
    <member name="P:System.Reflection.Metadata.BlobBuilder.Blobs.Current" />
    <member name="M:System.Reflection.Metadata.BlobBuilder.Blobs.GetEnumerator" />
    <member name="M:System.Reflection.Metadata.BlobBuilder.Blobs.MoveNext" />
    <member name="M:System.Reflection.Metadata.BlobBuilder.Blobs.Reset" />
    <member name="M:System.Reflection.Metadata.BlobBuilder.Blobs.System#Collections#Generic#IEnumerable{System#Reflection#Metadata#Blob}#GetEnumerator" />
    <member name="M:System.Reflection.Metadata.BlobBuilder.Blobs.System#Collections#IEnumerable#GetEnumerator" />
    <member name="P:System.Reflection.Metadata.BlobBuilder.Blobs.System#Collections#IEnumerator#Current" />
    <member name="M:System.Reflection.Metadata.BlobBuilder.Blobs.System#IDisposable#Dispose" />
    <member name="P:System.Reflection.Metadata.BlobBuilder.ChunkCapacity" />
    <member name="M:System.Reflection.Metadata.BlobBuilder.Clear" />
    <member name="M:System.Reflection.Metadata.BlobBuilder.ContentEquals(System.Reflection.Metadata.BlobBuilder)">
      <summary>Compares the current content of this writer with another one.</summary>
      <param name="other">A <see cref="T:System.Reflection.Metadata.BlobBuilder" /> instance to compare with this one.</param>
      <returns>
        <see langword="true" /> if equal; otherwise, <see langword="false" />.</returns>
      <exception cref="T:System.InvalidOperationException">Content is not available; the builder has been linked with another one.</exception>
    </member>
    <member name="P:System.Reflection.Metadata.BlobBuilder.Count" />
    <member name="M:System.Reflection.Metadata.BlobBuilder.Free" />
    <member name="P:System.Reflection.Metadata.BlobBuilder.FreeBytes" />
    <member name="M:System.Reflection.Metadata.BlobBuilder.FreeChunk" />
    <member name="M:System.Reflection.Metadata.BlobBuilder.GetBlobs">
      <summary>Returns a sequence of all blobs that represent the content of the builder.</summary>
      <returns>A sequence of blobs.</returns>
      <exception cref="T:System.InvalidOperationException">Content is not available; the builder has been linked with another one.</exception>
    </member>
    <member name="M:System.Reflection.Metadata.BlobBuilder.LinkPrefix(System.Reflection.Metadata.BlobBuilder)">
      <param name="prefix" />
      <exception cref="T:System.ArgumentNullException">
        <paramref name="prefix" /> is <see langword="null" />.</exception>
      <exception cref="T:System.InvalidOperationException">The builder is not writable; it has been linked with another one.</exception>
    </member>
    <member name="M:System.Reflection.Metadata.BlobBuilder.LinkSuffix(System.Reflection.Metadata.BlobBuilder)">
      <param name="suffix" />
      <exception cref="T:System.ArgumentNullException">
        <paramref name="suffix" /> is <see langword="null" />.</exception>
      <exception cref="T:System.InvalidOperationException">The builder is not writable; it has been linked with another one.</exception>
    </member>
    <member name="M:System.Reflection.Metadata.BlobBuilder.PadTo(System.Int32)">
      <param name="position" />
      <exception cref="T:System.InvalidOperationException">The builder is not writable; it has been linked with another one.</exception>
    </member>
    <member name="M:System.Reflection.Metadata.BlobBuilder.ReserveBytes(System.Int32)">
      <summary>Reserves a contiguous block of bytes.</summary>
      <param name="byteCount" />
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="byteCount" /> is negative.</exception>
      <exception cref="T:System.InvalidOperationException">The builder is not writable; it has been linked with another one.</exception>
    </member>
    <member name="M:System.Reflection.Metadata.BlobBuilder.ToArray">
      <exception cref="T:System.InvalidOperationException">Content is not available; the builder has been linked with another one.</exception>
    </member>
    <member name="M:System.Reflection.Metadata.BlobBuilder.ToArray(System.Int32,System.Int32)">
      <param name="start" />
      <param name="byteCount" />
      <exception cref="T:System.ArgumentOutOfRangeException">The range specified by <paramref name="start" /> and <paramref name="byteCount" /> falls outside of the bounds of the buffer content.</exception>
      <exception cref="T:System.InvalidOperationException">Content is not available; the builder has been linked with another one.</exception>
    </member>
    <member name="M:System.Reflection.Metadata.BlobBuilder.ToImmutableArray">
      <exception cref="T:System.InvalidOperationException">Content is not available; the builder has been linked with another one.</exception>
    </member>
    <member name="M:System.Reflection.Metadata.BlobBuilder.ToImmutableArray(System.Int32,System.Int32)">
      <param name="start" />
      <param name="byteCount" />
      <exception cref="T:System.ArgumentOutOfRangeException">The range specified by <paramref name="start" /> and <paramref name="byteCount" /> falls outside of the bounds of the buffer content.</exception>
      <exception cref="T:System.InvalidOperationException">Content is not available; the builder has been linked with another one.</exception>
    </member>
    <member name="M:System.Reflection.Metadata.BlobBuilder.TryWriteBytes(System.IO.Stream,System.Int32)">
      <summary>Attempts to write a sequence of bytes to the builder. A return value indicates the number of bytes successfully written.</summary>
      <param name="source" />
      <param name="byteCount" />
      <returns>The number of bytes successfully written from <paramref name="source" />.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="byteCount" /> is negative.</exception>
      <exception cref="T:System.InvalidOperationException">The builder is not writable; it has been linked with another one.</exception>
    </member>
    <member name="M:System.Reflection.Metadata.BlobBuilder.WriteBoolean(System.Boolean)">
      <summary>Writes a <see cref="T:System.Boolean" /> value to the builder.</summary>
      <param name="value">The value to write.</param>
      <exception cref="T:System.InvalidOperationException">The builder is not writable; it has been linked with another one.</exception>
    </member>
    <member name="M:System.Reflection.Metadata.BlobBuilder.WriteByte(System.Byte)">
      <summary>Writes a <see cref="T:System.Byte" /> value to the builder.</summary>
      <param name="value">The value to write.</param>
      <exception cref="T:System.InvalidOperationException">The builder is not writable; it has been linked with another one.</exception>
    </member>
    <member name="M:System.Reflection.Metadata.BlobBuilder.WriteBytes(System.Byte*,System.Int32)">
      <summary>Writes a specified number of bytes from a buffer to the builder.</summary>
      <param name="buffer" />
      <param name="byteCount">The number of bytes to write.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="buffer" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="byteCount" /> is negative.</exception>
      <exception cref="T:System.InvalidOperationException">The builder is not writable, it has been linked with another one.</exception>
    </member>
    <member name="M:System.Reflection.Metadata.BlobBuilder.WriteBytes(System.Byte,System.Int32)">
      <summary>Writes a specified number of occurrences of a byte value to the builder.</summary>
      <param name="value" />
      <param name="byteCount">The number of occurences of <paramref name="value" /> to write.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="byteCount" /> is negative.</exception>
      <exception cref="T:System.InvalidOperationException">The builder is not writable, it has been linked with another one.</exception>
    </member>
    <member name="M:System.Reflection.Metadata.BlobBuilder.WriteBytes(System.Byte[])">
      <summary>Writes the contents of a byte array to the builder.</summary>
      <param name="buffer">The byte array to write.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="buffer" /> is <see langword="null" />.</exception>
      <exception cref="T:System.InvalidOperationException">The builder is not writable; it has been linked with another one.</exception>
    </member>
    <member name="M:System.Reflection.Metadata.BlobBuilder.WriteBytes(System.Byte[],System.Int32,System.Int32)">
      <summary>Writes a specified number of bytes starting at a specified index in a byte array to the builder.</summary>
      <param name="buffer" />
      <param name="start" />
      <param name="byteCount">The number of bytes to write.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="buffer" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The range specified by <paramref name="start" /> and <paramref name="byteCount" /> falls outside of the bounds of <paramref name="buffer" />.</exception>
      <exception cref="T:System.InvalidOperationException">The builder is not writable; it has been linked with another one.</exception>
    </member>
    <member name="M:System.Reflection.Metadata.BlobBuilder.WriteBytes(System.Collections.Immutable.ImmutableArray{System.Byte})">
      <summary>Writes the contents of an immutable byte array to the builder.</summary>
      <param name="buffer">The array to write.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="buffer" /> is <see langword="null" />.</exception>
      <exception cref="T:System.InvalidOperationException">The builder is not writable; it has been linked with another one.</exception>
    </member>
    <member name="M:System.Reflection.Metadata.BlobBuilder.WriteBytes(System.Collections.Immutable.ImmutableArray{System.Byte},System.Int32,System.Int32)">
      <summary>Writes a specified number of bytes starting at a specified index of an immutable array to the builder.</summary>
      <param name="buffer" />
      <param name="start" />
      <param name="byteCount">The number of bytes to write.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="buffer" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The range specified by <paramref name="start" /> and <paramref name="byteCount" /> falls outside of the bounds of the <paramref name="buffer" />.</exception>
      <exception cref="T:System.InvalidOperationException">The builder is not writable; it has been linked with another one.</exception>
    </member>
    <member name="M:System.Reflection.Metadata.BlobBuilder.WriteCompressedInteger(System.Int32)">
      <summary>Implements compressed unsigned integer encoding as defined by ECMA-335-II chapter 23.2: Blobs and signatures.</summary>
      <param name="value">The value to write.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="value" /> can't be represented as a compressed unsigned integer.</exception>
      <exception cref="T:System.InvalidOperationException">The builder is not writable; it has been linked with another one.</exception>
    </member>
    <member name="M:System.Reflection.Metadata.BlobBuilder.WriteCompressedSignedInteger(System.Int32)">
      <summary>Implements compressed signed integer encoding as defined by ECMA-335-II chapter 23.2: Blobs and signatures.</summary>
      <param name="value">The value to write.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="value" /> can't be represented as a compressed signed integer.</exception>
      <exception cref="T:System.InvalidOperationException">The builder is not writable; it has been linked with another one.</exception>
    </member>
    <member name="M:System.Reflection.Metadata.BlobBuilder.WriteConstant(System.Object)">
      <summary>Writes a constant value (see ECMA-335 Partition II section 22.9) at the current position.</summary>
      <param name="value">The constant value to write.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="value" /> is not of a constant type.</exception>
      <exception cref="T:System.InvalidOperationException">The builder is not writable; it has been linked with another one.</exception>
    </member>
    <member name="M:System.Reflection.Metadata.BlobBuilder.WriteContentTo(System.IO.Stream)">
      <param name="destination" />
      <exception cref="T:System.ArgumentNullException">
        <paramref name="destination" /> is <see langword="null" />.</exception>
      <exception cref="T:System.InvalidOperationException">Content is not available, the builder has been linked with another one.</exception>
    </member>
    <member name="M:System.Reflection.Metadata.BlobBuilder.WriteContentTo(System.Reflection.Metadata.BlobBuilder)">
      <param name="destination" />
      <exception cref="T:System.ArgumentNullException">
        <paramref name="destination" /> is <see langword="null" />.</exception>
      <exception cref="T:System.InvalidOperationException">Content is not available, the builder has been linked with another one.</exception>
    </member>
    <member name="M:System.Reflection.Metadata.BlobBuilder.WriteContentTo(System.Reflection.Metadata.BlobWriter@)">
      <param name="destination" />
      <exception cref="T:System.ArgumentNullException">
        <paramref name="destination" /> is default (<see cref="T:System.Reflection.Metadata.BlobWriter" />).</exception>
      <exception cref="T:System.InvalidOperationException">Content is not available, the builder has been linked with another one.</exception>
    </member>
    <member name="M:System.Reflection.Metadata.BlobBuilder.WriteDateTime(System.DateTime)">
      <param name="value" />
      <exception cref="T:System.InvalidOperationException">Builder is not writable, it has been linked with another one.</exception>
    </member>
    <member name="M:System.Reflection.Metadata.BlobBuilder.WriteDecimal(System.Decimal)">
      <param name="value" />
      <exception cref="T:System.InvalidOperationException">Builder is not writable, it has been linked with another one.</exception>
    </member>
    <member name="M:System.Reflection.Metadata.BlobBuilder.WriteDouble(System.Double)">
      <param name="value" />
      <exception cref="T:System.InvalidOperationException">Builder is not writable, it has been linked with another one.</exception>
    </member>
    <member name="M:System.Reflection.Metadata.BlobBuilder.WriteGuid(System.Guid)">
      <param name="value" />
      <exception cref="T:System.InvalidOperationException">Builder is not writable, it has been linked with another one.</exception>
    </member>
    <member name="M:System.Reflection.Metadata.BlobBuilder.WriteInt16(System.Int16)">
      <param name="value" />
      <exception cref="T:System.InvalidOperationException">Builder is not writable, it has been linked with another one.</exception>
    </member>
    <member name="M:System.Reflection.Metadata.BlobBuilder.WriteInt16BE(System.Int16)">
      <param name="value" />
      <exception cref="T:System.InvalidOperationException">Builder is not writable, it has been linked with another one.</exception>
    </member>
    <member name="M:System.Reflection.Metadata.BlobBuilder.WriteInt32(System.Int32)">
      <param name="value" />
      <exception cref="T:System.InvalidOperationException">Builder is not writable, it has been linked with another one.</exception>
    </member>
    <member name="M:System.Reflection.Metadata.BlobBuilder.WriteInt32BE(System.Int32)">
      <param name="value" />
      <exception cref="T:System.InvalidOperationException">Builder is not writable, it has been linked with another one.</exception>
    </member>
    <member name="M:System.Reflection.Metadata.BlobBuilder.WriteInt64(System.Int64)">
      <param name="value" />
      <exception cref="T:System.InvalidOperationException">Builder is not writable, it has been linked with another one.</exception>
    </member>
    <member name="M:System.Reflection.Metadata.BlobBuilder.WriteReference(System.Int32,System.Boolean)">
      <summary>Writes a reference to a heap (heap offset) or a table (row number).</summary>
      <param name="reference">Heap offset or table row number.</param>
      <param name="isSmall">
        <see langword="true" /> to encode the reference as a 16-bit integer; <see langword="false" /> to encode it as a 32-bit integer.</param>
      <exception cref="T:System.InvalidOperationException">Builder is not writable, it has been linked with another one.</exception>
    </member>
    <member name="M:System.Reflection.Metadata.BlobBuilder.WriteSByte(System.SByte)">
      <param name="value" />
      <exception cref="T:System.InvalidOperationException">Builder is not writable, it has been linked with another one.</exception>
    </member>
    <member name="M:System.Reflection.Metadata.BlobBuilder.WriteSerializedString(System.String)">
      <summary>Writes a string in SerString format (see ECMA-335-II 23.3 Custom attributes).</summary>
      <param name="value" />
      <exception cref="T:System.InvalidOperationException">Builder is not writable, it has been linked with another one.</exception>
    </member>
    <member name="M:System.Reflection.Metadata.BlobBuilder.WriteSingle(System.Single)">
      <param name="value" />
      <exception cref="T:System.InvalidOperationException">Builder is not writable, it has been linked with another one.</exception>
    </member>
    <member name="M:System.Reflection.Metadata.BlobBuilder.WriteUInt16(System.UInt16)">
      <param name="value" />
      <exception cref="T:System.InvalidOperationException">Builder is not writable, it has been linked with another one.</exception>
    </member>
    <member name="M:System.Reflection.Metadata.BlobBuilder.WriteUInt16BE(System.UInt16)">
      <param name="value" />
      <exception cref="T:System.InvalidOperationException">Builder is not writable, it has been linked with another one.</exception>
    </member>
    <member name="M:System.Reflection.Metadata.BlobBuilder.WriteUInt32(System.UInt32)">
      <param name="value" />
      <exception cref="T:System.InvalidOperationException">Builder is not writable, it has been linked with another one.</exception>
    </member>
    <member name="M:System.Reflection.Metadata.BlobBuilder.WriteUInt32BE(System.UInt32)">
      <param name="value" />
      <exception cref="T:System.InvalidOperationException">Builder is not writable, it has been linked with another one.</exception>
    </member>
    <member name="M:System.Reflection.Metadata.BlobBuilder.WriteUInt64(System.UInt64)">
      <param name="value" />
      <exception cref="T:System.InvalidOperationException">Builder is not writable, it has been linked with another one.</exception>
    </member>
    <member name="M:System.Reflection.Metadata.BlobBuilder.WriteUserString(System.String)">
      <summary>Writes a string in User String (#US) heap format (see ECMA-335-II 24.2.4 #US and #Blob heaps).</summary>
      <param name="value" />
      <exception cref="T:System.InvalidOperationException">Builder is not writable, it has been linked with another one.</exception>
    </member>
    <member name="M:System.Reflection.Metadata.BlobBuilder.WriteUTF16(System.Char[])">
      <summary>Writes a UTF16 (little-endian) encoded character array at the current position.</summary>
      <param name="value" />
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" /> is <see langword="null" />.</exception>
      <exception cref="T:System.InvalidOperationException">Builder is not writable, it has been linked with another one.</exception>
    </member>
    <member name="M:System.Reflection.Metadata.BlobBuilder.WriteUTF16(System.String)">
      <summary>Writes UTF16 (little-endian) encoded string at the current position.</summary>
      <param name="value" />
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" /> is <see langword="null" />.</exception>
      <exception cref="T:System.InvalidOperationException">Builder is not writable, it has been linked with another one.</exception>
    </member>
    <member name="M:System.Reflection.Metadata.BlobBuilder.WriteUTF8(System.String,System.Boolean)">
      <summary>Writes a UTF8 encoded string at the current position.</summary>
      <param name="value">Constant value.</param>
      <param name="allowUnpairedSurrogates">
        <see langword="true" /> to encode unpaired surrogates as specified; <see langword="false" /> to replace them with a U+FFFD character.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" /> is <see langword="null" />.</exception>
      <exception cref="T:System.InvalidOperationException">Builder is not writable, it has been linked with another one.</exception>
    </member>
    <member name="T:System.Reflection.Metadata.BlobContentId" />
    <member name="M:System.Reflection.Metadata.BlobContentId.#ctor(System.Byte[])">
      <param name="id" />
    </member>
    <member name="M:System.Reflection.Metadata.BlobContentId.#ctor(System.Collections.Immutable.ImmutableArray{System.Byte})">
      <param name="id" />
    </member>
    <member name="M:System.Reflection.Metadata.BlobContentId.#ctor(System.Guid,System.UInt32)">
      <param name="guid" />
      <param name="stamp" />
    </member>
    <member name="M:System.Reflection.Metadata.BlobContentId.Equals(System.Object)">
      <param name="obj" />
    </member>
    <member name="M:System.Reflection.Metadata.BlobContentId.Equals(System.Reflection.Metadata.BlobContentId)">
      <param name="other" />
    </member>
    <member name="M:System.Reflection.Metadata.BlobContentId.FromHash(System.Byte[])">
      <param name="hashCode" />
    </member>
    <member name="M:System.Reflection.Metadata.BlobContentId.FromHash(System.Collections.Immutable.ImmutableArray{System.Byte})">
      <param name="hashCode" />
    </member>
    <member name="M:System.Reflection.Metadata.BlobContentId.GetHashCode" />
    <member name="M:System.Reflection.Metadata.BlobContentId.GetTimeBasedProvider" />
    <member name="P:System.Reflection.Metadata.BlobContentId.Guid" />
    <member name="P:System.Reflection.Metadata.BlobContentId.IsDefault" />
    <member name="M:System.Reflection.Metadata.BlobContentId.op_Equality(System.Reflection.Metadata.BlobContentId,System.Reflection.Metadata.BlobContentId)">
      <param name="left" />
      <param name="right" />
    </member>
    <member name="M:System.Reflection.Metadata.BlobContentId.op_Inequality(System.Reflection.Metadata.BlobContentId,System.Reflection.Metadata.BlobContentId)">
      <param name="left" />
      <param name="right" />
    </member>
    <member name="P:System.Reflection.Metadata.BlobContentId.Stamp" />
    <member name="T:System.Reflection.Metadata.BlobHandle" />
    <member name="M:System.Reflection.Metadata.BlobHandle.Equals(System.Object)">
      <param name="obj" />
    </member>
    <member name="M:System.Reflection.Metadata.BlobHandle.Equals(System.Reflection.Metadata.BlobHandle)">
      <param name="other" />
    </member>
    <member name="M:System.Reflection.Metadata.BlobHandle.GetHashCode" />
    <member name="P:System.Reflection.Metadata.BlobHandle.IsNil" />
    <member name="M:System.Reflection.Metadata.BlobHandle.op_Equality(System.Reflection.Metadata.BlobHandle,System.Reflection.Metadata.BlobHandle)">
      <param name="left" />
      <param name="right" />
    </member>
    <member name="M:System.Reflection.Metadata.BlobHandle.op_Explicit(System.Reflection.Metadata.Handle)~System.Reflection.Metadata.BlobHandle">
      <param name="handle" />
    </member>
    <member name="M:System.Reflection.Metadata.BlobHandle.op_Implicit(System.Reflection.Metadata.BlobHandle)~System.Reflection.Metadata.Handle">
      <param name="handle" />
    </member>
    <member name="M:System.Reflection.Metadata.BlobHandle.op_Inequality(System.Reflection.Metadata.BlobHandle,System.Reflection.Metadata.BlobHandle)">
      <param name="left" />
      <param name="right" />
    </member>
    <member name="T:System.Reflection.Metadata.BlobReader" />
    <member name="M:System.Reflection.Metadata.BlobReader.#ctor(System.Byte*,System.Int32)">
      <summary>Creates a reader of the specified memory block.</summary>
      <param name="buffer">A pointer to the start of the memory block.</param>
      <param name="length">Length in bytes of the memory block.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="buffer" /> is <see langword="null" /> and <paramref name="length" /> is greater than zero.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="length" /> is negative.</exception>
      <exception cref="T:System.PlatformNotSupportedException">The current platform is not little-endian.</exception>
    </member>
    <member name="M:System.Reflection.Metadata.BlobReader.Align(System.Byte)">
      <summary>Repositions the reader forward by the number of bytes required to satisfy the given alignment.</summary>
      <param name="alignment" />
    </member>
    <member name="P:System.Reflection.Metadata.BlobReader.CurrentPointer">
      <summary>Gets a pointer to the byte at the current position of the reader.</summary>
    </member>
    <member name="M:System.Reflection.Metadata.BlobReader.IndexOf(System.Byte)">
      <summary>Searches for a specified byte in the blob following the current position.</summary>
      <param name="value">The byte value to find.</param>
      <returns>The index relative to the current position, or -1 if the byte is not found in the blob following the current position.</returns>
    </member>
    <member name="P:System.Reflection.Metadata.BlobReader.Length">
      <summary>Gets the total length of the underlying memory block.</summary>
    </member>
    <member name="P:System.Reflection.Metadata.BlobReader.Offset">
      <summary>Gets or sets the offset from the start of the blob to the current position.</summary>
      <exception cref="T:System.BadImageFormatException">The offset is set outside the bounds of the underlying reader.</exception>
    </member>
    <member name="M:System.Reflection.Metadata.BlobReader.ReadBlobHandle">
      <summary>Reads a Blob heap handle encoded as a compressed integer.</summary>
    </member>
    <member name="M:System.Reflection.Metadata.BlobReader.ReadBoolean" />
    <member name="M:System.Reflection.Metadata.BlobReader.ReadByte" />
    <member name="M:System.Reflection.Metadata.BlobReader.ReadBytes(System.Int32)">
      <summary>Reads bytes starting at the current position.</summary>
      <param name="byteCount">The number of bytes to read.</param>
      <returns>The byte array.</returns>
      <exception cref="T:System.BadImageFormatException">
        <paramref name="byteCount" /> bytes not available.</exception>
    </member>
    <member name="M:System.Reflection.Metadata.BlobReader.ReadBytes(System.Int32,System.Byte[],System.Int32)">
      <summary>Reads bytes starting at the current position and writes them to the specified buffer starting at the specified offset.</summary>
      <param name="byteCount">The number of bytes to read.</param>
      <param name="buffer">The destination buffer the bytes read will be written to.</param>
      <param name="bufferOffset">The offset in the destination buffer where the bytes read will be written.</param>
      <exception cref="T:System.BadImageFormatException">
        <paramref name="byteCount" /> bytes not available.</exception>
    </member>
    <member name="M:System.Reflection.Metadata.BlobReader.ReadChar" />
    <member name="M:System.Reflection.Metadata.BlobReader.ReadCompressedInteger">
      <summary>Reads an unsigned compressed integer value. See Metadata Specification section II.23.2: Blobs and signatures.</summary>
      <returns>The value of the compressed integer that was read.</returns>
      <exception cref="T:System.BadImageFormatException">The data at the current position was not a valid compressed integer.</exception>
    </member>
    <member name="M:System.Reflection.Metadata.BlobReader.ReadCompressedSignedInteger">
      <summary>Reads a signed compressed integer value. See Metadata Specification section II.23.2: Blobs and signatures.</summary>
      <returns>The value of the compressed integer that was read.</returns>
      <exception cref="T:System.BadImageFormatException">The data at the current position was not a valid compressed integer.</exception>
    </member>
    <member name="M:System.Reflection.Metadata.BlobReader.ReadConstant(System.Reflection.Metadata.ConstantTypeCode)">
      <summary>Reads a constant value (see ECMA-335 Partition II section 22.9) from the current position.</summary>
      <param name="typeCode" />
      <returns>A boxed constant value. To avoid allocating the object use Read* methods directly.</returns>
      <exception cref="T:System.BadImageFormatException">Error while reading from the blob.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="typeCode" /> is not a valid <see cref="T:System.Reflection.Metadata.ConstantTypeCode" />.</exception>
    </member>
    <member name="M:System.Reflection.Metadata.BlobReader.ReadDateTime" />
    <member name="M:System.Reflection.Metadata.BlobReader.ReadDecimal">
      <summary>Reads a <see cref="T:System.Decimal" /> number.</summary>
      <exception cref="T:System.BadImageFormatException">The data at the current position was not a valid <see cref="T:System.Decimal" /> number.</exception>
    </member>
    <member name="M:System.Reflection.Metadata.BlobReader.ReadDouble" />
    <member name="M:System.Reflection.Metadata.BlobReader.ReadGuid" />
    <member name="M:System.Reflection.Metadata.BlobReader.ReadInt16" />
    <member name="M:System.Reflection.Metadata.BlobReader.ReadInt32" />
    <member name="M:System.Reflection.Metadata.BlobReader.ReadInt64" />
    <member name="M:System.Reflection.Metadata.BlobReader.ReadSByte" />
    <member name="M:System.Reflection.Metadata.BlobReader.ReadSerializationTypeCode">
      <summary>Reads a type code encoded in a serialized custom attribute value.</summary>
      <returns>
        <see cref="F:System.Reflection.Metadata.SerializationTypeCode.Invalid" /> if the encoding is invalid.</returns>
    </member>
    <member name="M:System.Reflection.Metadata.BlobReader.ReadSerializedString">
      <summary>Reads a string encoded as a compressed integer containing its length followed by its contents in UTF8. Null strings are encoded as a single 0xFF byte.</summary>
      <returns>A string value, or <see langword="null" />.</returns>
      <exception cref="T:System.BadImageFormatException">The encoding is invalid.</exception>
    </member>
    <member name="M:System.Reflection.Metadata.BlobReader.ReadSignatureHeader" />
    <member name="M:System.Reflection.Metadata.BlobReader.ReadSignatureTypeCode">
      <summary>Reads a type code encoded in a signature.</summary>
      <returns>The type code encoded in the serialized custom attribute value if the encoding is valid, or <see cref="F:System.Reflection.Metadata.SignatureTypeCode.Invalid" /> if the encoding is invalid.</returns>
    </member>
    <member name="M:System.Reflection.Metadata.BlobReader.ReadSingle" />
    <member name="M:System.Reflection.Metadata.BlobReader.ReadTypeHandle">
      <summary>Reads a type handle encoded in a signature as TypeDefOrRefOrSpecEncoded (see ECMA-335 II.23.2.8).</summary>
      <returns>The handle when the encoding is valid. Otherwise, a handle where the <see cref="P:System.Reflection.Metadata.EntityHandle.IsNil" /> property is <see langword="true" />.</returns>
    </member>
    <member name="M:System.Reflection.Metadata.BlobReader.ReadUInt16" />
    <member name="M:System.Reflection.Metadata.BlobReader.ReadUInt32" />
    <member name="M:System.Reflection.Metadata.BlobReader.ReadUInt64" />
    <member name="M:System.Reflection.Metadata.BlobReader.ReadUTF16(System.Int32)">
      <summary>Reads a UTF16 (little-endian) encoded string starting at the current position.</summary>
      <param name="byteCount">The number of bytes to read.</param>
      <returns>The string.</returns>
      <exception cref="T:System.BadImageFormatException">
        <paramref name="byteCount" /> bytes not available.</exception>
    </member>
    <member name="M:System.Reflection.Metadata.BlobReader.ReadUTF8(System.Int32)">
      <summary>Reads a UTF8 encoded string starting at the current position.</summary>
      <param name="byteCount">The number of bytes to read.</param>
      <returns>The string.</returns>
      <exception cref="T:System.BadImageFormatException">
        <paramref name="byteCount" /> bytes not available.</exception>
    </member>
    <member name="P:System.Reflection.Metadata.BlobReader.RemainingBytes">
      <summary>Gets the number of bytes remaining from current position to the end of the underlying memory block.</summary>
    </member>
    <member name="M:System.Reflection.Metadata.BlobReader.Reset">
      <summary>Repositions the reader to the start of the underlying memory block.</summary>
    </member>
    <member name="P:System.Reflection.Metadata.BlobReader.StartPointer">
      <summary>Gets a pointer to the byte at the start of the underlying memory block.</summary>
    </member>
    <member name="M:System.Reflection.Metadata.BlobReader.TryReadCompressedInteger(System.Int32@)">
      <summary>Reads an unsigned compressed integer value. See Metadata Specification section II.23.2: Blobs and signatures.</summary>
      <param name="value">The value of the compressed integer that was read.</param>
      <returns>
        <see langword="true" /> if the value was read successfully. <see langword="false" /> if the data at the current position was not a valid compressed integer.</returns>
    </member>
    <member name="M:System.Reflection.Metadata.BlobReader.TryReadCompressedSignedInteger(System.Int32@)">
      <summary>Reads a signed compressed integer value. See Metadata Specification section II.23.2: Blobs and signatures.</summary>
      <param name="value">The value of the compressed integer that was read.</param>
      <returns>
        <see langword="true" /> if the value was read successfully. <see langword="false" /> if the data at the current position was not a valid compressed integer.</returns>
    </member>
    <member name="T:System.Reflection.Metadata.BlobWriter" />
    <member name="M:System.Reflection.Metadata.BlobWriter.#ctor(System.Byte[])">
      <param name="buffer" />
    </member>
    <member name="M:System.Reflection.Metadata.BlobWriter.#ctor(System.Byte[],System.Int32,System.Int32)">
      <param name="buffer" />
      <param name="start" />
      <param name="count" />
    </member>
    <member name="M:System.Reflection.Metadata.BlobWriter.#ctor(System.Int32)">
      <param name="size" />
    </member>
    <member name="M:System.Reflection.Metadata.BlobWriter.#ctor(System.Reflection.Metadata.Blob)">
      <param name="blob" />
    </member>
    <member name="M:System.Reflection.Metadata.BlobWriter.Align(System.Int32)">
      <param name="alignment" />
    </member>
    <member name="P:System.Reflection.Metadata.BlobWriter.Blob" />
    <member name="M:System.Reflection.Metadata.BlobWriter.Clear" />
    <member name="M:System.Reflection.Metadata.BlobWriter.ContentEquals(System.Reflection.Metadata.BlobWriter)">
      <summary>Compares the current content of this writer with another one.</summary>
      <param name="other" />
    </member>
    <member name="P:System.Reflection.Metadata.BlobWriter.Length" />
    <member name="P:System.Reflection.Metadata.BlobWriter.Offset" />
    <member name="M:System.Reflection.Metadata.BlobWriter.PadTo(System.Int32)">
      <param name="offset" />
    </member>
    <member name="P:System.Reflection.Metadata.BlobWriter.RemainingBytes" />
    <member name="M:System.Reflection.Metadata.BlobWriter.ToArray" />
    <member name="M:System.Reflection.Metadata.BlobWriter.ToArray(System.Int32,System.Int32)">
      <param name="start" />
      <param name="byteCount" />
      <exception cref="T:System.ArgumentOutOfRangeException">The range specified by <paramref name="start" /> and <paramref name="byteCount" /> falls outside of the bounds of the buffer content.</exception>
    </member>
    <member name="M:System.Reflection.Metadata.BlobWriter.ToImmutableArray" />
    <member name="M:System.Reflection.Metadata.BlobWriter.ToImmutableArray(System.Int32,System.Int32)">
      <param name="start" />
      <param name="byteCount" />
      <exception cref="T:System.ArgumentOutOfRangeException">Range specified by <paramref name="start" /> and <paramref name="byteCount" /> falls outside of the bounds of the buffer content.</exception>
    </member>
    <member name="M:System.Reflection.Metadata.BlobWriter.WriteBoolean(System.Boolean)">
      <param name="value" />
    </member>
    <member name="M:System.Reflection.Metadata.BlobWriter.WriteByte(System.Byte)">
      <param name="value" />
    </member>
    <member name="M:System.Reflection.Metadata.BlobWriter.WriteBytes(System.Byte*,System.Int32)">
      <param name="buffer" />
      <param name="byteCount" />
      <exception cref="T:System.ArgumentNullException">
        <paramref name="buffer" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="byteCount" /> is negative.</exception>
    </member>
    <member name="M:System.Reflection.Metadata.BlobWriter.WriteBytes(System.Byte,System.Int32)">
      <param name="value" />
      <param name="byteCount" />
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="byteCount" /> is negative.</exception>
    </member>
    <member name="M:System.Reflection.Metadata.BlobWriter.WriteBytes(System.Byte[])">
      <param name="buffer" />
      <exception cref="T:System.ArgumentNullException">
        <paramref name="buffer" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Reflection.Metadata.BlobWriter.WriteBytes(System.Byte[],System.Int32,System.Int32)">
      <param name="buffer" />
      <param name="start" />
      <param name="byteCount" />
      <exception cref="T:System.ArgumentNullException">
        <paramref name="buffer" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Range specified by <paramref name="start" /> and <paramref name="byteCount" /> falls outside of the bounds of the <paramref name="buffer" />.</exception>
    </member>
    <member name="M:System.Reflection.Metadata.BlobWriter.WriteBytes(System.Collections.Immutable.ImmutableArray{System.Byte})">
      <param name="buffer" />
      <exception cref="T:System.ArgumentNullException">
        <paramref name="buffer" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Reflection.Metadata.BlobWriter.WriteBytes(System.Collections.Immutable.ImmutableArray{System.Byte},System.Int32,System.Int32)">
      <param name="buffer" />
      <param name="start" />
      <param name="byteCount" />
      <exception cref="T:System.ArgumentNullException">
        <paramref name="buffer" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Range specified by <paramref name="start" /> and <paramref name="byteCount" /> falls outside of the bounds of the <paramref name="buffer" />.</exception>
    </member>
    <member name="M:System.Reflection.Metadata.BlobWriter.WriteBytes(System.IO.Stream,System.Int32)">
      <param name="source" />
      <param name="byteCount" />
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="byteCount" /> is negative.</exception>
    </member>
    <member name="M:System.Reflection.Metadata.BlobWriter.WriteBytes(System.Reflection.Metadata.BlobBuilder)">
      <param name="source" />
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Reflection.Metadata.BlobWriter.WriteCompressedInteger(System.Int32)">
      <summary>Implements compressed unsigned integer encoding as defined by ECMA-335-II chapter 23.2: Blobs and signatures.</summary>
      <param name="value" />
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="value" /> can't be represented as a compressed unsigned integer.</exception>
    </member>
    <member name="M:System.Reflection.Metadata.BlobWriter.WriteCompressedSignedInteger(System.Int32)">
      <summary>Implements compressed signed integer encoding as defined by ECMA-335-II chapter 23.2: Blobs and signatures.</summary>
      <param name="value" />
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="value" /> can't be represented as a compressed signed integer.</exception>
    </member>
    <member name="M:System.Reflection.Metadata.BlobWriter.WriteConstant(System.Object)">
      <summary>Writes a constant value (see ECMA-335 Partition II section 22.9) at the current position.</summary>
      <param name="value" />
      <exception cref="T:System.ArgumentException">
        <paramref name="value" /> is not of a constant type.</exception>
    </member>
    <member name="M:System.Reflection.Metadata.BlobWriter.WriteDateTime(System.DateTime)">
      <param name="value" />
    </member>
    <member name="M:System.Reflection.Metadata.BlobWriter.WriteDecimal(System.Decimal)">
      <param name="value" />
    </member>
    <member name="M:System.Reflection.Metadata.BlobWriter.WriteDouble(System.Double)">
      <param name="value" />
    </member>
    <member name="M:System.Reflection.Metadata.BlobWriter.WriteGuid(System.Guid)">
      <param name="value" />
    </member>
    <member name="M:System.Reflection.Metadata.BlobWriter.WriteInt16(System.Int16)">
      <param name="value" />
    </member>
    <member name="M:System.Reflection.Metadata.BlobWriter.WriteInt16BE(System.Int16)">
      <param name="value" />
    </member>
    <member name="M:System.Reflection.Metadata.BlobWriter.WriteInt32(System.Int32)">
      <param name="value" />
    </member>
    <member name="M:System.Reflection.Metadata.BlobWriter.WriteInt32BE(System.Int32)">
      <param name="value" />
    </member>
    <member name="M:System.Reflection.Metadata.BlobWriter.WriteInt64(System.Int64)">
      <param name="value" />
    </member>
    <member name="M:System.Reflection.Metadata.BlobWriter.WriteReference(System.Int32,System.Boolean)">
      <summary>Writes a reference to a heap (heap offset) or a table (row number).</summary>
      <param name="reference">Heap offset or table row number.</param>
      <param name="isSmall">
        <see langword="true" /> to encode the reference as 16-bit integer, <see langword="false" /> to encode as 32-bit integer.</param>
    </member>
    <member name="M:System.Reflection.Metadata.BlobWriter.WriteSByte(System.SByte)">
      <param name="value" />
    </member>
    <member name="M:System.Reflection.Metadata.BlobWriter.WriteSerializedString(System.String)">
      <summary>Writes a string in SerString format (see ECMA-335-II 23.3 Custom attributes).</summary>
      <param name="str" />
      <exception cref="T:System.InvalidOperationException">The builder is not writable; it has been linked with another one.</exception>
    </member>
    <member name="M:System.Reflection.Metadata.BlobWriter.WriteSingle(System.Single)">
      <param name="value" />
    </member>
    <member name="M:System.Reflection.Metadata.BlobWriter.WriteUInt16(System.UInt16)">
      <param name="value" />
    </member>
    <member name="M:System.Reflection.Metadata.BlobWriter.WriteUInt16BE(System.UInt16)">
      <param name="value" />
    </member>
    <member name="M:System.Reflection.Metadata.BlobWriter.WriteUInt32(System.UInt32)">
      <param name="value" />
    </member>
    <member name="M:System.Reflection.Metadata.BlobWriter.WriteUInt32BE(System.UInt32)">
      <param name="value" />
    </member>
    <member name="M:System.Reflection.Metadata.BlobWriter.WriteUInt64(System.UInt64)">
      <param name="value" />
    </member>
    <member name="M:System.Reflection.Metadata.BlobWriter.WriteUserString(System.String)">
      <summary>Writes a string in User String (#US) heap format (see ECMA-335-II 24.2.4 #US and #Blob heaps).</summary>
      <param name="value" />
      <exception cref="T:System.InvalidOperationException">Builder is not writable, it has been linked with another one.</exception>
    </member>
    <member name="M:System.Reflection.Metadata.BlobWriter.WriteUTF16(System.Char[])">
      <summary>Writes a UTF16 (little-endian) encoded string at the current position.</summary>
      <param name="value" />
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Reflection.Metadata.BlobWriter.WriteUTF16(System.String)">
      <summary>Writes a UTF16 (little-endian) encoded string at the current position.</summary>
      <param name="value" />
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Reflection.Metadata.BlobWriter.WriteUTF8(System.String,System.Boolean)">
      <summary>Writes a UTF8 encoded string at the current position.</summary>
      <param name="value" />
      <param name="allowUnpairedSurrogates" />
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" /> is <see langword="null" />.</exception>
    </member>
    <member name="T:System.Reflection.Metadata.Constant" />
    <member name="P:System.Reflection.Metadata.Constant.Parent">
      <summary>Gets the parent handle (<see cref="T:System.Reflection.Metadata.ParameterHandle" />, <see cref="T:System.Reflection.Metadata.FieldDefinitionHandle" />, or <see cref="T:System.Reflection.Metadata.PropertyDefinitionHandle" />).</summary>
    </member>
    <member name="P:System.Reflection.Metadata.Constant.TypeCode">
      <summary>Gets a type code that identifies the type of the constant value.</summary>
    </member>
    <member name="P:System.Reflection.Metadata.Constant.Value">
      <summary>Gets the constant value.</summary>
    </member>
    <member name="T:System.Reflection.Metadata.ConstantHandle" />
    <member name="M:System.Reflection.Metadata.ConstantHandle.Equals(System.Object)">
      <param name="obj" />
    </member>
    <member name="M:System.Reflection.Metadata.ConstantHandle.Equals(System.Reflection.Metadata.ConstantHandle)">
      <param name="other" />
    </member>
    <member name="M:System.Reflection.Metadata.ConstantHandle.GetHashCode" />
    <member name="P:System.Reflection.Metadata.ConstantHandle.IsNil" />
    <member name="M:System.Reflection.Metadata.ConstantHandle.op_Equality(System.Reflection.Metadata.ConstantHandle,System.Reflection.Metadata.ConstantHandle)">
      <param name="left" />
      <param name="right" />
    </member>
    <member name="M:System.Reflection.Metadata.ConstantHandle.op_Explicit(System.Reflection.Metadata.EntityHandle)~System.Reflection.Metadata.ConstantHandle">
      <param name="handle" />
    </member>
    <member name="M:System.Reflection.Metadata.ConstantHandle.op_Explicit(System.Reflection.Metadata.Handle)~System.Reflection.Metadata.ConstantHandle">
      <param name="handle" />
    </member>
    <member name="M:System.Reflection.Metadata.ConstantHandle.op_Implicit(System.Reflection.Metadata.ConstantHandle)~System.Reflection.Metadata.EntityHandle">
      <param name="handle" />
    </member>
    <member name="M:System.Reflection.Metadata.ConstantHandle.op_Implicit(System.Reflection.Metadata.ConstantHandle)~System.Reflection.Metadata.Handle">
      <param name="handle" />
    </member>
    <member name="M:System.Reflection.Metadata.ConstantHandle.op_Inequality(System.Reflection.Metadata.ConstantHandle,System.Reflection.Metadata.ConstantHandle)">
      <param name="left" />
      <param name="right" />
    </member>
    <member name="T:System.Reflection.Metadata.ConstantTypeCode">
      <summary>Specifies values that represent types of metadata constants.</summary>
    </member>
    <member name="F:System.Reflection.Metadata.ConstantTypeCode.Boolean">
      <summary>A Boolean type.</summary>
    </member>
    <member name="F:System.Reflection.Metadata.ConstantTypeCode.Byte">
      <summary>An unsigned 1-byte integer.</summary>
    </member>
    <member name="F:System.Reflection.Metadata.ConstantTypeCode.Char">
      <summary>A character type.</summary>
    </member>
    <member name="F:System.Reflection.Metadata.ConstantTypeCode.Double">
      <summary>An 8-byte floating point type.</summary>
    </member>
    <member name="F:System.Reflection.Metadata.ConstantTypeCode.Int16">
      <summary>A signed 2-byte integer type.</summary>
    </member>
    <member name="F:System.Reflection.Metadata.ConstantTypeCode.Int32">
      <summary>A signed 4-byte integer type.</summary>
    </member>
    <member name="F:System.Reflection.Metadata.ConstantTypeCode.Int64">
      <summary>A signed 8-byte integer type.</summary>
    </member>
    <member name="F:System.Reflection.Metadata.ConstantTypeCode.Invalid">
      <summary>An invalid type.</summary>
    </member>
    <member name="F:System.Reflection.Metadata.ConstantTypeCode.NullReference">
      <summary>A null reference.</summary>
    </member>
    <member name="F:System.Reflection.Metadata.ConstantTypeCode.SByte">
      <summary>A signed 1-byte integer type.</summary>
    </member>
    <member name="F:System.Reflection.Metadata.ConstantTypeCode.Single">
      <summary>A 4-byte floating point type.</summary>
    </member>
    <member name="F:System.Reflection.Metadata.ConstantTypeCode.String">
      <summary>A <see cref="T:System.String" /> type.</summary>
    </member>
    <member name="F:System.Reflection.Metadata.ConstantTypeCode.UInt16">
      <summary>An unsigned 2-byte integer type.</summary>
    </member>
    <member name="F:System.Reflection.Metadata.ConstantTypeCode.UInt32">
      <summary>An unsigned 4-byte integer type.</summary>
    </member>
    <member name="F:System.Reflection.Metadata.ConstantTypeCode.UInt64">
      <summary>An unsigned 8-byte integer type.</summary>
    </member>
    <member name="T:System.Reflection.Metadata.CustomAttribute" />
    <member name="P:System.Reflection.Metadata.CustomAttribute.Constructor">
      <summary>Gets the constructor (the <see cref="T:System.Reflection.Metadata.MethodDefinitionHandle" /> or <see cref="T:System.Reflection.Metadata.MemberReferenceHandle" />) of the custom attribute type.</summary>
    </member>
    <member name="M:System.Reflection.Metadata.CustomAttribute.DecodeValue``1(System.Reflection.Metadata.ICustomAttributeTypeProvider{``0})">
      <summary>Decodes the arguments encoded in the value blob.</summary>
      <param name="provider" />
      <typeparam name="TType" />
    </member>
    <member name="P:System.Reflection.Metadata.CustomAttribute.Parent">
      <summary>Gets the handle of the metadata entity the attribute is applied to.</summary>
    </member>
    <member name="P:System.Reflection.Metadata.CustomAttribute.Value">
      <summary>Gets the value of the attribute.</summary>
    </member>
    <member name="T:System.Reflection.Metadata.CustomAttributeHandle" />
    <member name="M:System.Reflection.Metadata.CustomAttributeHandle.Equals(System.Object)">
      <param name="obj" />
    </member>
    <member name="M:System.Reflection.Metadata.CustomAttributeHandle.Equals(System.Reflection.Metadata.CustomAttributeHandle)">
      <param name="other" />
    </member>
    <member name="M:System.Reflection.Metadata.CustomAttributeHandle.GetHashCode" />
    <member name="P:System.Reflection.Metadata.CustomAttributeHandle.IsNil" />
    <member name="M:System.Reflection.Metadata.CustomAttributeHandle.op_Equality(System.Reflection.Metadata.CustomAttributeHandle,System.Reflection.Metadata.CustomAttributeHandle)">
      <param name="left" />
      <param name="right" />
    </member>
    <member name="M:System.Reflection.Metadata.CustomAttributeHandle.op_Explicit(System.Reflection.Metadata.EntityHandle)~System.Reflection.Metadata.CustomAttributeHandle">
      <param name="handle" />
    </member>
    <member name="M:System.Reflection.Metadata.CustomAttributeHandle.op_Explicit(System.Reflection.Metadata.Handle)~System.Reflection.Metadata.CustomAttributeHandle">
      <param name="handle" />
    </member>
    <member name="M:System.Reflection.Metadata.CustomAttributeHandle.op_Implicit(System.Reflection.Metadata.CustomAttributeHandle)~System.Reflection.Metadata.EntityHandle">
      <param name="handle" />
    </member>
    <member name="M:System.Reflection.Metadata.CustomAttributeHandle.op_Implicit(System.Reflection.Metadata.CustomAttributeHandle)~System.Reflection.Metadata.Handle">
      <param name="handle" />
    </member>
    <member name="M:System.Reflection.Metadata.CustomAttributeHandle.op_Inequality(System.Reflection.Metadata.CustomAttributeHandle,System.Reflection.Metadata.CustomAttributeHandle)">
      <param name="left" />
      <param name="right" />
    </member>
    <member name="T:System.Reflection.Metadata.CustomAttributeHandleCollection" />
    <member name="P:System.Reflection.Metadata.CustomAttributeHandleCollection.Count" />
    <member name="T:System.Reflection.Metadata.CustomAttributeHandleCollection.Enumerator" />
    <member name="P:System.Reflection.Metadata.CustomAttributeHandleCollection.Enumerator.Current" />
    <member name="M:System.Reflection.Metadata.CustomAttributeHandleCollection.Enumerator.MoveNext" />
    <member name="P:System.Reflection.Metadata.CustomAttributeHandleCollection.Enumerator.System#Collections#IEnumerator#Current" />
    <member name="M:System.Reflection.Metadata.CustomAttributeHandleCollection.Enumerator.System#Collections#IEnumerator#Reset" />
    <member name="M:System.Reflection.Metadata.CustomAttributeHandleCollection.Enumerator.System#IDisposable#Dispose" />
    <member name="M:System.Reflection.Metadata.CustomAttributeHandleCollection.GetEnumerator" />
    <member name="M:System.Reflection.Metadata.CustomAttributeHandleCollection.System#Collections#Generic#IEnumerable{System#Reflection#Metadata#CustomAttributeHandle}#GetEnumerator" />
    <member name="M:System.Reflection.Metadata.CustomAttributeHandleCollection.System#Collections#IEnumerable#GetEnumerator" />
    <member name="T:System.Reflection.Metadata.CustomAttributeNamedArgument`1">
      <summary>Represents a named argument decoded from a custom attribute signature.</summary>
      <typeparam name="TType">The type used to represent types of values decoded from the custom attribute signature.</typeparam>
    </member>
    <member name="M:System.Reflection.Metadata.CustomAttributeNamedArgument`1.#ctor(System.String,System.Reflection.Metadata.CustomAttributeNamedArgumentKind,`0,System.Object)">
      <summary>Initializes a new instance of the <see cref="T:System.Reflection.Metadata.CustomAttributeNamedArgument`1" /> structure using the specified name, kind, type, and value.</summary>
      <param name="name">The name of the argument.</param>
      <param name="kind">The kind of the argument.</param>
      <param name="type">The type of the argument.</param>
      <param name="value">The value of the argument.</param>
    </member>
    <member name="P:System.Reflection.Metadata.CustomAttributeNamedArgument`1.Kind">
      <summary>Gets the kind of argument.</summary>
      <returns>The argument kind.</returns>
    </member>
    <member name="P:System.Reflection.Metadata.CustomAttributeNamedArgument`1.Name">
      <summary>Gets the name of the argument.</summary>
      <returns>The argument name.</returns>
    </member>
    <member name="P:System.Reflection.Metadata.CustomAttributeNamedArgument`1.Type">
      <summary>Gets the type of the argument.</summary>
      <returns>The argument type.</returns>
    </member>
    <member name="P:System.Reflection.Metadata.CustomAttributeNamedArgument`1.Value">
      <summary>Gets the value of the argument.</summary>
      <returns>An object containing the argument value.</returns>
    </member>
    <member name="T:System.Reflection.Metadata.CustomAttributeNamedArgumentKind">
      <summary>Specifies constants that define the kinds of arguments in a custom attribute signature.</summary>
    </member>
    <member name="F:System.Reflection.Metadata.CustomAttributeNamedArgumentKind.Field">
      <summary>A field argument.</summary>
    </member>
    <member name="F:System.Reflection.Metadata.CustomAttributeNamedArgumentKind.Property">
      <summary>A property argument.</summary>
    </member>
    <member name="T:System.Reflection.Metadata.CustomAttributeTypedArgument`1">
      <summary>Represents a typed argument for a custom metadata attribute.</summary>
      <typeparam name="TType">The type of the argument.</typeparam>
    </member>
    <member name="M:System.Reflection.Metadata.CustomAttributeTypedArgument`1.#ctor(`0,System.Object)">
      <summary>Initializes a new instance of the <see cref="T:System.Reflection.Metadata.CustomAttributeTypedArgument`1" /> structure using the specified argument type and value.</summary>
      <param name="type">The type of the argument.</param>
      <param name="value">The argument value.</param>
    </member>
    <member name="P:System.Reflection.Metadata.CustomAttributeTypedArgument`1.Type">
      <summary>Gets the type of the argument.</summary>
      <returns>The argument type.</returns>
    </member>
    <member name="P:System.Reflection.Metadata.CustomAttributeTypedArgument`1.Value">
      <summary>Gets the value of the argument.</summary>
      <returns>The argument value.</returns>
    </member>
    <member name="T:System.Reflection.Metadata.CustomAttributeValue`1">
      <summary>Represents a custom attribute of the type specified by <paramref name="TType" />.</summary>
      <typeparam name="TType">The attribute type.</typeparam>
    </member>
    <member name="M:System.Reflection.Metadata.CustomAttributeValue`1.#ctor(System.Collections.Immutable.ImmutableArray{System.Reflection.Metadata.CustomAttributeTypedArgument{`0}},System.Collections.Immutable.ImmutableArray{System.Reflection.Metadata.CustomAttributeNamedArgument{`0}})">
      <summary>Initializes a new instance of the <see cref="T:System.Reflection.Metadata.CustomAttributeValue`1" /> structure using the specified fixed arguments and named arguments.</summary>
      <param name="fixedArguments">The fixed arguments.</param>
      <param name="namedArguments">The named arguments.</param>
    </member>
    <member name="P:System.Reflection.Metadata.CustomAttributeValue`1.FixedArguments">
      <summary>Gets the fixed arguments for the custom attribute.</summary>
      <returns>An immutable array of arguments.</returns>
    </member>
    <member name="P:System.Reflection.Metadata.CustomAttributeValue`1.NamedArguments">
      <summary>Gets the named arguments for the custom attribute value.</summary>
      <returns>An immutable array of arguments.</returns>
    </member>
    <member name="T:System.Reflection.Metadata.CustomDebugInformation" />
    <member name="P:System.Reflection.Metadata.CustomDebugInformation.Kind" />
    <member name="P:System.Reflection.Metadata.CustomDebugInformation.Parent" />
    <member name="P:System.Reflection.Metadata.CustomDebugInformation.Value" />
    <member name="T:System.Reflection.Metadata.CustomDebugInformationHandle" />
    <member name="M:System.Reflection.Metadata.CustomDebugInformationHandle.Equals(System.Object)">
      <param name="obj" />
    </member>
    <member name="M:System.Reflection.Metadata.CustomDebugInformationHandle.Equals(System.Reflection.Metadata.CustomDebugInformationHandle)">
      <param name="other" />
    </member>
    <member name="M:System.Reflection.Metadata.CustomDebugInformationHandle.GetHashCode" />
    <member name="P:System.Reflection.Metadata.CustomDebugInformationHandle.IsNil" />
    <member name="M:System.Reflection.Metadata.CustomDebugInformationHandle.op_Equality(System.Reflection.Metadata.CustomDebugInformationHandle,System.Reflection.Metadata.CustomDebugInformationHandle)">
      <param name="left" />
      <param name="right" />
    </member>
    <member name="M:System.Reflection.Metadata.CustomDebugInformationHandle.op_Explicit(System.Reflection.Metadata.EntityHandle)~System.Reflection.Metadata.CustomDebugInformationHandle">
      <param name="handle" />
    </member>
    <member name="M:System.Reflection.Metadata.CustomDebugInformationHandle.op_Explicit(System.Reflection.Metadata.Handle)~System.Reflection.Metadata.CustomDebugInformationHandle">
      <param name="handle" />
    </member>
    <member name="M:System.Reflection.Metadata.CustomDebugInformationHandle.op_Implicit(System.Reflection.Metadata.CustomDebugInformationHandle)~System.Reflection.Metadata.EntityHandle">
      <param name="handle" />
    </member>
    <member name="M:System.Reflection.Metadata.CustomDebugInformationHandle.op_Implicit(System.Reflection.Metadata.CustomDebugInformationHandle)~System.Reflection.Metadata.Handle">
      <param name="handle" />
    </member>
    <member name="M:System.Reflection.Metadata.CustomDebugInformationHandle.op_Inequality(System.Reflection.Metadata.CustomDebugInformationHandle,System.Reflection.Metadata.CustomDebugInformationHandle)">
      <param name="left" />
      <param name="right" />
    </member>
    <member name="T:System.Reflection.Metadata.CustomDebugInformationHandleCollection" />
    <member name="P:System.Reflection.Metadata.CustomDebugInformationHandleCollection.Count" />
    <member name="T:System.Reflection.Metadata.CustomDebugInformationHandleCollection.Enumerator" />
    <member name="P:System.Reflection.Metadata.CustomDebugInformationHandleCollection.Enumerator.Current" />
    <member name="M:System.Reflection.Metadata.CustomDebugInformationHandleCollection.Enumerator.MoveNext" />
    <member name="P:System.Reflection.Metadata.CustomDebugInformationHandleCollection.Enumerator.System#Collections#IEnumerator#Current" />
    <member name="M:System.Reflection.Metadata.CustomDebugInformationHandleCollection.Enumerator.System#Collections#IEnumerator#Reset" />
    <member name="M:System.Reflection.Metadata.CustomDebugInformationHandleCollection.Enumerator.System#IDisposable#Dispose" />
    <member name="M:System.Reflection.Metadata.CustomDebugInformationHandleCollection.GetEnumerator" />
    <member name="M:System.Reflection.Metadata.CustomDebugInformationHandleCollection.System#Collections#Generic#IEnumerable{System#Reflection#Metadata#CustomDebugInformationHandle}#GetEnumerator" />
    <member name="M:System.Reflection.Metadata.CustomDebugInformationHandleCollection.System#Collections#IEnumerable#GetEnumerator" />
    <member name="T:System.Reflection.Metadata.DebugMetadataHeader" />
    <member name="P:System.Reflection.Metadata.DebugMetadataHeader.EntryPoint" />
    <member name="P:System.Reflection.Metadata.DebugMetadataHeader.Id" />
    <member name="P:System.Reflection.Metadata.DebugMetadataHeader.IdStartOffset">
      <summary>Gets the offset (in bytes) from the start of the metadata blob to the start of the <see cref="P:System.Reflection.Metadata.DebugMetadataHeader.Id" /> blob.</summary>
    </member>
    <member name="T:System.Reflection.Metadata.DeclarativeSecurityAttribute" />
    <member name="P:System.Reflection.Metadata.DeclarativeSecurityAttribute.Action" />
    <member name="P:System.Reflection.Metadata.DeclarativeSecurityAttribute.Parent" />
    <member name="P:System.Reflection.Metadata.DeclarativeSecurityAttribute.PermissionSet" />
    <member name="T:System.Reflection.Metadata.DeclarativeSecurityAttributeHandle" />
    <member name="M:System.Reflection.Metadata.DeclarativeSecurityAttributeHandle.Equals(System.Object)">
      <param name="obj" />
    </member>
    <member name="M:System.Reflection.Metadata.DeclarativeSecurityAttributeHandle.Equals(System.Reflection.Metadata.DeclarativeSecurityAttributeHandle)">
      <param name="other" />
    </member>
    <member name="M:System.Reflection.Metadata.DeclarativeSecurityAttributeHandle.GetHashCode" />
    <member name="P:System.Reflection.Metadata.DeclarativeSecurityAttributeHandle.IsNil" />
    <member name="M:System.Reflection.Metadata.DeclarativeSecurityAttributeHandle.op_Equality(System.Reflection.Metadata.DeclarativeSecurityAttributeHandle,System.Reflection.Metadata.DeclarativeSecurityAttributeHandle)">
      <param name="left" />
      <param name="right" />
    </member>
    <member name="M:System.Reflection.Metadata.DeclarativeSecurityAttributeHandle.op_Explicit(System.Reflection.Metadata.EntityHandle)~System.Reflection.Metadata.DeclarativeSecurityAttributeHandle">
      <param name="handle" />
    </member>
    <member name="M:System.Reflection.Metadata.DeclarativeSecurityAttributeHandle.op_Explicit(System.Reflection.Metadata.Handle)~System.Reflection.Metadata.DeclarativeSecurityAttributeHandle">
      <param name="handle" />
    </member>
    <member name="M:System.Reflection.Metadata.DeclarativeSecurityAttributeHandle.op_Implicit(System.Reflection.Metadata.DeclarativeSecurityAttributeHandle)~System.Reflection.Metadata.EntityHandle">
      <param name="handle" />
    </member>
    <member name="M:System.Reflection.Metadata.DeclarativeSecurityAttributeHandle.op_Implicit(System.Reflection.Metadata.DeclarativeSecurityAttributeHandle)~System.Reflection.Metadata.Handle">
      <param name="handle" />
    </member>
    <member name="M:System.Reflection.Metadata.DeclarativeSecurityAttributeHandle.op_Inequality(System.Reflection.Metadata.DeclarativeSecurityAttributeHandle,System.Reflection.Metadata.DeclarativeSecurityAttributeHandle)">
      <param name="left" />
      <param name="right" />
    </member>
    <member name="T:System.Reflection.Metadata.DeclarativeSecurityAttributeHandleCollection" />
    <member name="P:System.Reflection.Metadata.DeclarativeSecurityAttributeHandleCollection.Count" />
    <member name="T:System.Reflection.Metadata.DeclarativeSecurityAttributeHandleCollection.Enumerator" />
    <member name="P:System.Reflection.Metadata.DeclarativeSecurityAttributeHandleCollection.Enumerator.Current" />
    <member name="M:System.Reflection.Metadata.DeclarativeSecurityAttributeHandleCollection.Enumerator.MoveNext" />
    <member name="P:System.Reflection.Metadata.DeclarativeSecurityAttributeHandleCollection.Enumerator.System#Collections#IEnumerator#Current" />
    <member name="M:System.Reflection.Metadata.DeclarativeSecurityAttributeHandleCollection.Enumerator.System#Collections#IEnumerator#Reset" />
    <member name="M:System.Reflection.Metadata.DeclarativeSecurityAttributeHandleCollection.Enumerator.System#IDisposable#Dispose" />
    <member name="M:System.Reflection.Metadata.DeclarativeSecurityAttributeHandleCollection.GetEnumerator" />
    <member name="M:System.Reflection.Metadata.DeclarativeSecurityAttributeHandleCollection.System#Collections#Generic#IEnumerable{System#Reflection#Metadata#DeclarativeSecurityAttributeHandle}#GetEnumerator" />
    <member name="M:System.Reflection.Metadata.DeclarativeSecurityAttributeHandleCollection.System#Collections#IEnumerable#GetEnumerator" />
    <member name="T:System.Reflection.Metadata.Document">
      <summary>The source document in the debug metadata.</summary>
    </member>
    <member name="P:System.Reflection.Metadata.Document.Hash">
      <summary>Gets the document content hash.</summary>
    </member>
    <member name="P:System.Reflection.Metadata.Document.HashAlgorithm">
      <summary>Gets the hash algorithm used to calculate the <see cref="P:System.Reflection.Metadata.Document.Hash" /> (SHA1, SHA256, etc.).</summary>
    </member>
    <member name="P:System.Reflection.Metadata.Document.Language">
      <summary>Gets the source code language (C#, VB, F#, etc.).</summary>
    </member>
    <member name="P:System.Reflection.Metadata.Document.Name">
      <summary>Gets the document name blob.</summary>
    </member>
    <member name="T:System.Reflection.Metadata.DocumentHandle" />
    <member name="M:System.Reflection.Metadata.DocumentHandle.Equals(System.Object)">
      <param name="obj" />
    </member>
    <member name="M:System.Reflection.Metadata.DocumentHandle.Equals(System.Reflection.Metadata.DocumentHandle)">
      <param name="other" />
    </member>
    <member name="M:System.Reflection.Metadata.DocumentHandle.GetHashCode" />
    <member name="P:System.Reflection.Metadata.DocumentHandle.IsNil" />
    <member name="M:System.Reflection.Metadata.DocumentHandle.op_Equality(System.Reflection.Metadata.DocumentHandle,System.Reflection.Metadata.DocumentHandle)">
      <param name="left" />
      <param name="right" />
    </member>
    <member name="M:System.Reflection.Metadata.DocumentHandle.op_Explicit(System.Reflection.Metadata.EntityHandle)~System.Reflection.Metadata.DocumentHandle">
      <param name="handle" />
    </member>
    <member name="M:System.Reflection.Metadata.DocumentHandle.op_Explicit(System.Reflection.Metadata.Handle)~System.Reflection.Metadata.DocumentHandle">
      <param name="handle" />
    </member>
    <member name="M:System.Reflection.Metadata.DocumentHandle.op_Implicit(System.Reflection.Metadata.DocumentHandle)~System.Reflection.Metadata.EntityHandle">
      <param name="handle" />
    </member>
    <member name="M:System.Reflection.Metadata.DocumentHandle.op_Implicit(System.Reflection.Metadata.DocumentHandle)~System.Reflection.Metadata.Handle">
      <param name="handle" />
    </member>
    <member name="M:System.Reflection.Metadata.DocumentHandle.op_Inequality(System.Reflection.Metadata.DocumentHandle,System.Reflection.Metadata.DocumentHandle)">
      <param name="left" />
      <param name="right" />
    </member>
    <member name="T:System.Reflection.Metadata.DocumentHandleCollection" />
    <member name="P:System.Reflection.Metadata.DocumentHandleCollection.Count" />
    <member name="T:System.Reflection.Metadata.DocumentHandleCollection.Enumerator" />
    <member name="P:System.Reflection.Metadata.DocumentHandleCollection.Enumerator.Current" />
    <member name="M:System.Reflection.Metadata.DocumentHandleCollection.Enumerator.MoveNext" />
    <member name="P:System.Reflection.Metadata.DocumentHandleCollection.Enumerator.System#Collections#IEnumerator#Current" />
    <member name="M:System.Reflection.Metadata.DocumentHandleCollection.Enumerator.System#Collections#IEnumerator#Reset" />
    <member name="M:System.Reflection.Metadata.DocumentHandleCollection.Enumerator.System#IDisposable#Dispose" />
    <member name="M:System.Reflection.Metadata.DocumentHandleCollection.GetEnumerator" />
    <member name="M:System.Reflection.Metadata.DocumentHandleCollection.System#Collections#Generic#IEnumerable{System#Reflection#Metadata#DocumentHandle}#GetEnumerator" />
    <member name="M:System.Reflection.Metadata.DocumentHandleCollection.System#Collections#IEnumerable#GetEnumerator" />
    <member name="T:System.Reflection.Metadata.DocumentNameBlobHandle">
      <summary>A <see cref="T:System.Reflection.Metadata.BlobHandle" /> representing a blob on #Blob heap in Portable PDB structured as Document Name.</summary>
    </member>
    <member name="M:System.Reflection.Metadata.DocumentNameBlobHandle.Equals(System.Object)">
      <param name="obj" />
    </member>
    <member name="M:System.Reflection.Metadata.DocumentNameBlobHandle.Equals(System.Reflection.Metadata.DocumentNameBlobHandle)">
      <param name="other" />
    </member>
    <member name="M:System.Reflection.Metadata.DocumentNameBlobHandle.GetHashCode" />
    <member name="P:System.Reflection.Metadata.DocumentNameBlobHandle.IsNil" />
    <member name="M:System.Reflection.Metadata.DocumentNameBlobHandle.op_Equality(System.Reflection.Metadata.DocumentNameBlobHandle,System.Reflection.Metadata.DocumentNameBlobHandle)">
      <param name="left" />
      <param name="right" />
    </member>
    <member name="M:System.Reflection.Metadata.DocumentNameBlobHandle.op_Explicit(System.Reflection.Metadata.BlobHandle)~System.Reflection.Metadata.DocumentNameBlobHandle">
      <param name="handle" />
    </member>
    <member name="M:System.Reflection.Metadata.DocumentNameBlobHandle.op_Implicit(System.Reflection.Metadata.DocumentNameBlobHandle)~System.Reflection.Metadata.BlobHandle">
      <param name="handle" />
    </member>
    <member name="M:System.Reflection.Metadata.DocumentNameBlobHandle.op_Inequality(System.Reflection.Metadata.DocumentNameBlobHandle,System.Reflection.Metadata.DocumentNameBlobHandle)">
      <param name="left" />
      <param name="right" />
    </member>
    <member name="T:System.Reflection.Metadata.Ecma335.ArrayShapeEncoder" />
    <member name="M:System.Reflection.Metadata.Ecma335.ArrayShapeEncoder.#ctor(System.Reflection.Metadata.BlobBuilder)">
      <param name="builder" />
    </member>
    <member name="P:System.Reflection.Metadata.Ecma335.ArrayShapeEncoder.Builder" />
    <member name="M:System.Reflection.Metadata.Ecma335.ArrayShapeEncoder.Shape(System.Int32,System.Collections.Immutable.ImmutableArray{System.Int32},System.Collections.Immutable.ImmutableArray{System.Int32})">
      <param name="rank" />
      <param name="sizes" />
      <param name="lowerBounds" />
    </member>
    <member name="T:System.Reflection.Metadata.Ecma335.BlobEncoder" />
    <member name="M:System.Reflection.Metadata.Ecma335.BlobEncoder.#ctor(System.Reflection.Metadata.BlobBuilder)">
      <param name="builder" />
    </member>
    <member name="P:System.Reflection.Metadata.Ecma335.BlobEncoder.Builder" />
    <member name="M:System.Reflection.Metadata.Ecma335.BlobEncoder.CustomAttributeSignature(System.Action{System.Reflection.Metadata.Ecma335.FixedArgumentsEncoder},System.Action{System.Reflection.Metadata.Ecma335.CustomAttributeNamedArgumentsEncoder})">
      <param name="fixedArguments" />
      <param name="namedArguments" />
    </member>
    <member name="M:System.Reflection.Metadata.Ecma335.BlobEncoder.CustomAttributeSignature(System.Reflection.Metadata.Ecma335.FixedArgumentsEncoder@,System.Reflection.Metadata.Ecma335.CustomAttributeNamedArgumentsEncoder@)">
      <param name="fixedArguments" />
      <param name="namedArguments" />
    </member>
    <member name="M:System.Reflection.Metadata.Ecma335.BlobEncoder.FieldSignature" />
    <member name="M:System.Reflection.Metadata.Ecma335.BlobEncoder.LocalVariableSignature(System.Int32)">
      <param name="variableCount" />
    </member>
    <member name="M:System.Reflection.Metadata.Ecma335.BlobEncoder.MethodSignature(System.Reflection.Metadata.SignatureCallingConvention,System.Int32,System.Boolean)">
      <param name="convention" />
      <param name="genericParameterCount" />
      <param name="isInstanceMethod" />
    </member>
    <member name="M:System.Reflection.Metadata.Ecma335.BlobEncoder.MethodSpecificationSignature(System.Int32)">
      <param name="genericArgumentCount" />
    </member>
    <member name="M:System.Reflection.Metadata.Ecma335.BlobEncoder.PermissionSetArguments(System.Int32)">
      <param name="argumentCount" />
    </member>
    <member name="M:System.Reflection.Metadata.Ecma335.BlobEncoder.PermissionSetBlob(System.Int32)">
      <param name="attributeCount" />
    </member>
    <member name="M:System.Reflection.Metadata.Ecma335.BlobEncoder.PropertySignature(System.Boolean)">
      <param name="isInstanceProperty" />
    </member>
    <member name="M:System.Reflection.Metadata.Ecma335.BlobEncoder.TypeSpecificationSignature" />
    <member name="T:System.Reflection.Metadata.Ecma335.CodedIndex" />
    <member name="M:System.Reflection.Metadata.Ecma335.CodedIndex.CustomAttributeType(System.Reflection.Metadata.EntityHandle)">
      <param name="handle" />
    </member>
    <member name="M:System.Reflection.Metadata.Ecma335.CodedIndex.HasConstant(System.Reflection.Metadata.EntityHandle)">
      <param name="handle" />
    </member>
    <member name="M:System.Reflection.Metadata.Ecma335.CodedIndex.HasCustomAttribute(System.Reflection.Metadata.EntityHandle)">
      <param name="handle" />
    </member>
    <member name="M:System.Reflection.Metadata.Ecma335.CodedIndex.HasCustomDebugInformation(System.Reflection.Metadata.EntityHandle)">
      <param name="handle" />
    </member>
    <member name="M:System.Reflection.Metadata.Ecma335.CodedIndex.HasDeclSecurity(System.Reflection.Metadata.EntityHandle)">
      <param name="handle" />
    </member>
    <member name="M:System.Reflection.Metadata.Ecma335.CodedIndex.HasFieldMarshal(System.Reflection.Metadata.EntityHandle)">
      <param name="handle" />
    </member>
    <member name="M:System.Reflection.Metadata.Ecma335.CodedIndex.HasSemantics(System.Reflection.Metadata.EntityHandle)">
      <param name="handle" />
    </member>
    <member name="M:System.Reflection.Metadata.Ecma335.CodedIndex.Implementation(System.Reflection.Metadata.EntityHandle)">
      <param name="handle" />
    </member>
    <member name="M:System.Reflection.Metadata.Ecma335.CodedIndex.MemberForwarded(System.Reflection.Metadata.EntityHandle)">
      <param name="handle" />
    </member>
    <member name="M:System.Reflection.Metadata.Ecma335.CodedIndex.MemberRefParent(System.Reflection.Metadata.EntityHandle)">
      <param name="handle" />
    </member>
    <member name="M:System.Reflection.Metadata.Ecma335.CodedIndex.MethodDefOrRef(System.Reflection.Metadata.EntityHandle)">
      <param name="handle" />
    </member>
    <member name="M:System.Reflection.Metadata.Ecma335.CodedIndex.ResolutionScope(System.Reflection.Metadata.EntityHandle)">
      <param name="handle" />
    </member>
    <member name="M:System.Reflection.Metadata.Ecma335.CodedIndex.TypeDefOrRef(System.Reflection.Metadata.EntityHandle)">
      <param name="handle" />
    </member>
    <member name="M:System.Reflection.Metadata.Ecma335.CodedIndex.TypeDefOrRefOrSpec(System.Reflection.Metadata.EntityHandle)">
      <param name="handle" />
    </member>
    <member name="M:System.Reflection.Metadata.Ecma335.CodedIndex.TypeOrMethodDef(System.Reflection.Metadata.EntityHandle)">
      <param name="handle" />
    </member>
    <member name="T:System.Reflection.Metadata.Ecma335.ControlFlowBuilder" />
    <member name="M:System.Reflection.Metadata.Ecma335.ControlFlowBuilder.#ctor" />
    <member name="M:System.Reflection.Metadata.Ecma335.ControlFlowBuilder.AddCatchRegion(System.Reflection.Metadata.Ecma335.LabelHandle,System.Reflection.Metadata.Ecma335.LabelHandle,System.Reflection.Metadata.Ecma335.LabelHandle,System.Reflection.Metadata.Ecma335.LabelHandle,System.Reflection.Metadata.EntityHandle)">
      <param name="tryStart" />
      <param name="tryEnd" />
      <param name="handlerStart" />
      <param name="handlerEnd" />
      <param name="catchType" />
    </member>
    <member name="M:System.Reflection.Metadata.Ecma335.ControlFlowBuilder.AddFaultRegion(System.Reflection.Metadata.Ecma335.LabelHandle,System.Reflection.Metadata.Ecma335.LabelHandle,System.Reflection.Metadata.Ecma335.LabelHandle,System.Reflection.Metadata.Ecma335.LabelHandle)">
      <param name="tryStart" />
      <param name="tryEnd" />
      <param name="handlerStart" />
      <param name="handlerEnd" />
    </member>
    <member name="M:System.Reflection.Metadata.Ecma335.ControlFlowBuilder.AddFilterRegion(System.Reflection.Metadata.Ecma335.LabelHandle,System.Reflection.Metadata.Ecma335.LabelHandle,System.Reflection.Metadata.Ecma335.LabelHandle,System.Reflection.Metadata.Ecma335.LabelHandle,System.Reflection.Metadata.Ecma335.LabelHandle)">
      <param name="tryStart" />
      <param name="tryEnd" />
      <param name="handlerStart" />
      <param name="handlerEnd" />
      <param name="filterStart" />
    </member>
    <member name="M:System.Reflection.Metadata.Ecma335.ControlFlowBuilder.AddFinallyRegion(System.Reflection.Metadata.Ecma335.LabelHandle,System.Reflection.Metadata.Ecma335.LabelHandle,System.Reflection.Metadata.Ecma335.LabelHandle,System.Reflection.Metadata.Ecma335.LabelHandle)">
      <param name="tryStart" />
      <param name="tryEnd" />
      <param name="handlerStart" />
      <param name="handlerEnd" />
    </member>
    <member name="T:System.Reflection.Metadata.Ecma335.CustomAttributeArrayTypeEncoder" />
    <member name="M:System.Reflection.Metadata.Ecma335.CustomAttributeArrayTypeEncoder.#ctor(System.Reflection.Metadata.BlobBuilder)">
      <param name="builder" />
    </member>
    <member name="P:System.Reflection.Metadata.Ecma335.CustomAttributeArrayTypeEncoder.Builder" />
    <member name="M:System.Reflection.Metadata.Ecma335.CustomAttributeArrayTypeEncoder.ElementType" />
    <member name="M:System.Reflection.Metadata.Ecma335.CustomAttributeArrayTypeEncoder.ObjectArray" />
    <member name="T:System.Reflection.Metadata.Ecma335.CustomAttributeElementTypeEncoder" />
    <member name="M:System.Reflection.Metadata.Ecma335.CustomAttributeElementTypeEncoder.#ctor(System.Reflection.Metadata.BlobBuilder)">
      <param name="builder" />
    </member>
    <member name="M:System.Reflection.Metadata.Ecma335.CustomAttributeElementTypeEncoder.Boolean" />
    <member name="P:System.Reflection.Metadata.Ecma335.CustomAttributeElementTypeEncoder.Builder" />
    <member name="M:System.Reflection.Metadata.Ecma335.CustomAttributeElementTypeEncoder.Byte" />
    <member name="M:System.Reflection.Metadata.Ecma335.CustomAttributeElementTypeEncoder.Char" />
    <member name="M:System.Reflection.Metadata.Ecma335.CustomAttributeElementTypeEncoder.Double" />
    <member name="M:System.Reflection.Metadata.Ecma335.CustomAttributeElementTypeEncoder.Enum(System.String)">
      <param name="enumTypeName" />
    </member>
    <member name="M:System.Reflection.Metadata.Ecma335.CustomAttributeElementTypeEncoder.Int16" />
    <member name="M:System.Reflection.Metadata.Ecma335.CustomAttributeElementTypeEncoder.Int32" />
    <member name="M:System.Reflection.Metadata.Ecma335.CustomAttributeElementTypeEncoder.Int64" />
    <member name="M:System.Reflection.Metadata.Ecma335.CustomAttributeElementTypeEncoder.PrimitiveType(System.Reflection.Metadata.PrimitiveSerializationTypeCode)">
      <param name="type" />
    </member>
    <member name="M:System.Reflection.Metadata.Ecma335.CustomAttributeElementTypeEncoder.SByte" />
    <member name="M:System.Reflection.Metadata.Ecma335.CustomAttributeElementTypeEncoder.Single" />
    <member name="M:System.Reflection.Metadata.Ecma335.CustomAttributeElementTypeEncoder.String" />
    <member name="M:System.Reflection.Metadata.Ecma335.CustomAttributeElementTypeEncoder.SystemType" />
    <member name="M:System.Reflection.Metadata.Ecma335.CustomAttributeElementTypeEncoder.UInt16" />
    <member name="M:System.Reflection.Metadata.Ecma335.CustomAttributeElementTypeEncoder.UInt32" />
    <member name="M:System.Reflection.Metadata.Ecma335.CustomAttributeElementTypeEncoder.UInt64" />
    <member name="T:System.Reflection.Metadata.Ecma335.CustomAttributeNamedArgumentsEncoder" />
    <member name="M:System.Reflection.Metadata.Ecma335.CustomAttributeNamedArgumentsEncoder.#ctor(System.Reflection.Metadata.BlobBuilder)">
      <param name="builder" />
    </member>
    <member name="P:System.Reflection.Metadata.Ecma335.CustomAttributeNamedArgumentsEncoder.Builder" />
    <member name="M:System.Reflection.Metadata.Ecma335.CustomAttributeNamedArgumentsEncoder.Count(System.Int32)">
      <param name="count" />
    </member>
    <member name="T:System.Reflection.Metadata.Ecma335.CustomModifiersEncoder" />
    <member name="M:System.Reflection.Metadata.Ecma335.CustomModifiersEncoder.#ctor(System.Reflection.Metadata.BlobBuilder)">
      <param name="builder" />
    </member>
    <member name="M:System.Reflection.Metadata.Ecma335.CustomModifiersEncoder.AddModifier(System.Reflection.Metadata.EntityHandle,System.Boolean)">
      <param name="type" />
      <param name="isOptional" />
    </member>
    <member name="P:System.Reflection.Metadata.Ecma335.CustomModifiersEncoder.Builder" />
    <member name="T:System.Reflection.Metadata.Ecma335.EditAndContinueLogEntry" />
    <member name="M:System.Reflection.Metadata.Ecma335.EditAndContinueLogEntry.#ctor(System.Reflection.Metadata.EntityHandle,System.Reflection.Metadata.Ecma335.EditAndContinueOperation)">
      <param name="handle" />
      <param name="operation" />
    </member>
    <member name="M:System.Reflection.Metadata.Ecma335.EditAndContinueLogEntry.Equals(System.Object)">
      <param name="obj" />
    </member>
    <member name="M:System.Reflection.Metadata.Ecma335.EditAndContinueLogEntry.Equals(System.Reflection.Metadata.Ecma335.EditAndContinueLogEntry)">
      <param name="other" />
    </member>
    <member name="M:System.Reflection.Metadata.Ecma335.EditAndContinueLogEntry.GetHashCode" />
    <member name="P:System.Reflection.Metadata.Ecma335.EditAndContinueLogEntry.Handle" />
    <member name="P:System.Reflection.Metadata.Ecma335.EditAndContinueLogEntry.Operation" />
    <member name="T:System.Reflection.Metadata.Ecma335.EditAndContinueOperation" />
    <member name="F:System.Reflection.Metadata.Ecma335.EditAndContinueOperation.AddEvent" />
    <member name="F:System.Reflection.Metadata.Ecma335.EditAndContinueOperation.AddField" />
    <member name="F:System.Reflection.Metadata.Ecma335.EditAndContinueOperation.AddMethod" />
    <member name="F:System.Reflection.Metadata.Ecma335.EditAndContinueOperation.AddParameter" />
    <member name="F:System.Reflection.Metadata.Ecma335.EditAndContinueOperation.AddProperty" />
    <member name="F:System.Reflection.Metadata.Ecma335.EditAndContinueOperation.Default" />
    <member name="T:System.Reflection.Metadata.Ecma335.ExceptionRegionEncoder" />
    <member name="M:System.Reflection.Metadata.Ecma335.ExceptionRegionEncoder.Add(System.Reflection.Metadata.ExceptionRegionKind,System.Int32,System.Int32,System.Int32,System.Int32,System.Reflection.Metadata.EntityHandle,System.Int32)">
      <param name="kind" />
      <param name="tryOffset" />
      <param name="tryLength" />
      <param name="handlerOffset" />
      <param name="handlerLength" />
      <param name="catchType" />
      <param name="filterOffset" />
    </member>
    <member name="M:System.Reflection.Metadata.Ecma335.ExceptionRegionEncoder.AddCatch(System.Int32,System.Int32,System.Int32,System.Int32,System.Reflection.Metadata.EntityHandle)">
      <param name="tryOffset" />
      <param name="tryLength" />
      <param name="handlerOffset" />
      <param name="handlerLength" />
      <param name="catchType" />
    </member>
    <member name="M:System.Reflection.Metadata.Ecma335.ExceptionRegionEncoder.AddFault(System.Int32,System.Int32,System.Int32,System.Int32)">
      <param name="tryOffset" />
      <param name="tryLength" />
      <param name="handlerOffset" />
      <param name="handlerLength" />
    </member>
    <member name="M:System.Reflection.Metadata.Ecma335.ExceptionRegionEncoder.AddFilter(System.Int32,System.Int32,System.Int32,System.Int32,System.Int32)">
      <param name="tryOffset" />
      <param name="tryLength" />
      <param name="handlerOffset" />
      <param name="handlerLength" />
      <param name="filterOffset" />
    </member>
    <member name="M:System.Reflection.Metadata.Ecma335.ExceptionRegionEncoder.AddFinally(System.Int32,System.Int32,System.Int32,System.Int32)">
      <param name="tryOffset" />
      <param name="tryLength" />
      <param name="handlerOffset" />
      <param name="handlerLength" />
    </member>
    <member name="P:System.Reflection.Metadata.Ecma335.ExceptionRegionEncoder.Builder" />
    <member name="P:System.Reflection.Metadata.Ecma335.ExceptionRegionEncoder.HasSmallFormat" />
    <member name="M:System.Reflection.Metadata.Ecma335.ExceptionRegionEncoder.IsSmallExceptionRegion(System.Int32,System.Int32)">
      <param name="startOffset" />
      <param name="length" />
    </member>
    <member name="M:System.Reflection.Metadata.Ecma335.ExceptionRegionEncoder.IsSmallRegionCount(System.Int32)">
      <param name="exceptionRegionCount" />
    </member>
    <member name="T:System.Reflection.Metadata.Ecma335.ExportedTypeExtensions" />
    <member name="M:System.Reflection.Metadata.Ecma335.ExportedTypeExtensions.GetTypeDefinitionId(System.Reflection.Metadata.ExportedType)">
      <param name="exportedType" />
    </member>
    <member name="T:System.Reflection.Metadata.Ecma335.FixedArgumentsEncoder" />
    <member name="M:System.Reflection.Metadata.Ecma335.FixedArgumentsEncoder.#ctor(System.Reflection.Metadata.BlobBuilder)">
      <param name="builder" />
    </member>
    <member name="M:System.Reflection.Metadata.Ecma335.FixedArgumentsEncoder.AddArgument" />
    <member name="P:System.Reflection.Metadata.Ecma335.FixedArgumentsEncoder.Builder" />
    <member name="T:System.Reflection.Metadata.Ecma335.FunctionPointerAttributes" />
    <member name="F:System.Reflection.Metadata.Ecma335.FunctionPointerAttributes.HasExplicitThis" />
    <member name="F:System.Reflection.Metadata.Ecma335.FunctionPointerAttributes.HasThis" />
    <member name="F:System.Reflection.Metadata.Ecma335.FunctionPointerAttributes.None" />
    <member name="T:System.Reflection.Metadata.Ecma335.GenericTypeArgumentsEncoder" />
    <member name="M:System.Reflection.Metadata.Ecma335.GenericTypeArgumentsEncoder.#ctor(System.Reflection.Metadata.BlobBuilder)">
      <param name="builder" />
    </member>
    <member name="M:System.Reflection.Metadata.Ecma335.GenericTypeArgumentsEncoder.AddArgument" />
    <member name="P:System.Reflection.Metadata.Ecma335.GenericTypeArgumentsEncoder.Builder" />
    <member name="T:System.Reflection.Metadata.Ecma335.HeapIndex" />
    <member name="F:System.Reflection.Metadata.Ecma335.HeapIndex.Blob" />
    <member name="F:System.Reflection.Metadata.Ecma335.HeapIndex.Guid" />
    <member name="F:System.Reflection.Metadata.Ecma335.HeapIndex.String" />
    <member name="F:System.Reflection.Metadata.Ecma335.HeapIndex.UserString" />
    <member name="T:System.Reflection.Metadata.Ecma335.InstructionEncoder" />
    <member name="M:System.Reflection.Metadata.Ecma335.InstructionEncoder.#ctor(System.Reflection.Metadata.BlobBuilder,System.Reflection.Metadata.Ecma335.ControlFlowBuilder)">
      <param name="codeBuilder" />
      <param name="controlFlowBuilder" />
    </member>
    <member name="M:System.Reflection.Metadata.Ecma335.InstructionEncoder.Branch(System.Reflection.Metadata.ILOpCode,System.Reflection.Metadata.Ecma335.LabelHandle)">
      <param name="code" />
      <param name="label" />
    </member>
    <member name="M:System.Reflection.Metadata.Ecma335.InstructionEncoder.Call(System.Reflection.Metadata.EntityHandle)">
      <param name="methodHandle" />
    </member>
    <member name="M:System.Reflection.Metadata.Ecma335.InstructionEncoder.Call(System.Reflection.Metadata.MemberReferenceHandle)">
      <param name="methodHandle" />
    </member>
    <member name="M:System.Reflection.Metadata.Ecma335.InstructionEncoder.Call(System.Reflection.Metadata.MethodDefinitionHandle)">
      <param name="methodHandle" />
    </member>
    <member name="M:System.Reflection.Metadata.Ecma335.InstructionEncoder.Call(System.Reflection.Metadata.MethodSpecificationHandle)">
      <param name="methodHandle" />
    </member>
    <member name="M:System.Reflection.Metadata.Ecma335.InstructionEncoder.CallIndirect(System.Reflection.Metadata.StandaloneSignatureHandle)">
      <param name="signature" />
    </member>
    <member name="P:System.Reflection.Metadata.Ecma335.InstructionEncoder.CodeBuilder" />
    <member name="P:System.Reflection.Metadata.Ecma335.InstructionEncoder.ControlFlowBuilder" />
    <member name="M:System.Reflection.Metadata.Ecma335.InstructionEncoder.DefineLabel" />
    <member name="M:System.Reflection.Metadata.Ecma335.InstructionEncoder.LoadArgument(System.Int32)">
      <param name="argumentIndex" />
    </member>
    <member name="M:System.Reflection.Metadata.Ecma335.InstructionEncoder.LoadArgumentAddress(System.Int32)">
      <param name="argumentIndex" />
    </member>
    <member name="M:System.Reflection.Metadata.Ecma335.InstructionEncoder.LoadConstantI4(System.Int32)">
      <param name="value" />
    </member>
    <member name="M:System.Reflection.Metadata.Ecma335.InstructionEncoder.LoadConstantI8(System.Int64)">
      <param name="value" />
    </member>
    <member name="M:System.Reflection.Metadata.Ecma335.InstructionEncoder.LoadConstantR4(System.Single)">
      <param name="value" />
    </member>
    <member name="M:System.Reflection.Metadata.Ecma335.InstructionEncoder.LoadConstantR8(System.Double)">
      <param name="value" />
    </member>
    <member name="M:System.Reflection.Metadata.Ecma335.InstructionEncoder.LoadLocal(System.Int32)">
      <param name="slotIndex" />
    </member>
    <member name="M:System.Reflection.Metadata.Ecma335.InstructionEncoder.LoadLocalAddress(System.Int32)">
      <param name="slotIndex" />
    </member>
    <member name="M:System.Reflection.Metadata.Ecma335.InstructionEncoder.LoadString(System.Reflection.Metadata.UserStringHandle)">
      <param name="handle" />
    </member>
    <member name="M:System.Reflection.Metadata.Ecma335.InstructionEncoder.MarkLabel(System.Reflection.Metadata.Ecma335.LabelHandle)">
      <param name="label" />
    </member>
    <member name="P:System.Reflection.Metadata.Ecma335.InstructionEncoder.Offset" />
    <member name="M:System.Reflection.Metadata.Ecma335.InstructionEncoder.OpCode(System.Reflection.Metadata.ILOpCode)">
      <param name="code" />
    </member>
    <member name="M:System.Reflection.Metadata.Ecma335.InstructionEncoder.StoreArgument(System.Int32)">
      <param name="argumentIndex" />
    </member>
    <member name="M:System.Reflection.Metadata.Ecma335.InstructionEncoder.StoreLocal(System.Int32)">
      <param name="slotIndex" />
    </member>
    <member name="M:System.Reflection.Metadata.Ecma335.InstructionEncoder.Token(System.Int32)">
      <param name="token" />
    </member>
    <member name="M:System.Reflection.Metadata.Ecma335.InstructionEncoder.Token(System.Reflection.Metadata.EntityHandle)">
      <param name="handle" />
    </member>
    <member name="T:System.Reflection.Metadata.Ecma335.LabelHandle" />
    <member name="M:System.Reflection.Metadata.Ecma335.LabelHandle.Equals(System.Object)">
      <param name="obj" />
    </member>
    <member name="M:System.Reflection.Metadata.Ecma335.LabelHandle.Equals(System.Reflection.Metadata.Ecma335.LabelHandle)">
      <param name="other" />
    </member>
    <member name="M:System.Reflection.Metadata.Ecma335.LabelHandle.GetHashCode" />
    <member name="P:System.Reflection.Metadata.Ecma335.LabelHandle.Id" />
    <member name="P:System.Reflection.Metadata.Ecma335.LabelHandle.IsNil" />
    <member name="M:System.Reflection.Metadata.Ecma335.LabelHandle.op_Equality(System.Reflection.Metadata.Ecma335.LabelHandle,System.Reflection.Metadata.Ecma335.LabelHandle)">
      <param name="left" />
      <param name="right" />
    </member>
    <member name="M:System.Reflection.Metadata.Ecma335.LabelHandle.op_Inequality(System.Reflection.Metadata.Ecma335.LabelHandle,System.Reflection.Metadata.Ecma335.LabelHandle)">
      <param name="left" />
      <param name="right" />
    </member>
    <member name="T:System.Reflection.Metadata.Ecma335.LiteralEncoder" />
    <member name="M:System.Reflection.Metadata.Ecma335.LiteralEncoder.#ctor(System.Reflection.Metadata.BlobBuilder)">
      <param name="builder" />
    </member>
    <member name="P:System.Reflection.Metadata.Ecma335.LiteralEncoder.Builder" />
    <member name="M:System.Reflection.Metadata.Ecma335.LiteralEncoder.Scalar" />
    <member name="M:System.Reflection.Metadata.Ecma335.LiteralEncoder.TaggedScalar(System.Action{System.Reflection.Metadata.Ecma335.CustomAttributeElementTypeEncoder},System.Action{System.Reflection.Metadata.Ecma335.ScalarEncoder})">
      <param name="type" />
      <param name="scalar" />
    </member>
    <member name="M:System.Reflection.Metadata.Ecma335.LiteralEncoder.TaggedScalar(System.Reflection.Metadata.Ecma335.CustomAttributeElementTypeEncoder@,System.Reflection.Metadata.Ecma335.ScalarEncoder@)">
      <param name="type" />
      <param name="scalar" />
    </member>
    <member name="M:System.Reflection.Metadata.Ecma335.LiteralEncoder.TaggedVector(System.Action{System.Reflection.Metadata.Ecma335.CustomAttributeArrayTypeEncoder},System.Action{System.Reflection.Metadata.Ecma335.VectorEncoder})">
      <param name="arrayType" />
      <param name="vector" />
    </member>
    <member name="M:System.Reflection.Metadata.Ecma335.LiteralEncoder.TaggedVector(System.Reflection.Metadata.Ecma335.CustomAttributeArrayTypeEncoder@,System.Reflection.Metadata.Ecma335.VectorEncoder@)">
      <param name="arrayType" />
      <param name="vector" />
    </member>
    <member name="M:System.Reflection.Metadata.Ecma335.LiteralEncoder.Vector" />
    <member name="T:System.Reflection.Metadata.Ecma335.LiteralsEncoder" />
    <member name="M:System.Reflection.Metadata.Ecma335.LiteralsEncoder.#ctor(System.Reflection.Metadata.BlobBuilder)">
      <param name="builder" />
    </member>
    <member name="M:System.Reflection.Metadata.Ecma335.LiteralsEncoder.AddLiteral" />
    <member name="P:System.Reflection.Metadata.Ecma335.LiteralsEncoder.Builder" />
    <member name="T:System.Reflection.Metadata.Ecma335.LocalVariablesEncoder" />
    <member name="M:System.Reflection.Metadata.Ecma335.LocalVariablesEncoder.#ctor(System.Reflection.Metadata.BlobBuilder)">
      <param name="builder" />
    </member>
    <member name="M:System.Reflection.Metadata.Ecma335.LocalVariablesEncoder.AddVariable" />
    <member name="P:System.Reflection.Metadata.Ecma335.LocalVariablesEncoder.Builder" />
    <member name="T:System.Reflection.Metadata.Ecma335.LocalVariableTypeEncoder" />
    <member name="M:System.Reflection.Metadata.Ecma335.LocalVariableTypeEncoder.#ctor(System.Reflection.Metadata.BlobBuilder)">
      <param name="builder" />
    </member>
    <member name="P:System.Reflection.Metadata.Ecma335.LocalVariableTypeEncoder.Builder" />
    <member name="M:System.Reflection.Metadata.Ecma335.LocalVariableTypeEncoder.CustomModifiers" />
    <member name="M:System.Reflection.Metadata.Ecma335.LocalVariableTypeEncoder.Type(System.Boolean,System.Boolean)">
      <param name="isByRef" />
      <param name="isPinned" />
    </member>
    <member name="M:System.Reflection.Metadata.Ecma335.LocalVariableTypeEncoder.TypedReference" />
    <member name="T:System.Reflection.Metadata.Ecma335.MetadataAggregator" />
    <member name="M:System.Reflection.Metadata.Ecma335.MetadataAggregator.#ctor(System.Collections.Generic.IReadOnlyList{System.Int32},System.Collections.Generic.IReadOnlyList{System.Int32},System.Collections.Generic.IReadOnlyList{System.Reflection.Metadata.MetadataReader})">
      <param name="baseTableRowCounts" />
      <param name="baseHeapSizes" />
      <param name="deltaReaders" />
    </member>
    <member name="M:System.Reflection.Metadata.Ecma335.MetadataAggregator.#ctor(System.Reflection.Metadata.MetadataReader,System.Collections.Generic.IReadOnlyList{System.Reflection.Metadata.MetadataReader})">
      <param name="baseReader" />
      <param name="deltaReaders" />
    </member>
    <member name="M:System.Reflection.Metadata.Ecma335.MetadataAggregator.GetGenerationHandle(System.Reflection.Metadata.Handle,System.Int32@)">
      <param name="handle" />
      <param name="generation" />
    </member>
    <member name="T:System.Reflection.Metadata.Ecma335.MetadataBuilder" />
    <member name="M:System.Reflection.Metadata.Ecma335.MetadataBuilder.#ctor(System.Int32,System.Int32,System.Int32,System.Int32)">
      <param name="userStringHeapStartOffset" />
      <param name="stringHeapStartOffset" />
      <param name="blobHeapStartOffset" />
      <param name="guidHeapStartOffset" />
    </member>
    <member name="M:System.Reflection.Metadata.Ecma335.MetadataBuilder.AddAssembly(System.Reflection.Metadata.StringHandle,System.Version,System.Reflection.Metadata.StringHandle,System.Reflection.Metadata.BlobHandle,System.Reflection.AssemblyFlags,System.Reflection.AssemblyHashAlgorithm)">
      <param name="name" />
      <param name="version" />
      <param name="culture" />
      <param name="publicKey" />
      <param name="flags" />
      <param name="hashAlgorithm" />
    </member>
    <member name="M:System.Reflection.Metadata.Ecma335.MetadataBuilder.AddAssemblyFile(System.Reflection.Metadata.StringHandle,System.Reflection.Metadata.BlobHandle,System.Boolean)">
      <param name="name" />
      <param name="hashValue" />
      <param name="containsMetadata" />
    </member>
    <member name="M:System.Reflection.Metadata.Ecma335.MetadataBuilder.AddAssemblyReference(System.Reflection.Metadata.StringHandle,System.Version,System.Reflection.Metadata.StringHandle,System.Reflection.Metadata.BlobHandle,System.Reflection.AssemblyFlags,System.Reflection.Metadata.BlobHandle)">
      <param name="name" />
      <param name="version" />
      <param name="culture" />
      <param name="publicKeyOrToken" />
      <param name="flags" />
      <param name="hashValue" />
    </member>
    <member name="M:System.Reflection.Metadata.Ecma335.MetadataBuilder.AddConstant(System.Reflection.Metadata.EntityHandle,System.Object)">
      <param name="parent" />
      <param name="value" />
    </member>
    <member name="M:System.Reflection.Metadata.Ecma335.MetadataBuilder.AddCustomAttribute(System.Reflection.Metadata.EntityHandle,System.Reflection.Metadata.EntityHandle,System.Reflection.Metadata.BlobHandle)">
      <param name="parent" />
      <param name="constructor" />
      <param name="value" />
    </member>
    <member name="M:System.Reflection.Metadata.Ecma335.MetadataBuilder.AddCustomDebugInformation(System.Reflection.Metadata.EntityHandle,System.Reflection.Metadata.GuidHandle,System.Reflection.Metadata.BlobHandle)">
      <param name="parent" />
      <param name="kind" />
      <param name="value" />
    </member>
    <member name="M:System.Reflection.Metadata.Ecma335.MetadataBuilder.AddDeclarativeSecurityAttribute(System.Reflection.Metadata.EntityHandle,System.Reflection.DeclarativeSecurityAction,System.Reflection.Metadata.BlobHandle)">
      <param name="parent" />
      <param name="action" />
      <param name="permissionSet" />
    </member>
    <member name="M:System.Reflection.Metadata.Ecma335.MetadataBuilder.AddDocument(System.Reflection.Metadata.BlobHandle,System.Reflection.Metadata.GuidHandle,System.Reflection.Metadata.BlobHandle,System.Reflection.Metadata.GuidHandle)">
      <param name="name" />
      <param name="hashAlgorithm" />
      <param name="hash" />
      <param name="language" />
    </member>
    <member name="M:System.Reflection.Metadata.Ecma335.MetadataBuilder.AddEncLogEntry(System.Reflection.Metadata.EntityHandle,System.Reflection.Metadata.Ecma335.EditAndContinueOperation)">
      <param name="entity" />
      <param name="code" />
    </member>
    <member name="M:System.Reflection.Metadata.Ecma335.MetadataBuilder.AddEncMapEntry(System.Reflection.Metadata.EntityHandle)">
      <param name="entity" />
    </member>
    <member name="M:System.Reflection.Metadata.Ecma335.MetadataBuilder.AddEvent(System.Reflection.EventAttributes,System.Reflection.Metadata.StringHandle,System.Reflection.Metadata.EntityHandle)">
      <param name="attributes" />
      <param name="name" />
      <param name="type" />
    </member>
    <member name="M:System.Reflection.Metadata.Ecma335.MetadataBuilder.AddEventMap(System.Reflection.Metadata.TypeDefinitionHandle,System.Reflection.Metadata.EventDefinitionHandle)">
      <param name="declaringType" />
      <param name="eventList" />
    </member>
    <member name="M:System.Reflection.Metadata.Ecma335.MetadataBuilder.AddExportedType(System.Reflection.TypeAttributes,System.Reflection.Metadata.StringHandle,System.Reflection.Metadata.StringHandle,System.Reflection.Metadata.EntityHandle,System.Int32)">
      <param name="attributes" />
      <param name="namespace" />
      <param name="name" />
      <param name="implementation" />
      <param name="typeDefinitionId" />
    </member>
    <member name="M:System.Reflection.Metadata.Ecma335.MetadataBuilder.AddFieldDefinition(System.Reflection.FieldAttributes,System.Reflection.Metadata.StringHandle,System.Reflection.Metadata.BlobHandle)">
      <param name="attributes" />
      <param name="name" />
      <param name="signature" />
    </member>
    <member name="M:System.Reflection.Metadata.Ecma335.MetadataBuilder.AddFieldLayout(System.Reflection.Metadata.FieldDefinitionHandle,System.Int32)">
      <param name="field" />
      <param name="offset" />
    </member>
    <member name="M:System.Reflection.Metadata.Ecma335.MetadataBuilder.AddFieldRelativeVirtualAddress(System.Reflection.Metadata.FieldDefinitionHandle,System.Int32)">
      <param name="field" />
      <param name="offset" />
    </member>
    <member name="M:System.Reflection.Metadata.Ecma335.MetadataBuilder.AddGenericParameter(System.Reflection.Metadata.EntityHandle,System.Reflection.GenericParameterAttributes,System.Reflection.Metadata.StringHandle,System.Int32)">
      <param name="parent" />
      <param name="attributes" />
      <param name="name" />
      <param name="index" />
    </member>
    <member name="M:System.Reflection.Metadata.Ecma335.MetadataBuilder.AddGenericParameterConstraint(System.Reflection.Metadata.GenericParameterHandle,System.Reflection.Metadata.EntityHandle)">
      <param name="genericParameter" />
      <param name="constraint" />
    </member>
    <member name="M:System.Reflection.Metadata.Ecma335.MetadataBuilder.AddImportScope(System.Reflection.Metadata.ImportScopeHandle,System.Reflection.Metadata.BlobHandle)">
      <param name="parentScope" />
      <param name="imports" />
    </member>
    <member name="M:System.Reflection.Metadata.Ecma335.MetadataBuilder.AddInterfaceImplementation(System.Reflection.Metadata.TypeDefinitionHandle,System.Reflection.Metadata.EntityHandle)">
      <param name="type" />
      <param name="implementedInterface" />
    </member>
    <member name="M:System.Reflection.Metadata.Ecma335.MetadataBuilder.AddLocalConstant(System.Reflection.Metadata.StringHandle,System.Reflection.Metadata.BlobHandle)">
      <param name="name" />
      <param name="signature" />
    </member>
    <member name="M:System.Reflection.Metadata.Ecma335.MetadataBuilder.AddLocalScope(System.Reflection.Metadata.MethodDefinitionHandle,System.Reflection.Metadata.ImportScopeHandle,System.Reflection.Metadata.LocalVariableHandle,System.Reflection.Metadata.LocalConstantHandle,System.Int32,System.Int32)">
      <param name="method" />
      <param name="importScope" />
      <param name="variableList" />
      <param name="constantList" />
      <param name="startOffset" />
      <param name="length" />
    </member>
    <member name="M:System.Reflection.Metadata.Ecma335.MetadataBuilder.AddLocalVariable(System.Reflection.Metadata.LocalVariableAttributes,System.Int32,System.Reflection.Metadata.StringHandle)">
      <param name="attributes" />
      <param name="index" />
      <param name="name" />
    </member>
    <member name="M:System.Reflection.Metadata.Ecma335.MetadataBuilder.AddManifestResource(System.Reflection.ManifestResourceAttributes,System.Reflection.Metadata.StringHandle,System.Reflection.Metadata.EntityHandle,System.UInt32)">
      <param name="attributes" />
      <param name="name" />
      <param name="implementation" />
      <param name="offset" />
    </member>
    <member name="M:System.Reflection.Metadata.Ecma335.MetadataBuilder.AddMarshallingDescriptor(System.Reflection.Metadata.EntityHandle,System.Reflection.Metadata.BlobHandle)">
      <param name="parent" />
      <param name="descriptor" />
    </member>
    <member name="M:System.Reflection.Metadata.Ecma335.MetadataBuilder.AddMemberReference(System.Reflection.Metadata.EntityHandle,System.Reflection.Metadata.StringHandle,System.Reflection.Metadata.BlobHandle)">
      <param name="parent" />
      <param name="name" />
      <param name="signature" />
    </member>
    <member name="M:System.Reflection.Metadata.Ecma335.MetadataBuilder.AddMethodDebugInformation(System.Reflection.Metadata.DocumentHandle,System.Reflection.Metadata.BlobHandle)">
      <param name="document" />
      <param name="sequencePoints" />
    </member>
    <member name="M:System.Reflection.Metadata.Ecma335.MetadataBuilder.AddMethodDefinition(System.Reflection.MethodAttributes,System.Reflection.MethodImplAttributes,System.Reflection.Metadata.StringHandle,System.Reflection.Metadata.BlobHandle,System.Int32,System.Reflection.Metadata.ParameterHandle)">
      <param name="attributes" />
      <param name="implAttributes" />
      <param name="name" />
      <param name="signature" />
      <param name="bodyOffset" />
      <param name="parameterList" />
    </member>
    <member name="M:System.Reflection.Metadata.Ecma335.MetadataBuilder.AddMethodImplementation(System.Reflection.Metadata.TypeDefinitionHandle,System.Reflection.Metadata.EntityHandle,System.Reflection.Metadata.EntityHandle)">
      <param name="type" />
      <param name="methodBody" />
      <param name="methodDeclaration" />
    </member>
    <member name="M:System.Reflection.Metadata.Ecma335.MetadataBuilder.AddMethodImport(System.Reflection.Metadata.MethodDefinitionHandle,System.Reflection.MethodImportAttributes,System.Reflection.Metadata.StringHandle,System.Reflection.Metadata.ModuleReferenceHandle)">
      <param name="method" />
      <param name="attributes" />
      <param name="name" />
      <param name="module" />
    </member>
    <member name="M:System.Reflection.Metadata.Ecma335.MetadataBuilder.AddMethodSemantics(System.Reflection.Metadata.EntityHandle,System.Reflection.MethodSemanticsAttributes,System.Reflection.Metadata.MethodDefinitionHandle)">
      <param name="association" />
      <param name="semantics" />
      <param name="methodDefinition" />
    </member>
    <member name="M:System.Reflection.Metadata.Ecma335.MetadataBuilder.AddMethodSpecification(System.Reflection.Metadata.EntityHandle,System.Reflection.Metadata.BlobHandle)">
      <param name="method" />
      <param name="instantiation" />
    </member>
    <member name="M:System.Reflection.Metadata.Ecma335.MetadataBuilder.AddModule(System.Int32,System.Reflection.Metadata.StringHandle,System.Reflection.Metadata.GuidHandle,System.Reflection.Metadata.GuidHandle,System.Reflection.Metadata.GuidHandle)">
      <param name="generation" />
      <param name="moduleName" />
      <param name="mvid" />
      <param name="encId" />
      <param name="encBaseId" />
    </member>
    <member name="M:System.Reflection.Metadata.Ecma335.MetadataBuilder.AddModuleReference(System.Reflection.Metadata.StringHandle)">
      <param name="moduleName" />
    </member>
    <member name="M:System.Reflection.Metadata.Ecma335.MetadataBuilder.AddNestedType(System.Reflection.Metadata.TypeDefinitionHandle,System.Reflection.Metadata.TypeDefinitionHandle)">
      <param name="type" />
      <param name="enclosingType" />
    </member>
    <member name="M:System.Reflection.Metadata.Ecma335.MetadataBuilder.AddParameter(System.Reflection.ParameterAttributes,System.Reflection.Metadata.StringHandle,System.Int32)">
      <param name="attributes" />
      <param name="name" />
      <param name="sequenceNumber" />
    </member>
    <member name="M:System.Reflection.Metadata.Ecma335.MetadataBuilder.AddProperty(System.Reflection.PropertyAttributes,System.Reflection.Metadata.StringHandle,System.Reflection.Metadata.BlobHandle)">
      <param name="attributes" />
      <param name="name" />
      <param name="signature" />
    </member>
    <member name="M:System.Reflection.Metadata.Ecma335.MetadataBuilder.AddPropertyMap(System.Reflection.Metadata.TypeDefinitionHandle,System.Reflection.Metadata.PropertyDefinitionHandle)">
      <param name="declaringType" />
      <param name="propertyList" />
    </member>
    <member name="M:System.Reflection.Metadata.Ecma335.MetadataBuilder.AddStandaloneSignature(System.Reflection.Metadata.BlobHandle)">
      <param name="signature" />
    </member>
    <member name="M:System.Reflection.Metadata.Ecma335.MetadataBuilder.AddStateMachineMethod(System.Reflection.Metadata.MethodDefinitionHandle,System.Reflection.Metadata.MethodDefinitionHandle)">
      <param name="moveNextMethod" />
      <param name="kickoffMethod" />
    </member>
    <member name="M:System.Reflection.Metadata.Ecma335.MetadataBuilder.AddTypeDefinition(System.Reflection.TypeAttributes,System.Reflection.Metadata.StringHandle,System.Reflection.Metadata.StringHandle,System.Reflection.Metadata.EntityHandle,System.Reflection.Metadata.FieldDefinitionHandle,System.Reflection.Metadata.MethodDefinitionHandle)">
      <param name="attributes" />
      <param name="namespace" />
      <param name="name" />
      <param name="baseType" />
      <param name="fieldList" />
      <param name="methodList" />
    </member>
    <member name="M:System.Reflection.Metadata.Ecma335.MetadataBuilder.AddTypeLayout(System.Reflection.Metadata.TypeDefinitionHandle,System.UInt16,System.UInt32)">
      <param name="type" />
      <param name="packingSize" />
      <param name="size" />
    </member>
    <member name="M:System.Reflection.Metadata.Ecma335.MetadataBuilder.AddTypeReference(System.Reflection.Metadata.EntityHandle,System.Reflection.Metadata.StringHandle,System.Reflection.Metadata.StringHandle)">
      <param name="resolutionScope" />
      <param name="namespace" />
      <param name="name" />
    </member>
    <member name="M:System.Reflection.Metadata.Ecma335.MetadataBuilder.AddTypeSpecification(System.Reflection.Metadata.BlobHandle)">
      <param name="signature" />
    </member>
    <member name="M:System.Reflection.Metadata.Ecma335.MetadataBuilder.GetOrAddBlob(System.Byte[])">
      <param name="value" />
    </member>
    <member name="M:System.Reflection.Metadata.Ecma335.MetadataBuilder.GetOrAddBlob(System.Collections.Immutable.ImmutableArray{System.Byte})">
      <param name="value" />
    </member>
    <member name="M:System.Reflection.Metadata.Ecma335.MetadataBuilder.GetOrAddBlob(System.Reflection.Metadata.BlobBuilder)">
      <param name="value" />
    </member>
    <member name="M:System.Reflection.Metadata.Ecma335.MetadataBuilder.GetOrAddBlobUTF16(System.String)">
      <param name="value" />
    </member>
    <member name="M:System.Reflection.Metadata.Ecma335.MetadataBuilder.GetOrAddBlobUTF8(System.String,System.Boolean)">
      <param name="value" />
      <param name="allowUnpairedSurrogates" />
    </member>
    <member name="M:System.Reflection.Metadata.Ecma335.MetadataBuilder.GetOrAddConstantBlob(System.Object)">
      <param name="value" />
    </member>
    <member name="M:System.Reflection.Metadata.Ecma335.MetadataBuilder.GetOrAddDocumentName(System.String)">
      <param name="value" />
    </member>
    <member name="M:System.Reflection.Metadata.Ecma335.MetadataBuilder.GetOrAddGuid(System.Guid)">
      <param name="guid" />
    </member>
    <member name="M:System.Reflection.Metadata.Ecma335.MetadataBuilder.GetOrAddString(System.String)">
      <param name="value" />
    </member>
    <member name="M:System.Reflection.Metadata.Ecma335.MetadataBuilder.GetOrAddUserString(System.String)">
      <param name="value" />
    </member>
    <member name="M:System.Reflection.Metadata.Ecma335.MetadataBuilder.GetRowCount(System.Reflection.Metadata.Ecma335.TableIndex)">
      <param name="table" />
    </member>
    <member name="M:System.Reflection.Metadata.Ecma335.MetadataBuilder.GetRowCounts" />
    <member name="M:System.Reflection.Metadata.Ecma335.MetadataBuilder.ReserveGuid" />
    <member name="M:System.Reflection.Metadata.Ecma335.MetadataBuilder.ReserveUserString(System.Int32)">
      <param name="length" />
    </member>
    <member name="M:System.Reflection.Metadata.Ecma335.MetadataBuilder.SetCapacity(System.Reflection.Metadata.Ecma335.HeapIndex,System.Int32)">
      <param name="heap" />
      <param name="byteCount" />
    </member>
    <member name="M:System.Reflection.Metadata.Ecma335.MetadataBuilder.SetCapacity(System.Reflection.Metadata.Ecma335.TableIndex,System.Int32)">
      <param name="table" />
      <param name="rowCount" />
    </member>
    <member name="T:System.Reflection.Metadata.Ecma335.MetadataReaderExtensions" />
    <member name="M:System.Reflection.Metadata.Ecma335.MetadataReaderExtensions.GetEditAndContinueLogEntries(System.Reflection.Metadata.MetadataReader)">
      <param name="reader" />
    </member>
    <member name="M:System.Reflection.Metadata.Ecma335.MetadataReaderExtensions.GetEditAndContinueMapEntries(System.Reflection.Metadata.MetadataReader)">
      <param name="reader" />
    </member>
    <member name="M:System.Reflection.Metadata.Ecma335.MetadataReaderExtensions.GetHeapMetadataOffset(System.Reflection.Metadata.MetadataReader,System.Reflection.Metadata.Ecma335.HeapIndex)">
      <param name="reader" />
      <param name="heapIndex" />
    </member>
    <member name="M:System.Reflection.Metadata.Ecma335.MetadataReaderExtensions.GetHeapSize(System.Reflection.Metadata.MetadataReader,System.Reflection.Metadata.Ecma335.HeapIndex)">
      <param name="reader" />
      <param name="heapIndex" />
    </member>
    <member name="M:System.Reflection.Metadata.Ecma335.MetadataReaderExtensions.GetNextHandle(System.Reflection.Metadata.MetadataReader,System.Reflection.Metadata.BlobHandle)">
      <param name="reader" />
      <param name="handle" />
    </member>
    <member name="M:System.Reflection.Metadata.Ecma335.MetadataReaderExtensions.GetNextHandle(System.Reflection.Metadata.MetadataReader,System.Reflection.Metadata.StringHandle)">
      <param name="reader" />
      <param name="handle" />
    </member>
    <member name="M:System.Reflection.Metadata.Ecma335.MetadataReaderExtensions.GetNextHandle(System.Reflection.Metadata.MetadataReader,System.Reflection.Metadata.UserStringHandle)">
      <param name="reader" />
      <param name="handle" />
    </member>
    <member name="M:System.Reflection.Metadata.Ecma335.MetadataReaderExtensions.GetTableMetadataOffset(System.Reflection.Metadata.MetadataReader,System.Reflection.Metadata.Ecma335.TableIndex)">
      <param name="reader" />
      <param name="tableIndex" />
    </member>
    <member name="M:System.Reflection.Metadata.Ecma335.MetadataReaderExtensions.GetTableRowCount(System.Reflection.Metadata.MetadataReader,System.Reflection.Metadata.Ecma335.TableIndex)">
      <param name="reader" />
      <param name="tableIndex" />
    </member>
    <member name="M:System.Reflection.Metadata.Ecma335.MetadataReaderExtensions.GetTableRowSize(System.Reflection.Metadata.MetadataReader,System.Reflection.Metadata.Ecma335.TableIndex)">
      <param name="reader" />
      <param name="tableIndex" />
    </member>
    <member name="M:System.Reflection.Metadata.Ecma335.MetadataReaderExtensions.GetTypesWithEvents(System.Reflection.Metadata.MetadataReader)">
      <param name="reader" />
    </member>
    <member name="M:System.Reflection.Metadata.Ecma335.MetadataReaderExtensions.GetTypesWithProperties(System.Reflection.Metadata.MetadataReader)">
      <param name="reader" />
    </member>
    <member name="M:System.Reflection.Metadata.Ecma335.MetadataReaderExtensions.ResolveSignatureTypeKind(System.Reflection.Metadata.MetadataReader,System.Reflection.Metadata.EntityHandle,System.Byte)">
      <param name="reader" />
      <param name="typeHandle" />
      <param name="rawTypeKind" />
    </member>
    <member name="T:System.Reflection.Metadata.Ecma335.MetadataRootBuilder" />
    <member name="M:System.Reflection.Metadata.Ecma335.MetadataRootBuilder.#ctor(System.Reflection.Metadata.Ecma335.MetadataBuilder,System.String,System.Boolean)">
      <param name="tablesAndHeaps" />
      <param name="metadataVersion" />
      <param name="suppressValidation" />
    </member>
    <member name="P:System.Reflection.Metadata.Ecma335.MetadataRootBuilder.MetadataVersion" />
    <member name="M:System.Reflection.Metadata.Ecma335.MetadataRootBuilder.Serialize(System.Reflection.Metadata.BlobBuilder,System.Int32,System.Int32)">
      <param name="builder" />
      <param name="methodBodyStreamRva" />
      <param name="mappedFieldDataStreamRva" />
    </member>
    <member name="P:System.Reflection.Metadata.Ecma335.MetadataRootBuilder.Sizes" />
    <member name="P:System.Reflection.Metadata.Ecma335.MetadataRootBuilder.SuppressValidation" />
    <member name="T:System.Reflection.Metadata.Ecma335.MetadataSizes" />
    <member name="P:System.Reflection.Metadata.Ecma335.MetadataSizes.ExternalRowCounts" />
    <member name="M:System.Reflection.Metadata.Ecma335.MetadataSizes.GetAlignedHeapSize(System.Reflection.Metadata.Ecma335.HeapIndex)">
      <param name="index" />
    </member>
    <member name="P:System.Reflection.Metadata.Ecma335.MetadataSizes.HeapSizes" />
    <member name="P:System.Reflection.Metadata.Ecma335.MetadataSizes.RowCounts" />
    <member name="T:System.Reflection.Metadata.Ecma335.MetadataTokens" />
    <member name="M:System.Reflection.Metadata.Ecma335.MetadataTokens.AssemblyFileHandle(System.Int32)">
      <param name="rowNumber" />
    </member>
    <member name="M:System.Reflection.Metadata.Ecma335.MetadataTokens.AssemblyReferenceHandle(System.Int32)">
      <param name="rowNumber" />
    </member>
    <member name="M:System.Reflection.Metadata.Ecma335.MetadataTokens.BlobHandle(System.Int32)">
      <param name="offset" />
    </member>
    <member name="M:System.Reflection.Metadata.Ecma335.MetadataTokens.ConstantHandle(System.Int32)">
      <param name="rowNumber" />
    </member>
    <member name="M:System.Reflection.Metadata.Ecma335.MetadataTokens.CustomAttributeHandle(System.Int32)">
      <param name="rowNumber" />
    </member>
    <member name="M:System.Reflection.Metadata.Ecma335.MetadataTokens.CustomDebugInformationHandle(System.Int32)">
      <param name="rowNumber" />
    </member>
    <member name="M:System.Reflection.Metadata.Ecma335.MetadataTokens.DeclarativeSecurityAttributeHandle(System.Int32)">
      <param name="rowNumber" />
    </member>
    <member name="M:System.Reflection.Metadata.Ecma335.MetadataTokens.DocumentHandle(System.Int32)">
      <param name="rowNumber" />
    </member>
    <member name="M:System.Reflection.Metadata.Ecma335.MetadataTokens.DocumentNameBlobHandle(System.Int32)">
      <param name="offset" />
    </member>
    <member name="M:System.Reflection.Metadata.Ecma335.MetadataTokens.EntityHandle(System.Int32)">
      <param name="token" />
    </member>
    <member name="M:System.Reflection.Metadata.Ecma335.MetadataTokens.EntityHandle(System.Reflection.Metadata.Ecma335.TableIndex,System.Int32)">
      <param name="tableIndex" />
      <param name="rowNumber" />
    </member>
    <member name="M:System.Reflection.Metadata.Ecma335.MetadataTokens.EventDefinitionHandle(System.Int32)">
      <param name="rowNumber" />
    </member>
    <member name="M:System.Reflection.Metadata.Ecma335.MetadataTokens.ExportedTypeHandle(System.Int32)">
      <param name="rowNumber" />
    </member>
    <member name="M:System.Reflection.Metadata.Ecma335.MetadataTokens.FieldDefinitionHandle(System.Int32)">
      <param name="rowNumber" />
    </member>
    <member name="M:System.Reflection.Metadata.Ecma335.MetadataTokens.GenericParameterConstraintHandle(System.Int32)">
      <param name="rowNumber" />
    </member>
    <member name="M:System.Reflection.Metadata.Ecma335.MetadataTokens.GenericParameterHandle(System.Int32)">
      <param name="rowNumber" />
    </member>
    <member name="M:System.Reflection.Metadata.Ecma335.MetadataTokens.GetHeapOffset(System.Reflection.Metadata.BlobHandle)">
      <param name="handle" />
    </member>
    <member name="M:System.Reflection.Metadata.Ecma335.MetadataTokens.GetHeapOffset(System.Reflection.Metadata.GuidHandle)">
      <param name="handle" />
    </member>
    <member name="M:System.Reflection.Metadata.Ecma335.MetadataTokens.GetHeapOffset(System.Reflection.Metadata.Handle)">
      <param name="handle" />
    </member>
    <member name="M:System.Reflection.Metadata.Ecma335.MetadataTokens.GetHeapOffset(System.Reflection.Metadata.MetadataReader,System.Reflection.Metadata.Handle)">
      <param name="reader" />
      <param name="handle" />
    </member>
    <member name="M:System.Reflection.Metadata.Ecma335.MetadataTokens.GetHeapOffset(System.Reflection.Metadata.StringHandle)">
      <param name="handle" />
    </member>
    <member name="M:System.Reflection.Metadata.Ecma335.MetadataTokens.GetHeapOffset(System.Reflection.Metadata.UserStringHandle)">
      <param name="handle" />
    </member>
    <member name="M:System.Reflection.Metadata.Ecma335.MetadataTokens.GetRowNumber(System.Reflection.Metadata.EntityHandle)">
      <param name="handle" />
    </member>
    <member name="M:System.Reflection.Metadata.Ecma335.MetadataTokens.GetRowNumber(System.Reflection.Metadata.MetadataReader,System.Reflection.Metadata.EntityHandle)">
      <param name="reader" />
      <param name="handle" />
    </member>
    <member name="M:System.Reflection.Metadata.Ecma335.MetadataTokens.GetToken(System.Reflection.Metadata.EntityHandle)">
      <param name="handle" />
    </member>
    <member name="M:System.Reflection.Metadata.Ecma335.MetadataTokens.GetToken(System.Reflection.Metadata.Handle)">
      <param name="handle" />
    </member>
    <member name="M:System.Reflection.Metadata.Ecma335.MetadataTokens.GetToken(System.Reflection.Metadata.MetadataReader,System.Reflection.Metadata.EntityHandle)">
      <param name="reader" />
      <param name="handle" />
    </member>
    <member name="M:System.Reflection.Metadata.Ecma335.MetadataTokens.GetToken(System.Reflection.Metadata.MetadataReader,System.Reflection.Metadata.Handle)">
      <param name="reader" />
      <param name="handle" />
    </member>
    <member name="M:System.Reflection.Metadata.Ecma335.MetadataTokens.GuidHandle(System.Int32)">
      <param name="offset" />
    </member>
    <member name="M:System.Reflection.Metadata.Ecma335.MetadataTokens.Handle(System.Int32)">
      <param name="token" />
    </member>
    <member name="M:System.Reflection.Metadata.Ecma335.MetadataTokens.Handle(System.Reflection.Metadata.Ecma335.TableIndex,System.Int32)">
      <param name="tableIndex" />
      <param name="rowNumber" />
    </member>
    <member name="F:System.Reflection.Metadata.Ecma335.MetadataTokens.HeapCount" />
    <member name="M:System.Reflection.Metadata.Ecma335.MetadataTokens.ImportScopeHandle(System.Int32)">
      <param name="rowNumber" />
    </member>
    <member name="M:System.Reflection.Metadata.Ecma335.MetadataTokens.InterfaceImplementationHandle(System.Int32)">
      <param name="rowNumber" />
    </member>
    <member name="M:System.Reflection.Metadata.Ecma335.MetadataTokens.LocalConstantHandle(System.Int32)">
      <param name="rowNumber" />
    </member>
    <member name="M:System.Reflection.Metadata.Ecma335.MetadataTokens.LocalScopeHandle(System.Int32)">
      <param name="rowNumber" />
    </member>
    <member name="M:System.Reflection.Metadata.Ecma335.MetadataTokens.LocalVariableHandle(System.Int32)">
      <param name="rowNumber" />
    </member>
    <member name="M:System.Reflection.Metadata.Ecma335.MetadataTokens.ManifestResourceHandle(System.Int32)">
      <param name="rowNumber" />
    </member>
    <member name="M:System.Reflection.Metadata.Ecma335.MetadataTokens.MemberReferenceHandle(System.Int32)">
      <param name="rowNumber" />
    </member>
    <member name="M:System.Reflection.Metadata.Ecma335.MetadataTokens.MethodDebugInformationHandle(System.Int32)">
      <param name="rowNumber" />
    </member>
    <member name="M:System.Reflection.Metadata.Ecma335.MetadataTokens.MethodDefinitionHandle(System.Int32)">
      <param name="rowNumber" />
    </member>
    <member name="M:System.Reflection.Metadata.Ecma335.MetadataTokens.MethodImplementationHandle(System.Int32)">
      <param name="rowNumber" />
    </member>
    <member name="M:System.Reflection.Metadata.Ecma335.MetadataTokens.MethodSpecificationHandle(System.Int32)">
      <param name="rowNumber" />
    </member>
    <member name="M:System.Reflection.Metadata.Ecma335.MetadataTokens.ModuleReferenceHandle(System.Int32)">
      <param name="rowNumber" />
    </member>
    <member name="M:System.Reflection.Metadata.Ecma335.MetadataTokens.ParameterHandle(System.Int32)">
      <param name="rowNumber" />
    </member>
    <member name="M:System.Reflection.Metadata.Ecma335.MetadataTokens.PropertyDefinitionHandle(System.Int32)">
      <param name="rowNumber" />
    </member>
    <member name="M:System.Reflection.Metadata.Ecma335.MetadataTokens.StandaloneSignatureHandle(System.Int32)">
      <param name="rowNumber" />
    </member>
    <member name="M:System.Reflection.Metadata.Ecma335.MetadataTokens.StringHandle(System.Int32)">
      <param name="offset" />
    </member>
    <member name="F:System.Reflection.Metadata.Ecma335.MetadataTokens.TableCount" />
    <member name="M:System.Reflection.Metadata.Ecma335.MetadataTokens.TryGetHeapIndex(System.Reflection.Metadata.HandleKind,System.Reflection.Metadata.Ecma335.HeapIndex@)">
      <param name="type" />
      <param name="index" />
    </member>
    <member name="M:System.Reflection.Metadata.Ecma335.MetadataTokens.TryGetTableIndex(System.Reflection.Metadata.HandleKind,System.Reflection.Metadata.Ecma335.TableIndex@)">
      <param name="type" />
      <param name="index" />
    </member>
    <member name="M:System.Reflection.Metadata.Ecma335.MetadataTokens.TypeDefinitionHandle(System.Int32)">
      <param name="rowNumber" />
    </member>
    <member name="M:System.Reflection.Metadata.Ecma335.MetadataTokens.TypeReferenceHandle(System.Int32)">
      <param name="rowNumber" />
    </member>
    <member name="M:System.Reflection.Metadata.Ecma335.MetadataTokens.TypeSpecificationHandle(System.Int32)">
      <param name="rowNumber" />
    </member>
    <member name="M:System.Reflection.Metadata.Ecma335.MetadataTokens.UserStringHandle(System.Int32)">
      <param name="offset" />
    </member>
    <member name="T:System.Reflection.Metadata.Ecma335.MethodBodyAttributes">
      <summary>Defines method body attributes.</summary>
    </member>
    <member name="F:System.Reflection.Metadata.Ecma335.MethodBodyAttributes.InitLocals">
      <summary>Initializes any locals the method defines to zero and dynamically allocates local memory.</summary>
    </member>
    <member name="F:System.Reflection.Metadata.Ecma335.MethodBodyAttributes.None">
      <summary>Performs no local memory initialization.</summary>
    </member>
    <member name="T:System.Reflection.Metadata.Ecma335.MethodBodyStreamEncoder">
      <summary>Provides an encoder for a method body stream.</summary>
    </member>
    <member name="M:System.Reflection.Metadata.Ecma335.MethodBodyStreamEncoder.#ctor(System.Reflection.Metadata.BlobBuilder)">
      <param name="builder" />
    </member>
    <member name="M:System.Reflection.Metadata.Ecma335.MethodBodyStreamEncoder.AddMethodBody(System.Int32,System.Int32,System.Int32,System.Boolean,System.Reflection.Metadata.StandaloneSignatureHandle,System.Reflection.Metadata.Ecma335.MethodBodyAttributes)">
      <summary>Encodes a method body and adds it to the method body stream, using the provided code size, maximum stack size, number of exception regions, local variables' signature handle, method body attributes and allowing to indicate whether the exception regions should be encoded in small format or not.</summary>
      <param name="codeSize">The number of bytes to be reserved for instructions.</param>
      <param name="maxStack">The maximum stack size.</param>
      <param name="exceptionRegionCount">The number of exception regions.</param>
      <param name="hasSmallExceptionRegions">
        <see langword="true" /> if the exception regions should be encoded in small format; <see langword="false" /> otherwise.</param>
      <param name="localVariablesSignature">The local variables' signature handle.</param>
      <param name="attributes">The method body attributes.</param>
      <returns>The offset of the encoded body within the method body stream.</returns>
    </member>
    <member name="M:System.Reflection.Metadata.Ecma335.MethodBodyStreamEncoder.AddMethodBody(System.Int32,System.Int32,System.Int32,System.Boolean,System.Reflection.Metadata.StandaloneSignatureHandle,System.Reflection.Metadata.Ecma335.MethodBodyAttributes,System.Boolean)">
      <summary>Encodes a method body and adds it to the method body stream, using the provided code size, maximum stack size, number of exception regions, local variables' signature handle, method body attributes, allowing to indicate whether the exception regions should be encoded in small format or not, and allowing to indicate whether the method should allocate from the dynamic local memory pool or not.</summary>
      <param name="codeSize">The number of bytes to be reserved for instructions.</param>
      <param name="maxStack">The maximum stack size.</param>
      <param name="exceptionRegionCount">The number of exception regions.</param>
      <param name="hasSmallExceptionRegions">
        <see langword="true" /> if the exception regions should be encoded in small format; <see langword="false" /> otherwise.</param>
      <param name="localVariablesSignature">The local variables' signature handle.</param>
      <param name="attributes">The method body attributes.</param>
      <param name="hasDynamicStackAllocation">
        <see langword="true" /> if the method allocates from the dynamic local memory pool (the <see langword="localloc" /> instruction); <see langword="false" /> otherwise.</param>
      <returns>The offset of the encoded body within the method body stream.</returns>
    </member>
    <member name="M:System.Reflection.Metadata.Ecma335.MethodBodyStreamEncoder.AddMethodBody(System.Reflection.Metadata.Ecma335.InstructionEncoder,System.Int32,System.Reflection.Metadata.StandaloneSignatureHandle,System.Reflection.Metadata.Ecma335.MethodBodyAttributes)">
      <summary>Encodes a method body and adds it to the method body stream.</summary>
      <param name="instructionEncoder">The instruction encoder.</param>
      <param name="maxStack">The maximum stack size.</param>
      <param name="localVariablesSignature">The local variables' signature handle.</param>
      <param name="attributes">The method body attributes.</param>
      <returns>The offset of the encoded body within the method body stream.</returns>
    </member>
    <member name="M:System.Reflection.Metadata.Ecma335.MethodBodyStreamEncoder.AddMethodBody(System.Reflection.Metadata.Ecma335.InstructionEncoder,System.Int32,System.Reflection.Metadata.StandaloneSignatureHandle,System.Reflection.Metadata.Ecma335.MethodBodyAttributes,System.Boolean)">
      <summary>Encodes a method body and adds it to the method body stream, using the provided instruction encoder, maximum stack size, local variables' signature handle, method body attributes, and allowing to indicate whether the method should allocate from the dynamic local memory pool or not.</summary>
      <param name="instructionEncoder">The instruction encoder.</param>
      <param name="maxStack">The maximum stack size.</param>
      <param name="localVariablesSignature">The local variables' signature handle.</param>
      <param name="attributes">The method body attributes.</param>
      <param name="hasDynamicStackAllocation">
        <see langword="true" /> if the method allocates from the dynamic local memory pool (the IL contains the <see langword="localloc" /> instruction); <see langword="false" /> otherwise.</param>
      <returns>The offset of the encoded body within the method body stream.</returns>
    </member>
    <member name="P:System.Reflection.Metadata.Ecma335.MethodBodyStreamEncoder.Builder" />
    <member name="T:System.Reflection.Metadata.Ecma335.MethodBodyStreamEncoder.MethodBody">
      <summary>Describes a method body. This class is meant to used along with the <see cref="T:System.Reflection.Metadata.Ecma335.MethodBodyStreamEncoder" /> class.</summary>
    </member>
    <member name="P:System.Reflection.Metadata.Ecma335.MethodBodyStreamEncoder.MethodBody.ExceptionRegions">
      <summary>Gets an encoder object that can be used to encode exception regions to the method body.</summary>
      <returns>An exception region encoder instance.</returns>
    </member>
    <member name="P:System.Reflection.Metadata.Ecma335.MethodBodyStreamEncoder.MethodBody.Instructions">
      <summary>Gets a blob reserved for instructions.</summary>
      <returns>A blob reserved for instructions.</returns>
    </member>
    <member name="P:System.Reflection.Metadata.Ecma335.MethodBodyStreamEncoder.MethodBody.Offset">
      <summary>Gets the offset of the encoded method body in the method body stream.</summary>
      <returns>The offset of the encoded method body in the method body stream.</returns>
    </member>
    <member name="T:System.Reflection.Metadata.Ecma335.MethodSignatureEncoder">
      <summary>Provides an encoder for method signatures.</summary>
    </member>
    <member name="M:System.Reflection.Metadata.Ecma335.MethodSignatureEncoder.#ctor(System.Reflection.Metadata.BlobBuilder,System.Boolean)">
      <param name="builder" />
      <param name="hasVarArgs" />
    </member>
    <member name="P:System.Reflection.Metadata.Ecma335.MethodSignatureEncoder.Builder" />
    <member name="P:System.Reflection.Metadata.Ecma335.MethodSignatureEncoder.HasVarArgs" />
    <member name="M:System.Reflection.Metadata.Ecma335.MethodSignatureEncoder.Parameters(System.Int32,System.Action{System.Reflection.Metadata.Ecma335.ReturnTypeEncoder},System.Action{System.Reflection.Metadata.Ecma335.ParametersEncoder})">
      <summary>Encodes the provided return type and parameters.</summary>
      <param name="parameterCount">The number of parameters.</param>
      <param name="returnType">The method that is called first to encode the return type.</param>
      <param name="parameters">The method that is called second to encode the parameters.</param>
    </member>
    <member name="M:System.Reflection.Metadata.Ecma335.MethodSignatureEncoder.Parameters(System.Int32,System.Reflection.Metadata.Ecma335.ReturnTypeEncoder@,System.Reflection.Metadata.Ecma335.ParametersEncoder@)">
      <summary>Encodes the provided return type and parameters, which must be used in the order they appear in the parameter list.</summary>
      <param name="parameterCount">The number of parameters.</param>
      <param name="returnType">The method that is called first to encode the return types.</param>
      <param name="parameters">The method that is called second to encode the parameters.</param>
    </member>
    <member name="T:System.Reflection.Metadata.Ecma335.NamedArgumentsEncoder" />
    <member name="M:System.Reflection.Metadata.Ecma335.NamedArgumentsEncoder.#ctor(System.Reflection.Metadata.BlobBuilder)">
      <summary>Initializes a new instance of the <see cref="T:System.Reflection.Metadata.Ecma335.NamedArgumentsEncoder" /> structure.</summary>
      <param name="builder">A builder for encoding the named argument.</param>
    </member>
    <member name="M:System.Reflection.Metadata.Ecma335.NamedArgumentsEncoder.AddArgument(System.Boolean,System.Action{System.Reflection.Metadata.Ecma335.NamedArgumentTypeEncoder},System.Action{System.Reflection.Metadata.Ecma335.NameEncoder},System.Action{System.Reflection.Metadata.Ecma335.LiteralEncoder})">
      <summary>Encodes a named argument (a field or property).</summary>
      <param name="isField">
        <see langword="true" /> to encode a field, <see langword="false" /> to encode a property.</param>
      <param name="type">The method to call first to encode the type of the argument.</param>
      <param name="name">The method to call second to encode the name of the field or property.</param>
      <param name="literal">The method to call third to encode the literal value of the argument.</param>
    </member>
    <member name="M:System.Reflection.Metadata.Ecma335.NamedArgumentsEncoder.AddArgument(System.Boolean,System.Reflection.Metadata.Ecma335.NamedArgumentTypeEncoder@,System.Reflection.Metadata.Ecma335.NameEncoder@,System.Reflection.Metadata.Ecma335.LiteralEncoder@)">
      <summary>Encodes a named argument (a field or property) and returns three encoders that must be used in the order they appear in the parameter list.</summary>
      <param name="isField">
        <see langword="true" /> to encode a field, <see langword="false" /> to encode a property.</param>
      <param name="type">The method to call first to encode the type of the argument.</param>
      <param name="name">The method to call second to encode the name of the field or property.</param>
      <param name="literal">The method to call third to encode the literal value of the argument.</param>
    </member>
    <member name="P:System.Reflection.Metadata.Ecma335.NamedArgumentsEncoder.Builder" />
    <member name="T:System.Reflection.Metadata.Ecma335.NamedArgumentTypeEncoder" />
    <member name="M:System.Reflection.Metadata.Ecma335.NamedArgumentTypeEncoder.#ctor(System.Reflection.Metadata.BlobBuilder)">
      <param name="builder" />
    </member>
    <member name="P:System.Reflection.Metadata.Ecma335.NamedArgumentTypeEncoder.Builder" />
    <member name="M:System.Reflection.Metadata.Ecma335.NamedArgumentTypeEncoder.Object" />
    <member name="M:System.Reflection.Metadata.Ecma335.NamedArgumentTypeEncoder.ScalarType" />
    <member name="M:System.Reflection.Metadata.Ecma335.NamedArgumentTypeEncoder.SZArray" />
    <member name="T:System.Reflection.Metadata.Ecma335.NameEncoder" />
    <member name="M:System.Reflection.Metadata.Ecma335.NameEncoder.#ctor(System.Reflection.Metadata.BlobBuilder)">
      <param name="builder" />
    </member>
    <member name="P:System.Reflection.Metadata.Ecma335.NameEncoder.Builder" />
    <member name="M:System.Reflection.Metadata.Ecma335.NameEncoder.Name(System.String)">
      <param name="name" />
    </member>
    <member name="T:System.Reflection.Metadata.Ecma335.ParametersEncoder" />
    <member name="M:System.Reflection.Metadata.Ecma335.ParametersEncoder.#ctor(System.Reflection.Metadata.BlobBuilder,System.Boolean)">
      <param name="builder" />
      <param name="hasVarArgs" />
    </member>
    <member name="M:System.Reflection.Metadata.Ecma335.ParametersEncoder.AddParameter" />
    <member name="P:System.Reflection.Metadata.Ecma335.ParametersEncoder.Builder" />
    <member name="P:System.Reflection.Metadata.Ecma335.ParametersEncoder.HasVarArgs" />
    <member name="M:System.Reflection.Metadata.Ecma335.ParametersEncoder.StartVarArgs" />
    <member name="T:System.Reflection.Metadata.Ecma335.ParameterTypeEncoder" />
    <member name="M:System.Reflection.Metadata.Ecma335.ParameterTypeEncoder.#ctor(System.Reflection.Metadata.BlobBuilder)">
      <param name="builder" />
    </member>
    <member name="P:System.Reflection.Metadata.Ecma335.ParameterTypeEncoder.Builder" />
    <member name="M:System.Reflection.Metadata.Ecma335.ParameterTypeEncoder.CustomModifiers" />
    <member name="M:System.Reflection.Metadata.Ecma335.ParameterTypeEncoder.Type(System.Boolean)">
      <param name="isByRef" />
    </member>
    <member name="M:System.Reflection.Metadata.Ecma335.ParameterTypeEncoder.TypedReference" />
    <member name="T:System.Reflection.Metadata.Ecma335.PermissionSetEncoder" />
    <member name="M:System.Reflection.Metadata.Ecma335.PermissionSetEncoder.#ctor(System.Reflection.Metadata.BlobBuilder)">
      <param name="builder" />
    </member>
    <member name="M:System.Reflection.Metadata.Ecma335.PermissionSetEncoder.AddPermission(System.String,System.Collections.Immutable.ImmutableArray{System.Byte})">
      <param name="typeName" />
      <param name="encodedArguments" />
    </member>
    <member name="M:System.Reflection.Metadata.Ecma335.PermissionSetEncoder.AddPermission(System.String,System.Reflection.Metadata.BlobBuilder)">
      <param name="typeName" />
      <param name="encodedArguments" />
    </member>
    <member name="P:System.Reflection.Metadata.Ecma335.PermissionSetEncoder.Builder" />
    <member name="T:System.Reflection.Metadata.Ecma335.PortablePdbBuilder">
      <summary>Represents the builder of a Portable PDB image.</summary>
    </member>
    <member name="M:System.Reflection.Metadata.Ecma335.PortablePdbBuilder.#ctor(System.Reflection.Metadata.Ecma335.MetadataBuilder,System.Collections.Immutable.ImmutableArray{System.Int32},System.Reflection.Metadata.MethodDefinitionHandle,System.Func{System.Collections.Generic.IEnumerable{System.Reflection.Metadata.Blob},System.Reflection.Metadata.BlobContentId})">
      <summary>Creates a builder of a Portable PDB image.</summary>
      <param name="tablesAndHeaps">A builder populated with debug metadata entities stored in tables and values stored in heaps. The entities and values are enumerated when serializing the Portable PDB image.</param>
      <param name="typeSystemRowCounts">The row counts of all tables that the associated type system metadata contain. Each slot in the array corresponds to a table (<see cref="T:System.Reflection.Metadata.Ecma335.TableIndex" />). The length of the array must be equal <see cref="F:System.Reflection.Metadata.Ecma335.MetadataTokens.TableCount" />.</param>
      <param name="entryPoint">An entry point method definition handle.</param>
      <param name="idProvider">A function that calculates the ID of content represented as a sequence of blobs. If not specified, a default function that ignores the content and returns a content ID based on the current time is used (<see cref="M:System.Reflection.Metadata.BlobContentId.GetTimeBasedProvider" />). You must specify a deterministic function to produce a deterministic Portable PDB image.</param>
    </member>
    <member name="P:System.Reflection.Metadata.Ecma335.PortablePdbBuilder.FormatVersion" />
    <member name="P:System.Reflection.Metadata.Ecma335.PortablePdbBuilder.IdProvider" />
    <member name="P:System.Reflection.Metadata.Ecma335.PortablePdbBuilder.MetadataVersion" />
    <member name="M:System.Reflection.Metadata.Ecma335.PortablePdbBuilder.Serialize(System.Reflection.Metadata.BlobBuilder)">
      <summary>Serializes portable PDB content into the given <see cref="T:System.Reflection.Metadata.BlobBuilder" />.</summary>
      <param name="builder">The builder to write to.</param>
      <returns>The ID of the serialized content.</returns>
    </member>
    <member name="T:System.Reflection.Metadata.Ecma335.ReturnTypeEncoder" />
    <member name="M:System.Reflection.Metadata.Ecma335.ReturnTypeEncoder.#ctor(System.Reflection.Metadata.BlobBuilder)">
      <param name="builder" />
    </member>
    <member name="P:System.Reflection.Metadata.Ecma335.ReturnTypeEncoder.Builder" />
    <member name="M:System.Reflection.Metadata.Ecma335.ReturnTypeEncoder.CustomModifiers" />
    <member name="M:System.Reflection.Metadata.Ecma335.ReturnTypeEncoder.Type(System.Boolean)">
      <param name="isByRef" />
    </member>
    <member name="M:System.Reflection.Metadata.Ecma335.ReturnTypeEncoder.TypedReference" />
    <member name="M:System.Reflection.Metadata.Ecma335.ReturnTypeEncoder.Void" />
    <member name="T:System.Reflection.Metadata.Ecma335.ScalarEncoder" />
    <member name="M:System.Reflection.Metadata.Ecma335.ScalarEncoder.#ctor(System.Reflection.Metadata.BlobBuilder)">
      <param name="builder" />
    </member>
    <member name="P:System.Reflection.Metadata.Ecma335.ScalarEncoder.Builder" />
    <member name="M:System.Reflection.Metadata.Ecma335.ScalarEncoder.Constant(System.Object)">
      <summary>Encodes a constant literal.</summary>
      <param name="value">A constant of type <see cref="T:System.Boolean" />, <see cref="T:System.Byte" />, <see cref="T:System.SByte" />, <see cref="T:System.Int16" />, <see cref="T:System.UInt16" />, <see cref="T:System.Int32" />, <see cref="T:System.UInt32" />, <see cref="T:System.Int64" />, <see cref="T:System.UInt64" />, <see cref="T:System.Single" />, <see cref="T:System.Double" />, <see cref="T:System.Char" /> (encoded as a two-byte Unicode character), <see cref="T:System.String" /> (encoded as SerString), or <see cref="T:System.Enum" /> (encoded as the underlying integer value).</param>
    </member>
    <member name="M:System.Reflection.Metadata.Ecma335.ScalarEncoder.NullArray">
      <summary>Encodes a <see langword="null" /> literal of type <see cref="T:System.Array" />.</summary>
    </member>
    <member name="M:System.Reflection.Metadata.Ecma335.ScalarEncoder.SystemType(System.String)">
      <summary>Encodes a literal of type <see cref="T:System.Type" /> (which can possibly be <see langword="null" />).</summary>
      <param name="serializedTypeName">The name of the type, or <see langword="null" />.</param>
    </member>
    <member name="T:System.Reflection.Metadata.Ecma335.SignatureDecoder`2">
      <summary>Decodes signature blobs.</summary>
      <typeparam name="TType" />
      <typeparam name="TGenericContext" />
    </member>
    <member name="M:System.Reflection.Metadata.Ecma335.SignatureDecoder`2.#ctor(System.Reflection.Metadata.ISignatureTypeProvider{`0,`1},System.Reflection.Metadata.MetadataReader,`1)">
      <summary>Creates a new <see cref="T:System.Reflection.Metadata.Ecma335.SignatureDecoder`2" />.</summary>
      <param name="provider">The provider used to obtain type symbols as the signature is decoded.</param>
      <param name="metadataReader">The metadata reader from which the signature was obtained. It may be <see langword="null" /> if the given provider allows it.</param>
      <param name="genericContext">Additional context needed to resolve generic parameters.</param>
    </member>
    <member name="M:System.Reflection.Metadata.Ecma335.SignatureDecoder`2.DecodeFieldSignature(System.Reflection.Metadata.BlobReader@)">
      <summary>Decodes a field signature blob and advances the reader past the signature.</summary>
      <param name="blobReader">The blob reader positioned at a field signature.</param>
      <returns>The decoded field type.</returns>
    </member>
    <member name="M:System.Reflection.Metadata.Ecma335.SignatureDecoder`2.DecodeLocalSignature(System.Reflection.Metadata.BlobReader@)">
      <summary>Decodes a local variable signature blob and advances the reader past the signature.</summary>
      <param name="blobReader">The blob reader positioned at a local variable signature.</param>
      <returns>The local variable types.</returns>
    </member>
    <member name="M:System.Reflection.Metadata.Ecma335.SignatureDecoder`2.DecodeMethodSignature(System.Reflection.Metadata.BlobReader@)">
      <summary>Decodes a method (definition, reference, or standalone) or a property signature blob.</summary>
      <param name="blobReader">A blob reader positioned at a method signature.</param>
      <returns>The decoded method signature.</returns>
    </member>
    <member name="M:System.Reflection.Metadata.Ecma335.SignatureDecoder`2.DecodeMethodSpecificationSignature(System.Reflection.Metadata.BlobReader@)">
      <summary>Decodes a method specification signature blob and advances the reader past the signature.</summary>
      <param name="blobReader">A blob reader positioned at a valid method specification signature.</param>
      <returns>The types used to instantiate a generic method via the method specification.</returns>
    </member>
    <member name="M:System.Reflection.Metadata.Ecma335.SignatureDecoder`2.DecodeType(System.Reflection.Metadata.BlobReader@,System.Boolean)">
      <summary>Decodes a type embedded in a signature and advances the reader past the type.</summary>
      <param name="blobReader">The blob reader positioned at the leading <see cref="T:System.Reflection.Metadata.SignatureTypeCode" />.</param>
      <param name="allowTypeSpecifications">
        <see langword="true" /> to allow a <see cref="T:System.Reflection.Metadata.TypeSpecificationHandle" /> to follow a (CLASS | VALUETYPE) in the signature; <see langword="false" /> otherwise.</param>
      <returns>The decoded type.</returns>
    </member>
    <member name="T:System.Reflection.Metadata.Ecma335.SignatureTypeEncoder" />
    <member name="M:System.Reflection.Metadata.Ecma335.SignatureTypeEncoder.#ctor(System.Reflection.Metadata.BlobBuilder)">
      <param name="builder" />
    </member>
    <member name="M:System.Reflection.Metadata.Ecma335.SignatureTypeEncoder.Array(System.Action{System.Reflection.Metadata.Ecma335.SignatureTypeEncoder},System.Action{System.Reflection.Metadata.Ecma335.ArrayShapeEncoder})">
      <summary>Encodes an array type.</summary>
      <param name="elementType">Called first, to encode the type of the element.</param>
      <param name="arrayShape">Called second, to encode the shape of the array.</param>
    </member>
    <member name="M:System.Reflection.Metadata.Ecma335.SignatureTypeEncoder.Array(System.Reflection.Metadata.Ecma335.SignatureTypeEncoder@,System.Reflection.Metadata.Ecma335.ArrayShapeEncoder@)">
      <summary>Encodes an array type. Returns a pair of encoders that must be used in the order they appear in the parameter list.</summary>
      <param name="elementType">Use first, to encode the type of the element.</param>
      <param name="arrayShape">Use second, to encode the shape of the array.</param>
    </member>
    <member name="M:System.Reflection.Metadata.Ecma335.SignatureTypeEncoder.Boolean" />
    <member name="P:System.Reflection.Metadata.Ecma335.SignatureTypeEncoder.Builder" />
    <member name="M:System.Reflection.Metadata.Ecma335.SignatureTypeEncoder.Byte" />
    <member name="M:System.Reflection.Metadata.Ecma335.SignatureTypeEncoder.Char" />
    <member name="M:System.Reflection.Metadata.Ecma335.SignatureTypeEncoder.CustomModifiers">
      <summary>Starts a signature of a type with custom modifiers.</summary>
    </member>
    <member name="M:System.Reflection.Metadata.Ecma335.SignatureTypeEncoder.Double" />
    <member name="M:System.Reflection.Metadata.Ecma335.SignatureTypeEncoder.FunctionPointer(System.Reflection.Metadata.SignatureCallingConvention,System.Reflection.Metadata.Ecma335.FunctionPointerAttributes,System.Int32)">
      <summary>Starts a function pointer signature.</summary>
      <param name="convention">Calling convention.</param>
      <param name="attributes">Function pointer attributes.</param>
      <param name="genericParameterCount">Generic parameter count.</param>
    </member>
    <member name="M:System.Reflection.Metadata.Ecma335.SignatureTypeEncoder.GenericInstantiation(System.Reflection.Metadata.EntityHandle,System.Int32,System.Boolean)">
      <summary>Starts a generic instantiation signature.</summary>
      <param name="genericType">
        <see cref="T:System.Reflection.Metadata.TypeDefinitionHandle" /> or <see cref="T:System.Reflection.Metadata.TypeReferenceHandle" />.</param>
      <param name="genericArgumentCount">Generic argument count.</param>
      <param name="isValueType">
        <see langword="true" /> to mark the type as value type, <see langword="false" /> to mark it as a reference type in the signature.</param>
    </member>
    <member name="M:System.Reflection.Metadata.Ecma335.SignatureTypeEncoder.GenericMethodTypeParameter(System.Int32)">
      <summary>Encodes a reference to type parameter of a containing generic method.</summary>
      <param name="parameterIndex">Parameter index.</param>
    </member>
    <member name="M:System.Reflection.Metadata.Ecma335.SignatureTypeEncoder.GenericTypeParameter(System.Int32)">
      <summary>Encodes a reference to type parameter of a containing generic type.</summary>
      <param name="parameterIndex">Parameter index.</param>
    </member>
    <member name="M:System.Reflection.Metadata.Ecma335.SignatureTypeEncoder.Int16" />
    <member name="M:System.Reflection.Metadata.Ecma335.SignatureTypeEncoder.Int32" />
    <member name="M:System.Reflection.Metadata.Ecma335.SignatureTypeEncoder.Int64" />
    <member name="M:System.Reflection.Metadata.Ecma335.SignatureTypeEncoder.IntPtr" />
    <member name="M:System.Reflection.Metadata.Ecma335.SignatureTypeEncoder.Object" />
    <member name="M:System.Reflection.Metadata.Ecma335.SignatureTypeEncoder.Pointer">
      <summary>Starts pointer signature.</summary>
    </member>
    <member name="M:System.Reflection.Metadata.Ecma335.SignatureTypeEncoder.PrimitiveType(System.Reflection.Metadata.PrimitiveTypeCode)">
      <summary>Writes primitive type code.</summary>
      <param name="type">Any primitive type code except for <see cref="F:System.Reflection.Metadata.PrimitiveTypeCode.TypedReference" /> and <see cref="F:System.Reflection.Metadata.PrimitiveTypeCode.Void" />.</param>
    </member>
    <member name="M:System.Reflection.Metadata.Ecma335.SignatureTypeEncoder.SByte" />
    <member name="M:System.Reflection.Metadata.Ecma335.SignatureTypeEncoder.Single" />
    <member name="M:System.Reflection.Metadata.Ecma335.SignatureTypeEncoder.String" />
    <member name="M:System.Reflection.Metadata.Ecma335.SignatureTypeEncoder.SZArray">
      <summary>Starts SZ array (vector) signature.</summary>
    </member>
    <member name="M:System.Reflection.Metadata.Ecma335.SignatureTypeEncoder.Type(System.Reflection.Metadata.EntityHandle,System.Boolean)">
      <summary>Encodes a reference to a type.</summary>
      <param name="type">
        <see cref="T:System.Reflection.Metadata.TypeDefinitionHandle" /> or <see cref="T:System.Reflection.Metadata.TypeReferenceHandle" />.</param>
      <param name="isValueType">
        <see langword="true" /> to mark the type as value type, <see langword="false" /> to mark it as a reference type in the signature.</param>
    </member>
    <member name="M:System.Reflection.Metadata.Ecma335.SignatureTypeEncoder.UInt16" />
    <member name="M:System.Reflection.Metadata.Ecma335.SignatureTypeEncoder.UInt32" />
    <member name="M:System.Reflection.Metadata.Ecma335.SignatureTypeEncoder.UInt64" />
    <member name="M:System.Reflection.Metadata.Ecma335.SignatureTypeEncoder.UIntPtr" />
    <member name="M:System.Reflection.Metadata.Ecma335.SignatureTypeEncoder.VoidPointer">
      <summary>Encodes a void pointer (void*).</summary>
    </member>
    <member name="T:System.Reflection.Metadata.Ecma335.TableIndex" />
    <member name="F:System.Reflection.Metadata.Ecma335.TableIndex.Assembly" />
    <member name="F:System.Reflection.Metadata.Ecma335.TableIndex.AssemblyOS" />
    <member name="F:System.Reflection.Metadata.Ecma335.TableIndex.AssemblyProcessor" />
    <member name="F:System.Reflection.Metadata.Ecma335.TableIndex.AssemblyRef" />
    <member name="F:System.Reflection.Metadata.Ecma335.TableIndex.AssemblyRefOS" />
    <member name="F:System.Reflection.Metadata.Ecma335.TableIndex.AssemblyRefProcessor" />
    <member name="F:System.Reflection.Metadata.Ecma335.TableIndex.ClassLayout" />
    <member name="F:System.Reflection.Metadata.Ecma335.TableIndex.Constant" />
    <member name="F:System.Reflection.Metadata.Ecma335.TableIndex.CustomAttribute" />
    <member name="F:System.Reflection.Metadata.Ecma335.TableIndex.CustomDebugInformation" />
    <member name="F:System.Reflection.Metadata.Ecma335.TableIndex.DeclSecurity" />
    <member name="F:System.Reflection.Metadata.Ecma335.TableIndex.Document" />
    <member name="F:System.Reflection.Metadata.Ecma335.TableIndex.EncLog" />
    <member name="F:System.Reflection.Metadata.Ecma335.TableIndex.EncMap" />
    <member name="F:System.Reflection.Metadata.Ecma335.TableIndex.Event" />
    <member name="F:System.Reflection.Metadata.Ecma335.TableIndex.EventMap" />
    <member name="F:System.Reflection.Metadata.Ecma335.TableIndex.EventPtr" />
    <member name="F:System.Reflection.Metadata.Ecma335.TableIndex.ExportedType" />
    <member name="F:System.Reflection.Metadata.Ecma335.TableIndex.Field" />
    <member name="F:System.Reflection.Metadata.Ecma335.TableIndex.FieldLayout" />
    <member name="F:System.Reflection.Metadata.Ecma335.TableIndex.FieldMarshal" />
    <member name="F:System.Reflection.Metadata.Ecma335.TableIndex.FieldPtr" />
    <member name="F:System.Reflection.Metadata.Ecma335.TableIndex.FieldRva" />
    <member name="F:System.Reflection.Metadata.Ecma335.TableIndex.File" />
    <member name="F:System.Reflection.Metadata.Ecma335.TableIndex.GenericParam" />
    <member name="F:System.Reflection.Metadata.Ecma335.TableIndex.GenericParamConstraint" />
    <member name="F:System.Reflection.Metadata.Ecma335.TableIndex.ImplMap" />
    <member name="F:System.Reflection.Metadata.Ecma335.TableIndex.ImportScope" />
    <member name="F:System.Reflection.Metadata.Ecma335.TableIndex.InterfaceImpl" />
    <member name="F:System.Reflection.Metadata.Ecma335.TableIndex.LocalConstant" />
    <member name="F:System.Reflection.Metadata.Ecma335.TableIndex.LocalScope" />
    <member name="F:System.Reflection.Metadata.Ecma335.TableIndex.LocalVariable" />
    <member name="F:System.Reflection.Metadata.Ecma335.TableIndex.ManifestResource" />
    <member name="F:System.Reflection.Metadata.Ecma335.TableIndex.MemberRef" />
    <member name="F:System.Reflection.Metadata.Ecma335.TableIndex.MethodDebugInformation" />
    <member name="F:System.Reflection.Metadata.Ecma335.TableIndex.MethodDef" />
    <member name="F:System.Reflection.Metadata.Ecma335.TableIndex.MethodImpl" />
    <member name="F:System.Reflection.Metadata.Ecma335.TableIndex.MethodPtr" />
    <member name="F:System.Reflection.Metadata.Ecma335.TableIndex.MethodSemantics" />
    <member name="F:System.Reflection.Metadata.Ecma335.TableIndex.MethodSpec" />
    <member name="F:System.Reflection.Metadata.Ecma335.TableIndex.Module" />
    <member name="F:System.Reflection.Metadata.Ecma335.TableIndex.ModuleRef" />
    <member name="F:System.Reflection.Metadata.Ecma335.TableIndex.NestedClass" />
    <member name="F:System.Reflection.Metadata.Ecma335.TableIndex.Param" />
    <member name="F:System.Reflection.Metadata.Ecma335.TableIndex.ParamPtr" />
    <member name="F:System.Reflection.Metadata.Ecma335.TableIndex.Property" />
    <member name="F:System.Reflection.Metadata.Ecma335.TableIndex.PropertyMap" />
    <member name="F:System.Reflection.Metadata.Ecma335.TableIndex.PropertyPtr" />
    <member name="F:System.Reflection.Metadata.Ecma335.TableIndex.StandAloneSig" />
    <member name="F:System.Reflection.Metadata.Ecma335.TableIndex.StateMachineMethod" />
    <member name="F:System.Reflection.Metadata.Ecma335.TableIndex.TypeDef" />
    <member name="F:System.Reflection.Metadata.Ecma335.TableIndex.TypeRef" />
    <member name="F:System.Reflection.Metadata.Ecma335.TableIndex.TypeSpec" />
    <member name="T:System.Reflection.Metadata.Ecma335.VectorEncoder" />
    <member name="M:System.Reflection.Metadata.Ecma335.VectorEncoder.#ctor(System.Reflection.Metadata.BlobBuilder)">
      <param name="builder" />
    </member>
    <member name="P:System.Reflection.Metadata.Ecma335.VectorEncoder.Builder" />
    <member name="M:System.Reflection.Metadata.Ecma335.VectorEncoder.Count(System.Int32)">
      <param name="count" />
    </member>
    <member name="T:System.Reflection.Metadata.EntityHandle">
      <summary>Represents a metadata entity (such as a type reference, type definition, type specification, method definition, or custom attribute).</summary>
    </member>
    <member name="F:System.Reflection.Metadata.EntityHandle.AssemblyDefinition" />
    <member name="M:System.Reflection.Metadata.EntityHandle.Equals(System.Object)">
      <summary>Returns a value that indicates whether the current instance and the specified object are equal.</summary>
      <param name="obj">The object to compare with the current instance.</param>
      <returns>
        <see langword="true" /> if <paramref name="obj" /> is an <see cref="T:System.Reflection.Metadata.EntityHandle" /> and is equal to the current instance; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Reflection.Metadata.EntityHandle.Equals(System.Reflection.Metadata.EntityHandle)">
      <summary>Returns a value that indicates whether the current instance and the specified <see cref="T:System.Reflection.Metadata.EntityHandle" /> are equal.</summary>
      <param name="other">The value to compare with the current instance.</param>
      <returns>
        <see langword="true" /> if the current instance and <paramref name="other" /> are equal; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Reflection.Metadata.EntityHandle.GetHashCode">
      <summary>Returns the hash code for this instance.</summary>
      <returns>The hash code for this instance.</returns>
    </member>
    <member name="P:System.Reflection.Metadata.EntityHandle.IsNil" />
    <member name="P:System.Reflection.Metadata.EntityHandle.Kind" />
    <member name="F:System.Reflection.Metadata.EntityHandle.ModuleDefinition" />
    <member name="M:System.Reflection.Metadata.EntityHandle.op_Equality(System.Reflection.Metadata.EntityHandle,System.Reflection.Metadata.EntityHandle)">
      <param name="left" />
      <param name="right" />
    </member>
    <member name="M:System.Reflection.Metadata.EntityHandle.op_Explicit(System.Reflection.Metadata.Handle)~System.Reflection.Metadata.EntityHandle">
      <param name="handle" />
    </member>
    <member name="M:System.Reflection.Metadata.EntityHandle.op_Implicit(System.Reflection.Metadata.EntityHandle)~System.Reflection.Metadata.Handle">
      <param name="handle" />
    </member>
    <member name="M:System.Reflection.Metadata.EntityHandle.op_Inequality(System.Reflection.Metadata.EntityHandle,System.Reflection.Metadata.EntityHandle)">
      <param name="left" />
      <param name="right" />
    </member>
    <member name="T:System.Reflection.Metadata.EventAccessors" />
    <member name="P:System.Reflection.Metadata.EventAccessors.Adder" />
    <member name="P:System.Reflection.Metadata.EventAccessors.Others" />
    <member name="P:System.Reflection.Metadata.EventAccessors.Raiser" />
    <member name="P:System.Reflection.Metadata.EventAccessors.Remover" />
    <member name="T:System.Reflection.Metadata.EventDefinition" />
    <member name="P:System.Reflection.Metadata.EventDefinition.Attributes" />
    <member name="M:System.Reflection.Metadata.EventDefinition.GetAccessors" />
    <member name="M:System.Reflection.Metadata.EventDefinition.GetCustomAttributes" />
    <member name="P:System.Reflection.Metadata.EventDefinition.Name" />
    <member name="P:System.Reflection.Metadata.EventDefinition.Type" />
    <member name="T:System.Reflection.Metadata.EventDefinitionHandle" />
    <member name="M:System.Reflection.Metadata.EventDefinitionHandle.Equals(System.Object)">
      <param name="obj" />
    </member>
    <member name="M:System.Reflection.Metadata.EventDefinitionHandle.Equals(System.Reflection.Metadata.EventDefinitionHandle)">
      <param name="other" />
    </member>
    <member name="M:System.Reflection.Metadata.EventDefinitionHandle.GetHashCode" />
    <member name="P:System.Reflection.Metadata.EventDefinitionHandle.IsNil" />
    <member name="M:System.Reflection.Metadata.EventDefinitionHandle.op_Equality(System.Reflection.Metadata.EventDefinitionHandle,System.Reflection.Metadata.EventDefinitionHandle)">
      <param name="left" />
      <param name="right" />
    </member>
    <member name="M:System.Reflection.Metadata.EventDefinitionHandle.op_Explicit(System.Reflection.Metadata.EntityHandle)~System.Reflection.Metadata.EventDefinitionHandle">
      <param name="handle" />
    </member>
    <member name="M:System.Reflection.Metadata.EventDefinitionHandle.op_Explicit(System.Reflection.Metadata.Handle)~System.Reflection.Metadata.EventDefinitionHandle">
      <param name="handle" />
    </member>
    <member name="M:System.Reflection.Metadata.EventDefinitionHandle.op_Implicit(System.Reflection.Metadata.EventDefinitionHandle)~System.Reflection.Metadata.EntityHandle">
      <param name="handle" />
    </member>
    <member name="M:System.Reflection.Metadata.EventDefinitionHandle.op_Implicit(System.Reflection.Metadata.EventDefinitionHandle)~System.Reflection.Metadata.Handle">
      <param name="handle" />
    </member>
    <member name="M:System.Reflection.Metadata.EventDefinitionHandle.op_Inequality(System.Reflection.Metadata.EventDefinitionHandle,System.Reflection.Metadata.EventDefinitionHandle)">
      <param name="left" />
      <param name="right" />
    </member>
    <member name="T:System.Reflection.Metadata.EventDefinitionHandleCollection" />
    <member name="P:System.Reflection.Metadata.EventDefinitionHandleCollection.Count" />
    <member name="T:System.Reflection.Metadata.EventDefinitionHandleCollection.Enumerator" />
    <member name="P:System.Reflection.Metadata.EventDefinitionHandleCollection.Enumerator.Current" />
    <member name="M:System.Reflection.Metadata.EventDefinitionHandleCollection.Enumerator.MoveNext" />
    <member name="P:System.Reflection.Metadata.EventDefinitionHandleCollection.Enumerator.System#Collections#IEnumerator#Current" />
    <member name="M:System.Reflection.Metadata.EventDefinitionHandleCollection.Enumerator.System#Collections#IEnumerator#Reset" />
    <member name="M:System.Reflection.Metadata.EventDefinitionHandleCollection.Enumerator.System#IDisposable#Dispose" />
    <member name="M:System.Reflection.Metadata.EventDefinitionHandleCollection.GetEnumerator" />
    <member name="M:System.Reflection.Metadata.EventDefinitionHandleCollection.System#Collections#Generic#IEnumerable{System#Reflection#Metadata#EventDefinitionHandle}#GetEnumerator" />
    <member name="M:System.Reflection.Metadata.EventDefinitionHandleCollection.System#Collections#IEnumerable#GetEnumerator" />
    <member name="T:System.Reflection.Metadata.ExceptionRegion" />
    <member name="P:System.Reflection.Metadata.ExceptionRegion.CatchType">
      <summary>Gets a TypeRef, TypeDef, or TypeSpec handle if the region represents a catch, or a nil token otherwise (<see langword="default" />(<see cref="T:System.Reflection.Metadata.EntityHandle" />)).</summary>
    </member>
    <member name="P:System.Reflection.Metadata.ExceptionRegion.FilterOffset">
      <summary>Gets the IL offset of the start of the filter block, or -1 if the region is not a filter.</summary>
    </member>
    <member name="P:System.Reflection.Metadata.ExceptionRegion.HandlerLength">
      <summary>Gets the length in bytes of the exception handler.</summary>
    </member>
    <member name="P:System.Reflection.Metadata.ExceptionRegion.HandlerOffset">
      <summary>Gets the starting IL offset of the exception handler.</summary>
    </member>
    <member name="P:System.Reflection.Metadata.ExceptionRegion.Kind" />
    <member name="P:System.Reflection.Metadata.ExceptionRegion.TryLength">
      <summary>Gets the length in bytes of the try block.</summary>
    </member>
    <member name="P:System.Reflection.Metadata.ExceptionRegion.TryOffset">
      <summary>Gets the starting IL offset of the try block.</summary>
    </member>
    <member name="T:System.Reflection.Metadata.ExceptionRegionKind" />
    <member name="F:System.Reflection.Metadata.ExceptionRegionKind.Catch" />
    <member name="F:System.Reflection.Metadata.ExceptionRegionKind.Fault" />
    <member name="F:System.Reflection.Metadata.ExceptionRegionKind.Filter" />
    <member name="F:System.Reflection.Metadata.ExceptionRegionKind.Finally" />
    <member name="T:System.Reflection.Metadata.ExportedType" />
    <member name="P:System.Reflection.Metadata.ExportedType.Attributes" />
    <member name="M:System.Reflection.Metadata.ExportedType.GetCustomAttributes" />
    <member name="P:System.Reflection.Metadata.ExportedType.Implementation">
      <summary>Gets a handle to resolve the implementation of the target type.</summary>
      <returns>
        <see cref="T:System.Reflection.Metadata.AssemblyFileHandle" /> representing another module in the assembly.
      <see cref="T:System.Reflection.Metadata.AssemblyReferenceHandle" /> representing another assembly if <see cref="P:System.Reflection.Metadata.ExportedType.IsForwarder" /> is <see langword="true" />.
      <see cref="T:System.Reflection.Metadata.ExportedTypeHandle" /> representing the declaring exported type in which this was is nested.</returns>
    </member>
    <member name="P:System.Reflection.Metadata.ExportedType.IsForwarder" />
    <member name="P:System.Reflection.Metadata.ExportedType.Name">
      <summary>Gets the name of the target type, or <see langword="default" /> if the type is nested or defined in a root namespace.</summary>
      <returns>A <see cref="T:System.Reflection.Metadata.StringHandle" /> struct instance.</returns>
    </member>
    <member name="P:System.Reflection.Metadata.ExportedType.Namespace">
      <summary>Gets the full name of the namespace that contains the target type, or <see langword="default" /> if the type is nested or defined in a root namespace.</summary>
    </member>
    <member name="P:System.Reflection.Metadata.ExportedType.NamespaceDefinition">
      <summary>Gets the definition handle of the namespace where the target type is defined, or <see langword="default" /> if the type is nested or defined in a root namespace.</summary>
    </member>
    <member name="T:System.Reflection.Metadata.ExportedTypeHandle" />
    <member name="M:System.Reflection.Metadata.ExportedTypeHandle.Equals(System.Object)">
      <param name="obj" />
    </member>
    <member name="M:System.Reflection.Metadata.ExportedTypeHandle.Equals(System.Reflection.Metadata.ExportedTypeHandle)">
      <param name="other" />
    </member>
    <member name="M:System.Reflection.Metadata.ExportedTypeHandle.GetHashCode" />
    <member name="P:System.Reflection.Metadata.ExportedTypeHandle.IsNil" />
    <member name="M:System.Reflection.Metadata.ExportedTypeHandle.op_Equality(System.Reflection.Metadata.ExportedTypeHandle,System.Reflection.Metadata.ExportedTypeHandle)">
      <param name="left" />
      <param name="right" />
    </member>
    <member name="M:System.Reflection.Metadata.ExportedTypeHandle.op_Explicit(System.Reflection.Metadata.EntityHandle)~System.Reflection.Metadata.ExportedTypeHandle">
      <param name="handle" />
    </member>
    <member name="M:System.Reflection.Metadata.ExportedTypeHandle.op_Explicit(System.Reflection.Metadata.Handle)~System.Reflection.Metadata.ExportedTypeHandle">
      <param name="handle" />
    </member>
    <member name="M:System.Reflection.Metadata.ExportedTypeHandle.op_Implicit(System.Reflection.Metadata.ExportedTypeHandle)~System.Reflection.Metadata.EntityHandle">
      <param name="handle" />
    </member>
    <member name="M:System.Reflection.Metadata.ExportedTypeHandle.op_Implicit(System.Reflection.Metadata.ExportedTypeHandle)~System.Reflection.Metadata.Handle">
      <param name="handle" />
    </member>
    <member name="M:System.Reflection.Metadata.ExportedTypeHandle.op_Inequality(System.Reflection.Metadata.ExportedTypeHandle,System.Reflection.Metadata.ExportedTypeHandle)">
      <param name="left" />
      <param name="right" />
    </member>
    <member name="T:System.Reflection.Metadata.ExportedTypeHandleCollection">
      <summary>Represents a collection of <see cref="T:System.Reflection.Metadata.TypeReferenceHandle" /> instances.</summary>
    </member>
    <member name="P:System.Reflection.Metadata.ExportedTypeHandleCollection.Count" />
    <member name="T:System.Reflection.Metadata.ExportedTypeHandleCollection.Enumerator" />
    <member name="P:System.Reflection.Metadata.ExportedTypeHandleCollection.Enumerator.Current" />
    <member name="M:System.Reflection.Metadata.ExportedTypeHandleCollection.Enumerator.MoveNext" />
    <member name="P:System.Reflection.Metadata.ExportedTypeHandleCollection.Enumerator.System#Collections#IEnumerator#Current" />
    <member name="M:System.Reflection.Metadata.ExportedTypeHandleCollection.Enumerator.System#Collections#IEnumerator#Reset" />
    <member name="M:System.Reflection.Metadata.ExportedTypeHandleCollection.Enumerator.System#IDisposable#Dispose" />
    <member name="M:System.Reflection.Metadata.ExportedTypeHandleCollection.GetEnumerator" />
    <member name="M:System.Reflection.Metadata.ExportedTypeHandleCollection.System#Collections#Generic#IEnumerable{System#Reflection#Metadata#ExportedTypeHandle}#GetEnumerator" />
    <member name="M:System.Reflection.Metadata.ExportedTypeHandleCollection.System#Collections#IEnumerable#GetEnumerator" />
    <member name="T:System.Reflection.Metadata.FieldDefinition" />
    <member name="P:System.Reflection.Metadata.FieldDefinition.Attributes" />
    <member name="M:System.Reflection.Metadata.FieldDefinition.DecodeSignature``2(System.Reflection.Metadata.ISignatureTypeProvider{``0,``1},``1)">
      <param name="provider" />
      <param name="genericContext" />
      <typeparam name="TType" />
      <typeparam name="TGenericContext" />
    </member>
    <member name="M:System.Reflection.Metadata.FieldDefinition.GetCustomAttributes" />
    <member name="M:System.Reflection.Metadata.FieldDefinition.GetDeclaringType" />
    <member name="M:System.Reflection.Metadata.FieldDefinition.GetDefaultValue" />
    <member name="M:System.Reflection.Metadata.FieldDefinition.GetMarshallingDescriptor" />
    <member name="M:System.Reflection.Metadata.FieldDefinition.GetOffset">
      <summary>Returns the field layout offset, or -1 if it is not available.</summary>
      <returns>The field definition offset, or -1 if it is not available.</returns>
    </member>
    <member name="M:System.Reflection.Metadata.FieldDefinition.GetRelativeVirtualAddress" />
    <member name="P:System.Reflection.Metadata.FieldDefinition.Name" />
    <member name="P:System.Reflection.Metadata.FieldDefinition.Signature" />
    <member name="T:System.Reflection.Metadata.FieldDefinitionHandle" />
    <member name="M:System.Reflection.Metadata.FieldDefinitionHandle.Equals(System.Object)">
      <param name="obj" />
    </member>
    <member name="M:System.Reflection.Metadata.FieldDefinitionHandle.Equals(System.Reflection.Metadata.FieldDefinitionHandle)">
      <param name="other" />
    </member>
    <member name="M:System.Reflection.Metadata.FieldDefinitionHandle.GetHashCode" />
    <member name="P:System.Reflection.Metadata.FieldDefinitionHandle.IsNil" />
    <member name="M:System.Reflection.Metadata.FieldDefinitionHandle.op_Equality(System.Reflection.Metadata.FieldDefinitionHandle,System.Reflection.Metadata.FieldDefinitionHandle)">
      <param name="left" />
      <param name="right" />
    </member>
    <member name="M:System.Reflection.Metadata.FieldDefinitionHandle.op_Explicit(System.Reflection.Metadata.EntityHandle)~System.Reflection.Metadata.FieldDefinitionHandle">
      <param name="handle" />
    </member>
    <member name="M:System.Reflection.Metadata.FieldDefinitionHandle.op_Explicit(System.Reflection.Metadata.Handle)~System.Reflection.Metadata.FieldDefinitionHandle">
      <param name="handle" />
    </member>
    <member name="M:System.Reflection.Metadata.FieldDefinitionHandle.op_Implicit(System.Reflection.Metadata.FieldDefinitionHandle)~System.Reflection.Metadata.EntityHandle">
      <param name="handle" />
    </member>
    <member name="M:System.Reflection.Metadata.FieldDefinitionHandle.op_Implicit(System.Reflection.Metadata.FieldDefinitionHandle)~System.Reflection.Metadata.Handle">
      <param name="handle" />
    </member>
    <member name="M:System.Reflection.Metadata.FieldDefinitionHandle.op_Inequality(System.Reflection.Metadata.FieldDefinitionHandle,System.Reflection.Metadata.FieldDefinitionHandle)">
      <param name="left" />
      <param name="right" />
    </member>
    <member name="T:System.Reflection.Metadata.FieldDefinitionHandleCollection" />
    <member name="P:System.Reflection.Metadata.FieldDefinitionHandleCollection.Count" />
    <member name="T:System.Reflection.Metadata.FieldDefinitionHandleCollection.Enumerator" />
    <member name="P:System.Reflection.Metadata.FieldDefinitionHandleCollection.Enumerator.Current" />
    <member name="M:System.Reflection.Metadata.FieldDefinitionHandleCollection.Enumerator.MoveNext" />
    <member name="P:System.Reflection.Metadata.FieldDefinitionHandleCollection.Enumerator.System#Collections#IEnumerator#Current" />
    <member name="M:System.Reflection.Metadata.FieldDefinitionHandleCollection.Enumerator.System#Collections#IEnumerator#Reset" />
    <member name="M:System.Reflection.Metadata.FieldDefinitionHandleCollection.Enumerator.System#IDisposable#Dispose" />
    <member name="M:System.Reflection.Metadata.FieldDefinitionHandleCollection.GetEnumerator" />
    <member name="M:System.Reflection.Metadata.FieldDefinitionHandleCollection.System#Collections#Generic#IEnumerable{System#Reflection#Metadata#FieldDefinitionHandle}#GetEnumerator" />
    <member name="M:System.Reflection.Metadata.FieldDefinitionHandleCollection.System#Collections#IEnumerable#GetEnumerator" />
    <member name="T:System.Reflection.Metadata.GenericParameter" />
    <member name="P:System.Reflection.Metadata.GenericParameter.Attributes">
      <summary>Gets the attributes specifying variance and constraints.</summary>
    </member>
    <member name="M:System.Reflection.Metadata.GenericParameter.GetConstraints" />
    <member name="M:System.Reflection.Metadata.GenericParameter.GetCustomAttributes" />
    <member name="P:System.Reflection.Metadata.GenericParameter.Index">
      <summary>Gets the zero-based index of the parameter within the declaring generic type or method declaration.</summary>
    </member>
    <member name="P:System.Reflection.Metadata.GenericParameter.Name">
      <summary>Gets the name of the generic parameter.</summary>
    </member>
    <member name="P:System.Reflection.Metadata.GenericParameter.Parent">
      <summary>Gets a <see cref="T:System.Reflection.Metadata.TypeDefinitionHandle" /> or <see cref="T:System.Reflection.Metadata.MethodDefinitionHandle" /> that represents the parent of this generic parameter.</summary>
    </member>
    <member name="T:System.Reflection.Metadata.GenericParameterConstraint" />
    <member name="M:System.Reflection.Metadata.GenericParameterConstraint.GetCustomAttributes" />
    <member name="P:System.Reflection.Metadata.GenericParameterConstraint.Parameter">
      <summary>Gets the constrained <see cref="T:System.Reflection.Metadata.GenericParameterHandle" />.</summary>
    </member>
    <member name="P:System.Reflection.Metadata.GenericParameterConstraint.Type">
      <summary>Gets a handle (<see cref="T:System.Reflection.Metadata.TypeDefinitionHandle" />, <see cref="T:System.Reflection.Metadata.TypeReferenceHandle" />, or <see cref="T:System.Reflection.Metadata.TypeSpecificationHandle" />)
specifying from which type this generic parameter is constrained to derive,
or which interface this generic parameter is constrained to implement.</summary>
      <returns>An <see cref="T:System.Reflection.Metadata.EntityHandle" /> instance.</returns>
    </member>
    <member name="T:System.Reflection.Metadata.GenericParameterConstraintHandle" />
    <member name="M:System.Reflection.Metadata.GenericParameterConstraintHandle.Equals(System.Object)">
      <param name="obj" />
    </member>
    <member name="M:System.Reflection.Metadata.GenericParameterConstraintHandle.Equals(System.Reflection.Metadata.GenericParameterConstraintHandle)">
      <param name="other" />
    </member>
    <member name="M:System.Reflection.Metadata.GenericParameterConstraintHandle.GetHashCode" />
    <member name="P:System.Reflection.Metadata.GenericParameterConstraintHandle.IsNil" />
    <member name="M:System.Reflection.Metadata.GenericParameterConstraintHandle.op_Equality(System.Reflection.Metadata.GenericParameterConstraintHandle,System.Reflection.Metadata.GenericParameterConstraintHandle)">
      <param name="left" />
      <param name="right" />
    </member>
    <member name="M:System.Reflection.Metadata.GenericParameterConstraintHandle.op_Explicit(System.Reflection.Metadata.EntityHandle)~System.Reflection.Metadata.GenericParameterConstraintHandle">
      <param name="handle" />
    </member>
    <member name="M:System.Reflection.Metadata.GenericParameterConstraintHandle.op_Explicit(System.Reflection.Metadata.Handle)~System.Reflection.Metadata.GenericParameterConstraintHandle">
      <param name="handle" />
    </member>
    <member name="M:System.Reflection.Metadata.GenericParameterConstraintHandle.op_Implicit(System.Reflection.Metadata.GenericParameterConstraintHandle)~System.Reflection.Metadata.EntityHandle">
      <param name="handle" />
    </member>
    <member name="M:System.Reflection.Metadata.GenericParameterConstraintHandle.op_Implicit(System.Reflection.Metadata.GenericParameterConstraintHandle)~System.Reflection.Metadata.Handle">
      <param name="handle" />
    </member>
    <member name="M:System.Reflection.Metadata.GenericParameterConstraintHandle.op_Inequality(System.Reflection.Metadata.GenericParameterConstraintHandle,System.Reflection.Metadata.GenericParameterConstraintHandle)">
      <param name="left" />
      <param name="right" />
    </member>
    <member name="T:System.Reflection.Metadata.GenericParameterConstraintHandleCollection">
      <summary>Represents a collection of constraints of a generic type parameter.</summary>
    </member>
    <member name="P:System.Reflection.Metadata.GenericParameterConstraintHandleCollection.Count" />
    <member name="T:System.Reflection.Metadata.GenericParameterConstraintHandleCollection.Enumerator" />
    <member name="P:System.Reflection.Metadata.GenericParameterConstraintHandleCollection.Enumerator.Current" />
    <member name="M:System.Reflection.Metadata.GenericParameterConstraintHandleCollection.Enumerator.MoveNext" />
    <member name="P:System.Reflection.Metadata.GenericParameterConstraintHandleCollection.Enumerator.System#Collections#IEnumerator#Current" />
    <member name="M:System.Reflection.Metadata.GenericParameterConstraintHandleCollection.Enumerator.System#Collections#IEnumerator#Reset" />
    <member name="M:System.Reflection.Metadata.GenericParameterConstraintHandleCollection.Enumerator.System#IDisposable#Dispose" />
    <member name="M:System.Reflection.Metadata.GenericParameterConstraintHandleCollection.GetEnumerator" />
    <member name="P:System.Reflection.Metadata.GenericParameterConstraintHandleCollection.Item(System.Int32)">
      <param name="index" />
    </member>
    <member name="M:System.Reflection.Metadata.GenericParameterConstraintHandleCollection.System#Collections#Generic#IEnumerable{System#Reflection#Metadata#GenericParameterConstraintHandle}#GetEnumerator" />
    <member name="M:System.Reflection.Metadata.GenericParameterConstraintHandleCollection.System#Collections#IEnumerable#GetEnumerator" />
    <member name="T:System.Reflection.Metadata.GenericParameterHandle" />
    <member name="M:System.Reflection.Metadata.GenericParameterHandle.Equals(System.Object)">
      <param name="obj" />
    </member>
    <member name="M:System.Reflection.Metadata.GenericParameterHandle.Equals(System.Reflection.Metadata.GenericParameterHandle)">
      <param name="other" />
    </member>
    <member name="M:System.Reflection.Metadata.GenericParameterHandle.GetHashCode" />
    <member name="P:System.Reflection.Metadata.GenericParameterHandle.IsNil" />
    <member name="M:System.Reflection.Metadata.GenericParameterHandle.op_Equality(System.Reflection.Metadata.GenericParameterHandle,System.Reflection.Metadata.GenericParameterHandle)">
      <param name="left" />
      <param name="right" />
    </member>
    <member name="M:System.Reflection.Metadata.GenericParameterHandle.op_Explicit(System.Reflection.Metadata.EntityHandle)~System.Reflection.Metadata.GenericParameterHandle">
      <param name="handle" />
    </member>
    <member name="M:System.Reflection.Metadata.GenericParameterHandle.op_Explicit(System.Reflection.Metadata.Handle)~System.Reflection.Metadata.GenericParameterHandle">
      <param name="handle" />
    </member>
    <member name="M:System.Reflection.Metadata.GenericParameterHandle.op_Implicit(System.Reflection.Metadata.GenericParameterHandle)~System.Reflection.Metadata.EntityHandle">
      <param name="handle" />
    </member>
    <member name="M:System.Reflection.Metadata.GenericParameterHandle.op_Implicit(System.Reflection.Metadata.GenericParameterHandle)~System.Reflection.Metadata.Handle">
      <param name="handle" />
    </member>
    <member name="M:System.Reflection.Metadata.GenericParameterHandle.op_Inequality(System.Reflection.Metadata.GenericParameterHandle,System.Reflection.Metadata.GenericParameterHandle)">
      <param name="left" />
      <param name="right" />
    </member>
    <member name="T:System.Reflection.Metadata.GenericParameterHandleCollection">
      <summary>Represents a collection of generic type parameters of a method or type.</summary>
    </member>
    <member name="P:System.Reflection.Metadata.GenericParameterHandleCollection.Count" />
    <member name="T:System.Reflection.Metadata.GenericParameterHandleCollection.Enumerator" />
    <member name="P:System.Reflection.Metadata.GenericParameterHandleCollection.Enumerator.Current" />
    <member name="M:System.Reflection.Metadata.GenericParameterHandleCollection.Enumerator.MoveNext" />
    <member name="P:System.Reflection.Metadata.GenericParameterHandleCollection.Enumerator.System#Collections#IEnumerator#Current" />
    <member name="M:System.Reflection.Metadata.GenericParameterHandleCollection.Enumerator.System#Collections#IEnumerator#Reset" />
    <member name="M:System.Reflection.Metadata.GenericParameterHandleCollection.Enumerator.System#IDisposable#Dispose" />
    <member name="M:System.Reflection.Metadata.GenericParameterHandleCollection.GetEnumerator" />
    <member name="P:System.Reflection.Metadata.GenericParameterHandleCollection.Item(System.Int32)">
      <param name="index" />
    </member>
    <member name="M:System.Reflection.Metadata.GenericParameterHandleCollection.System#Collections#Generic#IEnumerable{System#Reflection#Metadata#GenericParameterHandle}#GetEnumerator" />
    <member name="M:System.Reflection.Metadata.GenericParameterHandleCollection.System#Collections#IEnumerable#GetEnumerator" />
    <member name="T:System.Reflection.Metadata.GuidHandle" />
    <member name="M:System.Reflection.Metadata.GuidHandle.Equals(System.Object)">
      <param name="obj" />
    </member>
    <member name="M:System.Reflection.Metadata.GuidHandle.Equals(System.Reflection.Metadata.GuidHandle)">
      <param name="other" />
    </member>
    <member name="M:System.Reflection.Metadata.GuidHandle.GetHashCode" />
    <member name="P:System.Reflection.Metadata.GuidHandle.IsNil" />
    <member name="M:System.Reflection.Metadata.GuidHandle.op_Equality(System.Reflection.Metadata.GuidHandle,System.Reflection.Metadata.GuidHandle)">
      <param name="left" />
      <param name="right" />
    </member>
    <member name="M:System.Reflection.Metadata.GuidHandle.op_Explicit(System.Reflection.Metadata.Handle)~System.Reflection.Metadata.GuidHandle">
      <param name="handle" />
    </member>
    <member name="M:System.Reflection.Metadata.GuidHandle.op_Implicit(System.Reflection.Metadata.GuidHandle)~System.Reflection.Metadata.Handle">
      <param name="handle" />
    </member>
    <member name="M:System.Reflection.Metadata.GuidHandle.op_Inequality(System.Reflection.Metadata.GuidHandle,System.Reflection.Metadata.GuidHandle)">
      <param name="left" />
      <param name="right" />
    </member>
    <member name="T:System.Reflection.Metadata.Handle">
      <summary>Represents any metadata entity (such as a type reference, a type definition, a type specification, a method definition, or a custom attribute) or value (a string, blob, guid, or user string).</summary>
    </member>
    <member name="F:System.Reflection.Metadata.Handle.AssemblyDefinition" />
    <member name="M:System.Reflection.Metadata.Handle.Equals(System.Object)">
      <param name="obj" />
    </member>
    <member name="M:System.Reflection.Metadata.Handle.Equals(System.Reflection.Metadata.Handle)">
      <param name="other" />
    </member>
    <member name="M:System.Reflection.Metadata.Handle.GetHashCode" />
    <member name="P:System.Reflection.Metadata.Handle.IsNil" />
    <member name="P:System.Reflection.Metadata.Handle.Kind" />
    <member name="F:System.Reflection.Metadata.Handle.ModuleDefinition" />
    <member name="M:System.Reflection.Metadata.Handle.op_Equality(System.Reflection.Metadata.Handle,System.Reflection.Metadata.Handle)">
      <param name="left" />
      <param name="right" />
    </member>
    <member name="M:System.Reflection.Metadata.Handle.op_Inequality(System.Reflection.Metadata.Handle,System.Reflection.Metadata.Handle)">
      <param name="left" />
      <param name="right" />
    </member>
    <member name="T:System.Reflection.Metadata.HandleComparer" />
    <member name="M:System.Reflection.Metadata.HandleComparer.Compare(System.Reflection.Metadata.EntityHandle,System.Reflection.Metadata.EntityHandle)">
      <summary>Compares two entity handles.</summary>
      <param name="x">The first entity handle to compare.</param>
      <param name="y">The second entity handle to compare.</param>
      <returns>Zero if the two entity handles are equal, and a non-zero value of they are not.</returns>
    </member>
    <member name="M:System.Reflection.Metadata.HandleComparer.Compare(System.Reflection.Metadata.Handle,System.Reflection.Metadata.Handle)">
      <summary>Compares two handles.</summary>
      <param name="x">The first handle to compare.</param>
      <param name="y">The second handle to compare.</param>
      <returns>Zero if the two handles are equal, and a non-zero value if they are not.</returns>
    </member>
    <member name="P:System.Reflection.Metadata.HandleComparer.Default" />
    <member name="M:System.Reflection.Metadata.HandleComparer.Equals(System.Reflection.Metadata.EntityHandle,System.Reflection.Metadata.EntityHandle)">
      <param name="x" />
      <param name="y" />
    </member>
    <member name="M:System.Reflection.Metadata.HandleComparer.Equals(System.Reflection.Metadata.Handle,System.Reflection.Metadata.Handle)">
      <param name="x" />
      <param name="y" />
    </member>
    <member name="M:System.Reflection.Metadata.HandleComparer.GetHashCode(System.Reflection.Metadata.EntityHandle)">
      <param name="obj" />
    </member>
    <member name="M:System.Reflection.Metadata.HandleComparer.GetHashCode(System.Reflection.Metadata.Handle)">
      <param name="obj" />
    </member>
    <member name="T:System.Reflection.Metadata.HandleKind" />
    <member name="F:System.Reflection.Metadata.HandleKind.AssemblyDefinition" />
    <member name="F:System.Reflection.Metadata.HandleKind.AssemblyFile" />
    <member name="F:System.Reflection.Metadata.HandleKind.AssemblyReference" />
    <member name="F:System.Reflection.Metadata.HandleKind.Blob" />
    <member name="F:System.Reflection.Metadata.HandleKind.Constant" />
    <member name="F:System.Reflection.Metadata.HandleKind.CustomAttribute" />
    <member name="F:System.Reflection.Metadata.HandleKind.CustomDebugInformation" />
    <member name="F:System.Reflection.Metadata.HandleKind.DeclarativeSecurityAttribute" />
    <member name="F:System.Reflection.Metadata.HandleKind.Document" />
    <member name="F:System.Reflection.Metadata.HandleKind.EventDefinition" />
    <member name="F:System.Reflection.Metadata.HandleKind.ExportedType" />
    <member name="F:System.Reflection.Metadata.HandleKind.FieldDefinition" />
    <member name="F:System.Reflection.Metadata.HandleKind.GenericParameter" />
    <member name="F:System.Reflection.Metadata.HandleKind.GenericParameterConstraint" />
    <member name="F:System.Reflection.Metadata.HandleKind.Guid" />
    <member name="F:System.Reflection.Metadata.HandleKind.ImportScope" />
    <member name="F:System.Reflection.Metadata.HandleKind.InterfaceImplementation" />
    <member name="F:System.Reflection.Metadata.HandleKind.LocalConstant" />
    <member name="F:System.Reflection.Metadata.HandleKind.LocalScope" />
    <member name="F:System.Reflection.Metadata.HandleKind.LocalVariable" />
    <member name="F:System.Reflection.Metadata.HandleKind.ManifestResource" />
    <member name="F:System.Reflection.Metadata.HandleKind.MemberReference" />
    <member name="F:System.Reflection.Metadata.HandleKind.MethodDebugInformation" />
    <member name="F:System.Reflection.Metadata.HandleKind.MethodDefinition" />
    <member name="F:System.Reflection.Metadata.HandleKind.MethodImplementation" />
    <member name="F:System.Reflection.Metadata.HandleKind.MethodSpecification" />
    <member name="F:System.Reflection.Metadata.HandleKind.ModuleDefinition" />
    <member name="F:System.Reflection.Metadata.HandleKind.ModuleReference" />
    <member name="F:System.Reflection.Metadata.HandleKind.NamespaceDefinition" />
    <member name="F:System.Reflection.Metadata.HandleKind.Parameter" />
    <member name="F:System.Reflection.Metadata.HandleKind.PropertyDefinition" />
    <member name="F:System.Reflection.Metadata.HandleKind.StandaloneSignature" />
    <member name="F:System.Reflection.Metadata.HandleKind.String" />
    <member name="F:System.Reflection.Metadata.HandleKind.TypeDefinition" />
    <member name="F:System.Reflection.Metadata.HandleKind.TypeReference" />
    <member name="F:System.Reflection.Metadata.HandleKind.TypeSpecification" />
    <member name="F:System.Reflection.Metadata.HandleKind.UserString" />
    <member name="T:System.Reflection.Metadata.IConstructedTypeProvider`1">
      <typeparam name="TType" />
    </member>
    <member name="M:System.Reflection.Metadata.IConstructedTypeProvider`1.GetArrayType(`0,System.Reflection.Metadata.ArrayShape)">
      <summary>Gets the type symbol for a generalized array of the given element type and shape.</summary>
      <param name="elementType">The type of the elements in the array.</param>
      <param name="shape">The shape (rank, sizes, and lower bounds) of the array.</param>
    </member>
    <member name="M:System.Reflection.Metadata.IConstructedTypeProvider`1.GetByReferenceType(`0)">
      <summary>Gets the type symbol for a managed pointer to the given element type.</summary>
      <param name="elementType" />
    </member>
    <member name="M:System.Reflection.Metadata.IConstructedTypeProvider`1.GetGenericInstantiation(`0,System.Collections.Immutable.ImmutableArray{`0})">
      <summary>Gets the type symbol for a generic instantiation of the given generic type with the given type arguments.</summary>
      <param name="genericType" />
      <param name="typeArguments" />
    </member>
    <member name="M:System.Reflection.Metadata.IConstructedTypeProvider`1.GetPointerType(`0)">
      <summary>Gets the type symbol for an unmanaged pointer to the given element type.</summary>
      <param name="elementType" />
    </member>
    <member name="T:System.Reflection.Metadata.ICustomAttributeTypeProvider`1">
      <typeparam name="TType" />
    </member>
    <member name="M:System.Reflection.Metadata.ICustomAttributeTypeProvider`1.GetSystemType">
      <summary>Gets the <typeparamref name="TType" /> representation for <see cref="T:System.Type" />.</summary>
    </member>
    <member name="M:System.Reflection.Metadata.ICustomAttributeTypeProvider`1.GetTypeFromSerializedName(System.String)">
      <summary>Gets the type symbol for the given serialized type name.</summary>
      <param name="name">The serialized type name in so-called "reflection notation" format (as understood by the <see cref="M:System.Type.GetType(System.String)" /> method.)</param>
      <returns>A <typeparamref name="TType" /> instance.</returns>
      <exception cref="T:System.BadImageFormatException">The name is malformed.</exception>
    </member>
    <member name="M:System.Reflection.Metadata.ICustomAttributeTypeProvider`1.GetUnderlyingEnumType(`0)">
      <summary>Gets the underlying type of the given enum type symbol.</summary>
      <param name="type">An enum type.</param>
      <returns>A type code that indicates the underlying type of the enumeration.</returns>
      <exception cref="T:System.BadImageFormatException">The given type symbol does not represent an enum.</exception>
    </member>
    <member name="M:System.Reflection.Metadata.ICustomAttributeTypeProvider`1.IsSystemType(`0)">
      <summary>Verifies if the given type represents <see cref="T:System.Type" />.</summary>
      <param name="type">The type to verify.</param>
      <returns>
        <see langword="true" /> if the given type is a <see cref="T:System.Type" />, <see langword="false" /> otherwise.</returns>
    </member>
    <member name="T:System.Reflection.Metadata.ILOpCode" />
    <member name="F:System.Reflection.Metadata.ILOpCode.Add" />
    <member name="F:System.Reflection.Metadata.ILOpCode.Add_ovf" />
    <member name="F:System.Reflection.Metadata.ILOpCode.Add_ovf_un" />
    <member name="F:System.Reflection.Metadata.ILOpCode.And" />
    <member name="F:System.Reflection.Metadata.ILOpCode.Arglist" />
    <member name="F:System.Reflection.Metadata.ILOpCode.Beq" />
    <member name="F:System.Reflection.Metadata.ILOpCode.Beq_s" />
    <member name="F:System.Reflection.Metadata.ILOpCode.Bge" />
    <member name="F:System.Reflection.Metadata.ILOpCode.Bge_s" />
    <member name="F:System.Reflection.Metadata.ILOpCode.Bge_un" />
    <member name="F:System.Reflection.Metadata.ILOpCode.Bge_un_s" />
    <member name="F:System.Reflection.Metadata.ILOpCode.Bgt" />
    <member name="F:System.Reflection.Metadata.ILOpCode.Bgt_s" />
    <member name="F:System.Reflection.Metadata.ILOpCode.Bgt_un" />
    <member name="F:System.Reflection.Metadata.ILOpCode.Bgt_un_s" />
    <member name="F:System.Reflection.Metadata.ILOpCode.Ble" />
    <member name="F:System.Reflection.Metadata.ILOpCode.Ble_s" />
    <member name="F:System.Reflection.Metadata.ILOpCode.Ble_un" />
    <member name="F:System.Reflection.Metadata.ILOpCode.Ble_un_s" />
    <member name="F:System.Reflection.Metadata.ILOpCode.Blt" />
    <member name="F:System.Reflection.Metadata.ILOpCode.Blt_s" />
    <member name="F:System.Reflection.Metadata.ILOpCode.Blt_un" />
    <member name="F:System.Reflection.Metadata.ILOpCode.Blt_un_s" />
    <member name="F:System.Reflection.Metadata.ILOpCode.Bne_un" />
    <member name="F:System.Reflection.Metadata.ILOpCode.Bne_un_s" />
    <member name="F:System.Reflection.Metadata.ILOpCode.Box" />
    <member name="F:System.Reflection.Metadata.ILOpCode.Br" />
    <member name="F:System.Reflection.Metadata.ILOpCode.Br_s" />
    <member name="F:System.Reflection.Metadata.ILOpCode.Break" />
    <member name="F:System.Reflection.Metadata.ILOpCode.Brfalse" />
    <member name="F:System.Reflection.Metadata.ILOpCode.Brfalse_s" />
    <member name="F:System.Reflection.Metadata.ILOpCode.Brtrue" />
    <member name="F:System.Reflection.Metadata.ILOpCode.Brtrue_s" />
    <member name="F:System.Reflection.Metadata.ILOpCode.Call" />
    <member name="F:System.Reflection.Metadata.ILOpCode.Calli" />
    <member name="F:System.Reflection.Metadata.ILOpCode.Callvirt" />
    <member name="F:System.Reflection.Metadata.ILOpCode.Castclass" />
    <member name="F:System.Reflection.Metadata.ILOpCode.Ceq" />
    <member name="F:System.Reflection.Metadata.ILOpCode.Cgt" />
    <member name="F:System.Reflection.Metadata.ILOpCode.Cgt_un" />
    <member name="F:System.Reflection.Metadata.ILOpCode.Ckfinite" />
    <member name="F:System.Reflection.Metadata.ILOpCode.Clt" />
    <member name="F:System.Reflection.Metadata.ILOpCode.Clt_un" />
    <member name="F:System.Reflection.Metadata.ILOpCode.Constrained" />
    <member name="F:System.Reflection.Metadata.ILOpCode.Conv_i" />
    <member name="F:System.Reflection.Metadata.ILOpCode.Conv_i1" />
    <member name="F:System.Reflection.Metadata.ILOpCode.Conv_i2" />
    <member name="F:System.Reflection.Metadata.ILOpCode.Conv_i4" />
    <member name="F:System.Reflection.Metadata.ILOpCode.Conv_i8" />
    <member name="F:System.Reflection.Metadata.ILOpCode.Conv_ovf_i" />
    <member name="F:System.Reflection.Metadata.ILOpCode.Conv_ovf_i_un" />
    <member name="F:System.Reflection.Metadata.ILOpCode.Conv_ovf_i1" />
    <member name="F:System.Reflection.Metadata.ILOpCode.Conv_ovf_i1_un" />
    <member name="F:System.Reflection.Metadata.ILOpCode.Conv_ovf_i2" />
    <member name="F:System.Reflection.Metadata.ILOpCode.Conv_ovf_i2_un" />
    <member name="F:System.Reflection.Metadata.ILOpCode.Conv_ovf_i4" />
    <member name="F:System.Reflection.Metadata.ILOpCode.Conv_ovf_i4_un" />
    <member name="F:System.Reflection.Metadata.ILOpCode.Conv_ovf_i8" />
    <member name="F:System.Reflection.Metadata.ILOpCode.Conv_ovf_i8_un" />
    <member name="F:System.Reflection.Metadata.ILOpCode.Conv_ovf_u" />
    <member name="F:System.Reflection.Metadata.ILOpCode.Conv_ovf_u_un" />
    <member name="F:System.Reflection.Metadata.ILOpCode.Conv_ovf_u1" />
    <member name="F:System.Reflection.Metadata.ILOpCode.Conv_ovf_u1_un" />
    <member name="F:System.Reflection.Metadata.ILOpCode.Conv_ovf_u2" />
    <member name="F:System.Reflection.Metadata.ILOpCode.Conv_ovf_u2_un" />
    <member name="F:System.Reflection.Metadata.ILOpCode.Conv_ovf_u4" />
    <member name="F:System.Reflection.Metadata.ILOpCode.Conv_ovf_u4_un" />
    <member name="F:System.Reflection.Metadata.ILOpCode.Conv_ovf_u8" />
    <member name="F:System.Reflection.Metadata.ILOpCode.Conv_ovf_u8_un" />
    <member name="F:System.Reflection.Metadata.ILOpCode.Conv_r_un" />
    <member name="F:System.Reflection.Metadata.ILOpCode.Conv_r4" />
    <member name="F:System.Reflection.Metadata.ILOpCode.Conv_r8" />
    <member name="F:System.Reflection.Metadata.ILOpCode.Conv_u" />
    <member name="F:System.Reflection.Metadata.ILOpCode.Conv_u1" />
    <member name="F:System.Reflection.Metadata.ILOpCode.Conv_u2" />
    <member name="F:System.Reflection.Metadata.ILOpCode.Conv_u4" />
    <member name="F:System.Reflection.Metadata.ILOpCode.Conv_u8" />
    <member name="F:System.Reflection.Metadata.ILOpCode.Cpblk" />
    <member name="F:System.Reflection.Metadata.ILOpCode.Cpobj" />
    <member name="F:System.Reflection.Metadata.ILOpCode.Div" />
    <member name="F:System.Reflection.Metadata.ILOpCode.Div_un" />
    <member name="F:System.Reflection.Metadata.ILOpCode.Dup" />
    <member name="F:System.Reflection.Metadata.ILOpCode.Endfilter" />
    <member name="F:System.Reflection.Metadata.ILOpCode.Endfinally" />
    <member name="F:System.Reflection.Metadata.ILOpCode.Initblk" />
    <member name="F:System.Reflection.Metadata.ILOpCode.Initobj" />
    <member name="F:System.Reflection.Metadata.ILOpCode.Isinst" />
    <member name="F:System.Reflection.Metadata.ILOpCode.Jmp" />
    <member name="F:System.Reflection.Metadata.ILOpCode.Ldarg" />
    <member name="F:System.Reflection.Metadata.ILOpCode.Ldarg_0" />
    <member name="F:System.Reflection.Metadata.ILOpCode.Ldarg_1" />
    <member name="F:System.Reflection.Metadata.ILOpCode.Ldarg_2" />
    <member name="F:System.Reflection.Metadata.ILOpCode.Ldarg_3" />
    <member name="F:System.Reflection.Metadata.ILOpCode.Ldarg_s" />
    <member name="F:System.Reflection.Metadata.ILOpCode.Ldarga" />
    <member name="F:System.Reflection.Metadata.ILOpCode.Ldarga_s" />
    <member name="F:System.Reflection.Metadata.ILOpCode.Ldc_i4" />
    <member name="F:System.Reflection.Metadata.ILOpCode.Ldc_i4_0" />
    <member name="F:System.Reflection.Metadata.ILOpCode.Ldc_i4_1" />
    <member name="F:System.Reflection.Metadata.ILOpCode.Ldc_i4_2" />
    <member name="F:System.Reflection.Metadata.ILOpCode.Ldc_i4_3" />
    <member name="F:System.Reflection.Metadata.ILOpCode.Ldc_i4_4" />
    <member name="F:System.Reflection.Metadata.ILOpCode.Ldc_i4_5" />
    <member name="F:System.Reflection.Metadata.ILOpCode.Ldc_i4_6" />
    <member name="F:System.Reflection.Metadata.ILOpCode.Ldc_i4_7" />
    <member name="F:System.Reflection.Metadata.ILOpCode.Ldc_i4_8" />
    <member name="F:System.Reflection.Metadata.ILOpCode.Ldc_i4_m1" />
    <member name="F:System.Reflection.Metadata.ILOpCode.Ldc_i4_s" />
    <member name="F:System.Reflection.Metadata.ILOpCode.Ldc_i8" />
    <member name="F:System.Reflection.Metadata.ILOpCode.Ldc_r4" />
    <member name="F:System.Reflection.Metadata.ILOpCode.Ldc_r8" />
    <member name="F:System.Reflection.Metadata.ILOpCode.Ldelem" />
    <member name="F:System.Reflection.Metadata.ILOpCode.Ldelem_i" />
    <member name="F:System.Reflection.Metadata.ILOpCode.Ldelem_i1" />
    <member name="F:System.Reflection.Metadata.ILOpCode.Ldelem_i2" />
    <member name="F:System.Reflection.Metadata.ILOpCode.Ldelem_i4" />
    <member name="F:System.Reflection.Metadata.ILOpCode.Ldelem_i8" />
    <member name="F:System.Reflection.Metadata.ILOpCode.Ldelem_r4" />
    <member name="F:System.Reflection.Metadata.ILOpCode.Ldelem_r8" />
    <member name="F:System.Reflection.Metadata.ILOpCode.Ldelem_ref" />
    <member name="F:System.Reflection.Metadata.ILOpCode.Ldelem_u1" />
    <member name="F:System.Reflection.Metadata.ILOpCode.Ldelem_u2" />
    <member name="F:System.Reflection.Metadata.ILOpCode.Ldelem_u4" />
    <member name="F:System.Reflection.Metadata.ILOpCode.Ldelema" />
    <member name="F:System.Reflection.Metadata.ILOpCode.Ldfld" />
    <member name="F:System.Reflection.Metadata.ILOpCode.Ldflda" />
    <member name="F:System.Reflection.Metadata.ILOpCode.Ldftn" />
    <member name="F:System.Reflection.Metadata.ILOpCode.Ldind_i" />
    <member name="F:System.Reflection.Metadata.ILOpCode.Ldind_i1" />
    <member name="F:System.Reflection.Metadata.ILOpCode.Ldind_i2" />
    <member name="F:System.Reflection.Metadata.ILOpCode.Ldind_i4" />
    <member name="F:System.Reflection.Metadata.ILOpCode.Ldind_i8" />
    <member name="F:System.Reflection.Metadata.ILOpCode.Ldind_r4" />
    <member name="F:System.Reflection.Metadata.ILOpCode.Ldind_r8" />
    <member name="F:System.Reflection.Metadata.ILOpCode.Ldind_ref" />
    <member name="F:System.Reflection.Metadata.ILOpCode.Ldind_u1" />
    <member name="F:System.Reflection.Metadata.ILOpCode.Ldind_u2" />
    <member name="F:System.Reflection.Metadata.ILOpCode.Ldind_u4" />
    <member name="F:System.Reflection.Metadata.ILOpCode.Ldlen" />
    <member name="F:System.Reflection.Metadata.ILOpCode.Ldloc" />
    <member name="F:System.Reflection.Metadata.ILOpCode.Ldloc_0" />
    <member name="F:System.Reflection.Metadata.ILOpCode.Ldloc_1" />
    <member name="F:System.Reflection.Metadata.ILOpCode.Ldloc_2" />
    <member name="F:System.Reflection.Metadata.ILOpCode.Ldloc_3" />
    <member name="F:System.Reflection.Metadata.ILOpCode.Ldloc_s" />
    <member name="F:System.Reflection.Metadata.ILOpCode.Ldloca" />
    <member name="F:System.Reflection.Metadata.ILOpCode.Ldloca_s" />
    <member name="F:System.Reflection.Metadata.ILOpCode.Ldnull" />
    <member name="F:System.Reflection.Metadata.ILOpCode.Ldobj" />
    <member name="F:System.Reflection.Metadata.ILOpCode.Ldsfld" />
    <member name="F:System.Reflection.Metadata.ILOpCode.Ldsflda" />
    <member name="F:System.Reflection.Metadata.ILOpCode.Ldstr" />
    <member name="F:System.Reflection.Metadata.ILOpCode.Ldtoken" />
    <member name="F:System.Reflection.Metadata.ILOpCode.Ldvirtftn" />
    <member name="F:System.Reflection.Metadata.ILOpCode.Leave" />
    <member name="F:System.Reflection.Metadata.ILOpCode.Leave_s" />
    <member name="F:System.Reflection.Metadata.ILOpCode.Localloc" />
    <member name="F:System.Reflection.Metadata.ILOpCode.Mkrefany" />
    <member name="F:System.Reflection.Metadata.ILOpCode.Mul" />
    <member name="F:System.Reflection.Metadata.ILOpCode.Mul_ovf" />
    <member name="F:System.Reflection.Metadata.ILOpCode.Mul_ovf_un" />
    <member name="F:System.Reflection.Metadata.ILOpCode.Neg" />
    <member name="F:System.Reflection.Metadata.ILOpCode.Newarr" />
    <member name="F:System.Reflection.Metadata.ILOpCode.Newobj" />
    <member name="F:System.Reflection.Metadata.ILOpCode.Nop" />
    <member name="F:System.Reflection.Metadata.ILOpCode.Not" />
    <member name="F:System.Reflection.Metadata.ILOpCode.Or" />
    <member name="F:System.Reflection.Metadata.ILOpCode.Pop" />
    <member name="F:System.Reflection.Metadata.ILOpCode.Readonly" />
    <member name="F:System.Reflection.Metadata.ILOpCode.Refanytype" />
    <member name="F:System.Reflection.Metadata.ILOpCode.Refanyval" />
    <member name="F:System.Reflection.Metadata.ILOpCode.Rem" />
    <member name="F:System.Reflection.Metadata.ILOpCode.Rem_un" />
    <member name="F:System.Reflection.Metadata.ILOpCode.Ret" />
    <member name="F:System.Reflection.Metadata.ILOpCode.Rethrow" />
    <member name="F:System.Reflection.Metadata.ILOpCode.Shl" />
    <member name="F:System.Reflection.Metadata.ILOpCode.Shr" />
    <member name="F:System.Reflection.Metadata.ILOpCode.Shr_un" />
    <member name="F:System.Reflection.Metadata.ILOpCode.Sizeof" />
    <member name="F:System.Reflection.Metadata.ILOpCode.Starg" />
    <member name="F:System.Reflection.Metadata.ILOpCode.Starg_s" />
    <member name="F:System.Reflection.Metadata.ILOpCode.Stelem" />
    <member name="F:System.Reflection.Metadata.ILOpCode.Stelem_i" />
    <member name="F:System.Reflection.Metadata.ILOpCode.Stelem_i1" />
    <member name="F:System.Reflection.Metadata.ILOpCode.Stelem_i2" />
    <member name="F:System.Reflection.Metadata.ILOpCode.Stelem_i4" />
    <member name="F:System.Reflection.Metadata.ILOpCode.Stelem_i8" />
    <member name="F:System.Reflection.Metadata.ILOpCode.Stelem_r4" />
    <member name="F:System.Reflection.Metadata.ILOpCode.Stelem_r8" />
    <member name="F:System.Reflection.Metadata.ILOpCode.Stelem_ref" />
    <member name="F:System.Reflection.Metadata.ILOpCode.Stfld" />
    <member name="F:System.Reflection.Metadata.ILOpCode.Stind_i" />
    <member name="F:System.Reflection.Metadata.ILOpCode.Stind_i1" />
    <member name="F:System.Reflection.Metadata.ILOpCode.Stind_i2" />
    <member name="F:System.Reflection.Metadata.ILOpCode.Stind_i4" />
    <member name="F:System.Reflection.Metadata.ILOpCode.Stind_i8" />
    <member name="F:System.Reflection.Metadata.ILOpCode.Stind_r4" />
    <member name="F:System.Reflection.Metadata.ILOpCode.Stind_r8" />
    <member name="F:System.Reflection.Metadata.ILOpCode.Stind_ref" />
    <member name="F:System.Reflection.Metadata.ILOpCode.Stloc" />
    <member name="F:System.Reflection.Metadata.ILOpCode.Stloc_0" />
    <member name="F:System.Reflection.Metadata.ILOpCode.Stloc_1" />
    <member name="F:System.Reflection.Metadata.ILOpCode.Stloc_2" />
    <member name="F:System.Reflection.Metadata.ILOpCode.Stloc_3" />
    <member name="F:System.Reflection.Metadata.ILOpCode.Stloc_s" />
    <member name="F:System.Reflection.Metadata.ILOpCode.Stobj" />
    <member name="F:System.Reflection.Metadata.ILOpCode.Stsfld" />
    <member name="F:System.Reflection.Metadata.ILOpCode.Sub" />
    <member name="F:System.Reflection.Metadata.ILOpCode.Sub_ovf" />
    <member name="F:System.Reflection.Metadata.ILOpCode.Sub_ovf_un" />
    <member name="F:System.Reflection.Metadata.ILOpCode.Switch" />
    <member name="F:System.Reflection.Metadata.ILOpCode.Tail" />
    <member name="F:System.Reflection.Metadata.ILOpCode.Throw" />
    <member name="F:System.Reflection.Metadata.ILOpCode.Unaligned" />
    <member name="F:System.Reflection.Metadata.ILOpCode.Unbox" />
    <member name="F:System.Reflection.Metadata.ILOpCode.Unbox_any" />
    <member name="F:System.Reflection.Metadata.ILOpCode.Volatile" />
    <member name="F:System.Reflection.Metadata.ILOpCode.Xor" />
    <member name="T:System.Reflection.Metadata.ILOpCodeExtensions" />
    <member name="M:System.Reflection.Metadata.ILOpCodeExtensions.GetBranchOperandSize(System.Reflection.Metadata.ILOpCode)">
      <summary>Calculates the size of the specified branch instruction operand.</summary>
      <param name="opCode">The branch op-code.</param>
      <returns>1 if <paramref name="opCode" /> is a short branch, or 4 if it is a long branch.</returns>
      <exception cref="T:System.ArgumentException">The specified <paramref name="opCode" /> is not a branch op-code.</exception>
    </member>
    <member name="M:System.Reflection.Metadata.ILOpCodeExtensions.GetLongBranch(System.Reflection.Metadata.ILOpCode)">
      <summary>Gets a long form of the specified branch op-code.</summary>
      <param name="opCode">The branch op-code.</param>
      <returns>The long form of the branch op-code.</returns>
      <exception cref="T:System.ArgumentException">The specified <paramref name="opCode" /> is not a branch op-code.</exception>
    </member>
    <member name="M:System.Reflection.Metadata.ILOpCodeExtensions.GetShortBranch(System.Reflection.Metadata.ILOpCode)">
      <summary>Gets a short form of the specified branch op-code.</summary>
      <param name="opCode">The branch op-code.</param>
      <returns>The short form of the branch op-code.</returns>
      <exception cref="T:System.ArgumentException">The specified <paramref name="opCode" /> is not a branch op-code.</exception>
    </member>
    <member name="M:System.Reflection.Metadata.ILOpCodeExtensions.IsBranch(System.Reflection.Metadata.ILOpCode)">
      <summary>Verifies if the specified op-code is a branch to a label.</summary>
      <param name="opCode" />
      <returns>
        <see langword="true" /> if the specified op-code is a branch to a label, <see langword="false" /> otherwise.</returns>
    </member>
    <member name="T:System.Reflection.Metadata.ImageFormatLimitationException">
      <summary>The exception that is thrown when an attempt to write metadata exceeds a limit given by the format specification. For example, when the heap size limit is exceeded.</summary>
    </member>
    <member name="M:System.Reflection.Metadata.ImageFormatLimitationException.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Reflection.Metadata.ImageFormatLimitationException" /> class.</summary>
    </member>
    <member name="M:System.Reflection.Metadata.ImageFormatLimitationException.#ctor(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
      <summary>Initializes a new instance of the <see cref="T:System.Reflection.Metadata.ImageFormatLimitationException" /> class with serialized data.</summary>
      <param name="info">The object that holds the serialized object data.</param>
      <param name="context">The contextual information about the source or destination.</param>
    </member>
    <member name="M:System.Reflection.Metadata.ImageFormatLimitationException.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Reflection.Metadata.ImageFormatLimitationException" /> class with a specified error message.</summary>
      <param name="message">The error message that explains the reason for this exception.</param>
    </member>
    <member name="M:System.Reflection.Metadata.ImageFormatLimitationException.#ctor(System.String,System.Exception)">
      <summary>Initializes a new instance of the <see cref="T:System.Reflection.Metadata.ImageFormatLimitationException" /> class with a specified error message and the exception that is the cause of this exception.</summary>
      <param name="message">The error message that explains the reason for this exception.</param>
      <param name="innerException">The exception that is the cause of the current exception, or <see langword="null" /> if no inner exception is specified.</param>
    </member>
    <member name="T:System.Reflection.Metadata.ImportDefinition" />
    <member name="P:System.Reflection.Metadata.ImportDefinition.Alias" />
    <member name="P:System.Reflection.Metadata.ImportDefinition.Kind" />
    <member name="P:System.Reflection.Metadata.ImportDefinition.TargetAssembly" />
    <member name="P:System.Reflection.Metadata.ImportDefinition.TargetNamespace" />
    <member name="P:System.Reflection.Metadata.ImportDefinition.TargetType" />
    <member name="T:System.Reflection.Metadata.ImportDefinitionCollection" />
    <member name="T:System.Reflection.Metadata.ImportDefinitionCollection.Enumerator" />
    <member name="P:System.Reflection.Metadata.ImportDefinitionCollection.Enumerator.Current" />
    <member name="M:System.Reflection.Metadata.ImportDefinitionCollection.Enumerator.MoveNext">
      <exception cref="T:System.BadImageFormatException">Invalid blob format.</exception>
    </member>
    <member name="M:System.Reflection.Metadata.ImportDefinitionCollection.Enumerator.Reset" />
    <member name="P:System.Reflection.Metadata.ImportDefinitionCollection.Enumerator.System#Collections#IEnumerator#Current" />
    <member name="M:System.Reflection.Metadata.ImportDefinitionCollection.Enumerator.System#IDisposable#Dispose" />
    <member name="M:System.Reflection.Metadata.ImportDefinitionCollection.GetEnumerator" />
    <member name="M:System.Reflection.Metadata.ImportDefinitionCollection.System#Collections#Generic#IEnumerable{System#Reflection#Metadata#ImportDefinition}#GetEnumerator" />
    <member name="M:System.Reflection.Metadata.ImportDefinitionCollection.System#Collections#IEnumerable#GetEnumerator" />
    <member name="T:System.Reflection.Metadata.ImportDefinitionKind" />
    <member name="F:System.Reflection.Metadata.ImportDefinitionKind.AliasAssemblyNamespace" />
    <member name="F:System.Reflection.Metadata.ImportDefinitionKind.AliasAssemblyReference" />
    <member name="F:System.Reflection.Metadata.ImportDefinitionKind.AliasNamespace" />
    <member name="F:System.Reflection.Metadata.ImportDefinitionKind.AliasType" />
    <member name="F:System.Reflection.Metadata.ImportDefinitionKind.ImportAssemblyNamespace" />
    <member name="F:System.Reflection.Metadata.ImportDefinitionKind.ImportAssemblyReferenceAlias" />
    <member name="F:System.Reflection.Metadata.ImportDefinitionKind.ImportNamespace" />
    <member name="F:System.Reflection.Metadata.ImportDefinitionKind.ImportType" />
    <member name="F:System.Reflection.Metadata.ImportDefinitionKind.ImportXmlNamespace" />
    <member name="T:System.Reflection.Metadata.ImportScope">
      <summary>Provides information about the lexical scope within which a group of imports are available. This information is stored in debug metadata.</summary>
    </member>
    <member name="M:System.Reflection.Metadata.ImportScope.GetImports" />
    <member name="P:System.Reflection.Metadata.ImportScope.ImportsBlob" />
    <member name="P:System.Reflection.Metadata.ImportScope.Parent" />
    <member name="T:System.Reflection.Metadata.ImportScopeCollection" />
    <member name="P:System.Reflection.Metadata.ImportScopeCollection.Count" />
    <member name="T:System.Reflection.Metadata.ImportScopeCollection.Enumerator" />
    <member name="P:System.Reflection.Metadata.ImportScopeCollection.Enumerator.Current" />
    <member name="M:System.Reflection.Metadata.ImportScopeCollection.Enumerator.MoveNext" />
    <member name="P:System.Reflection.Metadata.ImportScopeCollection.Enumerator.System#Collections#IEnumerator#Current" />
    <member name="M:System.Reflection.Metadata.ImportScopeCollection.Enumerator.System#Collections#IEnumerator#Reset" />
    <member name="M:System.Reflection.Metadata.ImportScopeCollection.Enumerator.System#IDisposable#Dispose" />
    <member name="M:System.Reflection.Metadata.ImportScopeCollection.GetEnumerator" />
    <member name="M:System.Reflection.Metadata.ImportScopeCollection.System#Collections#Generic#IEnumerable{System#Reflection#Metadata#ImportScopeHandle}#GetEnumerator" />
    <member name="M:System.Reflection.Metadata.ImportScopeCollection.System#Collections#IEnumerable#GetEnumerator" />
    <member name="T:System.Reflection.Metadata.ImportScopeHandle" />
    <member name="M:System.Reflection.Metadata.ImportScopeHandle.Equals(System.Object)">
      <param name="obj" />
    </member>
    <member name="M:System.Reflection.Metadata.ImportScopeHandle.Equals(System.Reflection.Metadata.ImportScopeHandle)">
      <param name="other" />
    </member>
    <member name="M:System.Reflection.Metadata.ImportScopeHandle.GetHashCode" />
    <member name="P:System.Reflection.Metadata.ImportScopeHandle.IsNil" />
    <member name="M:System.Reflection.Metadata.ImportScopeHandle.op_Equality(System.Reflection.Metadata.ImportScopeHandle,System.Reflection.Metadata.ImportScopeHandle)">
      <param name="left" />
      <param name="right" />
    </member>
    <member name="M:System.Reflection.Metadata.ImportScopeHandle.op_Explicit(System.Reflection.Metadata.EntityHandle)~System.Reflection.Metadata.ImportScopeHandle">
      <param name="handle" />
    </member>
    <member name="M:System.Reflection.Metadata.ImportScopeHandle.op_Explicit(System.Reflection.Metadata.Handle)~System.Reflection.Metadata.ImportScopeHandle">
      <param name="handle" />
    </member>
    <member name="M:System.Reflection.Metadata.ImportScopeHandle.op_Implicit(System.Reflection.Metadata.ImportScopeHandle)~System.Reflection.Metadata.EntityHandle">
      <param name="handle" />
    </member>
    <member name="M:System.Reflection.Metadata.ImportScopeHandle.op_Implicit(System.Reflection.Metadata.ImportScopeHandle)~System.Reflection.Metadata.Handle">
      <param name="handle" />
    </member>
    <member name="M:System.Reflection.Metadata.ImportScopeHandle.op_Inequality(System.Reflection.Metadata.ImportScopeHandle,System.Reflection.Metadata.ImportScopeHandle)">
      <param name="left" />
      <param name="right" />
    </member>
    <member name="T:System.Reflection.Metadata.InterfaceImplementation" />
    <member name="M:System.Reflection.Metadata.InterfaceImplementation.GetCustomAttributes" />
    <member name="P:System.Reflection.Metadata.InterfaceImplementation.Interface">
      <summary>Gets the interface that is implemented (<see cref="T:System.Reflection.Metadata.TypeDefinitionHandle" />, <see cref="T:System.Reflection.Metadata.TypeReferenceHandle" />, or <see cref="T:System.Reflection.Metadata.TypeSpecificationHandle" />).</summary>
    </member>
    <member name="T:System.Reflection.Metadata.InterfaceImplementationHandle" />
    <member name="M:System.Reflection.Metadata.InterfaceImplementationHandle.Equals(System.Object)">
      <param name="obj" />
    </member>
    <member name="M:System.Reflection.Metadata.InterfaceImplementationHandle.Equals(System.Reflection.Metadata.InterfaceImplementationHandle)">
      <param name="other" />
    </member>
    <member name="M:System.Reflection.Metadata.InterfaceImplementationHandle.GetHashCode" />
    <member name="P:System.Reflection.Metadata.InterfaceImplementationHandle.IsNil" />
    <member name="M:System.Reflection.Metadata.InterfaceImplementationHandle.op_Equality(System.Reflection.Metadata.InterfaceImplementationHandle,System.Reflection.Metadata.InterfaceImplementationHandle)">
      <param name="left" />
      <param name="right" />
    </member>
    <member name="M:System.Reflection.Metadata.InterfaceImplementationHandle.op_Explicit(System.Reflection.Metadata.EntityHandle)~System.Reflection.Metadata.InterfaceImplementationHandle">
      <param name="handle" />
    </member>
    <member name="M:System.Reflection.Metadata.InterfaceImplementationHandle.op_Explicit(System.Reflection.Metadata.Handle)~System.Reflection.Metadata.InterfaceImplementationHandle">
      <param name="handle" />
    </member>
    <member name="M:System.Reflection.Metadata.InterfaceImplementationHandle.op_Implicit(System.Reflection.Metadata.InterfaceImplementationHandle)~System.Reflection.Metadata.EntityHandle">
      <param name="handle" />
    </member>
    <member name="M:System.Reflection.Metadata.InterfaceImplementationHandle.op_Implicit(System.Reflection.Metadata.InterfaceImplementationHandle)~System.Reflection.Metadata.Handle">
      <param name="handle" />
    </member>
    <member name="M:System.Reflection.Metadata.InterfaceImplementationHandle.op_Inequality(System.Reflection.Metadata.InterfaceImplementationHandle,System.Reflection.Metadata.InterfaceImplementationHandle)">
      <param name="left" />
      <param name="right" />
    </member>
    <member name="T:System.Reflection.Metadata.InterfaceImplementationHandleCollection" />
    <member name="P:System.Reflection.Metadata.InterfaceImplementationHandleCollection.Count" />
    <member name="T:System.Reflection.Metadata.InterfaceImplementationHandleCollection.Enumerator" />
    <member name="P:System.Reflection.Metadata.InterfaceImplementationHandleCollection.Enumerator.Current" />
    <member name="M:System.Reflection.Metadata.InterfaceImplementationHandleCollection.Enumerator.MoveNext" />
    <member name="P:System.Reflection.Metadata.InterfaceImplementationHandleCollection.Enumerator.System#Collections#IEnumerator#Current" />
    <member name="M:System.Reflection.Metadata.InterfaceImplementationHandleCollection.Enumerator.System#Collections#IEnumerator#Reset" />
    <member name="M:System.Reflection.Metadata.InterfaceImplementationHandleCollection.Enumerator.System#IDisposable#Dispose" />
    <member name="M:System.Reflection.Metadata.InterfaceImplementationHandleCollection.GetEnumerator" />
    <member name="M:System.Reflection.Metadata.InterfaceImplementationHandleCollection.System#Collections#Generic#IEnumerable{System#Reflection#Metadata#InterfaceImplementationHandle}#GetEnumerator" />
    <member name="M:System.Reflection.Metadata.InterfaceImplementationHandleCollection.System#Collections#IEnumerable#GetEnumerator" />
    <member name="T:System.Reflection.Metadata.ISignatureTypeProvider`2">
      <typeparam name="TType" />
      <typeparam name="TGenericContext" />
    </member>
    <member name="M:System.Reflection.Metadata.ISignatureTypeProvider`2.GetFunctionPointerType(System.Reflection.Metadata.MethodSignature{`0})">
      <summary>Gets the type symbol for the function pointer type of the given method <paramref name="signature" />.</summary>
      <param name="signature" />
      <returns>The type symbol for the function pointer type.</returns>
    </member>
    <member name="M:System.Reflection.Metadata.ISignatureTypeProvider`2.GetGenericMethodParameter(`1,System.Int32)">
      <summary>Gets the type symbol for the generic method parameter at the given zero-based <paramref name="index" />.</summary>
      <param name="genericContext" />
      <param name="index" />
      <returns>The type symbol for the generic method parameter at <paramref name="index" />.</returns>
    </member>
    <member name="M:System.Reflection.Metadata.ISignatureTypeProvider`2.GetGenericTypeParameter(`1,System.Int32)">
      <summary>Gets the type symbol for the generic type parameter at the given zero-based <paramref name="index" />.</summary>
      <param name="genericContext" />
      <param name="index" />
      <returns>The type symbol for the generic type parameter at the given zero-based <paramref name="index" />.</returns>
    </member>
    <member name="M:System.Reflection.Metadata.ISignatureTypeProvider`2.GetModifiedType(`0,`0,System.Boolean)">
      <summary>Gets the type symbol for a type with a custom modifier applied.</summary>
      <param name="modifier">The modifier type applied.</param>
      <param name="unmodifiedType">The type symbol of the underlying type without modifiers applied.</param>
      <param name="isRequired">
        <see langword="true" /> if the modifier is required, <see langword="false" /> if it's optional.</param>
      <returns>The type symbol.</returns>
    </member>
    <member name="M:System.Reflection.Metadata.ISignatureTypeProvider`2.GetPinnedType(`0)">
      <summary>Gets the type symbol for a local variable type that is marked as pinned.</summary>
      <param name="elementType" />
      <returns>The type symbol for the local variable type.</returns>
    </member>
    <member name="M:System.Reflection.Metadata.ISignatureTypeProvider`2.GetTypeFromSpecification(System.Reflection.Metadata.MetadataReader,`1,System.Reflection.Metadata.TypeSpecificationHandle,System.Byte)">
      <summary>Gets the type symbol for a type specification.</summary>
      <param name="reader">The metadata reader that was passed to the signature decoder. It may be <see langword="null" />.</param>
      <param name="genericContext">The context that was passed to the signature decoder.</param>
      <param name="handle">The type specification handle.</param>
      <param name="rawTypeKind">The kind of the type, as specified in the signature. To interpret this value, use <see cref="M:System.Reflection.Metadata.Ecma335.MetadataReaderExtensions.ResolveSignatureTypeKind(System.Reflection.Metadata.MetadataReader,System.Reflection.Metadata.EntityHandle,System.Byte)" />.</param>
      <returns>The type symbol for the type specification.</returns>
    </member>
    <member name="T:System.Reflection.Metadata.ISimpleTypeProvider`1">
      <typeparam name="TType" />
    </member>
    <member name="M:System.Reflection.Metadata.ISimpleTypeProvider`1.GetPrimitiveType(System.Reflection.Metadata.PrimitiveTypeCode)">
      <summary>Gets the type symbol for a primitive type.</summary>
      <param name="typeCode" />
      <returns>The type symbol for <param name="typeCode" />.</returns>
    </member>
    <member name="M:System.Reflection.Metadata.ISimpleTypeProvider`1.GetTypeFromDefinition(System.Reflection.Metadata.MetadataReader,System.Reflection.Metadata.TypeDefinitionHandle,System.Byte)">
      <summary>Gets the type symbol for a type definition.</summary>
      <param name="reader">The metadata reader that was passed to the signature decoder. It may be <see langword="null" />.</param>
      <param name="handle">The type definition handle.</param>
      <param name="rawTypeKind">The kind of the type, as specified in the signature. To interpret this value use <see cref="M:System.Reflection.Metadata.Ecma335.MetadataReaderExtensions.ResolveSignatureTypeKind(System.Reflection.Metadata.MetadataReader,System.Reflection.Metadata.EntityHandle,System.Byte)" />.</param>
      <returns>The type symbol.</returns>
    </member>
    <member name="M:System.Reflection.Metadata.ISimpleTypeProvider`1.GetTypeFromReference(System.Reflection.Metadata.MetadataReader,System.Reflection.Metadata.TypeReferenceHandle,System.Byte)">
      <summary>Gets the type symbol for a type reference.</summary>
      <param name="reader">The metadata reader that was passed to the signature decoder. It may be <see langword="null" />.</param>
      <param name="handle">The type definition handle.</param>
      <param name="rawTypeKind">The kind of the type as specified in the signature. To interpret this value, use <see cref="M:System.Reflection.Metadata.Ecma335.MetadataReaderExtensions.ResolveSignatureTypeKind(System.Reflection.Metadata.MetadataReader,System.Reflection.Metadata.EntityHandle,System.Byte)" />.</param>
      <returns>The type symbol.</returns>
    </member>
    <member name="T:System.Reflection.Metadata.ISZArrayTypeProvider`1">
      <typeparam name="TType" />
    </member>
    <member name="M:System.Reflection.Metadata.ISZArrayTypeProvider`1.GetSZArrayType(`0)">
      <summary>Gets the type symbol for a single-dimensional array of the given element type with a lower bounds of zero.</summary>
      <param name="elementType" />
      <returns>A <typeparamref name="TType" /> instance.</returns>
    </member>
    <member name="T:System.Reflection.Metadata.LocalConstant">
      <summary>Provides information about local constants. This information is stored in debug metadata.</summary>
    </member>
    <member name="P:System.Reflection.Metadata.LocalConstant.Name" />
    <member name="P:System.Reflection.Metadata.LocalConstant.Signature">
      <summary>Gets the constant signature.</summary>
      <returns>The constant signature.</returns>
    </member>
    <member name="T:System.Reflection.Metadata.LocalConstantHandle" />
    <member name="M:System.Reflection.Metadata.LocalConstantHandle.Equals(System.Object)">
      <param name="obj" />
    </member>
    <member name="M:System.Reflection.Metadata.LocalConstantHandle.Equals(System.Reflection.Metadata.LocalConstantHandle)">
      <param name="other" />
    </member>
    <member name="M:System.Reflection.Metadata.LocalConstantHandle.GetHashCode" />
    <member name="P:System.Reflection.Metadata.LocalConstantHandle.IsNil" />
    <member name="M:System.Reflection.Metadata.LocalConstantHandle.op_Equality(System.Reflection.Metadata.LocalConstantHandle,System.Reflection.Metadata.LocalConstantHandle)">
      <param name="left" />
      <param name="right" />
    </member>
    <member name="M:System.Reflection.Metadata.LocalConstantHandle.op_Explicit(System.Reflection.Metadata.EntityHandle)~System.Reflection.Metadata.LocalConstantHandle">
      <param name="handle" />
    </member>
    <member name="M:System.Reflection.Metadata.LocalConstantHandle.op_Explicit(System.Reflection.Metadata.Handle)~System.Reflection.Metadata.LocalConstantHandle">
      <param name="handle" />
    </member>
    <member name="M:System.Reflection.Metadata.LocalConstantHandle.op_Implicit(System.Reflection.Metadata.LocalConstantHandle)~System.Reflection.Metadata.EntityHandle">
      <param name="handle" />
    </member>
    <member name="M:System.Reflection.Metadata.LocalConstantHandle.op_Implicit(System.Reflection.Metadata.LocalConstantHandle)~System.Reflection.Metadata.Handle">
      <param name="handle" />
    </member>
    <member name="M:System.Reflection.Metadata.LocalConstantHandle.op_Inequality(System.Reflection.Metadata.LocalConstantHandle,System.Reflection.Metadata.LocalConstantHandle)">
      <param name="left" />
      <param name="right" />
    </member>
    <member name="T:System.Reflection.Metadata.LocalConstantHandleCollection" />
    <member name="P:System.Reflection.Metadata.LocalConstantHandleCollection.Count" />
    <member name="T:System.Reflection.Metadata.LocalConstantHandleCollection.Enumerator" />
    <member name="P:System.Reflection.Metadata.LocalConstantHandleCollection.Enumerator.Current" />
    <member name="M:System.Reflection.Metadata.LocalConstantHandleCollection.Enumerator.MoveNext" />
    <member name="P:System.Reflection.Metadata.LocalConstantHandleCollection.Enumerator.System#Collections#IEnumerator#Current" />
    <member name="M:System.Reflection.Metadata.LocalConstantHandleCollection.Enumerator.System#Collections#IEnumerator#Reset" />
    <member name="M:System.Reflection.Metadata.LocalConstantHandleCollection.Enumerator.System#IDisposable#Dispose" />
    <member name="M:System.Reflection.Metadata.LocalConstantHandleCollection.GetEnumerator" />
    <member name="M:System.Reflection.Metadata.LocalConstantHandleCollection.System#Collections#Generic#IEnumerable{System#Reflection#Metadata#LocalConstantHandle}#GetEnumerator" />
    <member name="M:System.Reflection.Metadata.LocalConstantHandleCollection.System#Collections#IEnumerable#GetEnumerator" />
    <member name="T:System.Reflection.Metadata.LocalScope">
      <summary>Provides information about the scope of local variables and constants. This information is stored in debug metadata.</summary>
    </member>
    <member name="P:System.Reflection.Metadata.LocalScope.EndOffset" />
    <member name="M:System.Reflection.Metadata.LocalScope.GetChildren" />
    <member name="M:System.Reflection.Metadata.LocalScope.GetLocalConstants" />
    <member name="M:System.Reflection.Metadata.LocalScope.GetLocalVariables" />
    <member name="P:System.Reflection.Metadata.LocalScope.ImportScope" />
    <member name="P:System.Reflection.Metadata.LocalScope.Length" />
    <member name="P:System.Reflection.Metadata.LocalScope.Method" />
    <member name="P:System.Reflection.Metadata.LocalScope.StartOffset" />
    <member name="T:System.Reflection.Metadata.LocalScopeHandle" />
    <member name="M:System.Reflection.Metadata.LocalScopeHandle.Equals(System.Object)">
      <param name="obj" />
    </member>
    <member name="M:System.Reflection.Metadata.LocalScopeHandle.Equals(System.Reflection.Metadata.LocalScopeHandle)">
      <param name="other" />
    </member>
    <member name="M:System.Reflection.Metadata.LocalScopeHandle.GetHashCode" />
    <member name="P:System.Reflection.Metadata.LocalScopeHandle.IsNil" />
    <member name="M:System.Reflection.Metadata.LocalScopeHandle.op_Equality(System.Reflection.Metadata.LocalScopeHandle,System.Reflection.Metadata.LocalScopeHandle)">
      <param name="left" />
      <param name="right" />
    </member>
    <member name="M:System.Reflection.Metadata.LocalScopeHandle.op_Explicit(System.Reflection.Metadata.EntityHandle)~System.Reflection.Metadata.LocalScopeHandle">
      <param name="handle" />
    </member>
    <member name="M:System.Reflection.Metadata.LocalScopeHandle.op_Explicit(System.Reflection.Metadata.Handle)~System.Reflection.Metadata.LocalScopeHandle">
      <param name="handle" />
    </member>
    <member name="M:System.Reflection.Metadata.LocalScopeHandle.op_Implicit(System.Reflection.Metadata.LocalScopeHandle)~System.Reflection.Metadata.EntityHandle">
      <param name="handle" />
    </member>
    <member name="M:System.Reflection.Metadata.LocalScopeHandle.op_Implicit(System.Reflection.Metadata.LocalScopeHandle)~System.Reflection.Metadata.Handle">
      <param name="handle" />
    </member>
    <member name="M:System.Reflection.Metadata.LocalScopeHandle.op_Inequality(System.Reflection.Metadata.LocalScopeHandle,System.Reflection.Metadata.LocalScopeHandle)">
      <param name="left" />
      <param name="right" />
    </member>
    <member name="T:System.Reflection.Metadata.LocalScopeHandleCollection" />
    <member name="T:System.Reflection.Metadata.LocalScopeHandleCollection.ChildrenEnumerator" />
    <member name="P:System.Reflection.Metadata.LocalScopeHandleCollection.ChildrenEnumerator.Current" />
    <member name="M:System.Reflection.Metadata.LocalScopeHandleCollection.ChildrenEnumerator.MoveNext" />
    <member name="P:System.Reflection.Metadata.LocalScopeHandleCollection.ChildrenEnumerator.System#Collections#IEnumerator#Current" />
    <member name="M:System.Reflection.Metadata.LocalScopeHandleCollection.ChildrenEnumerator.System#Collections#IEnumerator#Reset" />
    <member name="M:System.Reflection.Metadata.LocalScopeHandleCollection.ChildrenEnumerator.System#IDisposable#Dispose" />
    <member name="P:System.Reflection.Metadata.LocalScopeHandleCollection.Count" />
    <member name="T:System.Reflection.Metadata.LocalScopeHandleCollection.Enumerator" />
    <member name="P:System.Reflection.Metadata.LocalScopeHandleCollection.Enumerator.Current" />
    <member name="M:System.Reflection.Metadata.LocalScopeHandleCollection.Enumerator.MoveNext" />
    <member name="P:System.Reflection.Metadata.LocalScopeHandleCollection.Enumerator.System#Collections#IEnumerator#Current" />
    <member name="M:System.Reflection.Metadata.LocalScopeHandleCollection.Enumerator.System#Collections#IEnumerator#Reset" />
    <member name="M:System.Reflection.Metadata.LocalScopeHandleCollection.Enumerator.System#IDisposable#Dispose" />
    <member name="M:System.Reflection.Metadata.LocalScopeHandleCollection.GetEnumerator" />
    <member name="M:System.Reflection.Metadata.LocalScopeHandleCollection.System#Collections#Generic#IEnumerable{System#Reflection#Metadata#LocalScopeHandle}#GetEnumerator" />
    <member name="M:System.Reflection.Metadata.LocalScopeHandleCollection.System#Collections#IEnumerable#GetEnumerator" />
    <member name="T:System.Reflection.Metadata.LocalVariable">
      <summary>Provides information about local variables. This information is stored in debug metadata.</summary>
    </member>
    <member name="P:System.Reflection.Metadata.LocalVariable.Attributes" />
    <member name="P:System.Reflection.Metadata.LocalVariable.Index" />
    <member name="P:System.Reflection.Metadata.LocalVariable.Name" />
    <member name="T:System.Reflection.Metadata.LocalVariableAttributes" />
    <member name="F:System.Reflection.Metadata.LocalVariableAttributes.DebuggerHidden" />
    <member name="F:System.Reflection.Metadata.LocalVariableAttributes.None" />
    <member name="T:System.Reflection.Metadata.LocalVariableHandle" />
    <member name="M:System.Reflection.Metadata.LocalVariableHandle.Equals(System.Object)">
      <param name="obj" />
    </member>
    <member name="M:System.Reflection.Metadata.LocalVariableHandle.Equals(System.Reflection.Metadata.LocalVariableHandle)">
      <param name="other" />
    </member>
    <member name="M:System.Reflection.Metadata.LocalVariableHandle.GetHashCode" />
    <member name="P:System.Reflection.Metadata.LocalVariableHandle.IsNil" />
    <member name="M:System.Reflection.Metadata.LocalVariableHandle.op_Equality(System.Reflection.Metadata.LocalVariableHandle,System.Reflection.Metadata.LocalVariableHandle)">
      <param name="left" />
      <param name="right" />
    </member>
    <member name="M:System.Reflection.Metadata.LocalVariableHandle.op_Explicit(System.Reflection.Metadata.EntityHandle)~System.Reflection.Metadata.LocalVariableHandle">
      <param name="handle" />
    </member>
    <member name="M:System.Reflection.Metadata.LocalVariableHandle.op_Explicit(System.Reflection.Metadata.Handle)~System.Reflection.Metadata.LocalVariableHandle">
      <param name="handle" />
    </member>
    <member name="M:System.Reflection.Metadata.LocalVariableHandle.op_Implicit(System.Reflection.Metadata.LocalVariableHandle)~System.Reflection.Metadata.EntityHandle">
      <param name="handle" />
    </member>
    <member name="M:System.Reflection.Metadata.LocalVariableHandle.op_Implicit(System.Reflection.Metadata.LocalVariableHandle)~System.Reflection.Metadata.Handle">
      <param name="handle" />
    </member>
    <member name="M:System.Reflection.Metadata.LocalVariableHandle.op_Inequality(System.Reflection.Metadata.LocalVariableHandle,System.Reflection.Metadata.LocalVariableHandle)">
      <param name="left" />
      <param name="right" />
    </member>
    <member name="T:System.Reflection.Metadata.LocalVariableHandleCollection" />
    <member name="P:System.Reflection.Metadata.LocalVariableHandleCollection.Count" />
    <member name="T:System.Reflection.Metadata.LocalVariableHandleCollection.Enumerator" />
    <member name="P:System.Reflection.Metadata.LocalVariableHandleCollection.Enumerator.Current" />
    <member name="M:System.Reflection.Metadata.LocalVariableHandleCollection.Enumerator.MoveNext" />
    <member name="P:System.Reflection.Metadata.LocalVariableHandleCollection.Enumerator.System#Collections#IEnumerator#Current" />
    <member name="M:System.Reflection.Metadata.LocalVariableHandleCollection.Enumerator.System#Collections#IEnumerator#Reset" />
    <member name="M:System.Reflection.Metadata.LocalVariableHandleCollection.Enumerator.System#IDisposable#Dispose" />
    <member name="M:System.Reflection.Metadata.LocalVariableHandleCollection.GetEnumerator" />
    <member name="M:System.Reflection.Metadata.LocalVariableHandleCollection.System#Collections#Generic#IEnumerable{System#Reflection#Metadata#LocalVariableHandle}#GetEnumerator" />
    <member name="M:System.Reflection.Metadata.LocalVariableHandleCollection.System#Collections#IEnumerable#GetEnumerator" />
    <member name="T:System.Reflection.Metadata.ManifestResource" />
    <member name="P:System.Reflection.Metadata.ManifestResource.Attributes">
      <summary>Gets the manifest resource attributes.</summary>
      <returns>A bitwise combination of the enumeration values that specify the manifest resource attributes.</returns>
    </member>
    <member name="M:System.Reflection.Metadata.ManifestResource.GetCustomAttributes" />
    <member name="P:System.Reflection.Metadata.ManifestResource.Implementation">
      <summary>Gets the implementation entity handle.</summary>
      <returns>An EntityHandle instance. If the <see cref="P:System.Reflection.Metadata.EntityHandle.IsNil" /> property is <see langword="true" />, the returned handle will have default values.</returns>
    </member>
    <member name="P:System.Reflection.Metadata.ManifestResource.Name">
      <summary>Gets the resource name.</summary>
      <returns>The resource name.</returns>
    </member>
    <member name="P:System.Reflection.Metadata.ManifestResource.Offset">
      <summary>Gets the byte offset within the referenced file at which this resource record begins.</summary>
      <returns>The byte offset within the referenced file at which this resource record begins.</returns>
    </member>
    <member name="T:System.Reflection.Metadata.ManifestResourceHandle" />
    <member name="M:System.Reflection.Metadata.ManifestResourceHandle.Equals(System.Object)">
      <param name="obj" />
    </member>
    <member name="M:System.Reflection.Metadata.ManifestResourceHandle.Equals(System.Reflection.Metadata.ManifestResourceHandle)">
      <param name="other" />
    </member>
    <member name="M:System.Reflection.Metadata.ManifestResourceHandle.GetHashCode" />
    <member name="P:System.Reflection.Metadata.ManifestResourceHandle.IsNil" />
    <member name="M:System.Reflection.Metadata.ManifestResourceHandle.op_Equality(System.Reflection.Metadata.ManifestResourceHandle,System.Reflection.Metadata.ManifestResourceHandle)">
      <param name="left" />
      <param name="right" />
    </member>
    <member name="M:System.Reflection.Metadata.ManifestResourceHandle.op_Explicit(System.Reflection.Metadata.EntityHandle)~System.Reflection.Metadata.ManifestResourceHandle">
      <param name="handle" />
    </member>
    <member name="M:System.Reflection.Metadata.ManifestResourceHandle.op_Explicit(System.Reflection.Metadata.Handle)~System.Reflection.Metadata.ManifestResourceHandle">
      <param name="handle" />
    </member>
    <member name="M:System.Reflection.Metadata.ManifestResourceHandle.op_Implicit(System.Reflection.Metadata.ManifestResourceHandle)~System.Reflection.Metadata.EntityHandle">
      <param name="handle" />
    </member>
    <member name="M:System.Reflection.Metadata.ManifestResourceHandle.op_Implicit(System.Reflection.Metadata.ManifestResourceHandle)~System.Reflection.Metadata.Handle">
      <param name="handle" />
    </member>
    <member name="M:System.Reflection.Metadata.ManifestResourceHandle.op_Inequality(System.Reflection.Metadata.ManifestResourceHandle,System.Reflection.Metadata.ManifestResourceHandle)">
      <param name="left" />
      <param name="right" />
    </member>
    <member name="T:System.Reflection.Metadata.ManifestResourceHandleCollection">
      <summary>Represents a collection of <see cref="T:System.Reflection.Metadata.ManifestResourceHandle" /> instances.</summary>
    </member>
    <member name="P:System.Reflection.Metadata.ManifestResourceHandleCollection.Count" />
    <member name="T:System.Reflection.Metadata.ManifestResourceHandleCollection.Enumerator" />
    <member name="P:System.Reflection.Metadata.ManifestResourceHandleCollection.Enumerator.Current" />
    <member name="M:System.Reflection.Metadata.ManifestResourceHandleCollection.Enumerator.MoveNext" />
    <member name="P:System.Reflection.Metadata.ManifestResourceHandleCollection.Enumerator.System#Collections#IEnumerator#Current" />
    <member name="M:System.Reflection.Metadata.ManifestResourceHandleCollection.Enumerator.System#Collections#IEnumerator#Reset" />
    <member name="M:System.Reflection.Metadata.ManifestResourceHandleCollection.Enumerator.System#IDisposable#Dispose" />
    <member name="M:System.Reflection.Metadata.ManifestResourceHandleCollection.GetEnumerator" />
    <member name="M:System.Reflection.Metadata.ManifestResourceHandleCollection.System#Collections#Generic#IEnumerable{System#Reflection#Metadata#ManifestResourceHandle}#GetEnumerator" />
    <member name="M:System.Reflection.Metadata.ManifestResourceHandleCollection.System#Collections#IEnumerable#GetEnumerator" />
    <member name="T:System.Reflection.Metadata.MemberReference" />
    <member name="M:System.Reflection.Metadata.MemberReference.DecodeFieldSignature``2(System.Reflection.Metadata.ISignatureTypeProvider{``0,``1},``1)">
      <param name="provider" />
      <param name="genericContext" />
      <typeparam name="TType" />
      <typeparam name="TGenericContext" />
    </member>
    <member name="M:System.Reflection.Metadata.MemberReference.DecodeMethodSignature``2(System.Reflection.Metadata.ISignatureTypeProvider{``0,``1},``1)">
      <param name="provider" />
      <param name="genericContext" />
      <typeparam name="TType" />
      <typeparam name="TGenericContext" />
    </member>
    <member name="M:System.Reflection.Metadata.MemberReference.GetCustomAttributes" />
    <member name="M:System.Reflection.Metadata.MemberReference.GetKind">
      <summary>Determines if the member reference is to a method or field.</summary>
      <returns>One of the enumeration values that indicates the kind of member reference.</returns>
      <exception cref="T:System.BadImageFormatException">The member reference signature is invalid.</exception>
    </member>
    <member name="P:System.Reflection.Metadata.MemberReference.Name" />
    <member name="P:System.Reflection.Metadata.MemberReference.Parent">
      <summary>Gets the parent entity handle.</summary>
      <returns>An entity handle instance. If the <see cref="P:System.Reflection.Metadata.EntityHandle.IsNil" /> property is <see langword="true" />, the returned handle will have default values.</returns>
    </member>
    <member name="P:System.Reflection.Metadata.MemberReference.Signature">
      <summary>Gets a handle to the signature blob.</summary>
      <returns>A handle to the signature blob.</returns>
    </member>
    <member name="T:System.Reflection.Metadata.MemberReferenceHandle" />
    <member name="M:System.Reflection.Metadata.MemberReferenceHandle.Equals(System.Object)">
      <param name="obj" />
    </member>
    <member name="M:System.Reflection.Metadata.MemberReferenceHandle.Equals(System.Reflection.Metadata.MemberReferenceHandle)">
      <param name="other" />
    </member>
    <member name="M:System.Reflection.Metadata.MemberReferenceHandle.GetHashCode" />
    <member name="P:System.Reflection.Metadata.MemberReferenceHandle.IsNil" />
    <member name="M:System.Reflection.Metadata.MemberReferenceHandle.op_Equality(System.Reflection.Metadata.MemberReferenceHandle,System.Reflection.Metadata.MemberReferenceHandle)">
      <param name="left" />
      <param name="right" />
    </member>
    <member name="M:System.Reflection.Metadata.MemberReferenceHandle.op_Explicit(System.Reflection.Metadata.EntityHandle)~System.Reflection.Metadata.MemberReferenceHandle">
      <param name="handle" />
    </member>
    <member name="M:System.Reflection.Metadata.MemberReferenceHandle.op_Explicit(System.Reflection.Metadata.Handle)~System.Reflection.Metadata.MemberReferenceHandle">
      <param name="handle" />
    </member>
    <member name="M:System.Reflection.Metadata.MemberReferenceHandle.op_Implicit(System.Reflection.Metadata.MemberReferenceHandle)~System.Reflection.Metadata.EntityHandle">
      <param name="handle" />
    </member>
    <member name="M:System.Reflection.Metadata.MemberReferenceHandle.op_Implicit(System.Reflection.Metadata.MemberReferenceHandle)~System.Reflection.Metadata.Handle">
      <param name="handle" />
    </member>
    <member name="M:System.Reflection.Metadata.MemberReferenceHandle.op_Inequality(System.Reflection.Metadata.MemberReferenceHandle,System.Reflection.Metadata.MemberReferenceHandle)">
      <param name="left" />
      <param name="right" />
    </member>
    <member name="T:System.Reflection.Metadata.MemberReferenceHandleCollection">
      <summary>Represents a collection of <see cref="T:System.Reflection.Metadata.MemberReferenceHandle" /> instances.</summary>
    </member>
    <member name="P:System.Reflection.Metadata.MemberReferenceHandleCollection.Count" />
    <member name="T:System.Reflection.Metadata.MemberReferenceHandleCollection.Enumerator" />
    <member name="P:System.Reflection.Metadata.MemberReferenceHandleCollection.Enumerator.Current" />
    <member name="M:System.Reflection.Metadata.MemberReferenceHandleCollection.Enumerator.MoveNext" />
    <member name="P:System.Reflection.Metadata.MemberReferenceHandleCollection.Enumerator.System#Collections#IEnumerator#Current" />
    <member name="M:System.Reflection.Metadata.MemberReferenceHandleCollection.Enumerator.System#Collections#IEnumerator#Reset" />
    <member name="M:System.Reflection.Metadata.MemberReferenceHandleCollection.Enumerator.System#IDisposable#Dispose" />
    <member name="M:System.Reflection.Metadata.MemberReferenceHandleCollection.GetEnumerator" />
    <member name="M:System.Reflection.Metadata.MemberReferenceHandleCollection.System#Collections#Generic#IEnumerable{System#Reflection#Metadata#MemberReferenceHandle}#GetEnumerator" />
    <member name="M:System.Reflection.Metadata.MemberReferenceHandleCollection.System#Collections#IEnumerable#GetEnumerator" />
    <member name="T:System.Reflection.Metadata.MemberReferenceKind">
      <summary>Specifies constants that indicate whether a <see cref="T:System.Reflection.Metadata.MemberReference" /> references a method or field.</summary>
    </member>
    <member name="F:System.Reflection.Metadata.MemberReferenceKind.Field">
      <summary>The <see cref="T:System.Reflection.Metadata.MemberReference" /> references a field.</summary>
    </member>
    <member name="F:System.Reflection.Metadata.MemberReferenceKind.Method">
      <summary>The <see cref="T:System.Reflection.Metadata.MemberReference" /> references a method.</summary>
    </member>
    <member name="T:System.Reflection.Metadata.MetadataKind" />
    <member name="F:System.Reflection.Metadata.MetadataKind.Ecma335">
      <summary>CLI metadata.</summary>
    </member>
    <member name="F:System.Reflection.Metadata.MetadataKind.ManagedWindowsMetadata">
      <summary>Windows metadata generated by managed compilers.</summary>
    </member>
    <member name="F:System.Reflection.Metadata.MetadataKind.WindowsMetadata">
      <summary>Windows metadata.</summary>
    </member>
    <member name="T:System.Reflection.Metadata.MetadataReader">
      <summary>Reads metadata as defined by the ECMA 335 CLI specification.</summary>
    </member>
    <member name="M:System.Reflection.Metadata.MetadataReader.#ctor(System.Byte*,System.Int32)">
      <summary>Initializes a new instance of the <see cref="T:System.Reflection.Metadata.MetadataReader" /> class from the metadata stored at the given memory location.</summary>
      <param name="metadata">A pointer to the first byte in a block of metadata.</param>
      <param name="length">The number of bytes in the block.</param>
    </member>
    <member name="M:System.Reflection.Metadata.MetadataReader.#ctor(System.Byte*,System.Int32,System.Reflection.Metadata.MetadataReaderOptions)">
      <summary>Initializes a new instance of the <see cref="T:System.Reflection.Metadata.MetadataReader" /> class from the metadata stored at the given memory location.</summary>
      <param name="metadata" />
      <param name="length" />
      <param name="options" />
    </member>
    <member name="M:System.Reflection.Metadata.MetadataReader.#ctor(System.Byte*,System.Int32,System.Reflection.Metadata.MetadataReaderOptions,System.Reflection.Metadata.MetadataStringDecoder)">
      <summary>Initializes a new instance of the <see cref="T:System.Reflection.Metadata.MetadataReader" /> class from the metadata stored at the given memory location.</summary>
      <param name="metadata" />
      <param name="length" />
      <param name="options" />
      <param name="utf8Decoder" />
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="length" /> is not positive.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="metadata" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentException">The encoding of <paramref name="utf8Decoder" /> is not <see cref="T:System.Text.UTF8Encoding" />.</exception>
      <exception cref="T:System.PlatformNotSupportedException">The current platform is big-endian.</exception>
      <exception cref="T:System.BadImageFormatException">Bad metadata header.</exception>
    </member>
    <member name="P:System.Reflection.Metadata.MetadataReader.AssemblyFiles" />
    <member name="P:System.Reflection.Metadata.MetadataReader.AssemblyReferences" />
    <member name="P:System.Reflection.Metadata.MetadataReader.CustomAttributes" />
    <member name="P:System.Reflection.Metadata.MetadataReader.CustomDebugInformation" />
    <member name="P:System.Reflection.Metadata.MetadataReader.DebugMetadataHeader">
      <summary>Gets the information decoded from #Pdb stream, or <see langword="null" /> if the stream is not present.</summary>
      <returns>The information decoded from #Pdb stream, or <see langword="null" /> if the stream is not present.</returns>
    </member>
    <member name="P:System.Reflection.Metadata.MetadataReader.DeclarativeSecurityAttributes" />
    <member name="P:System.Reflection.Metadata.MetadataReader.Documents" />
    <member name="P:System.Reflection.Metadata.MetadataReader.EventDefinitions" />
    <member name="P:System.Reflection.Metadata.MetadataReader.ExportedTypes" />
    <member name="P:System.Reflection.Metadata.MetadataReader.FieldDefinitions" />
    <member name="M:System.Reflection.Metadata.MetadataReader.GetAssemblyDefinition" />
    <member name="M:System.Reflection.Metadata.MetadataReader.GetAssemblyFile(System.Reflection.Metadata.AssemblyFileHandle)">
      <param name="handle" />
    </member>
    <member name="M:System.Reflection.Metadata.MetadataReader.GetAssemblyReference(System.Reflection.Metadata.AssemblyReferenceHandle)">
      <param name="handle" />
    </member>
    <member name="M:System.Reflection.Metadata.MetadataReader.GetBlobBytes(System.Reflection.Metadata.BlobHandle)">
      <param name="handle" />
    </member>
    <member name="M:System.Reflection.Metadata.MetadataReader.GetBlobContent(System.Reflection.Metadata.BlobHandle)">
      <param name="handle" />
    </member>
    <member name="M:System.Reflection.Metadata.MetadataReader.GetBlobReader(System.Reflection.Metadata.BlobHandle)">
      <param name="handle" />
    </member>
    <member name="M:System.Reflection.Metadata.MetadataReader.GetBlobReader(System.Reflection.Metadata.StringHandle)">
      <param name="handle" />
    </member>
    <member name="M:System.Reflection.Metadata.MetadataReader.GetConstant(System.Reflection.Metadata.ConstantHandle)">
      <param name="handle" />
    </member>
    <member name="M:System.Reflection.Metadata.MetadataReader.GetCustomAttribute(System.Reflection.Metadata.CustomAttributeHandle)">
      <param name="handle" />
    </member>
    <member name="M:System.Reflection.Metadata.MetadataReader.GetCustomAttributes(System.Reflection.Metadata.EntityHandle)">
      <param name="handle" />
    </member>
    <member name="M:System.Reflection.Metadata.MetadataReader.GetCustomDebugInformation(System.Reflection.Metadata.CustomDebugInformationHandle)">
      <param name="handle" />
    </member>
    <member name="M:System.Reflection.Metadata.MetadataReader.GetCustomDebugInformation(System.Reflection.Metadata.EntityHandle)">
      <param name="handle" />
    </member>
    <member name="M:System.Reflection.Metadata.MetadataReader.GetDeclarativeSecurityAttribute(System.Reflection.Metadata.DeclarativeSecurityAttributeHandle)">
      <param name="handle" />
    </member>
    <member name="M:System.Reflection.Metadata.MetadataReader.GetDocument(System.Reflection.Metadata.DocumentHandle)">
      <param name="handle" />
    </member>
    <member name="M:System.Reflection.Metadata.MetadataReader.GetEventDefinition(System.Reflection.Metadata.EventDefinitionHandle)">
      <param name="handle" />
    </member>
    <member name="M:System.Reflection.Metadata.MetadataReader.GetExportedType(System.Reflection.Metadata.ExportedTypeHandle)">
      <param name="handle" />
    </member>
    <member name="M:System.Reflection.Metadata.MetadataReader.GetFieldDefinition(System.Reflection.Metadata.FieldDefinitionHandle)">
      <param name="handle" />
    </member>
    <member name="M:System.Reflection.Metadata.MetadataReader.GetGenericParameter(System.Reflection.Metadata.GenericParameterHandle)">
      <param name="handle" />
    </member>
    <member name="M:System.Reflection.Metadata.MetadataReader.GetGenericParameterConstraint(System.Reflection.Metadata.GenericParameterConstraintHandle)">
      <param name="handle" />
    </member>
    <member name="M:System.Reflection.Metadata.MetadataReader.GetGuid(System.Reflection.Metadata.GuidHandle)">
      <param name="handle" />
    </member>
    <member name="M:System.Reflection.Metadata.MetadataReader.GetImportScope(System.Reflection.Metadata.ImportScopeHandle)">
      <param name="handle" />
    </member>
    <member name="M:System.Reflection.Metadata.MetadataReader.GetInterfaceImplementation(System.Reflection.Metadata.InterfaceImplementationHandle)">
      <param name="handle" />
    </member>
    <member name="M:System.Reflection.Metadata.MetadataReader.GetLocalConstant(System.Reflection.Metadata.LocalConstantHandle)">
      <param name="handle" />
    </member>
    <member name="M:System.Reflection.Metadata.MetadataReader.GetLocalScope(System.Reflection.Metadata.LocalScopeHandle)">
      <param name="handle" />
    </member>
    <member name="M:System.Reflection.Metadata.MetadataReader.GetLocalScopes(System.Reflection.Metadata.MethodDebugInformationHandle)">
      <param name="handle" />
    </member>
    <member name="M:System.Reflection.Metadata.MetadataReader.GetLocalScopes(System.Reflection.Metadata.MethodDefinitionHandle)">
      <param name="handle" />
    </member>
    <member name="M:System.Reflection.Metadata.MetadataReader.GetLocalVariable(System.Reflection.Metadata.LocalVariableHandle)">
      <param name="handle" />
    </member>
    <member name="M:System.Reflection.Metadata.MetadataReader.GetManifestResource(System.Reflection.Metadata.ManifestResourceHandle)">
      <param name="handle" />
    </member>
    <member name="M:System.Reflection.Metadata.MetadataReader.GetMemberReference(System.Reflection.Metadata.MemberReferenceHandle)">
      <param name="handle" />
    </member>
    <member name="M:System.Reflection.Metadata.MetadataReader.GetMethodDebugInformation(System.Reflection.Metadata.MethodDebugInformationHandle)">
      <param name="handle" />
    </member>
    <member name="M:System.Reflection.Metadata.MetadataReader.GetMethodDebugInformation(System.Reflection.Metadata.MethodDefinitionHandle)">
      <param name="handle" />
    </member>
    <member name="M:System.Reflection.Metadata.MetadataReader.GetMethodDefinition(System.Reflection.Metadata.MethodDefinitionHandle)">
      <param name="handle" />
    </member>
    <member name="M:System.Reflection.Metadata.MetadataReader.GetMethodImplementation(System.Reflection.Metadata.MethodImplementationHandle)">
      <param name="handle" />
    </member>
    <member name="M:System.Reflection.Metadata.MetadataReader.GetMethodSpecification(System.Reflection.Metadata.MethodSpecificationHandle)">
      <param name="handle" />
    </member>
    <member name="M:System.Reflection.Metadata.MetadataReader.GetModuleDefinition" />
    <member name="M:System.Reflection.Metadata.MetadataReader.GetModuleReference(System.Reflection.Metadata.ModuleReferenceHandle)">
      <param name="handle" />
    </member>
    <member name="M:System.Reflection.Metadata.MetadataReader.GetNamespaceDefinition(System.Reflection.Metadata.NamespaceDefinitionHandle)">
      <param name="handle" />
    </member>
    <member name="M:System.Reflection.Metadata.MetadataReader.GetNamespaceDefinitionRoot" />
    <member name="M:System.Reflection.Metadata.MetadataReader.GetParameter(System.Reflection.Metadata.ParameterHandle)">
      <param name="handle" />
    </member>
    <member name="M:System.Reflection.Metadata.MetadataReader.GetPropertyDefinition(System.Reflection.Metadata.PropertyDefinitionHandle)">
      <param name="handle" />
    </member>
    <member name="M:System.Reflection.Metadata.MetadataReader.GetStandaloneSignature(System.Reflection.Metadata.StandaloneSignatureHandle)">
      <param name="handle" />
    </member>
    <member name="M:System.Reflection.Metadata.MetadataReader.GetString(System.Reflection.Metadata.DocumentNameBlobHandle)">
      <param name="handle" />
    </member>
    <member name="M:System.Reflection.Metadata.MetadataReader.GetString(System.Reflection.Metadata.NamespaceDefinitionHandle)">
      <param name="handle" />
    </member>
    <member name="M:System.Reflection.Metadata.MetadataReader.GetString(System.Reflection.Metadata.StringHandle)">
      <param name="handle" />
    </member>
    <member name="M:System.Reflection.Metadata.MetadataReader.GetTypeDefinition(System.Reflection.Metadata.TypeDefinitionHandle)">
      <param name="handle" />
    </member>
    <member name="M:System.Reflection.Metadata.MetadataReader.GetTypeReference(System.Reflection.Metadata.TypeReferenceHandle)">
      <param name="handle" />
    </member>
    <member name="M:System.Reflection.Metadata.MetadataReader.GetTypeSpecification(System.Reflection.Metadata.TypeSpecificationHandle)">
      <param name="handle" />
    </member>
    <member name="M:System.Reflection.Metadata.MetadataReader.GetUserString(System.Reflection.Metadata.UserStringHandle)">
      <param name="handle" />
    </member>
    <member name="P:System.Reflection.Metadata.MetadataReader.ImportScopes" />
    <member name="P:System.Reflection.Metadata.MetadataReader.IsAssembly">
      <summary>Gets a value that indicates whether the metadata represents an assembly.</summary>
      <returns>
        <see langword="true" /> if the metadata represents an assembly; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Reflection.Metadata.MetadataReader.LocalConstants" />
    <member name="P:System.Reflection.Metadata.MetadataReader.LocalScopes" />
    <member name="P:System.Reflection.Metadata.MetadataReader.LocalVariables" />
    <member name="P:System.Reflection.Metadata.MetadataReader.ManifestResources" />
    <member name="P:System.Reflection.Metadata.MetadataReader.MemberReferences" />
    <member name="P:System.Reflection.Metadata.MetadataReader.MetadataKind">
      <summary>Gets the metadata kind.</summary>
      <returns>One of the enumeration values that specifies the metadata kind.</returns>
    </member>
    <member name="P:System.Reflection.Metadata.MetadataReader.MetadataLength">
      <summary>Gets the length of the underlying data.</summary>
      <returns>The length of the underlying data.</returns>
    </member>
    <member name="P:System.Reflection.Metadata.MetadataReader.MetadataPointer">
      <summary>Gets the pointer to the underlying data.</summary>
      <returns>The pointer to the underlying data.</returns>
    </member>
    <member name="P:System.Reflection.Metadata.MetadataReader.MetadataVersion">
      <summary>Gets the version string read from metadata header.</summary>
      <returns>The version string read from metadata header.</returns>
    </member>
    <member name="P:System.Reflection.Metadata.MetadataReader.MethodDebugInformation" />
    <member name="P:System.Reflection.Metadata.MetadataReader.MethodDefinitions" />
    <member name="P:System.Reflection.Metadata.MetadataReader.Options">
      <summary>Gets the <see cref="T:System.Reflection.Metadata.MetadataReaderOptions" /> passed to the constructor.</summary>
      <returns>A bitwise combination of the enumeration values that describes the <see cref="T:System.Reflection.Metadata.MetadataReaderOptions" /> enum value.</returns>
    </member>
    <member name="P:System.Reflection.Metadata.MetadataReader.PropertyDefinitions" />
    <member name="P:System.Reflection.Metadata.MetadataReader.StringComparer">
      <summary>Gets the comparer used to compare strings stored in metadata.</summary>
      <returns>The comparer used to compare strings stored in metadata.</returns>
    </member>
    <member name="P:System.Reflection.Metadata.MetadataReader.TypeDefinitions" />
    <member name="P:System.Reflection.Metadata.MetadataReader.TypeReferences" />
    <member name="P:System.Reflection.Metadata.MetadataReader.UTF8Decoder">
      <summary>Gets the decoder used by the reader to produce string instances from UTF8-encoded byte sequences.</summary>
      <returns>The decoder used by the reader to produce string instances from UTF8-encoded byte sequences.</returns>
    </member>
    <member name="T:System.Reflection.Metadata.MetadataReaderOptions" />
    <member name="F:System.Reflection.Metadata.MetadataReaderOptions.ApplyWindowsRuntimeProjections">
      <summary>Windows Runtime projections are enabled (on by default).</summary>
    </member>
    <member name="F:System.Reflection.Metadata.MetadataReaderOptions.Default">
      <summary>The options that are used when a <see cref="T:System.Reflection.Metadata.MetadataReader" /> is obtained via an overload that does not take a <see cref="T:System.Reflection.Metadata.MetadataReaderOptions" /> argument.</summary>
    </member>
    <member name="F:System.Reflection.Metadata.MetadataReaderOptions.None">
      <summary>All options are disabled.</summary>
    </member>
    <member name="T:System.Reflection.Metadata.MetadataReaderProvider">
      <summary>Provides a <see cref="T:System.Reflection.Metadata.MetadataReader" /> for metadata stored in an array of bytes, a memory block, or a stream.</summary>
    </member>
    <member name="M:System.Reflection.Metadata.MetadataReaderProvider.Dispose">
      <summary>Disposes all memory allocated by the reader.</summary>
    </member>
    <member name="M:System.Reflection.Metadata.MetadataReaderProvider.FromMetadataImage(System.Byte*,System.Int32)">
      <summary>Creates a metadata provider over an image stored in memory.</summary>
      <param name="start">Pointer to the start of the metadata blob.</param>
      <param name="size">The size of the metadata blob.</param>
      <returns>The new metadata provider.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="start" /> is <see cref="F:System.IntPtr.Zero" />.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="size" /> is negative.</exception>
    </member>
    <member name="M:System.Reflection.Metadata.MetadataReaderProvider.FromMetadataImage(System.Collections.Immutable.ImmutableArray{System.Byte})">
      <summary>Creates a provider over a byte array.</summary>
      <param name="image">Metadata image.</param>
      <returns>The new provider.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="image" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Reflection.Metadata.MetadataReaderProvider.FromMetadataStream(System.IO.Stream,System.Reflection.Metadata.MetadataStreamOptions,System.Int32)">
      <summary>Creates a provider for a stream of the specified size beginning at its current position.</summary>
      <param name="stream">A <see cref="T:System.IO.Stream" /> instance.</param>
      <param name="options">Options specifying how sections of the image are read from the stream.</param>
      <param name="size">Size of the metadata blob in the stream. If not specified, the metadata blob is assumed to span to the end of the stream.</param>
      <returns>The new provider.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="stream" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="stream" /> doesn't support read and seek operations.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Size is negative or extends past the end of the stream.</exception>
      <exception cref="T:System.IO.IOException">Error reading from the stream (only when <see cref="F:System.Reflection.Metadata.MetadataStreamOptions.PrefetchMetadata" /> is specified).</exception>
    </member>
    <member name="M:System.Reflection.Metadata.MetadataReaderProvider.FromPortablePdbImage(System.Byte*,System.Int32)">
      <summary>Creates a portable PDB metadata provider over a blob stored in memory.</summary>
      <param name="start">Pointer to the start of the portable PDB blob.</param>
      <param name="size">The size of the portable PDB blob.</param>
      <returns>The new portable PDB metadata provider.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="start" /> is <see cref="F:System.IntPtr.Zero" />.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="size" /> is negative.</exception>
    </member>
    <member name="M:System.Reflection.Metadata.MetadataReaderProvider.FromPortablePdbImage(System.Collections.Immutable.ImmutableArray{System.Byte})">
      <summary>Creates a portable PDB metadata provider over a byte array.</summary>
      <param name="image">A portable PDB image.</param>
      <returns>The new portable PDB metadata provider .</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="image" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Reflection.Metadata.MetadataReaderProvider.FromPortablePdbStream(System.IO.Stream,System.Reflection.Metadata.MetadataStreamOptions,System.Int32)">
      <summary>Creates a provider for a stream of the specified size beginning at its current position.</summary>
      <param name="stream">The stream.</param>
      <param name="options">Options specifying how sections of the image are read from the stream.</param>
      <param name="size">Size of the metadata blob in the stream. If not specified, the metadata blob is assumed to span to the end of the stream.</param>
      <returns>A <see cref="T:System.Reflection.Metadata.MetadataReaderProvider" /> instance.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="stream" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="stream" /> doesn't support read and seek operations.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Size is negative or extends past the end of the stream.</exception>
    </member>
    <member name="M:System.Reflection.Metadata.MetadataReaderProvider.GetMetadataReader(System.Reflection.Metadata.MetadataReaderOptions,System.Reflection.Metadata.MetadataStringDecoder)">
      <summary>Gets a <see cref="T:System.Reflection.Metadata.MetadataReader" /> from a <see cref="T:System.Reflection.Metadata.MetadataReaderProvider" />.</summary>
      <param name="options">A bitwise combination of the enumeration values that represent the configuration when reading the metadata.</param>
      <param name="utf8Decoder">The encoding to use.</param>
      <returns>A <see cref="T:System.Reflection.Metadata.MetadataReader" /> instance..</returns>
      <exception cref="T:System.ArgumentException">The encoding of <paramref name="utf8Decoder" /> is not <see cref="T:System.Text.UTF8Encoding" />.</exception>
      <exception cref="T:System.PlatformNotSupportedException">The current platform is big-endian.</exception>
      <exception cref="T:System.IO.IOException">IO error while reading from the underlying stream.</exception>
      <exception cref="T:System.ObjectDisposedException">Provider has been disposed.</exception>
    </member>
    <member name="T:System.Reflection.Metadata.MetadataStreamOptions" />
    <member name="F:System.Reflection.Metadata.MetadataStreamOptions.Default">
      <summary>By default, the stream is disposed when <see cref="T:System.Reflection.Metadata.MetadataReaderProvider" /> is disposed and sections of the PE image are read lazily.</summary>
    </member>
    <member name="F:System.Reflection.Metadata.MetadataStreamOptions.LeaveOpen">
      <summary>Keeps the stream open when the <see cref="T:System.Reflection.Metadata.MetadataReaderProvider" /> is disposed.</summary>
    </member>
    <member name="F:System.Reflection.Metadata.MetadataStreamOptions.PrefetchMetadata">
      <summary>Reads PDB metadata into memory right away.
The underlying file may be closed and even deleted after the <see cref="T:System.Reflection.Metadata.MetadataReaderProvider" /> is constructed. <see cref="T:System.Reflection.Metadata.MetadataReaderProvider" /> closes the stream automatically by the time the constructor returns unless <see cref="F:System.Reflection.Metadata.MetadataStreamOptions.LeaveOpen" /> is specified.</summary>
    </member>
    <member name="T:System.Reflection.Metadata.MetadataStringComparer">
      <summary>Provides string comparison helpers to query strings in metadata while avoiding allocation if possible.</summary>
    </member>
    <member name="M:System.Reflection.Metadata.MetadataStringComparer.Equals(System.Reflection.Metadata.DocumentNameBlobHandle,System.String)">
      <param name="handle" />
      <param name="value" />
    </member>
    <member name="M:System.Reflection.Metadata.MetadataStringComparer.Equals(System.Reflection.Metadata.DocumentNameBlobHandle,System.String,System.Boolean)">
      <param name="handle" />
      <param name="value" />
      <param name="ignoreCase" />
    </member>
    <member name="M:System.Reflection.Metadata.MetadataStringComparer.Equals(System.Reflection.Metadata.NamespaceDefinitionHandle,System.String)">
      <param name="handle" />
      <param name="value" />
    </member>
    <member name="M:System.Reflection.Metadata.MetadataStringComparer.Equals(System.Reflection.Metadata.NamespaceDefinitionHandle,System.String,System.Boolean)">
      <param name="handle" />
      <param name="value" />
      <param name="ignoreCase" />
    </member>
    <member name="M:System.Reflection.Metadata.MetadataStringComparer.Equals(System.Reflection.Metadata.StringHandle,System.String)">
      <param name="handle" />
      <param name="value" />
    </member>
    <member name="M:System.Reflection.Metadata.MetadataStringComparer.Equals(System.Reflection.Metadata.StringHandle,System.String,System.Boolean)">
      <param name="handle" />
      <param name="value" />
      <param name="ignoreCase" />
    </member>
    <member name="M:System.Reflection.Metadata.MetadataStringComparer.StartsWith(System.Reflection.Metadata.StringHandle,System.String)">
      <param name="handle" />
      <param name="value" />
    </member>
    <member name="M:System.Reflection.Metadata.MetadataStringComparer.StartsWith(System.Reflection.Metadata.StringHandle,System.String,System.Boolean)">
      <param name="handle" />
      <param name="value" />
      <param name="ignoreCase" />
    </member>
    <member name="T:System.Reflection.Metadata.MetadataStringDecoder">
      <summary>Provides the <see cref="T:System.Reflection.Metadata.MetadataReader" /> with a custom mechanism for decoding byte sequences in metadata that represent text.</summary>
    </member>
    <member name="M:System.Reflection.Metadata.MetadataStringDecoder.#ctor(System.Text.Encoding)">
      <summary>Initializes a new instance of the <see cref="T:System.Reflection.Metadata.MetadataStringDecoder" /> class using the given encoding.</summary>
      <param name="encoding">The encoding to use.</param>
    </member>
    <member name="P:System.Reflection.Metadata.MetadataStringDecoder.DefaultUTF8">
      <summary>Gets the default decoder used by <see cref="T:System.Reflection.Metadata.MetadataReader" /> to decode UTF-8 when no decoder is provided to the constructor.</summary>
      <returns>The default decoder used by <see cref="T:System.Reflection.Metadata.MetadataReader" /> to decode UTF-8.</returns>
    </member>
    <member name="P:System.Reflection.Metadata.MetadataStringDecoder.Encoding">
      <summary>Gets the encoding used by this instance.</summary>
      <returns>The encoding used by this instance.</returns>
    </member>
    <member name="M:System.Reflection.Metadata.MetadataStringDecoder.GetString(System.Byte*,System.Int32)">
      <summary>Obtains strings for byte sequences in metadata. Override this to cache strings if required. Otherwise, it is implemented by forwarding straight to <see cref="P:System.Reflection.Metadata.MetadataStringDecoder.Encoding" /> and every call will allocate a new string.</summary>
      <param name="bytes">Pointer to bytes to decode.</param>
      <param name="byteCount">Number of bytes to decode.</param>
      <returns>The decoded string.</returns>
    </member>
    <member name="T:System.Reflection.Metadata.MethodBodyBlock" />
    <member name="M:System.Reflection.Metadata.MethodBodyBlock.Create(System.Reflection.Metadata.BlobReader)">
      <param name="reader" />
    </member>
    <member name="P:System.Reflection.Metadata.MethodBodyBlock.ExceptionRegions" />
    <member name="M:System.Reflection.Metadata.MethodBodyBlock.GetILBytes" />
    <member name="M:System.Reflection.Metadata.MethodBodyBlock.GetILContent" />
    <member name="M:System.Reflection.Metadata.MethodBodyBlock.GetILReader" />
    <member name="P:System.Reflection.Metadata.MethodBodyBlock.LocalSignature" />
    <member name="P:System.Reflection.Metadata.MethodBodyBlock.LocalVariablesInitialized" />
    <member name="P:System.Reflection.Metadata.MethodBodyBlock.MaxStack" />
    <member name="P:System.Reflection.Metadata.MethodBodyBlock.Size">
      <summary>Gets the size of the method body, including the header, IL, and exception regions.</summary>
      <returns>The size of the method body.</returns>
    </member>
    <member name="T:System.Reflection.Metadata.MethodDebugInformation">
      <summary>Provides debug information associated with a method definition. This information is stored in debug metadata.</summary>
    </member>
    <member name="P:System.Reflection.Metadata.MethodDebugInformation.Document">
      <summary>Gets the handle of the single document containing all sequence points of the method.</summary>
      <returns>The handle of the single document containing all sequence points of the method, or a handle whose <see cref="P:System.Reflection.Metadata.DocumentHandle.IsNil" /> property is <see langword="true" /> if the method doesn't have sequence points or spans multiple documents.</returns>
    </member>
    <member name="M:System.Reflection.Metadata.MethodDebugInformation.GetSequencePoints">
      <summary>Returns a collection of sequence points decoded from <see cref="P:System.Reflection.Metadata.MethodDebugInformation.SequencePointsBlob" />.</summary>
      <returns>A collection of sequence points.</returns>
    </member>
    <member name="M:System.Reflection.Metadata.MethodDebugInformation.GetStateMachineKickoffMethod">
      <summary>Returns the kickoff method of the state machine.</summary>
      <returns>The kickoff method of the state machine, if the method is a <c>MoveNext</c> method of a state machine. Otherwise, it returns a handle whose <see cref="P:System.Reflection.Metadata.MethodDefinitionHandle.IsNil" /> property is <see langword="true" />..</returns>
    </member>
    <member name="P:System.Reflection.Metadata.MethodDebugInformation.LocalSignature">
      <summary>Returns a local signature handle.</summary>
      <returns>A local signature handle, or a handle whose <see cref="P:System.Reflection.Metadata.StandaloneSignatureHandle.IsNil" /> property is <see langword="true" /> if the method doesn't define any local variables.</returns>
    </member>
    <member name="P:System.Reflection.Metadata.MethodDebugInformation.SequencePointsBlob">
      <summary>Returns a blob encoding sequence points.</summary>
      <returns>A blob encoding sequence points, or a handle whose <see cref="P:System.Reflection.Metadata.BlobHandle.IsNil" /> property is <see langword="true" /> if the method doesn't have sequence points.</returns>
    </member>
    <member name="T:System.Reflection.Metadata.MethodDebugInformationHandle" />
    <member name="M:System.Reflection.Metadata.MethodDebugInformationHandle.Equals(System.Object)">
      <param name="obj" />
    </member>
    <member name="M:System.Reflection.Metadata.MethodDebugInformationHandle.Equals(System.Reflection.Metadata.MethodDebugInformationHandle)">
      <param name="other" />
    </member>
    <member name="M:System.Reflection.Metadata.MethodDebugInformationHandle.GetHashCode" />
    <member name="P:System.Reflection.Metadata.MethodDebugInformationHandle.IsNil" />
    <member name="M:System.Reflection.Metadata.MethodDebugInformationHandle.op_Equality(System.Reflection.Metadata.MethodDebugInformationHandle,System.Reflection.Metadata.MethodDebugInformationHandle)">
      <param name="left" />
      <param name="right" />
    </member>
    <member name="M:System.Reflection.Metadata.MethodDebugInformationHandle.op_Explicit(System.Reflection.Metadata.EntityHandle)~System.Reflection.Metadata.MethodDebugInformationHandle">
      <param name="handle" />
    </member>
    <member name="M:System.Reflection.Metadata.MethodDebugInformationHandle.op_Explicit(System.Reflection.Metadata.Handle)~System.Reflection.Metadata.MethodDebugInformationHandle">
      <param name="handle" />
    </member>
    <member name="M:System.Reflection.Metadata.MethodDebugInformationHandle.op_Implicit(System.Reflection.Metadata.MethodDebugInformationHandle)~System.Reflection.Metadata.EntityHandle">
      <param name="handle" />
    </member>
    <member name="M:System.Reflection.Metadata.MethodDebugInformationHandle.op_Implicit(System.Reflection.Metadata.MethodDebugInformationHandle)~System.Reflection.Metadata.Handle">
      <param name="handle" />
    </member>
    <member name="M:System.Reflection.Metadata.MethodDebugInformationHandle.op_Inequality(System.Reflection.Metadata.MethodDebugInformationHandle,System.Reflection.Metadata.MethodDebugInformationHandle)">
      <param name="left" />
      <param name="right" />
    </member>
    <member name="M:System.Reflection.Metadata.MethodDebugInformationHandle.ToDefinitionHandle">
      <summary>Returns a handle to a <see cref="T:System.Reflection.Metadata.MethodDefinition" /> that corresponds to this handle.</summary>
      <returns>A method definition handle that corresponds to this handle.</returns>
    </member>
    <member name="T:System.Reflection.Metadata.MethodDebugInformationHandleCollection" />
    <member name="P:System.Reflection.Metadata.MethodDebugInformationHandleCollection.Count" />
    <member name="T:System.Reflection.Metadata.MethodDebugInformationHandleCollection.Enumerator" />
    <member name="P:System.Reflection.Metadata.MethodDebugInformationHandleCollection.Enumerator.Current" />
    <member name="M:System.Reflection.Metadata.MethodDebugInformationHandleCollection.Enumerator.MoveNext" />
    <member name="P:System.Reflection.Metadata.MethodDebugInformationHandleCollection.Enumerator.System#Collections#IEnumerator#Current" />
    <member name="M:System.Reflection.Metadata.MethodDebugInformationHandleCollection.Enumerator.System#Collections#IEnumerator#Reset" />
    <member name="M:System.Reflection.Metadata.MethodDebugInformationHandleCollection.Enumerator.System#IDisposable#Dispose" />
    <member name="M:System.Reflection.Metadata.MethodDebugInformationHandleCollection.GetEnumerator" />
    <member name="M:System.Reflection.Metadata.MethodDebugInformationHandleCollection.System#Collections#Generic#IEnumerable{System#Reflection#Metadata#MethodDebugInformationHandle}#GetEnumerator" />
    <member name="M:System.Reflection.Metadata.MethodDebugInformationHandleCollection.System#Collections#IEnumerable#GetEnumerator" />
    <member name="T:System.Reflection.Metadata.MethodDefinition" />
    <member name="P:System.Reflection.Metadata.MethodDefinition.Attributes" />
    <member name="M:System.Reflection.Metadata.MethodDefinition.DecodeSignature``2(System.Reflection.Metadata.ISignatureTypeProvider{``0,``1},``1)">
      <param name="provider" />
      <param name="genericContext" />
      <typeparam name="TType" />
      <typeparam name="TGenericContext" />
    </member>
    <member name="M:System.Reflection.Metadata.MethodDefinition.GetCustomAttributes" />
    <member name="M:System.Reflection.Metadata.MethodDefinition.GetDeclarativeSecurityAttributes" />
    <member name="M:System.Reflection.Metadata.MethodDefinition.GetDeclaringType" />
    <member name="M:System.Reflection.Metadata.MethodDefinition.GetGenericParameters" />
    <member name="M:System.Reflection.Metadata.MethodDefinition.GetImport" />
    <member name="M:System.Reflection.Metadata.MethodDefinition.GetParameters" />
    <member name="P:System.Reflection.Metadata.MethodDefinition.ImplAttributes" />
    <member name="P:System.Reflection.Metadata.MethodDefinition.Name" />
    <member name="P:System.Reflection.Metadata.MethodDefinition.RelativeVirtualAddress" />
    <member name="P:System.Reflection.Metadata.MethodDefinition.Signature" />
    <member name="T:System.Reflection.Metadata.MethodDefinitionHandle" />
    <member name="M:System.Reflection.Metadata.MethodDefinitionHandle.Equals(System.Object)">
      <param name="obj" />
    </member>
    <member name="M:System.Reflection.Metadata.MethodDefinitionHandle.Equals(System.Reflection.Metadata.MethodDefinitionHandle)">
      <param name="other" />
    </member>
    <member name="M:System.Reflection.Metadata.MethodDefinitionHandle.GetHashCode" />
    <member name="P:System.Reflection.Metadata.MethodDefinitionHandle.IsNil" />
    <member name="M:System.Reflection.Metadata.MethodDefinitionHandle.op_Equality(System.Reflection.Metadata.MethodDefinitionHandle,System.Reflection.Metadata.MethodDefinitionHandle)">
      <param name="left" />
      <param name="right" />
    </member>
    <member name="M:System.Reflection.Metadata.MethodDefinitionHandle.op_Explicit(System.Reflection.Metadata.EntityHandle)~System.Reflection.Metadata.MethodDefinitionHandle">
      <param name="handle" />
    </member>
    <member name="M:System.Reflection.Metadata.MethodDefinitionHandle.op_Explicit(System.Reflection.Metadata.Handle)~System.Reflection.Metadata.MethodDefinitionHandle">
      <param name="handle" />
    </member>
    <member name="M:System.Reflection.Metadata.MethodDefinitionHandle.op_Implicit(System.Reflection.Metadata.MethodDefinitionHandle)~System.Reflection.Metadata.EntityHandle">
      <param name="handle" />
    </member>
    <member name="M:System.Reflection.Metadata.MethodDefinitionHandle.op_Implicit(System.Reflection.Metadata.MethodDefinitionHandle)~System.Reflection.Metadata.Handle">
      <param name="handle" />
    </member>
    <member name="M:System.Reflection.Metadata.MethodDefinitionHandle.op_Inequality(System.Reflection.Metadata.MethodDefinitionHandle,System.Reflection.Metadata.MethodDefinitionHandle)">
      <param name="left" />
      <param name="right" />
    </member>
    <member name="M:System.Reflection.Metadata.MethodDefinitionHandle.ToDebugInformationHandle">
      <summary>Returns a handle to a <see cref="T:System.Reflection.Metadata.MethodDebugInformation" /> that corresponds to this handle.</summary>
      <returns>A method debug information handle that corresponds to this handle.</returns>
    </member>
    <member name="T:System.Reflection.Metadata.MethodDefinitionHandleCollection" />
    <member name="P:System.Reflection.Metadata.MethodDefinitionHandleCollection.Count" />
    <member name="T:System.Reflection.Metadata.MethodDefinitionHandleCollection.Enumerator" />
    <member name="P:System.Reflection.Metadata.MethodDefinitionHandleCollection.Enumerator.Current" />
    <member name="M:System.Reflection.Metadata.MethodDefinitionHandleCollection.Enumerator.MoveNext" />
    <member name="P:System.Reflection.Metadata.MethodDefinitionHandleCollection.Enumerator.System#Collections#IEnumerator#Current" />
    <member name="M:System.Reflection.Metadata.MethodDefinitionHandleCollection.Enumerator.System#Collections#IEnumerator#Reset" />
    <member name="M:System.Reflection.Metadata.MethodDefinitionHandleCollection.Enumerator.System#IDisposable#Dispose" />
    <member name="M:System.Reflection.Metadata.MethodDefinitionHandleCollection.GetEnumerator" />
    <member name="M:System.Reflection.Metadata.MethodDefinitionHandleCollection.System#Collections#Generic#IEnumerable{System#Reflection#Metadata#MethodDefinitionHandle}#GetEnumerator" />
    <member name="M:System.Reflection.Metadata.MethodDefinitionHandleCollection.System#Collections#IEnumerable#GetEnumerator" />
    <member name="T:System.Reflection.Metadata.MethodImplementation" />
    <member name="M:System.Reflection.Metadata.MethodImplementation.GetCustomAttributes" />
    <member name="P:System.Reflection.Metadata.MethodImplementation.MethodBody" />
    <member name="P:System.Reflection.Metadata.MethodImplementation.MethodDeclaration" />
    <member name="P:System.Reflection.Metadata.MethodImplementation.Type" />
    <member name="T:System.Reflection.Metadata.MethodImplementationHandle" />
    <member name="M:System.Reflection.Metadata.MethodImplementationHandle.Equals(System.Object)">
      <param name="obj" />
    </member>
    <member name="M:System.Reflection.Metadata.MethodImplementationHandle.Equals(System.Reflection.Metadata.MethodImplementationHandle)">
      <param name="other" />
    </member>
    <member name="M:System.Reflection.Metadata.MethodImplementationHandle.GetHashCode" />
    <member name="P:System.Reflection.Metadata.MethodImplementationHandle.IsNil" />
    <member name="M:System.Reflection.Metadata.MethodImplementationHandle.op_Equality(System.Reflection.Metadata.MethodImplementationHandle,System.Reflection.Metadata.MethodImplementationHandle)">
      <param name="left" />
      <param name="right" />
    </member>
    <member name="M:System.Reflection.Metadata.MethodImplementationHandle.op_Explicit(System.Reflection.Metadata.EntityHandle)~System.Reflection.Metadata.MethodImplementationHandle">
      <param name="handle" />
    </member>
    <member name="M:System.Reflection.Metadata.MethodImplementationHandle.op_Explicit(System.Reflection.Metadata.Handle)~System.Reflection.Metadata.MethodImplementationHandle">
      <param name="handle" />
    </member>
    <member name="M:System.Reflection.Metadata.MethodImplementationHandle.op_Implicit(System.Reflection.Metadata.MethodImplementationHandle)~System.Reflection.Metadata.EntityHandle">
      <param name="handle" />
    </member>
    <member name="M:System.Reflection.Metadata.MethodImplementationHandle.op_Implicit(System.Reflection.Metadata.MethodImplementationHandle)~System.Reflection.Metadata.Handle">
      <param name="handle" />
    </member>
    <member name="M:System.Reflection.Metadata.MethodImplementationHandle.op_Inequality(System.Reflection.Metadata.MethodImplementationHandle,System.Reflection.Metadata.MethodImplementationHandle)">
      <param name="left" />
      <param name="right" />
    </member>
    <member name="T:System.Reflection.Metadata.MethodImplementationHandleCollection" />
    <member name="P:System.Reflection.Metadata.MethodImplementationHandleCollection.Count" />
    <member name="T:System.Reflection.Metadata.MethodImplementationHandleCollection.Enumerator" />
    <member name="P:System.Reflection.Metadata.MethodImplementationHandleCollection.Enumerator.Current" />
    <member name="M:System.Reflection.Metadata.MethodImplementationHandleCollection.Enumerator.MoveNext" />
    <member name="P:System.Reflection.Metadata.MethodImplementationHandleCollection.Enumerator.System#Collections#IEnumerator#Current" />
    <member name="M:System.Reflection.Metadata.MethodImplementationHandleCollection.Enumerator.System#Collections#IEnumerator#Reset" />
    <member name="M:System.Reflection.Metadata.MethodImplementationHandleCollection.Enumerator.System#IDisposable#Dispose" />
    <member name="M:System.Reflection.Metadata.MethodImplementationHandleCollection.GetEnumerator" />
    <member name="M:System.Reflection.Metadata.MethodImplementationHandleCollection.System#Collections#Generic#IEnumerable{System#Reflection#Metadata#MethodImplementationHandle}#GetEnumerator" />
    <member name="M:System.Reflection.Metadata.MethodImplementationHandleCollection.System#Collections#IEnumerable#GetEnumerator" />
    <member name="T:System.Reflection.Metadata.MethodImport" />
    <member name="P:System.Reflection.Metadata.MethodImport.Attributes" />
    <member name="P:System.Reflection.Metadata.MethodImport.Module" />
    <member name="P:System.Reflection.Metadata.MethodImport.Name" />
    <member name="T:System.Reflection.Metadata.MethodSignature`1">
      <summary>Represents a method (definition, reference, or standalone) or property signature. In the case of properties, the signature matches that of a getter with a distinguishing <see cref="T:System.Reflection.Metadata.SignatureHeader" />.</summary>
      <typeparam name="TType">The method type.</typeparam>
    </member>
    <member name="M:System.Reflection.Metadata.MethodSignature`1.#ctor(System.Reflection.Metadata.SignatureHeader,`0,System.Int32,System.Int32,System.Collections.Immutable.ImmutableArray{`0})">
      <summary>Initializes a new instance of the <see cref="T:System.Reflection.Metadata.MethodSignature`1" /> structure using the specified header, return type, and parameter information.</summary>
      <param name="header">The information in the leading byte of the signature (kind, calling convention, flags).</param>
      <param name="returnType">The return type of the method.</param>
      <param name="requiredParameterCount">The number of required parameters.</param>
      <param name="genericParameterCount">The number of generic type parameters.</param>
      <param name="parameterTypes">The parameter types.</param>
    </member>
    <member name="P:System.Reflection.Metadata.MethodSignature`1.GenericParameterCount">
      <summary>Gets the number of generic type parameters for the method.</summary>
      <returns>The number of generic type parameters, or 0 for non-generic methods.</returns>
    </member>
    <member name="P:System.Reflection.Metadata.MethodSignature`1.Header">
      <summary>Gets the information in the leading byte of the signature (kind, calling convention, flags).</summary>
      <returns>The header signature.</returns>
    </member>
    <member name="P:System.Reflection.Metadata.MethodSignature`1.ParameterTypes">
      <summary>Gets the method's parameter types.</summary>
      <returns>An immutable collection of parameter types.</returns>
    </member>
    <member name="P:System.Reflection.Metadata.MethodSignature`1.RequiredParameterCount">
      <summary>Gets the number of parameters that are required for the method.</summary>
      <returns>The number of required parameters.</returns>
    </member>
    <member name="P:System.Reflection.Metadata.MethodSignature`1.ReturnType">
      <summary>Gets the return type of the method.</summary>
      <returns>The return type.</returns>
    </member>
    <member name="T:System.Reflection.Metadata.MethodSpecification" />
    <member name="M:System.Reflection.Metadata.MethodSpecification.DecodeSignature``2(System.Reflection.Metadata.ISignatureTypeProvider{``0,``1},``1)">
      <param name="provider" />
      <param name="genericContext" />
      <typeparam name="TType" />
      <typeparam name="TGenericContext" />
    </member>
    <member name="M:System.Reflection.Metadata.MethodSpecification.GetCustomAttributes" />
    <member name="P:System.Reflection.Metadata.MethodSpecification.Method">
      <summary>Gets a <see langword="MethodDef" /> or <see langword="MemberRef" /> handle specifying which generic method this instance refers to (that is, which generic method it is an instantiation of).</summary>
      <returns>A <see langword="MethodDef" /> or <see langword="MemberRef" /> handle specifying which generic method this instance refers to.</returns>
    </member>
    <member name="P:System.Reflection.Metadata.MethodSpecification.Signature">
      <summary>Gets a handle to the signature blob.</summary>
      <returns>A handle to the signature blob.</returns>
    </member>
    <member name="T:System.Reflection.Metadata.MethodSpecificationHandle" />
    <member name="M:System.Reflection.Metadata.MethodSpecificationHandle.Equals(System.Object)">
      <param name="obj" />
    </member>
    <member name="M:System.Reflection.Metadata.MethodSpecificationHandle.Equals(System.Reflection.Metadata.MethodSpecificationHandle)">
      <param name="other" />
    </member>
    <member name="M:System.Reflection.Metadata.MethodSpecificationHandle.GetHashCode" />
    <member name="P:System.Reflection.Metadata.MethodSpecificationHandle.IsNil" />
    <member name="M:System.Reflection.Metadata.MethodSpecificationHandle.op_Equality(System.Reflection.Metadata.MethodSpecificationHandle,System.Reflection.Metadata.MethodSpecificationHandle)">
      <param name="left" />
      <param name="right" />
    </member>
    <member name="M:System.Reflection.Metadata.MethodSpecificationHandle.op_Explicit(System.Reflection.Metadata.EntityHandle)~System.Reflection.Metadata.MethodSpecificationHandle">
      <param name="handle" />
    </member>
    <member name="M:System.Reflection.Metadata.MethodSpecificationHandle.op_Explicit(System.Reflection.Metadata.Handle)~System.Reflection.Metadata.MethodSpecificationHandle">
      <param name="handle" />
    </member>
    <member name="M:System.Reflection.Metadata.MethodSpecificationHandle.op_Implicit(System.Reflection.Metadata.MethodSpecificationHandle)~System.Reflection.Metadata.EntityHandle">
      <param name="handle" />
    </member>
    <member name="M:System.Reflection.Metadata.MethodSpecificationHandle.op_Implicit(System.Reflection.Metadata.MethodSpecificationHandle)~System.Reflection.Metadata.Handle">
      <param name="handle" />
    </member>
    <member name="M:System.Reflection.Metadata.MethodSpecificationHandle.op_Inequality(System.Reflection.Metadata.MethodSpecificationHandle,System.Reflection.Metadata.MethodSpecificationHandle)">
      <param name="left" />
      <param name="right" />
    </member>
    <member name="T:System.Reflection.Metadata.ModuleDefinition" />
    <member name="P:System.Reflection.Metadata.ModuleDefinition.BaseGenerationId" />
    <member name="P:System.Reflection.Metadata.ModuleDefinition.Generation" />
    <member name="P:System.Reflection.Metadata.ModuleDefinition.GenerationId" />
    <member name="M:System.Reflection.Metadata.ModuleDefinition.GetCustomAttributes" />
    <member name="P:System.Reflection.Metadata.ModuleDefinition.Mvid" />
    <member name="P:System.Reflection.Metadata.ModuleDefinition.Name" />
    <member name="T:System.Reflection.Metadata.ModuleDefinitionHandle" />
    <member name="M:System.Reflection.Metadata.ModuleDefinitionHandle.Equals(System.Object)">
      <param name="obj" />
    </member>
    <member name="M:System.Reflection.Metadata.ModuleDefinitionHandle.Equals(System.Reflection.Metadata.ModuleDefinitionHandle)">
      <param name="other" />
    </member>
    <member name="M:System.Reflection.Metadata.ModuleDefinitionHandle.GetHashCode" />
    <member name="P:System.Reflection.Metadata.ModuleDefinitionHandle.IsNil" />
    <member name="M:System.Reflection.Metadata.ModuleDefinitionHandle.op_Equality(System.Reflection.Metadata.ModuleDefinitionHandle,System.Reflection.Metadata.ModuleDefinitionHandle)">
      <param name="left" />
      <param name="right" />
    </member>
    <member name="M:System.Reflection.Metadata.ModuleDefinitionHandle.op_Explicit(System.Reflection.Metadata.EntityHandle)~System.Reflection.Metadata.ModuleDefinitionHandle">
      <param name="handle" />
    </member>
    <member name="M:System.Reflection.Metadata.ModuleDefinitionHandle.op_Explicit(System.Reflection.Metadata.Handle)~System.Reflection.Metadata.ModuleDefinitionHandle">
      <param name="handle" />
    </member>
    <member name="M:System.Reflection.Metadata.ModuleDefinitionHandle.op_Implicit(System.Reflection.Metadata.ModuleDefinitionHandle)~System.Reflection.Metadata.EntityHandle">
      <param name="handle" />
    </member>
    <member name="M:System.Reflection.Metadata.ModuleDefinitionHandle.op_Implicit(System.Reflection.Metadata.ModuleDefinitionHandle)~System.Reflection.Metadata.Handle">
      <param name="handle" />
    </member>
    <member name="M:System.Reflection.Metadata.ModuleDefinitionHandle.op_Inequality(System.Reflection.Metadata.ModuleDefinitionHandle,System.Reflection.Metadata.ModuleDefinitionHandle)">
      <param name="left" />
      <param name="right" />
    </member>
    <member name="T:System.Reflection.Metadata.ModuleReference" />
    <member name="M:System.Reflection.Metadata.ModuleReference.GetCustomAttributes" />
    <member name="P:System.Reflection.Metadata.ModuleReference.Name" />
    <member name="T:System.Reflection.Metadata.ModuleReferenceHandle" />
    <member name="M:System.Reflection.Metadata.ModuleReferenceHandle.Equals(System.Object)">
      <param name="obj" />
    </member>
    <member name="M:System.Reflection.Metadata.ModuleReferenceHandle.Equals(System.Reflection.Metadata.ModuleReferenceHandle)">
      <param name="other" />
    </member>
    <member name="M:System.Reflection.Metadata.ModuleReferenceHandle.GetHashCode" />
    <member name="P:System.Reflection.Metadata.ModuleReferenceHandle.IsNil" />
    <member name="M:System.Reflection.Metadata.ModuleReferenceHandle.op_Equality(System.Reflection.Metadata.ModuleReferenceHandle,System.Reflection.Metadata.ModuleReferenceHandle)">
      <param name="left" />
      <param name="right" />
    </member>
    <member name="M:System.Reflection.Metadata.ModuleReferenceHandle.op_Explicit(System.Reflection.Metadata.EntityHandle)~System.Reflection.Metadata.ModuleReferenceHandle">
      <param name="handle" />
    </member>
    <member name="M:System.Reflection.Metadata.ModuleReferenceHandle.op_Explicit(System.Reflection.Metadata.Handle)~System.Reflection.Metadata.ModuleReferenceHandle">
      <param name="handle" />
    </member>
    <member name="M:System.Reflection.Metadata.ModuleReferenceHandle.op_Implicit(System.Reflection.Metadata.ModuleReferenceHandle)~System.Reflection.Metadata.EntityHandle">
      <param name="handle" />
    </member>
    <member name="M:System.Reflection.Metadata.ModuleReferenceHandle.op_Implicit(System.Reflection.Metadata.ModuleReferenceHandle)~System.Reflection.Metadata.Handle">
      <param name="handle" />
    </member>
    <member name="M:System.Reflection.Metadata.ModuleReferenceHandle.op_Inequality(System.Reflection.Metadata.ModuleReferenceHandle,System.Reflection.Metadata.ModuleReferenceHandle)">
      <param name="left" />
      <param name="right" />
    </member>
    <member name="T:System.Reflection.Metadata.NamespaceDefinition" />
    <member name="P:System.Reflection.Metadata.NamespaceDefinition.ExportedTypes">
      <summary>Gets all exported types that reside directly in a namespace.</summary>
      <returns>An immutable array of exported type handles.</returns>
    </member>
    <member name="P:System.Reflection.Metadata.NamespaceDefinition.Name">
      <summary>Gets the unqualified name of the namespace definition.</summary>
      <returns>The unqualified name of the namespace definition.</returns>
    </member>
    <member name="P:System.Reflection.Metadata.NamespaceDefinition.NamespaceDefinitions">
      <summary>Gets the namespace definitions that are direct children of the current namespace definition.</summary>
      <returns>An immutable array of namespace definitions that are direct children of the current namespace definition.</returns>
    </member>
    <member name="P:System.Reflection.Metadata.NamespaceDefinition.Parent">
      <summary>Gets the parent namespace.</summary>
      <returns>The parent namespace.</returns>
    </member>
    <member name="P:System.Reflection.Metadata.NamespaceDefinition.TypeDefinitions">
      <summary>Gets all type definitions that reside directly in a namespace.</summary>
      <returns>An immutable array of type definition handles.</returns>
    </member>
    <member name="T:System.Reflection.Metadata.NamespaceDefinitionHandle">
      <summary>Provides a handle to a namespace definition.</summary>
    </member>
    <member name="M:System.Reflection.Metadata.NamespaceDefinitionHandle.Equals(System.Object)">
      <param name="obj" />
    </member>
    <member name="M:System.Reflection.Metadata.NamespaceDefinitionHandle.Equals(System.Reflection.Metadata.NamespaceDefinitionHandle)">
      <param name="other" />
    </member>
    <member name="M:System.Reflection.Metadata.NamespaceDefinitionHandle.GetHashCode" />
    <member name="P:System.Reflection.Metadata.NamespaceDefinitionHandle.IsNil" />
    <member name="M:System.Reflection.Metadata.NamespaceDefinitionHandle.op_Equality(System.Reflection.Metadata.NamespaceDefinitionHandle,System.Reflection.Metadata.NamespaceDefinitionHandle)">
      <param name="left" />
      <param name="right" />
    </member>
    <member name="M:System.Reflection.Metadata.NamespaceDefinitionHandle.op_Explicit(System.Reflection.Metadata.Handle)~System.Reflection.Metadata.NamespaceDefinitionHandle">
      <param name="handle" />
    </member>
    <member name="M:System.Reflection.Metadata.NamespaceDefinitionHandle.op_Implicit(System.Reflection.Metadata.NamespaceDefinitionHandle)~System.Reflection.Metadata.Handle">
      <param name="handle" />
    </member>
    <member name="M:System.Reflection.Metadata.NamespaceDefinitionHandle.op_Inequality(System.Reflection.Metadata.NamespaceDefinitionHandle,System.Reflection.Metadata.NamespaceDefinitionHandle)">
      <param name="left" />
      <param name="right" />
    </member>
    <member name="T:System.Reflection.Metadata.Parameter" />
    <member name="P:System.Reflection.Metadata.Parameter.Attributes" />
    <member name="M:System.Reflection.Metadata.Parameter.GetCustomAttributes" />
    <member name="M:System.Reflection.Metadata.Parameter.GetDefaultValue" />
    <member name="M:System.Reflection.Metadata.Parameter.GetMarshallingDescriptor" />
    <member name="P:System.Reflection.Metadata.Parameter.Name" />
    <member name="P:System.Reflection.Metadata.Parameter.SequenceNumber" />
    <member name="T:System.Reflection.Metadata.ParameterHandle" />
    <member name="M:System.Reflection.Metadata.ParameterHandle.Equals(System.Object)">
      <param name="obj" />
    </member>
    <member name="M:System.Reflection.Metadata.ParameterHandle.Equals(System.Reflection.Metadata.ParameterHandle)">
      <param name="other" />
    </member>
    <member name="M:System.Reflection.Metadata.ParameterHandle.GetHashCode" />
    <member name="P:System.Reflection.Metadata.ParameterHandle.IsNil" />
    <member name="M:System.Reflection.Metadata.ParameterHandle.op_Equality(System.Reflection.Metadata.ParameterHandle,System.Reflection.Metadata.ParameterHandle)">
      <param name="left" />
      <param name="right" />
    </member>
    <member name="M:System.Reflection.Metadata.ParameterHandle.op_Explicit(System.Reflection.Metadata.EntityHandle)~System.Reflection.Metadata.ParameterHandle">
      <param name="handle" />
    </member>
    <member name="M:System.Reflection.Metadata.ParameterHandle.op_Explicit(System.Reflection.Metadata.Handle)~System.Reflection.Metadata.ParameterHandle">
      <param name="handle" />
    </member>
    <member name="M:System.Reflection.Metadata.ParameterHandle.op_Implicit(System.Reflection.Metadata.ParameterHandle)~System.Reflection.Metadata.EntityHandle">
      <param name="handle" />
    </member>
    <member name="M:System.Reflection.Metadata.ParameterHandle.op_Implicit(System.Reflection.Metadata.ParameterHandle)~System.Reflection.Metadata.Handle">
      <param name="handle" />
    </member>
    <member name="M:System.Reflection.Metadata.ParameterHandle.op_Inequality(System.Reflection.Metadata.ParameterHandle,System.Reflection.Metadata.ParameterHandle)">
      <param name="left" />
      <param name="right" />
    </member>
    <member name="T:System.Reflection.Metadata.ParameterHandleCollection">
      <summary>Contains a collection of parameters of a specified method.</summary>
    </member>
    <member name="P:System.Reflection.Metadata.ParameterHandleCollection.Count" />
    <member name="T:System.Reflection.Metadata.ParameterHandleCollection.Enumerator" />
    <member name="P:System.Reflection.Metadata.ParameterHandleCollection.Enumerator.Current" />
    <member name="M:System.Reflection.Metadata.ParameterHandleCollection.Enumerator.MoveNext" />
    <member name="P:System.Reflection.Metadata.ParameterHandleCollection.Enumerator.System#Collections#IEnumerator#Current" />
    <member name="M:System.Reflection.Metadata.ParameterHandleCollection.Enumerator.System#Collections#IEnumerator#Reset" />
    <member name="M:System.Reflection.Metadata.ParameterHandleCollection.Enumerator.System#IDisposable#Dispose" />
    <member name="M:System.Reflection.Metadata.ParameterHandleCollection.GetEnumerator" />
    <member name="M:System.Reflection.Metadata.ParameterHandleCollection.System#Collections#Generic#IEnumerable{System#Reflection#Metadata#ParameterHandle}#GetEnumerator" />
    <member name="M:System.Reflection.Metadata.ParameterHandleCollection.System#Collections#IEnumerable#GetEnumerator" />
    <member name="T:System.Reflection.Metadata.PEReaderExtensions" />
    <member name="M:System.Reflection.Metadata.PEReaderExtensions.GetMetadataReader(System.Reflection.PortableExecutable.PEReader)">
      <summary>Gets a <see cref="T:System.Reflection.Metadata.MetadataReader" /> from a <see cref="T:System.Reflection.PortableExecutable.PEReader" />.</summary>
      <param name="peReader">The current <see cref="T:System.Reflection.PortableExecutable.PEReader" /> instance.</param>
      <returns>A metdata reader.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="peReader" /> is <see langword="null" />.</exception>
      <exception cref="T:System.PlatformNotSupportedException">The current platform is big-endian.</exception>
      <exception cref="T:System.IO.IOException">IO error while reading from the underlying stream.</exception>
    </member>
    <member name="M:System.Reflection.Metadata.PEReaderExtensions.GetMetadataReader(System.Reflection.PortableExecutable.PEReader,System.Reflection.Metadata.MetadataReaderOptions)">
      <summary>Gets a  metadata reader with the specified metadata reading configuration from a <see cref="T:System.Reflection.PortableExecutable.PEReader" />.</summary>
      <param name="peReader">The current <see cref="T:System.Reflection.PortableExecutable.PEReader" /> instance.</param>
      <param name="options">An  enumeration value indicating the metadata reading configuration.</param>
      <returns>A  metadata reader with the specified metadata reading configuration.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="peReader" /> is <see langword="null" />.</exception>
      <exception cref="T:System.PlatformNotSupportedException">The current platform is big-endian.</exception>
      <exception cref="T:System.IO.IOException">IO error while reading from the underlying stream.</exception>
    </member>
    <member name="M:System.Reflection.Metadata.PEReaderExtensions.GetMetadataReader(System.Reflection.PortableExecutable.PEReader,System.Reflection.Metadata.MetadataReaderOptions,System.Reflection.Metadata.MetadataStringDecoder)">
      <summary>Gets a metadata reader with the specified metadata reading configuration and encoding configuration from a <see cref="T:System.Reflection.PortableExecutable.PEReader" />.</summary>
      <param name="peReader">The current <see cref="T:System.Reflection.PortableExecutable.PEReader" /> instance.</param>
      <param name="options">An enumeration value indicating the metadata reading configuration.</param>
      <param name="utf8Decoder">A metadata string decoder with the encoding configuration.</param>
      <returns>&gt;A metadata reader with the specified metadata reading configuration and encoding configuration.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="peReader" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentException">The encoding of <paramref name="utf8Decoder" /> is not <see cref="T:System.Text.UTF8Encoding" />.</exception>
      <exception cref="T:System.PlatformNotSupportedException">The current platform is big-endian.</exception>
      <exception cref="T:System.IO.IOException">IO error while reading from the underlying stream.</exception>
    </member>
    <member name="M:System.Reflection.Metadata.PEReaderExtensions.GetMethodBody(System.Reflection.PortableExecutable.PEReader,System.Int32)">
      <summary>Returns a body block of a method with the specified Relative Virtual Address (RVA);</summary>
      <param name="peReader">The current <see cref="T:System.Reflection.PortableExecutable.PEReader" /> instance.</param>
      <param name="relativeVirtualAddress">The Relative Virtual Address (RVA).</param>
      <returns>A method block body instance.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="peReader" /> is <see langword="null" />.</exception>
      <exception cref="T:System.BadImageFormatException">The body is not found in the metadata or is invalid.</exception>
      <exception cref="T:System.InvalidOperationException">The section where the method is stored is not available.</exception>
      <exception cref="T:System.IO.IOException">IO error occurred while reading from the underlying stream.</exception>
    </member>
    <member name="T:System.Reflection.Metadata.PrimitiveSerializationTypeCode">
      <summary>Specifies constants that define the type codes used to encode types of primitive values in a <see cref="T:System.Reflection.Metadata.CustomAttribute" /> value blob.</summary>
    </member>
    <member name="F:System.Reflection.Metadata.PrimitiveSerializationTypeCode.Boolean">
      <summary>A <see cref="T:System.Boolean" /> type.</summary>
    </member>
    <member name="F:System.Reflection.Metadata.PrimitiveSerializationTypeCode.Byte">
      <summary>An unsigned 1-byte integer type.</summary>
    </member>
    <member name="F:System.Reflection.Metadata.PrimitiveSerializationTypeCode.Char">
      <summary>A <see cref="T:System.Char" /> type.</summary>
    </member>
    <member name="F:System.Reflection.Metadata.PrimitiveSerializationTypeCode.Double">
      <summary>An 8-byte floating point type.</summary>
    </member>
    <member name="F:System.Reflection.Metadata.PrimitiveSerializationTypeCode.Int16">
      <summary>A signed 2-byte integer type.</summary>
    </member>
    <member name="F:System.Reflection.Metadata.PrimitiveSerializationTypeCode.Int32">
      <summary>A signed 4-byte integer type.</summary>
    </member>
    <member name="F:System.Reflection.Metadata.PrimitiveSerializationTypeCode.Int64">
      <summary>A signed 8-byte integer type.</summary>
    </member>
    <member name="F:System.Reflection.Metadata.PrimitiveSerializationTypeCode.SByte">
      <summary>A signed 1-byte integer type.</summary>
    </member>
    <member name="F:System.Reflection.Metadata.PrimitiveSerializationTypeCode.Single">
      <summary>A 4-byte floating point type.</summary>
    </member>
    <member name="F:System.Reflection.Metadata.PrimitiveSerializationTypeCode.String">
      <summary>A <see cref="T:System.String" /> type.</summary>
    </member>
    <member name="F:System.Reflection.Metadata.PrimitiveSerializationTypeCode.UInt16">
      <summary>An unsigned 2-byte integer type.</summary>
    </member>
    <member name="F:System.Reflection.Metadata.PrimitiveSerializationTypeCode.UInt32">
      <summary>An unsigned 4-byte integer type.</summary>
    </member>
    <member name="F:System.Reflection.Metadata.PrimitiveSerializationTypeCode.UInt64">
      <summary>An unsigned 8-byte integer type.</summary>
    </member>
    <member name="T:System.Reflection.Metadata.PrimitiveTypeCode">
      <summary>Specifies constants that define primitive types found in metadata signatures.</summary>
    </member>
    <member name="F:System.Reflection.Metadata.PrimitiveTypeCode.Boolean">
      <summary>A <see cref="T:System.Boolean" /> type.</summary>
    </member>
    <member name="F:System.Reflection.Metadata.PrimitiveTypeCode.Byte">
      <summary>A <see cref="T:System.Byte" /> type.</summary>
    </member>
    <member name="F:System.Reflection.Metadata.PrimitiveTypeCode.Char">
      <summary>A <see cref="T:System.Char" /> type.</summary>
    </member>
    <member name="F:System.Reflection.Metadata.PrimitiveTypeCode.Double">
      <summary>A <see cref="T:System.Double" /> type.</summary>
    </member>
    <member name="F:System.Reflection.Metadata.PrimitiveTypeCode.Int16">
      <summary>An <see cref="T:System.Int16" /> type.</summary>
    </member>
    <member name="F:System.Reflection.Metadata.PrimitiveTypeCode.Int32">
      <summary>An <see cref="T:System.Int32" /> type.</summary>
    </member>
    <member name="F:System.Reflection.Metadata.PrimitiveTypeCode.Int64">
      <summary>An <see cref="T:System.Int64" /> type.</summary>
    </member>
    <member name="F:System.Reflection.Metadata.PrimitiveTypeCode.IntPtr">
      <summary>An <see cref="T:System.IntPtr" /> type.</summary>
    </member>
    <member name="F:System.Reflection.Metadata.PrimitiveTypeCode.Object">
      <summary>An <see cref="T:System.Object" /> type.</summary>
    </member>
    <member name="F:System.Reflection.Metadata.PrimitiveTypeCode.SByte">
      <summary>An <see cref="T:System.SByte" /> type.</summary>
    </member>
    <member name="F:System.Reflection.Metadata.PrimitiveTypeCode.Single">
      <summary>A <see cref="T:System.Single" /> type.</summary>
    </member>
    <member name="F:System.Reflection.Metadata.PrimitiveTypeCode.String">
      <summary>A <see cref="T:System.String" /> type.</summary>
    </member>
    <member name="F:System.Reflection.Metadata.PrimitiveTypeCode.TypedReference">
      <summary>A typed reference.</summary>
    </member>
    <member name="F:System.Reflection.Metadata.PrimitiveTypeCode.UInt16">
      <summary>A <see cref="T:System.UInt16" /> type.</summary>
    </member>
    <member name="F:System.Reflection.Metadata.PrimitiveTypeCode.UInt32">
      <summary>A <see cref="T:System.UInt32" /> type.</summary>
    </member>
    <member name="F:System.Reflection.Metadata.PrimitiveTypeCode.UInt64">
      <summary>A <see cref="T:System.UInt64" /> type.</summary>
    </member>
    <member name="F:System.Reflection.Metadata.PrimitiveTypeCode.UIntPtr">
      <summary>A <see cref="T:System.UIntPtr" /> type.</summary>
    </member>
    <member name="F:System.Reflection.Metadata.PrimitiveTypeCode.Void">
      <summary>A <see cref="T:System.Void" /> type.</summary>
    </member>
    <member name="T:System.Reflection.Metadata.PropertyAccessors" />
    <member name="P:System.Reflection.Metadata.PropertyAccessors.Getter" />
    <member name="P:System.Reflection.Metadata.PropertyAccessors.Others" />
    <member name="P:System.Reflection.Metadata.PropertyAccessors.Setter" />
    <member name="T:System.Reflection.Metadata.PropertyDefinition" />
    <member name="P:System.Reflection.Metadata.PropertyDefinition.Attributes" />
    <member name="M:System.Reflection.Metadata.PropertyDefinition.DecodeSignature``2(System.Reflection.Metadata.ISignatureTypeProvider{``0,``1},``1)">
      <param name="provider" />
      <param name="genericContext" />
      <typeparam name="TType" />
      <typeparam name="TGenericContext" />
    </member>
    <member name="M:System.Reflection.Metadata.PropertyDefinition.GetAccessors" />
    <member name="M:System.Reflection.Metadata.PropertyDefinition.GetCustomAttributes" />
    <member name="M:System.Reflection.Metadata.PropertyDefinition.GetDefaultValue" />
    <member name="P:System.Reflection.Metadata.PropertyDefinition.Name" />
    <member name="P:System.Reflection.Metadata.PropertyDefinition.Signature" />
    <member name="T:System.Reflection.Metadata.PropertyDefinitionHandle" />
    <member name="M:System.Reflection.Metadata.PropertyDefinitionHandle.Equals(System.Object)">
      <param name="obj" />
    </member>
    <member name="M:System.Reflection.Metadata.PropertyDefinitionHandle.Equals(System.Reflection.Metadata.PropertyDefinitionHandle)">
      <param name="other" />
    </member>
    <member name="M:System.Reflection.Metadata.PropertyDefinitionHandle.GetHashCode" />
    <member name="P:System.Reflection.Metadata.PropertyDefinitionHandle.IsNil" />
    <member name="M:System.Reflection.Metadata.PropertyDefinitionHandle.op_Equality(System.Reflection.Metadata.PropertyDefinitionHandle,System.Reflection.Metadata.PropertyDefinitionHandle)">
      <param name="left" />
      <param name="right" />
    </member>
    <member name="M:System.Reflection.Metadata.PropertyDefinitionHandle.op_Explicit(System.Reflection.Metadata.EntityHandle)~System.Reflection.Metadata.PropertyDefinitionHandle">
      <param name="handle" />
    </member>
    <member name="M:System.Reflection.Metadata.PropertyDefinitionHandle.op_Explicit(System.Reflection.Metadata.Handle)~System.Reflection.Metadata.PropertyDefinitionHandle">
      <param name="handle" />
    </member>
    <member name="M:System.Reflection.Metadata.PropertyDefinitionHandle.op_Implicit(System.Reflection.Metadata.PropertyDefinitionHandle)~System.Reflection.Metadata.EntityHandle">
      <param name="handle" />
    </member>
    <member name="M:System.Reflection.Metadata.PropertyDefinitionHandle.op_Implicit(System.Reflection.Metadata.PropertyDefinitionHandle)~System.Reflection.Metadata.Handle">
      <param name="handle" />
    </member>
    <member name="M:System.Reflection.Metadata.PropertyDefinitionHandle.op_Inequality(System.Reflection.Metadata.PropertyDefinitionHandle,System.Reflection.Metadata.PropertyDefinitionHandle)">
      <param name="left" />
      <param name="right" />
    </member>
    <member name="T:System.Reflection.Metadata.PropertyDefinitionHandleCollection" />
    <member name="P:System.Reflection.Metadata.PropertyDefinitionHandleCollection.Count" />
    <member name="T:System.Reflection.Metadata.PropertyDefinitionHandleCollection.Enumerator" />
    <member name="P:System.Reflection.Metadata.PropertyDefinitionHandleCollection.Enumerator.Current" />
    <member name="M:System.Reflection.Metadata.PropertyDefinitionHandleCollection.Enumerator.MoveNext" />
    <member name="P:System.Reflection.Metadata.PropertyDefinitionHandleCollection.Enumerator.System#Collections#IEnumerator#Current" />
    <member name="M:System.Reflection.Metadata.PropertyDefinitionHandleCollection.Enumerator.System#Collections#IEnumerator#Reset" />
    <member name="M:System.Reflection.Metadata.PropertyDefinitionHandleCollection.Enumerator.System#IDisposable#Dispose" />
    <member name="M:System.Reflection.Metadata.PropertyDefinitionHandleCollection.GetEnumerator" />
    <member name="M:System.Reflection.Metadata.PropertyDefinitionHandleCollection.System#Collections#Generic#IEnumerable{System#Reflection#Metadata#PropertyDefinitionHandle}#GetEnumerator" />
    <member name="M:System.Reflection.Metadata.PropertyDefinitionHandleCollection.System#Collections#IEnumerable#GetEnumerator" />
    <member name="T:System.Reflection.Metadata.ReservedBlob`1">
      <summary>Represents a handle and a corresponding blob on a metadata heap that was reserved for future content update.</summary>
      <typeparam name="THandle" />
    </member>
    <member name="P:System.Reflection.Metadata.ReservedBlob`1.Content" />
    <member name="M:System.Reflection.Metadata.ReservedBlob`1.CreateWriter">
      <summary>Returns a <see cref="T:System.Reflection.Metadata.BlobWriter" /> to be used to update the content.</summary>
      <returns>A blob writer to be used to update the content.</returns>
    </member>
    <member name="P:System.Reflection.Metadata.ReservedBlob`1.Handle">
      <summary>Gets the reserved blob handle.</summary>
      <returns>The reserved bloc handle.</returns>
    </member>
    <member name="T:System.Reflection.Metadata.SequencePoint" />
    <member name="P:System.Reflection.Metadata.SequencePoint.Document" />
    <member name="P:System.Reflection.Metadata.SequencePoint.EndColumn" />
    <member name="P:System.Reflection.Metadata.SequencePoint.EndLine" />
    <member name="M:System.Reflection.Metadata.SequencePoint.Equals(System.Object)">
      <param name="obj" />
    </member>
    <member name="M:System.Reflection.Metadata.SequencePoint.Equals(System.Reflection.Metadata.SequencePoint)">
      <param name="other" />
    </member>
    <member name="M:System.Reflection.Metadata.SequencePoint.GetHashCode" />
    <member name="F:System.Reflection.Metadata.SequencePoint.HiddenLine" />
    <member name="P:System.Reflection.Metadata.SequencePoint.IsHidden" />
    <member name="P:System.Reflection.Metadata.SequencePoint.Offset" />
    <member name="P:System.Reflection.Metadata.SequencePoint.StartColumn" />
    <member name="P:System.Reflection.Metadata.SequencePoint.StartLine" />
    <member name="T:System.Reflection.Metadata.SequencePointCollection" />
    <member name="T:System.Reflection.Metadata.SequencePointCollection.Enumerator" />
    <member name="P:System.Reflection.Metadata.SequencePointCollection.Enumerator.Current" />
    <member name="M:System.Reflection.Metadata.SequencePointCollection.Enumerator.MoveNext" />
    <member name="M:System.Reflection.Metadata.SequencePointCollection.Enumerator.Reset" />
    <member name="P:System.Reflection.Metadata.SequencePointCollection.Enumerator.System#Collections#IEnumerator#Current" />
    <member name="M:System.Reflection.Metadata.SequencePointCollection.Enumerator.System#IDisposable#Dispose" />
    <member name="M:System.Reflection.Metadata.SequencePointCollection.GetEnumerator" />
    <member name="M:System.Reflection.Metadata.SequencePointCollection.System#Collections#Generic#IEnumerable{System#Reflection#Metadata#SequencePoint}#GetEnumerator" />
    <member name="M:System.Reflection.Metadata.SequencePointCollection.System#Collections#IEnumerable#GetEnumerator" />
    <member name="T:System.Reflection.Metadata.SerializationTypeCode">
      <summary>Specifies type codes used to encode the types of values in a <see cref="T:System.Reflection.Metadata.CustomAttributeValue`1" /> blob.</summary>
    </member>
    <member name="F:System.Reflection.Metadata.SerializationTypeCode.Boolean">
      <summary>A value equivalent to <see cref="F:System.Reflection.Metadata.SignatureTypeCode.Boolean" />.</summary>
    </member>
    <member name="F:System.Reflection.Metadata.SerializationTypeCode.Byte">
      <summary>A value equivalent to <see cref="F:System.Reflection.Metadata.SignatureTypeCode.Byte" />.</summary>
    </member>
    <member name="F:System.Reflection.Metadata.SerializationTypeCode.Char">
      <summary>A value equivalent to <see cref="F:System.Reflection.Metadata.SignatureTypeCode.Char" />.</summary>
    </member>
    <member name="F:System.Reflection.Metadata.SerializationTypeCode.Double">
      <summary>A value equivalent to <see cref="F:System.Reflection.Metadata.SignatureTypeCode.Double" />.</summary>
    </member>
    <member name="F:System.Reflection.Metadata.SerializationTypeCode.Enum">
      <summary>The attribute argument is an Enum instance.</summary>
    </member>
    <member name="F:System.Reflection.Metadata.SerializationTypeCode.Int16">
      <summary>A value equivalent to <see cref="F:System.Reflection.Metadata.SignatureTypeCode.Int16" />.</summary>
    </member>
    <member name="F:System.Reflection.Metadata.SerializationTypeCode.Int32">
      <summary>A value equivalent to <see cref="F:System.Reflection.Metadata.SignatureTypeCode.Int32" />.</summary>
    </member>
    <member name="F:System.Reflection.Metadata.SerializationTypeCode.Int64">
      <summary>A value equivalent to <see cref="F:System.Reflection.Metadata.SignatureTypeCode.Int64" />.</summary>
    </member>
    <member name="F:System.Reflection.Metadata.SerializationTypeCode.Invalid">
      <summary>A value equivalent to <see cref="F:System.Reflection.Metadata.SignatureTypeCode.Invalid" />.</summary>
    </member>
    <member name="F:System.Reflection.Metadata.SerializationTypeCode.SByte">
      <summary>A value equivalent to <see cref="F:System.Reflection.Metadata.SignatureTypeCode.SByte" />.</summary>
    </member>
    <member name="F:System.Reflection.Metadata.SerializationTypeCode.Single">
      <summary>A value equivalent to <see cref="F:System.Reflection.Metadata.SignatureTypeCode.Single" />.</summary>
    </member>
    <member name="F:System.Reflection.Metadata.SerializationTypeCode.String">
      <summary>A value equivalent to <see cref="F:System.Reflection.Metadata.SignatureTypeCode.String" />.</summary>
    </member>
    <member name="F:System.Reflection.Metadata.SerializationTypeCode.SZArray">
      <summary>A value equivalent to <see cref="F:System.Reflection.Metadata.SignatureTypeCode.SZArray" />.</summary>
    </member>
    <member name="F:System.Reflection.Metadata.SerializationTypeCode.TaggedObject">
      <summary>The attribute argument is "boxed" (passed to a parameter, field, or property of type object) and carries type information in the attribute blob.</summary>
    </member>
    <member name="F:System.Reflection.Metadata.SerializationTypeCode.Type">
      <summary>The attribute argument is a <see cref="T:System.Type" /> instance.</summary>
    </member>
    <member name="F:System.Reflection.Metadata.SerializationTypeCode.UInt16">
      <summary>A value equivalent to <see cref="F:System.Reflection.Metadata.SignatureTypeCode.UInt16" />.</summary>
    </member>
    <member name="F:System.Reflection.Metadata.SerializationTypeCode.UInt32">
      <summary>A value equivalent to <see cref="F:System.Reflection.Metadata.SignatureTypeCode.UInt32" />.</summary>
    </member>
    <member name="F:System.Reflection.Metadata.SerializationTypeCode.UInt64">
      <summary>A value equivalent to <see cref="F:System.Reflection.Metadata.SignatureTypeCode.UInt64" />.</summary>
    </member>
    <member name="T:System.Reflection.Metadata.SignatureAttributes">
      <summary>Specifies additional flags that can be applied to method signatures. The underlying values of the fields in this type correspond to the representation in the leading signature byte represented by a <see cref="T:System.Reflection.Metadata.SignatureHeader" /> structure.</summary>
    </member>
    <member name="F:System.Reflection.Metadata.SignatureAttributes.ExplicitThis">
      <summary>Indicates the first explicitly declared parameter that represents the instance pointer.</summary>
    </member>
    <member name="F:System.Reflection.Metadata.SignatureAttributes.Generic">
      <summary>A generic method.</summary>
    </member>
    <member name="F:System.Reflection.Metadata.SignatureAttributes.Instance">
      <summary>An instance method.</summary>
    </member>
    <member name="F:System.Reflection.Metadata.SignatureAttributes.None">
      <summary>No flags.</summary>
    </member>
    <member name="T:System.Reflection.Metadata.SignatureCallingConvention">
      <summary>Specifies how arguments in a given signature are passed from the caller to the callee. The underlying values of the fields in this type correspond to the representation in the leading signature byte represented by a <see cref="T:System.Reflection.Metadata.SignatureHeader" /> structure.</summary>
    </member>
    <member name="F:System.Reflection.Metadata.SignatureCallingConvention.CDecl">
      <summary>An unmanaged C/C++ style calling convention where the call stack is cleaned by the caller.</summary>
    </member>
    <member name="F:System.Reflection.Metadata.SignatureCallingConvention.Default">
      <summary>A managed calling convention with a fixed-length argument list.</summary>
    </member>
    <member name="F:System.Reflection.Metadata.SignatureCallingConvention.FastCall">
      <summary>An unmanaged calling convention where arguments are passed in registers when possible.</summary>
    </member>
    <member name="F:System.Reflection.Metadata.SignatureCallingConvention.StdCall">
      <summary>An unmanaged calling convention where the call stack is cleaned up by the callee.</summary>
    </member>
    <member name="F:System.Reflection.Metadata.SignatureCallingConvention.ThisCall">
      <summary>An unmanaged C++ style calling convention for calling instance member functions with a fixed argument list.</summary>
    </member>
    <member name="F:System.Reflection.Metadata.SignatureCallingConvention.VarArgs">
      <summary>A managed calling convention for passing extra arguments.</summary>
    </member>
    <member name="T:System.Reflection.Metadata.SignatureHeader">
      <summary>Represents the signature characteristics specified by the leading byte of signature blobs.</summary>
    </member>
    <member name="M:System.Reflection.Metadata.SignatureHeader.#ctor(System.Byte)">
      <summary>Initializes a new instance of the <see cref="T:System.Reflection.Metadata.SignatureHeader" /> structure using the specified byte value.</summary>
      <param name="rawValue">The byte.</param>
    </member>
    <member name="M:System.Reflection.Metadata.SignatureHeader.#ctor(System.Reflection.Metadata.SignatureKind,System.Reflection.Metadata.SignatureCallingConvention,System.Reflection.Metadata.SignatureAttributes)">
      <summary>Initializes a new instance of the <see cref="T:System.Reflection.Metadata.SignatureHeader" /> structure using the specified signature kind, calling convention and signature attributes.</summary>
      <param name="kind">The signature kind.</param>
      <param name="convention">The calling convention.</param>
      <param name="attributes">The signature attributes.</param>
    </member>
    <member name="P:System.Reflection.Metadata.SignatureHeader.Attributes">
      <summary>Gets the signature attributes.</summary>
      <returns>The attributes.</returns>
    </member>
    <member name="P:System.Reflection.Metadata.SignatureHeader.CallingConvention">
      <summary>Gets the calling convention.</summary>
      <returns>The calling convention.</returns>
    </member>
    <member name="F:System.Reflection.Metadata.SignatureHeader.CallingConventionOrKindMask">
      <summary>Gets the mask value for the calling convention or signature kind. The default <see cref="F:System.Reflection.Metadata.SignatureHeader.CallingConventionOrKindMask" /> value is 15 (0x0F).</summary>
    </member>
    <member name="M:System.Reflection.Metadata.SignatureHeader.Equals(System.Object)">
      <summary>Compares the specified object with this <see cref="T:System.Reflection.Metadata.SignatureHeader" /> for equality.</summary>
      <param name="obj">The object to compare.</param>
      <returns>
        <see langword="true" /> if the objects are equal; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Reflection.Metadata.SignatureHeader.Equals(System.Reflection.Metadata.SignatureHeader)">
      <summary>Compares two <see cref="T:System.Reflection.Metadata.SignatureHeader" /> values for equality.</summary>
      <param name="other">The value to compare.</param>
      <returns>
        <see langword="true" /> if the values are equal; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Reflection.Metadata.SignatureHeader.GetHashCode">
      <summary>Gets a hash code for the current object.</summary>
      <returns>A hash code for the current object.</returns>
    </member>
    <member name="P:System.Reflection.Metadata.SignatureHeader.HasExplicitThis">
      <summary>Gets a value that indicates whether this <see cref="T:System.Reflection.Metadata.SignatureHeader" /> structure has the <see cref="F:System.Reflection.Metadata.SignatureAttributes.ExplicitThis" /> signature attribute.</summary>
      <returns>
        <see langword="true" /> if the <see cref="F:System.Reflection.Metadata.SignatureAttributes.ExplicitThis" /> attribute is present; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Reflection.Metadata.SignatureHeader.IsGeneric">
      <summary>Gets a value that indicates whether this <see cref="T:System.Reflection.Metadata.SignatureHeader" /> structure has the <see cref="F:System.Reflection.Metadata.SignatureAttributes.Generic" /> signature attribute.</summary>
      <returns>
        <see langword="true" /> if the <see cref="F:System.Reflection.Metadata.SignatureAttributes.Generic" /> attribute is present; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Reflection.Metadata.SignatureHeader.IsInstance">
      <summary>Gets a value that indicates whether this <see cref="T:System.Reflection.Metadata.SignatureHeader" /> structure has the <see cref="F:System.Reflection.Metadata.SignatureAttributes.Instance" /> signature attribute.</summary>
      <returns>
        <see langword="true" /> if the <see cref="F:System.Reflection.Metadata.SignatureAttributes.Instance" /> attribute is present; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Reflection.Metadata.SignatureHeader.Kind">
      <summary>Gets the signature kind.</summary>
      <returns>The signature kind.</returns>
    </member>
    <member name="M:System.Reflection.Metadata.SignatureHeader.op_Equality(System.Reflection.Metadata.SignatureHeader,System.Reflection.Metadata.SignatureHeader)">
      <summary>Compares two <see cref="T:System.Reflection.Metadata.SignatureHeader" /> values for equality.</summary>
      <param name="left">The first value to compare.</param>
      <param name="right">The second value to compare.</param>
      <returns>
        <see langword="true" /> if the values are equal; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Reflection.Metadata.SignatureHeader.op_Inequality(System.Reflection.Metadata.SignatureHeader,System.Reflection.Metadata.SignatureHeader)">
      <summary>Determines whether two <see cref="T:System.Reflection.Metadata.SignatureHeader" /> values are unequal.</summary>
      <param name="left">The first value to compare.</param>
      <param name="right">The second value to compare.</param>
      <returns>
        <see langword="true" /> if the values are unequal; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Reflection.Metadata.SignatureHeader.RawValue">
      <summary>Gets the raw value of the header byte.</summary>
      <returns>The raw value of the header byte.</returns>
    </member>
    <member name="M:System.Reflection.Metadata.SignatureHeader.ToString">
      <summary>Returns a string that represents the current object.</summary>
      <returns>A string that represents the current object.</returns>
    </member>
    <member name="T:System.Reflection.Metadata.SignatureKind">
      <summary>Specifies the signature kind. The underlying values of the fields in this type correspond to the representation in the leading signature byte represented by a <see cref="T:System.Reflection.Metadata.SignatureHeader" /> structure.</summary>
    </member>
    <member name="F:System.Reflection.Metadata.SignatureKind.Field">
      <summary>A field signature.</summary>
    </member>
    <member name="F:System.Reflection.Metadata.SignatureKind.LocalVariables">
      <summary>A local variables signature.</summary>
    </member>
    <member name="F:System.Reflection.Metadata.SignatureKind.Method">
      <summary>A method reference, method definition, or standalone method signature.</summary>
    </member>
    <member name="F:System.Reflection.Metadata.SignatureKind.MethodSpecification">
      <summary>A method specification signature.</summary>
    </member>
    <member name="F:System.Reflection.Metadata.SignatureKind.Property">
      <summary>A property signature.</summary>
    </member>
    <member name="T:System.Reflection.Metadata.SignatureTypeCode">
      <summary>Specifies constants that define type codes used in signature encoding.</summary>
    </member>
    <member name="F:System.Reflection.Metadata.SignatureTypeCode.Array">
      <summary>Represents a generalized <see cref="T:System.Array" /> in signatures.</summary>
    </member>
    <member name="F:System.Reflection.Metadata.SignatureTypeCode.Boolean">
      <summary>Represents a <see cref="T:System.Boolean" /> in signatures.</summary>
    </member>
    <member name="F:System.Reflection.Metadata.SignatureTypeCode.ByReference">
      <summary>Represents managed pointers (byref return values and parameters) in signatures. It is followed in the blob by the signature encoding of the underlying type.</summary>
    </member>
    <member name="F:System.Reflection.Metadata.SignatureTypeCode.Byte">
      <summary>Represents a <see cref="T:System.Byte" /> in signatures.</summary>
    </member>
    <member name="F:System.Reflection.Metadata.SignatureTypeCode.Char">
      <summary>Represents a <see cref="T:System.Char" /> in signatures.</summary>
    </member>
    <member name="F:System.Reflection.Metadata.SignatureTypeCode.Double">
      <summary>Represents a <see cref="T:System.Double" /> in signatures.</summary>
    </member>
    <member name="F:System.Reflection.Metadata.SignatureTypeCode.FunctionPointer">
      <summary>Represents function pointer types in signatures.</summary>
    </member>
    <member name="F:System.Reflection.Metadata.SignatureTypeCode.GenericMethodParameter">
      <summary>Represents a generic method parameter used within a signature.</summary>
    </member>
    <member name="F:System.Reflection.Metadata.SignatureTypeCode.GenericTypeInstance">
      <summary>Represents the instantiation of a generic type in signatures.</summary>
    </member>
    <member name="F:System.Reflection.Metadata.SignatureTypeCode.GenericTypeParameter">
      <summary>Represents a generic type parameter used within a signature.</summary>
    </member>
    <member name="F:System.Reflection.Metadata.SignatureTypeCode.Int16">
      <summary>Represents an <see cref="T:System.Int16" /> in signatures.</summary>
    </member>
    <member name="F:System.Reflection.Metadata.SignatureTypeCode.Int32">
      <summary>Represents an <see cref="T:System.Int32" /> in signatures.</summary>
    </member>
    <member name="F:System.Reflection.Metadata.SignatureTypeCode.Int64">
      <summary>Represents an <see cref="T:System.Int64" /> in signatures.</summary>
    </member>
    <member name="F:System.Reflection.Metadata.SignatureTypeCode.IntPtr">
      <summary>Represents an <see cref="T:System.IntPtr" /> in signatures.</summary>
    </member>
    <member name="F:System.Reflection.Metadata.SignatureTypeCode.Invalid">
      <summary>Represents an invalid or uninitialized type code. It will not appear in valid signatures.</summary>
    </member>
    <member name="F:System.Reflection.Metadata.SignatureTypeCode.Object">
      <summary>Represents an <see cref="T:System.Object" /> in signatures.</summary>
    </member>
    <member name="F:System.Reflection.Metadata.SignatureTypeCode.OptionalModifier">
      <summary>Represents a custom modifier applied to a type within a signature that the caller can ignore.</summary>
    </member>
    <member name="F:System.Reflection.Metadata.SignatureTypeCode.Pinned">
      <summary>Represents a local variable that is pinned by garbage collector.</summary>
    </member>
    <member name="F:System.Reflection.Metadata.SignatureTypeCode.Pointer">
      <summary>Represents an unmanaged pointer in signatures. It is followed in the blob by the signature encoding of the underlying type.</summary>
    </member>
    <member name="F:System.Reflection.Metadata.SignatureTypeCode.RequiredModifier">
      <summary>Represents a custom modifier applied to a type within a signature that the caller must understand.</summary>
    </member>
    <member name="F:System.Reflection.Metadata.SignatureTypeCode.SByte">
      <summary>Represents an <see cref="T:System.SByte" /> in signatures.</summary>
    </member>
    <member name="F:System.Reflection.Metadata.SignatureTypeCode.Sentinel">
      <summary>Represents a marker to indicate the end of fixed arguments and the beginning of variable arguments.</summary>
    </member>
    <member name="F:System.Reflection.Metadata.SignatureTypeCode.Single">
      <summary>Represents a <see cref="T:System.Single" /> in signatures.</summary>
    </member>
    <member name="F:System.Reflection.Metadata.SignatureTypeCode.String">
      <summary>Represents a <see cref="T:System.String" /> in signatures.</summary>
    </member>
    <member name="F:System.Reflection.Metadata.SignatureTypeCode.SZArray">
      <summary>Represents a single dimensional <see cref="T:System.Array" /> with a lower bound of 0.</summary>
    </member>
    <member name="F:System.Reflection.Metadata.SignatureTypeCode.TypedReference">
      <summary>Represents a typed reference in signatures.</summary>
    </member>
    <member name="F:System.Reflection.Metadata.SignatureTypeCode.TypeHandle">
      <summary>Precedes a type <see cref="T:System.Reflection.Metadata.EntityHandle" /> in signatures. In raw metadata, this is encoded as either ELEMENT_TYPE_CLASS (0x12) for reference types or ELEMENT_TYPE_VALUETYPE (0x11) for value types. This is collapsed to a single code because Windows Runtime projections can project from class to value type or vice-versa, and the raw code is misleading in those cases.</summary>
    </member>
    <member name="F:System.Reflection.Metadata.SignatureTypeCode.UInt16">
      <summary>Represents a <see cref="T:System.UInt16" /> in signatures.</summary>
    </member>
    <member name="F:System.Reflection.Metadata.SignatureTypeCode.UInt32">
      <summary>Represents a <see cref="T:System.UInt32" /> in signatures.</summary>
    </member>
    <member name="F:System.Reflection.Metadata.SignatureTypeCode.UInt64">
      <summary>Represents a <see cref="T:System.UInt64" /> in signatures.</summary>
    </member>
    <member name="F:System.Reflection.Metadata.SignatureTypeCode.UIntPtr">
      <summary>Represents a <see cref="T:System.UIntPtr" /> in signatures.</summary>
    </member>
    <member name="F:System.Reflection.Metadata.SignatureTypeCode.Void">
      <summary>Represents <see cref="T:System.Void" /> in signatures.</summary>
    </member>
    <member name="T:System.Reflection.Metadata.SignatureTypeKind">
      <summary>Indicates the type definition of the signature.</summary>
    </member>
    <member name="F:System.Reflection.Metadata.SignatureTypeKind.Class">
      <summary>The type definition or reference refers to a class.</summary>
    </member>
    <member name="F:System.Reflection.Metadata.SignatureTypeKind.Unknown">
      <summary>It isn't known in the current context if the type reference or definition is a class or value type.</summary>
    </member>
    <member name="F:System.Reflection.Metadata.SignatureTypeKind.ValueType">
      <summary>The type definition or reference refers to a value type.</summary>
    </member>
    <member name="T:System.Reflection.Metadata.StandaloneSignature" />
    <member name="M:System.Reflection.Metadata.StandaloneSignature.DecodeLocalSignature``2(System.Reflection.Metadata.ISignatureTypeProvider{``0,``1},``1)">
      <param name="provider" />
      <param name="genericContext" />
      <typeparam name="TType" />
      <typeparam name="TGenericContext" />
    </member>
    <member name="M:System.Reflection.Metadata.StandaloneSignature.DecodeMethodSignature``2(System.Reflection.Metadata.ISignatureTypeProvider{``0,``1},``1)">
      <param name="provider" />
      <param name="genericContext" />
      <typeparam name="TType" />
      <typeparam name="TGenericContext" />
    </member>
    <member name="M:System.Reflection.Metadata.StandaloneSignature.GetCustomAttributes" />
    <member name="M:System.Reflection.Metadata.StandaloneSignature.GetKind">
      <summary>Determines the kind of signature, which can be <see cref="F:System.Reflection.Metadata.SignatureKind.Method" /> or <see cref="F:System.Reflection.Metadata.SignatureKind.LocalVariables" />.</summary>
      <returns>An enumeration value that indicates the signature kind.</returns>
      <exception cref="T:System.BadImageFormatException">The signature is invalid.</exception>
    </member>
    <member name="P:System.Reflection.Metadata.StandaloneSignature.Signature">
      <summary>Gets a handle to the signature blob.</summary>
      <returns>A handle to the signature blob.</returns>
    </member>
    <member name="T:System.Reflection.Metadata.StandaloneSignatureHandle" />
    <member name="M:System.Reflection.Metadata.StandaloneSignatureHandle.Equals(System.Object)">
      <param name="obj" />
    </member>
    <member name="M:System.Reflection.Metadata.StandaloneSignatureHandle.Equals(System.Reflection.Metadata.StandaloneSignatureHandle)">
      <param name="other" />
    </member>
    <member name="M:System.Reflection.Metadata.StandaloneSignatureHandle.GetHashCode" />
    <member name="P:System.Reflection.Metadata.StandaloneSignatureHandle.IsNil" />
    <member name="M:System.Reflection.Metadata.StandaloneSignatureHandle.op_Equality(System.Reflection.Metadata.StandaloneSignatureHandle,System.Reflection.Metadata.StandaloneSignatureHandle)">
      <param name="left" />
      <param name="right" />
    </member>
    <member name="M:System.Reflection.Metadata.StandaloneSignatureHandle.op_Explicit(System.Reflection.Metadata.EntityHandle)~System.Reflection.Metadata.StandaloneSignatureHandle">
      <param name="handle" />
    </member>
    <member name="M:System.Reflection.Metadata.StandaloneSignatureHandle.op_Explicit(System.Reflection.Metadata.Handle)~System.Reflection.Metadata.StandaloneSignatureHandle">
      <param name="handle" />
    </member>
    <member name="M:System.Reflection.Metadata.StandaloneSignatureHandle.op_Implicit(System.Reflection.Metadata.StandaloneSignatureHandle)~System.Reflection.Metadata.EntityHandle">
      <param name="handle" />
    </member>
    <member name="M:System.Reflection.Metadata.StandaloneSignatureHandle.op_Implicit(System.Reflection.Metadata.StandaloneSignatureHandle)~System.Reflection.Metadata.Handle">
      <param name="handle" />
    </member>
    <member name="M:System.Reflection.Metadata.StandaloneSignatureHandle.op_Inequality(System.Reflection.Metadata.StandaloneSignatureHandle,System.Reflection.Metadata.StandaloneSignatureHandle)">
      <param name="left" />
      <param name="right" />
    </member>
    <member name="T:System.Reflection.Metadata.StandaloneSignatureKind">
      <summary>Indicates whether a <see cref="T:System.Reflection.Metadata.StandaloneSignature" /> represents a standalone method or local variable signature.</summary>
    </member>
    <member name="F:System.Reflection.Metadata.StandaloneSignatureKind.LocalVariables">
      <summary>The <see cref="T:System.Reflection.Metadata.MemberReference" /> references a local variable signature.</summary>
    </member>
    <member name="F:System.Reflection.Metadata.StandaloneSignatureKind.Method">
      <summary>The <see cref="T:System.Reflection.Metadata.StandaloneSignature" /> represents a standalone method signature.</summary>
    </member>
    <member name="T:System.Reflection.Metadata.StringHandle" />
    <member name="M:System.Reflection.Metadata.StringHandle.Equals(System.Object)">
      <param name="obj" />
    </member>
    <member name="M:System.Reflection.Metadata.StringHandle.Equals(System.Reflection.Metadata.StringHandle)">
      <param name="other" />
    </member>
    <member name="M:System.Reflection.Metadata.StringHandle.GetHashCode" />
    <member name="P:System.Reflection.Metadata.StringHandle.IsNil" />
    <member name="M:System.Reflection.Metadata.StringHandle.op_Equality(System.Reflection.Metadata.StringHandle,System.Reflection.Metadata.StringHandle)">
      <param name="left" />
      <param name="right" />
    </member>
    <member name="M:System.Reflection.Metadata.StringHandle.op_Explicit(System.Reflection.Metadata.Handle)~System.Reflection.Metadata.StringHandle">
      <param name="handle" />
    </member>
    <member name="M:System.Reflection.Metadata.StringHandle.op_Implicit(System.Reflection.Metadata.StringHandle)~System.Reflection.Metadata.Handle">
      <param name="handle" />
    </member>
    <member name="M:System.Reflection.Metadata.StringHandle.op_Inequality(System.Reflection.Metadata.StringHandle,System.Reflection.Metadata.StringHandle)">
      <param name="left" />
      <param name="right" />
    </member>
    <member name="T:System.Reflection.Metadata.TypeDefinition" />
    <member name="P:System.Reflection.Metadata.TypeDefinition.Attributes" />
    <member name="P:System.Reflection.Metadata.TypeDefinition.BaseType">
      <summary>Gets the base type of the type definition: either <see cref="T:System.Reflection.Metadata.TypeSpecificationHandle" />, <see cref="T:System.Reflection.Metadata.TypeReferenceHandle" /> or <see cref="T:System.Reflection.Metadata.TypeDefinitionHandle" />.</summary>
      <returns>The base type of the type definition.</returns>
    </member>
    <member name="M:System.Reflection.Metadata.TypeDefinition.GetCustomAttributes" />
    <member name="M:System.Reflection.Metadata.TypeDefinition.GetDeclarativeSecurityAttributes" />
    <member name="M:System.Reflection.Metadata.TypeDefinition.GetDeclaringType">
      <summary>Returns the enclosing type of a specified nested type.</summary>
      <returns>The enclosing type of the specified nested type, or a handle a handle whose <see cref="P:System.Reflection.Metadata.TypeDefinitionHandle.IsNil" /> property is <see langword="true" /> if the type is not nested.</returns>
    </member>
    <member name="M:System.Reflection.Metadata.TypeDefinition.GetEvents" />
    <member name="M:System.Reflection.Metadata.TypeDefinition.GetFields" />
    <member name="M:System.Reflection.Metadata.TypeDefinition.GetGenericParameters" />
    <member name="M:System.Reflection.Metadata.TypeDefinition.GetInterfaceImplementations" />
    <member name="M:System.Reflection.Metadata.TypeDefinition.GetLayout" />
    <member name="M:System.Reflection.Metadata.TypeDefinition.GetMethodImplementations" />
    <member name="M:System.Reflection.Metadata.TypeDefinition.GetMethods" />
    <member name="M:System.Reflection.Metadata.TypeDefinition.GetNestedTypes">
      <summary>Returns an array of types nested in the specified type.</summary>
      <returns>An immutable array of type definition handles that represent types nested in the specified type.</returns>
    </member>
    <member name="M:System.Reflection.Metadata.TypeDefinition.GetProperties" />
    <member name="P:System.Reflection.Metadata.TypeDefinition.IsNested">
      <summary>Gets a value that indicates whether this is a nested type.</summary>
      <returns>
        <see langword="true" /> if it is a nested type, <see langword="false" /> otherwise.</returns>
    </member>
    <member name="P:System.Reflection.Metadata.TypeDefinition.Name">
      <summary>Gets the name of the type.</summary>
      <returns>The name of the type.</returns>
    </member>
    <member name="P:System.Reflection.Metadata.TypeDefinition.Namespace">
      <summary>Gets the full name of the namespace where the type is defined.</summary>
      <returns>The full name of the namespace where the type is defined, or a handle whose <see cref="P:System.Reflection.Metadata.StringHandle.IsNil" /> property is <see langword="true" /> if the type is nested or defined in a root namespace.</returns>
    </member>
    <member name="P:System.Reflection.Metadata.TypeDefinition.NamespaceDefinition">
      <summary>Gets the definition handle of the namespace where the type is defined.</summary>
      <returns>The definition handle of the namespace where the type is defined, or a handle whose <see cref="P:System.Reflection.Metadata.NamespaceDefinitionHandle.IsNil" /> property is <see langword="true" />  if the type is nested or defined in a root namespace.</returns>
    </member>
    <member name="T:System.Reflection.Metadata.TypeDefinitionHandle" />
    <member name="M:System.Reflection.Metadata.TypeDefinitionHandle.Equals(System.Object)">
      <param name="obj" />
    </member>
    <member name="M:System.Reflection.Metadata.TypeDefinitionHandle.Equals(System.Reflection.Metadata.TypeDefinitionHandle)">
      <param name="other" />
    </member>
    <member name="M:System.Reflection.Metadata.TypeDefinitionHandle.GetHashCode" />
    <member name="P:System.Reflection.Metadata.TypeDefinitionHandle.IsNil" />
    <member name="M:System.Reflection.Metadata.TypeDefinitionHandle.op_Equality(System.Reflection.Metadata.TypeDefinitionHandle,System.Reflection.Metadata.TypeDefinitionHandle)">
      <param name="left" />
      <param name="right" />
    </member>
    <member name="M:System.Reflection.Metadata.TypeDefinitionHandle.op_Explicit(System.Reflection.Metadata.EntityHandle)~System.Reflection.Metadata.TypeDefinitionHandle">
      <param name="handle" />
    </member>
    <member name="M:System.Reflection.Metadata.TypeDefinitionHandle.op_Explicit(System.Reflection.Metadata.Handle)~System.Reflection.Metadata.TypeDefinitionHandle">
      <param name="handle" />
    </member>
    <member name="M:System.Reflection.Metadata.TypeDefinitionHandle.op_Implicit(System.Reflection.Metadata.TypeDefinitionHandle)~System.Reflection.Metadata.EntityHandle">
      <param name="handle" />
    </member>
    <member name="M:System.Reflection.Metadata.TypeDefinitionHandle.op_Implicit(System.Reflection.Metadata.TypeDefinitionHandle)~System.Reflection.Metadata.Handle">
      <param name="handle" />
    </member>
    <member name="M:System.Reflection.Metadata.TypeDefinitionHandle.op_Inequality(System.Reflection.Metadata.TypeDefinitionHandle,System.Reflection.Metadata.TypeDefinitionHandle)">
      <param name="left" />
      <param name="right" />
    </member>
    <member name="T:System.Reflection.Metadata.TypeDefinitionHandleCollection">
      <summary>Contains a collection of <see cref="T:System.Reflection.Metadata.TypeDefinitionHandle" /> instances.</summary>
    </member>
    <member name="P:System.Reflection.Metadata.TypeDefinitionHandleCollection.Count" />
    <member name="T:System.Reflection.Metadata.TypeDefinitionHandleCollection.Enumerator" />
    <member name="P:System.Reflection.Metadata.TypeDefinitionHandleCollection.Enumerator.Current" />
    <member name="M:System.Reflection.Metadata.TypeDefinitionHandleCollection.Enumerator.MoveNext" />
    <member name="P:System.Reflection.Metadata.TypeDefinitionHandleCollection.Enumerator.System#Collections#IEnumerator#Current" />
    <member name="M:System.Reflection.Metadata.TypeDefinitionHandleCollection.Enumerator.System#Collections#IEnumerator#Reset" />
    <member name="M:System.Reflection.Metadata.TypeDefinitionHandleCollection.Enumerator.System#IDisposable#Dispose" />
    <member name="M:System.Reflection.Metadata.TypeDefinitionHandleCollection.GetEnumerator" />
    <member name="M:System.Reflection.Metadata.TypeDefinitionHandleCollection.System#Collections#Generic#IEnumerable{System#Reflection#Metadata#TypeDefinitionHandle}#GetEnumerator" />
    <member name="M:System.Reflection.Metadata.TypeDefinitionHandleCollection.System#Collections#IEnumerable#GetEnumerator" />
    <member name="T:System.Reflection.Metadata.TypeLayout" />
    <member name="M:System.Reflection.Metadata.TypeLayout.#ctor(System.Int32,System.Int32)">
      <param name="size" />
      <param name="packingSize" />
    </member>
    <member name="P:System.Reflection.Metadata.TypeLayout.IsDefault" />
    <member name="P:System.Reflection.Metadata.TypeLayout.PackingSize" />
    <member name="P:System.Reflection.Metadata.TypeLayout.Size" />
    <member name="T:System.Reflection.Metadata.TypeReference" />
    <member name="P:System.Reflection.Metadata.TypeReference.Name">
      <summary>Gets the name of the target type.</summary>
      <returns>The name of the target type.</returns>
    </member>
    <member name="P:System.Reflection.Metadata.TypeReference.Namespace">
      <summary>Gets the full name of the namespace where the target type is defined.</summary>
      <returns>The full name of the namespace where the target type is defined, or a handle whose the <see cref="P:System.Reflection.Metadata.StringHandle.IsNil" /> property is <see langword="true" /> if the type is nested or defined in a root namespace,</returns>
    </member>
    <member name="P:System.Reflection.Metadata.TypeReference.ResolutionScope">
      <summary>Gets the resolution scope in which the target type is defined and is uniquely identified by the specified <see cref="P:System.Reflection.Metadata.TypeReference.Namespace" /> and <see cref="P:System.Reflection.Metadata.TypeReference.Name" />.</summary>
      <returns>The resolution scope in which the target type is uniquely defined.</returns>
    </member>
    <member name="T:System.Reflection.Metadata.TypeReferenceHandle" />
    <member name="M:System.Reflection.Metadata.TypeReferenceHandle.Equals(System.Object)">
      <param name="obj" />
    </member>
    <member name="M:System.Reflection.Metadata.TypeReferenceHandle.Equals(System.Reflection.Metadata.TypeReferenceHandle)">
      <param name="other" />
    </member>
    <member name="M:System.Reflection.Metadata.TypeReferenceHandle.GetHashCode" />
    <member name="P:System.Reflection.Metadata.TypeReferenceHandle.IsNil" />
    <member name="M:System.Reflection.Metadata.TypeReferenceHandle.op_Equality(System.Reflection.Metadata.TypeReferenceHandle,System.Reflection.Metadata.TypeReferenceHandle)">
      <param name="left" />
      <param name="right" />
    </member>
    <member name="M:System.Reflection.Metadata.TypeReferenceHandle.op_Explicit(System.Reflection.Metadata.EntityHandle)~System.Reflection.Metadata.TypeReferenceHandle">
      <param name="handle" />
    </member>
    <member name="M:System.Reflection.Metadata.TypeReferenceHandle.op_Explicit(System.Reflection.Metadata.Handle)~System.Reflection.Metadata.TypeReferenceHandle">
      <param name="handle" />
    </member>
    <member name="M:System.Reflection.Metadata.TypeReferenceHandle.op_Implicit(System.Reflection.Metadata.TypeReferenceHandle)~System.Reflection.Metadata.EntityHandle">
      <param name="handle" />
    </member>
    <member name="M:System.Reflection.Metadata.TypeReferenceHandle.op_Implicit(System.Reflection.Metadata.TypeReferenceHandle)~System.Reflection.Metadata.Handle">
      <param name="handle" />
    </member>
    <member name="M:System.Reflection.Metadata.TypeReferenceHandle.op_Inequality(System.Reflection.Metadata.TypeReferenceHandle,System.Reflection.Metadata.TypeReferenceHandle)">
      <param name="left" />
      <param name="right" />
    </member>
    <member name="T:System.Reflection.Metadata.TypeReferenceHandleCollection">
      <summary>Contains a collection of <see cref="T:System.Reflection.Metadata.TypeReferenceHandle" /> instances.</summary>
    </member>
    <member name="P:System.Reflection.Metadata.TypeReferenceHandleCollection.Count" />
    <member name="T:System.Reflection.Metadata.TypeReferenceHandleCollection.Enumerator" />
    <member name="P:System.Reflection.Metadata.TypeReferenceHandleCollection.Enumerator.Current" />
    <member name="M:System.Reflection.Metadata.TypeReferenceHandleCollection.Enumerator.MoveNext" />
    <member name="P:System.Reflection.Metadata.TypeReferenceHandleCollection.Enumerator.System#Collections#IEnumerator#Current" />
    <member name="M:System.Reflection.Metadata.TypeReferenceHandleCollection.Enumerator.System#Collections#IEnumerator#Reset" />
    <member name="M:System.Reflection.Metadata.TypeReferenceHandleCollection.Enumerator.System#IDisposable#Dispose" />
    <member name="M:System.Reflection.Metadata.TypeReferenceHandleCollection.GetEnumerator" />
    <member name="M:System.Reflection.Metadata.TypeReferenceHandleCollection.System#Collections#Generic#IEnumerable{System#Reflection#Metadata#TypeReferenceHandle}#GetEnumerator" />
    <member name="M:System.Reflection.Metadata.TypeReferenceHandleCollection.System#Collections#IEnumerable#GetEnumerator" />
    <member name="T:System.Reflection.Metadata.TypeSpecification" />
    <member name="M:System.Reflection.Metadata.TypeSpecification.DecodeSignature``2(System.Reflection.Metadata.ISignatureTypeProvider{``0,``1},``1)">
      <param name="provider" />
      <param name="genericContext" />
      <typeparam name="TType" />
      <typeparam name="TGenericContext" />
    </member>
    <member name="M:System.Reflection.Metadata.TypeSpecification.GetCustomAttributes" />
    <member name="P:System.Reflection.Metadata.TypeSpecification.Signature" />
    <member name="T:System.Reflection.Metadata.TypeSpecificationHandle" />
    <member name="M:System.Reflection.Metadata.TypeSpecificationHandle.Equals(System.Object)">
      <param name="obj" />
    </member>
    <member name="M:System.Reflection.Metadata.TypeSpecificationHandle.Equals(System.Reflection.Metadata.TypeSpecificationHandle)">
      <param name="other" />
    </member>
    <member name="M:System.Reflection.Metadata.TypeSpecificationHandle.GetHashCode" />
    <member name="P:System.Reflection.Metadata.TypeSpecificationHandle.IsNil" />
    <member name="M:System.Reflection.Metadata.TypeSpecificationHandle.op_Equality(System.Reflection.Metadata.TypeSpecificationHandle,System.Reflection.Metadata.TypeSpecificationHandle)">
      <param name="left" />
      <param name="right" />
    </member>
    <member name="M:System.Reflection.Metadata.TypeSpecificationHandle.op_Explicit(System.Reflection.Metadata.EntityHandle)~System.Reflection.Metadata.TypeSpecificationHandle">
      <param name="handle" />
    </member>
    <member name="M:System.Reflection.Metadata.TypeSpecificationHandle.op_Explicit(System.Reflection.Metadata.Handle)~System.Reflection.Metadata.TypeSpecificationHandle">
      <param name="handle" />
    </member>
    <member name="M:System.Reflection.Metadata.TypeSpecificationHandle.op_Implicit(System.Reflection.Metadata.TypeSpecificationHandle)~System.Reflection.Metadata.EntityHandle">
      <param name="handle" />
    </member>
    <member name="M:System.Reflection.Metadata.TypeSpecificationHandle.op_Implicit(System.Reflection.Metadata.TypeSpecificationHandle)~System.Reflection.Metadata.Handle">
      <param name="handle" />
    </member>
    <member name="M:System.Reflection.Metadata.TypeSpecificationHandle.op_Inequality(System.Reflection.Metadata.TypeSpecificationHandle,System.Reflection.Metadata.TypeSpecificationHandle)">
      <param name="left" />
      <param name="right" />
    </member>
    <member name="T:System.Reflection.Metadata.UserStringHandle">
      <summary>Represents a handle to the user string heap.</summary>
    </member>
    <member name="M:System.Reflection.Metadata.UserStringHandle.Equals(System.Object)">
      <param name="obj" />
    </member>
    <member name="M:System.Reflection.Metadata.UserStringHandle.Equals(System.Reflection.Metadata.UserStringHandle)">
      <param name="other" />
    </member>
    <member name="M:System.Reflection.Metadata.UserStringHandle.GetHashCode" />
    <member name="P:System.Reflection.Metadata.UserStringHandle.IsNil" />
    <member name="M:System.Reflection.Metadata.UserStringHandle.op_Equality(System.Reflection.Metadata.UserStringHandle,System.Reflection.Metadata.UserStringHandle)">
      <param name="left" />
      <param name="right" />
    </member>
    <member name="M:System.Reflection.Metadata.UserStringHandle.op_Explicit(System.Reflection.Metadata.Handle)~System.Reflection.Metadata.UserStringHandle">
      <param name="handle" />
    </member>
    <member name="M:System.Reflection.Metadata.UserStringHandle.op_Implicit(System.Reflection.Metadata.UserStringHandle)~System.Reflection.Metadata.Handle">
      <param name="handle" />
    </member>
    <member name="M:System.Reflection.Metadata.UserStringHandle.op_Inequality(System.Reflection.Metadata.UserStringHandle,System.Reflection.Metadata.UserStringHandle)">
      <param name="left" />
      <param name="right" />
    </member>
    <member name="T:System.Reflection.MethodImportAttributes" />
    <member name="F:System.Reflection.MethodImportAttributes.BestFitMappingDisable" />
    <member name="F:System.Reflection.MethodImportAttributes.BestFitMappingEnable" />
    <member name="F:System.Reflection.MethodImportAttributes.BestFitMappingMask" />
    <member name="F:System.Reflection.MethodImportAttributes.CallingConventionCDecl" />
    <member name="F:System.Reflection.MethodImportAttributes.CallingConventionFastCall" />
    <member name="F:System.Reflection.MethodImportAttributes.CallingConventionMask" />
    <member name="F:System.Reflection.MethodImportAttributes.CallingConventionStdCall" />
    <member name="F:System.Reflection.MethodImportAttributes.CallingConventionThisCall" />
    <member name="F:System.Reflection.MethodImportAttributes.CallingConventionWinApi" />
    <member name="F:System.Reflection.MethodImportAttributes.CharSetAnsi" />
    <member name="F:System.Reflection.MethodImportAttributes.CharSetAuto" />
    <member name="F:System.Reflection.MethodImportAttributes.CharSetMask" />
    <member name="F:System.Reflection.MethodImportAttributes.CharSetUnicode" />
    <member name="F:System.Reflection.MethodImportAttributes.ExactSpelling" />
    <member name="F:System.Reflection.MethodImportAttributes.None" />
    <member name="F:System.Reflection.MethodImportAttributes.SetLastError" />
    <member name="F:System.Reflection.MethodImportAttributes.ThrowOnUnmappableCharDisable" />
    <member name="F:System.Reflection.MethodImportAttributes.ThrowOnUnmappableCharEnable" />
    <member name="F:System.Reflection.MethodImportAttributes.ThrowOnUnmappableCharMask" />
    <member name="T:System.Reflection.MethodSemanticsAttributes" />
    <member name="F:System.Reflection.MethodSemanticsAttributes.Adder">
      <summary>Used to add a handler for an event. Corresponds to the <see langword="AddOn" /> flag in the Ecma 335 CLI specification.
CLS-compliant adders are named the with <see langword="add_" /> prefix.</summary>
    </member>
    <member name="F:System.Reflection.MethodSemanticsAttributes.Getter">
      <summary>Reads the value of the property.
CLS-compliant getters are named with `get_` prefix.</summary>
    </member>
    <member name="F:System.Reflection.MethodSemanticsAttributes.Other">
      <summary>Other method for a property (not a getter or setter) or an event (not an adder, remover, or raiser).</summary>
    </member>
    <member name="F:System.Reflection.MethodSemanticsAttributes.Raiser">
      <summary>Used to indicate that an event has occurred. Corresponds to the <see langword="Fire" /> flag in the Ecma 335 CLI specification.
 CLS-compliant raisers are named with the <see langword="raise_" /> prefix.</summary>
    </member>
    <member name="F:System.Reflection.MethodSemanticsAttributes.Remover">
      <summary>Used to remove a handler for an event. Corresponds to the <see langword="RemoveOn" /> flag in the Ecma 335 CLI specification.
CLS-compliant removers are named with the <see langword="remove_" /> prefix.</summary>
    </member>
    <member name="F:System.Reflection.MethodSemanticsAttributes.Setter">
      <summary>Used to modify the value of the property.
CLS-compliant setters are named with the <see langword="set_" /> prefix.</summary>
    </member>
    <member name="T:System.Reflection.PortableExecutable.Characteristics" />
    <member name="F:System.Reflection.PortableExecutable.Characteristics.AggressiveWSTrim" />
    <member name="F:System.Reflection.PortableExecutable.Characteristics.Bit32Machine" />
    <member name="F:System.Reflection.PortableExecutable.Characteristics.BytesReversedHi" />
    <member name="F:System.Reflection.PortableExecutable.Characteristics.BytesReversedLo" />
    <member name="F:System.Reflection.PortableExecutable.Characteristics.DebugStripped" />
    <member name="F:System.Reflection.PortableExecutable.Characteristics.Dll" />
    <member name="F:System.Reflection.PortableExecutable.Characteristics.ExecutableImage" />
    <member name="F:System.Reflection.PortableExecutable.Characteristics.LargeAddressAware" />
    <member name="F:System.Reflection.PortableExecutable.Characteristics.LineNumsStripped" />
    <member name="F:System.Reflection.PortableExecutable.Characteristics.LocalSymsStripped" />
    <member name="F:System.Reflection.PortableExecutable.Characteristics.NetRunFromSwap" />
    <member name="F:System.Reflection.PortableExecutable.Characteristics.RelocsStripped" />
    <member name="F:System.Reflection.PortableExecutable.Characteristics.RemovableRunFromSwap" />
    <member name="F:System.Reflection.PortableExecutable.Characteristics.System" />
    <member name="F:System.Reflection.PortableExecutable.Characteristics.UpSystemOnly" />
    <member name="T:System.Reflection.PortableExecutable.CodeViewDebugDirectoryData">
      <summary>Provides information about a Program Debug Database (PDB) file.</summary>
    </member>
    <member name="P:System.Reflection.PortableExecutable.CodeViewDebugDirectoryData.Age">
      <summary>The iteration of the PDB. The first iteration is 1. The iteration is incremented each time the PDB content is augmented.</summary>
    </member>
    <member name="P:System.Reflection.PortableExecutable.CodeViewDebugDirectoryData.Guid">
      <summary>The Globally Unique Identifier (GUID) of the associated PDB.</summary>
    </member>
    <member name="P:System.Reflection.PortableExecutable.CodeViewDebugDirectoryData.Path">
      <summary>The path to the .pdb file that contains debug information for the PE/COFF file.</summary>
    </member>
    <member name="T:System.Reflection.PortableExecutable.CoffHeader">
      <summary>Represents the header of a COFF file.</summary>
    </member>
    <member name="P:System.Reflection.PortableExecutable.CoffHeader.Characteristics">
      <summary>Gets the flags that indicate the attributes of the file.</summary>
      <returns>The flags that indicate the attributes of the file.</returns>
    </member>
    <member name="P:System.Reflection.PortableExecutable.CoffHeader.Machine">
      <summary>Gets the type of the target machine.</summary>
      <returns>The type of the target machine.</returns>
    </member>
    <member name="P:System.Reflection.PortableExecutable.CoffHeader.NumberOfSections">
      <summary>Gets the number of sections. This indicates the size of the section table, which immediately follows the headers.</summary>
      <returns>The number of sections.</returns>
    </member>
    <member name="P:System.Reflection.PortableExecutable.CoffHeader.NumberOfSymbols">
      <summary>Gets the number of entries in the symbol table. This data can be used to locate the string table, which immediately follows the symbol table. This value should be zero for a PE image.</summary>
    </member>
    <member name="P:System.Reflection.PortableExecutable.CoffHeader.PointerToSymbolTable">
      <summary>Gets the file pointer to the COFF symbol table.</summary>
      <returns>The file pointer to the COFF symbol table, or zero if no COFF symbol table is present. This value should be zero for a PE image.</returns>
    </member>
    <member name="P:System.Reflection.PortableExecutable.CoffHeader.SizeOfOptionalHeader">
      <summary>Gets the size of the optional header, which is required for executable files but not for object files. This value should be zero for an object file.</summary>
      <returns>The size of the optional header.</returns>
    </member>
    <member name="P:System.Reflection.PortableExecutable.CoffHeader.TimeDateStamp">
      <summary>Gets a value that indicates when the file was created.</summary>
      <returns>The low 32 bits of the number of seconds since 00:00 January 1, 1970, which indicates when the file was created.</returns>
    </member>
    <member name="T:System.Reflection.PortableExecutable.CorFlags" />
    <member name="F:System.Reflection.PortableExecutable.CorFlags.ILLibrary" />
    <member name="F:System.Reflection.PortableExecutable.CorFlags.ILOnly" />
    <member name="F:System.Reflection.PortableExecutable.CorFlags.NativeEntryPoint" />
    <member name="F:System.Reflection.PortableExecutable.CorFlags.Prefers32Bit" />
    <member name="F:System.Reflection.PortableExecutable.CorFlags.Requires32Bit" />
    <member name="F:System.Reflection.PortableExecutable.CorFlags.StrongNameSigned" />
    <member name="F:System.Reflection.PortableExecutable.CorFlags.TrackDebugData" />
    <member name="T:System.Reflection.PortableExecutable.CorHeader" />
    <member name="P:System.Reflection.PortableExecutable.CorHeader.CodeManagerTableDirectory" />
    <member name="P:System.Reflection.PortableExecutable.CorHeader.EntryPointTokenOrRelativeVirtualAddress" />
    <member name="P:System.Reflection.PortableExecutable.CorHeader.ExportAddressTableJumpsDirectory" />
    <member name="P:System.Reflection.PortableExecutable.CorHeader.Flags" />
    <member name="P:System.Reflection.PortableExecutable.CorHeader.MajorRuntimeVersion" />
    <member name="P:System.Reflection.PortableExecutable.CorHeader.ManagedNativeHeaderDirectory" />
    <member name="P:System.Reflection.PortableExecutable.CorHeader.MetadataDirectory" />
    <member name="P:System.Reflection.PortableExecutable.CorHeader.MinorRuntimeVersion" />
    <member name="P:System.Reflection.PortableExecutable.CorHeader.ResourcesDirectory" />
    <member name="P:System.Reflection.PortableExecutable.CorHeader.StrongNameSignatureDirectory" />
    <member name="P:System.Reflection.PortableExecutable.CorHeader.VtableFixupsDirectory" />
    <member name="T:System.Reflection.PortableExecutable.DebugDirectoryBuilder" />
    <member name="M:System.Reflection.PortableExecutable.DebugDirectoryBuilder.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Reflection.PortableExecutable.DebugDirectoryBuilder" /> class.</summary>
    </member>
    <member name="M:System.Reflection.PortableExecutable.DebugDirectoryBuilder.AddCodeViewEntry(System.String,System.Reflection.Metadata.BlobContentId,System.UInt16)">
      <summary>Adds a CodeView entry.</summary>
      <param name="pdbPath">The path to the PDB. It should not be empty.</param>
      <param name="pdbContentId">The unique id of the PDB content.</param>
      <param name="portablePdbVersion">The version of Portable PDB format (e.g. 0x0100 for 1.0), or 0 if the PDB is not portable.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="pdbPath" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="pdbPath" /> contains a NUL character.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="portablePdbVersion" /> is smaller than 0x0100.</exception>
    </member>
    <member name="M:System.Reflection.PortableExecutable.DebugDirectoryBuilder.AddEmbeddedPortablePdbEntry(System.Reflection.Metadata.BlobBuilder,System.UInt16)">
      <summary>Adds an Embedded Portable PDB entry.</summary>
      <param name="debugMetadata">A Portable PDB metadata builder.</param>
      <param name="portablePdbVersion">The version of Portable PDB format (e.g. 0x0100 for 1.0).</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="debugMetadata" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="portablePdbVersion" /> is smaller than 0x0100.</exception>
    </member>
    <member name="M:System.Reflection.PortableExecutable.DebugDirectoryBuilder.AddEntry(System.Reflection.PortableExecutable.DebugDirectoryEntryType,System.UInt32,System.UInt32)">
      <summary>Adds an entry of the specified type.</summary>
      <param name="type">The entry type.</param>
      <param name="version">The entry version.</param>
      <param name="stamp">The entry stamp.</param>
    </member>
    <member name="M:System.Reflection.PortableExecutable.DebugDirectoryBuilder.AddEntry``1(System.Reflection.PortableExecutable.DebugDirectoryEntryType,System.UInt32,System.UInt32,``0,System.Action{System.Reflection.Metadata.BlobBuilder,``0})">
      <summary>Adds an entry of the specified type and serializes its data.</summary>
      <param name="type">The entry type.</param>
      <param name="version">The entry version.</param>
      <param name="stamp">The entry stamp.</param>
      <param name="data">The data to pass to <paramref name="dataSerializer" />.</param>
      <param name="dataSerializer">A serializer for serializing data to a <see cref="T:System.Reflection.Metadata.BlobBuilder" />.</param>
      <typeparam name="TData">The type of the data passed to <paramref name="dataSerializer" />.</typeparam>
    </member>
    <member name="M:System.Reflection.PortableExecutable.DebugDirectoryBuilder.AddPdbChecksumEntry(System.String,System.Collections.Immutable.ImmutableArray{System.Byte})">
      <summary>Adds PDB checksum entry.</summary>
      <param name="algorithmName">The hash algorithm name (for example, "SHA256").</param>
      <param name="checksum">The checksum.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="algorithmName" /> or <paramref name="checksum" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="algorithmName" /> or <paramref name="checksum" /> is empty.</exception>
    </member>
    <member name="M:System.Reflection.PortableExecutable.DebugDirectoryBuilder.AddReproducibleEntry">
      <summary>Adds a reproducible entry.</summary>
    </member>
    <member name="T:System.Reflection.PortableExecutable.DebugDirectoryEntry">
      <summary>Identifies the location, size and format of a block of debug information.</summary>
    </member>
    <member name="M:System.Reflection.PortableExecutable.DebugDirectoryEntry.#ctor(System.UInt32,System.UInt16,System.UInt16,System.Reflection.PortableExecutable.DebugDirectoryEntryType,System.Int32,System.Int32,System.Int32)">
      <summary>Initializes a new instance of the <see cref="T:System.Reflection.PortableExecutable.DebugDirectoryEntry" /> structure.</summary>
      <param name="stamp" />
      <param name="majorVersion" />
      <param name="minorVersion" />
      <param name="type" />
      <param name="dataSize" />
      <param name="dataRelativeVirtualAddress" />
      <param name="dataPointer" />
    </member>
    <member name="P:System.Reflection.PortableExecutable.DebugDirectoryEntry.DataPointer">
      <summary>Gets the file pointer to the debug data.</summary>
      <returns>The file pointer to the debug data.</returns>
    </member>
    <member name="P:System.Reflection.PortableExecutable.DebugDirectoryEntry.DataRelativeVirtualAddress">
      <summary>Gets the address of the debug data when loaded, relative to the image base.</summary>
      <returns>The address of the debug data relative to the image base.</returns>
    </member>
    <member name="P:System.Reflection.PortableExecutable.DebugDirectoryEntry.DataSize">
      <summary>Gets the size of the debug data (not including the debug directory itself).</summary>
      <returns>the size of the debug data (excluding the debug directory).</returns>
    </member>
    <member name="P:System.Reflection.PortableExecutable.DebugDirectoryEntry.IsPortableCodeView">
      <summary>Gets a value that indicates if the entry is a <see cref="F:System.Reflection.PortableExecutable.DebugDirectoryEntryType.CodeView" /> entry that points to a Portable PDB.</summary>
      <returns>
        <see langword="true" /> if the entry is a <see cref="F:System.Reflection.PortableExecutable.DebugDirectoryEntryType.CodeView" /> entry pointing to a Portable PDB; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Reflection.PortableExecutable.DebugDirectoryEntry.MajorVersion">
      <summary>Gets the major version number of the debug data format.</summary>
      <returns>The major version number of the debug data format.</returns>
    </member>
    <member name="P:System.Reflection.PortableExecutable.DebugDirectoryEntry.MinorVersion">
      <summary>Gets the minor version number of the debug data format.</summary>
      <returns>The minor version number of the debug data format.</returns>
    </member>
    <member name="P:System.Reflection.PortableExecutable.DebugDirectoryEntry.Stamp">
      <summary>Get the time and date that the debug data was created if the PE/COFF file is not deterministic; otherwise, gets a value based on the hash of the content.</summary>
      <returns>for a non-deterministic PE/COFF file, the time and date that the debug data was created; otherwise, a value based on the hash of the content.</returns>
    </member>
    <member name="P:System.Reflection.PortableExecutable.DebugDirectoryEntry.Type">
      <summary>Gets the format of the debugging information.</summary>
      <returns>The format of the debugging information.</returns>
    </member>
    <member name="T:System.Reflection.PortableExecutable.DebugDirectoryEntryType" />
    <member name="F:System.Reflection.PortableExecutable.DebugDirectoryEntryType.CodeView">
      <summary>Associated PDB file description.</summary>
    </member>
    <member name="F:System.Reflection.PortableExecutable.DebugDirectoryEntryType.Coff">
      <summary>The COFF debug information (line numbers, symbol table, and string table). This type of debug information is also pointed to by fields in the file headers.</summary>
    </member>
    <member name="F:System.Reflection.PortableExecutable.DebugDirectoryEntryType.EmbeddedPortablePdb">
      <summary>The entry points to a blob containing Embedded Portable PDB. The Embedded Portable PDB blob has the following format:- blob ::= uncompressed-size data- Data spans the remainder of the blob and contains a Deflate-compressed Portable PDB.</summary>
    </member>
    <member name="F:System.Reflection.PortableExecutable.DebugDirectoryEntryType.PdbChecksum">
      <summary>The entry stores a crypto hash of the content of the symbol file the PE/COFF file was built with. The hash can be used to validate that a given PDB file was built with the PE/COFF file and not altered in any way. More than one entry can be present if multiple PDBs were produced during the build of the PE/COFF file (e.g. private and public symbols).</summary>
    </member>
    <member name="F:System.Reflection.PortableExecutable.DebugDirectoryEntryType.Reproducible">
      <summary>The presence of this entry indicates a deterministic PE/COFF file. See the Remarks section for more information.</summary>
    </member>
    <member name="F:System.Reflection.PortableExecutable.DebugDirectoryEntryType.Unknown">
      <summary>An unknown value that should be ignored by all tools.</summary>
    </member>
    <member name="T:System.Reflection.PortableExecutable.DirectoryEntry" />
    <member name="M:System.Reflection.PortableExecutable.DirectoryEntry.#ctor(System.Int32,System.Int32)">
      <param name="relativeVirtualAddress" />
      <param name="size" />
    </member>
    <member name="F:System.Reflection.PortableExecutable.DirectoryEntry.RelativeVirtualAddress" />
    <member name="F:System.Reflection.PortableExecutable.DirectoryEntry.Size" />
    <member name="T:System.Reflection.PortableExecutable.DllCharacteristics">
      <summary>Describes the characteristics of a dynamic link library.</summary>
    </member>
    <member name="F:System.Reflection.PortableExecutable.DllCharacteristics.AppContainer">
      <summary>The image must run inside an AppContainer.</summary>
    </member>
    <member name="F:System.Reflection.PortableExecutable.DllCharacteristics.DynamicBase">
      <summary>The DLL can be relocated.</summary>
    </member>
    <member name="F:System.Reflection.PortableExecutable.DllCharacteristics.HighEntropyVirtualAddressSpace">
      <summary>The image can handle a high entropy 64-bit virtual address space.</summary>
    </member>
    <member name="F:System.Reflection.PortableExecutable.DllCharacteristics.NoBind">
      <summary>Do not bind this image.</summary>
    </member>
    <member name="F:System.Reflection.PortableExecutable.DllCharacteristics.NoIsolation">
      <summary>The image understands isolation and doesn't want it.</summary>
    </member>
    <member name="F:System.Reflection.PortableExecutable.DllCharacteristics.NoSeh">
      <summary>The image does not use SEH. No SE handler may reside in this image.</summary>
    </member>
    <member name="F:System.Reflection.PortableExecutable.DllCharacteristics.NxCompatible">
      <summary>The image is NX compatible.</summary>
    </member>
    <member name="F:System.Reflection.PortableExecutable.DllCharacteristics.ProcessInit">
      <summary>Reserved.</summary>
    </member>
    <member name="F:System.Reflection.PortableExecutable.DllCharacteristics.ProcessTerm">
      <summary>Reserved.</summary>
    </member>
    <member name="F:System.Reflection.PortableExecutable.DllCharacteristics.TerminalServerAware">
      <summary>The image is Terminal Server aware.</summary>
    </member>
    <member name="F:System.Reflection.PortableExecutable.DllCharacteristics.ThreadInit">
      <summary>Reserved.</summary>
    </member>
    <member name="F:System.Reflection.PortableExecutable.DllCharacteristics.ThreadTerm">
      <summary>Reserved.</summary>
    </member>
    <member name="F:System.Reflection.PortableExecutable.DllCharacteristics.WdmDriver">
      <summary>The driver uses the WDM model.</summary>
    </member>
    <member name="T:System.Reflection.PortableExecutable.Machine">
      <summary>Specifies the target machine's CPU architecture.</summary>
    </member>
    <member name="F:System.Reflection.PortableExecutable.Machine.Alpha">
      <summary>Alpha.</summary>
    </member>
    <member name="F:System.Reflection.PortableExecutable.Machine.Alpha64">
      <summary>ALPHA64.</summary>
    </member>
    <member name="F:System.Reflection.PortableExecutable.Machine.AM33">
      <summary>Matsushita AM33.</summary>
    </member>
    <member name="F:System.Reflection.PortableExecutable.Machine.Amd64">
      <summary>AMD64 (K8).</summary>
    </member>
    <member name="F:System.Reflection.PortableExecutable.Machine.Arm">
      <summary>ARM little endian.</summary>
    </member>
    <member name="F:System.Reflection.PortableExecutable.Machine.Arm64">
      <summary>ARM64.</summary>
    </member>
    <member name="F:System.Reflection.PortableExecutable.Machine.ArmThumb2">
      <summary>ARM Thumb-2 little endian.</summary>
    </member>
    <member name="F:System.Reflection.PortableExecutable.Machine.Ebc">
      <summary>EFI Byte Code.</summary>
    </member>
    <member name="F:System.Reflection.PortableExecutable.Machine.I386">
      <summary>Intel 386.</summary>
    </member>
    <member name="F:System.Reflection.PortableExecutable.Machine.IA64">
      <summary>Intel 64.</summary>
    </member>
    <member name="F:System.Reflection.PortableExecutable.Machine.M32R">
      <summary>M32R little-endian.</summary>
    </member>
    <member name="F:System.Reflection.PortableExecutable.Machine.MIPS16">
      <summary>MIPS.</summary>
    </member>
    <member name="F:System.Reflection.PortableExecutable.Machine.MipsFpu">
      <summary>MIPS with FPU.</summary>
    </member>
    <member name="F:System.Reflection.PortableExecutable.Machine.MipsFpu16">
      <summary>MIPS16 with FPU.</summary>
    </member>
    <member name="F:System.Reflection.PortableExecutable.Machine.PowerPC">
      <summary>IBM PowerPC little endian.</summary>
    </member>
    <member name="F:System.Reflection.PortableExecutable.Machine.PowerPCFP">
      <summary>PowerPCFP.</summary>
    </member>
    <member name="F:System.Reflection.PortableExecutable.Machine.SH3">
      <summary>Hitachi SH3 little endian.</summary>
    </member>
    <member name="F:System.Reflection.PortableExecutable.Machine.SH3Dsp">
      <summary>Hitachi SH3 DSP.</summary>
    </member>
    <member name="F:System.Reflection.PortableExecutable.Machine.SH3E">
      <summary>Hitachi SH3 little endian.</summary>
    </member>
    <member name="F:System.Reflection.PortableExecutable.Machine.SH4">
      <summary>Hitachi SH4 little endian.</summary>
    </member>
    <member name="F:System.Reflection.PortableExecutable.Machine.SH5">
      <summary>Hitachi SH5.</summary>
    </member>
    <member name="F:System.Reflection.PortableExecutable.Machine.Thumb">
      <summary>Thumb.</summary>
    </member>
    <member name="F:System.Reflection.PortableExecutable.Machine.Tricore">
      <summary>Infineon.</summary>
    </member>
    <member name="F:System.Reflection.PortableExecutable.Machine.Unknown">
      <summary>The target CPU is unknown or not specified.</summary>
    </member>
    <member name="F:System.Reflection.PortableExecutable.Machine.WceMipsV2">
      <summary>MIPS little-endian WCE v2.</summary>
    </member>
    <member name="T:System.Reflection.PortableExecutable.ManagedPEBuilder" />
    <member name="M:System.Reflection.PortableExecutable.ManagedPEBuilder.#ctor(System.Reflection.PortableExecutable.PEHeaderBuilder,System.Reflection.Metadata.Ecma335.MetadataRootBuilder,System.Func{System.Collections.Generic.IEnumerable{System.Reflection.Metadata.Blob},System.Reflection.Metadata.BlobContentId},System.Reflection.Metadata.BlobBuilder,System.Reflection.Metadata.BlobBuilder,System.Reflection.Metadata.BlobBuilder,System.Reflection.PortableExecutable.ResourceSectionBuilder,System.Reflection.PortableExecutable.DebugDirectoryBuilder,System.Int32,System.Reflection.Metadata.MethodDefinitionHandle,System.Reflection.PortableExecutable.CorFlags)">
      <param name="header" />
      <param name="metadataRootBuilder" />
      <param name="deterministicIdProvider" />
      <param name="ilStream" />
      <param name="mappedFieldData" />
      <param name="managedResources" />
      <param name="nativeResources" />
      <param name="debugDirectoryBuilder" />
      <param name="strongNameSignatureSize" />
      <param name="entryPoint" />
      <param name="flags" />
    </member>
    <member name="M:System.Reflection.PortableExecutable.ManagedPEBuilder.CreateSections" />
    <member name="M:System.Reflection.PortableExecutable.ManagedPEBuilder.GetDirectories" />
    <member name="F:System.Reflection.PortableExecutable.ManagedPEBuilder.ManagedResourcesDataAlignment" />
    <member name="F:System.Reflection.PortableExecutable.ManagedPEBuilder.MappedFieldDataAlignment" />
    <member name="M:System.Reflection.PortableExecutable.ManagedPEBuilder.SerializeSection(System.String,System.Reflection.PortableExecutable.SectionLocation)">
      <param name="name" />
      <param name="location" />
    </member>
    <member name="M:System.Reflection.PortableExecutable.ManagedPEBuilder.Sign(System.Reflection.Metadata.BlobBuilder,System.Func{System.Collections.Generic.IEnumerable{System.Reflection.Metadata.Blob},System.Byte[]})">
      <param name="peImage" />
      <param name="signatureProvider" />
    </member>
    <member name="T:System.Reflection.PortableExecutable.PdbChecksumDebugDirectoryData">
      <summary>Represents a PDB Checksum debug directory entry.</summary>
    </member>
    <member name="P:System.Reflection.PortableExecutable.PdbChecksumDebugDirectoryData.AlgorithmName">
      <summary>The name of the crypto hash algorithm used to calculate the checksum.</summary>
      <returns>A string representing the name of the crypto hash algorithm used to calculate the checksum.</returns>
    </member>
    <member name="P:System.Reflection.PortableExecutable.PdbChecksumDebugDirectoryData.Checksum">
      <summary>The checksum of the PDB content.</summary>
      <returns>An immutable array of bytes representing the checksum of the PDB content.</returns>
    </member>
    <member name="T:System.Reflection.PortableExecutable.PEBuilder" />
    <member name="M:System.Reflection.PortableExecutable.PEBuilder.#ctor(System.Reflection.PortableExecutable.PEHeaderBuilder,System.Func{System.Collections.Generic.IEnumerable{System.Reflection.Metadata.Blob},System.Reflection.Metadata.BlobContentId})">
      <param name="header" />
      <param name="deterministicIdProvider" />
    </member>
    <member name="M:System.Reflection.PortableExecutable.PEBuilder.CreateSections" />
    <member name="M:System.Reflection.PortableExecutable.PEBuilder.GetDirectories" />
    <member name="M:System.Reflection.PortableExecutable.PEBuilder.GetSections" />
    <member name="P:System.Reflection.PortableExecutable.PEBuilder.Header" />
    <member name="P:System.Reflection.PortableExecutable.PEBuilder.IdProvider" />
    <member name="P:System.Reflection.PortableExecutable.PEBuilder.IsDeterministic" />
    <member name="T:System.Reflection.PortableExecutable.PEBuilder.Section" />
    <member name="M:System.Reflection.PortableExecutable.PEBuilder.Section.#ctor(System.String,System.Reflection.PortableExecutable.SectionCharacteristics)">
      <param name="name" />
      <param name="characteristics" />
    </member>
    <member name="F:System.Reflection.PortableExecutable.PEBuilder.Section.Characteristics" />
    <member name="F:System.Reflection.PortableExecutable.PEBuilder.Section.Name" />
    <member name="M:System.Reflection.PortableExecutable.PEBuilder.Serialize(System.Reflection.Metadata.BlobBuilder)">
      <param name="builder" />
    </member>
    <member name="M:System.Reflection.PortableExecutable.PEBuilder.SerializeSection(System.String,System.Reflection.PortableExecutable.SectionLocation)">
      <param name="name" />
      <param name="location" />
    </member>
    <member name="T:System.Reflection.PortableExecutable.PEDirectoriesBuilder">
      <summary>Builds PE directories.</summary>
    </member>
    <member name="M:System.Reflection.PortableExecutable.PEDirectoriesBuilder.#ctor">
      <summary>Initializes an instance of the <see cref="T:System.Reflection.PortableExecutable.PEDirectoriesBuilder" /> class.</summary>
    </member>
    <member name="P:System.Reflection.PortableExecutable.PEDirectoriesBuilder.AddressOfEntryPoint">
      <summary>The address of the entry point relative to the image base when the PE file is loaded into memory.</summary>
      <returns>For program images, this is the starting address. For device drivers, this is the address of the initialization function. An entry point is optional for DLLs. When no entry point is present, this field must be zero.</returns>
    </member>
    <member name="P:System.Reflection.PortableExecutable.PEDirectoriesBuilder.BaseRelocationTable">
      <summary>The base relocation table image directory entry.</summary>
      <returns>A directory entry instance.</returns>
    </member>
    <member name="P:System.Reflection.PortableExecutable.PEDirectoriesBuilder.BoundImportTable">
      <summary>The bound import image directory entry.</summary>
      <returns>A directory entry instance.</returns>
    </member>
    <member name="P:System.Reflection.PortableExecutable.PEDirectoriesBuilder.CopyrightTable">
      <summary>The copyright/architecture image directory entry.</summary>
      <returns>A directory entry instance.</returns>
    </member>
    <member name="P:System.Reflection.PortableExecutable.PEDirectoriesBuilder.CorHeaderTable">
      <summary>The COM descriptortable image directory entry.</summary>
      <returns>A directory entry instance.</returns>
    </member>
    <member name="P:System.Reflection.PortableExecutable.PEDirectoriesBuilder.DebugTable">
      <summary>The debug table image directory entry.</summary>
      <returns>A directory entry instance.</returns>
    </member>
    <member name="P:System.Reflection.PortableExecutable.PEDirectoriesBuilder.DelayImportTable">
      <summary>The delay import table image directory entry.</summary>
      <returns>A directory entry instance.</returns>
    </member>
    <member name="P:System.Reflection.PortableExecutable.PEDirectoriesBuilder.ExceptionTable">
      <summary>The exception table image directory entry.</summary>
      <returns>A directory entry instance.</returns>
    </member>
    <member name="P:System.Reflection.PortableExecutable.PEDirectoriesBuilder.ExportTable">
      <summary>The export table image directory entry.</summary>
      <returns>A directory entry instance.</returns>
    </member>
    <member name="P:System.Reflection.PortableExecutable.PEDirectoriesBuilder.GlobalPointerTable">
      <summary>The global pointer table image directory entry.</summary>
      <returns>A directory entry instance.</returns>
    </member>
    <member name="P:System.Reflection.PortableExecutable.PEDirectoriesBuilder.ImportAddressTable">
      <summary>The import address table (IAT) image directory entry.</summary>
      <returns>A directory entry instance.</returns>
    </member>
    <member name="P:System.Reflection.PortableExecutable.PEDirectoriesBuilder.ImportTable">
      <summary>The import table image directory entry.</summary>
      <returns>A directory entry instance.</returns>
    </member>
    <member name="P:System.Reflection.PortableExecutable.PEDirectoriesBuilder.LoadConfigTable">
      <summary>The load configuration table image directory entry.</summary>
      <returns>A directory entry instance.</returns>
    </member>
    <member name="P:System.Reflection.PortableExecutable.PEDirectoriesBuilder.ResourceTable">
      <summary>The resource table image directory entry.</summary>
      <returns>A directory entry instance.</returns>
    </member>
    <member name="P:System.Reflection.PortableExecutable.PEDirectoriesBuilder.ThreadLocalStorageTable">
      <summary>The thread local storage (TLS) table image directory entry.</summary>
      <returns>A directory entry instance.</returns>
    </member>
    <member name="T:System.Reflection.PortableExecutable.PEHeader" />
    <member name="P:System.Reflection.PortableExecutable.PEHeader.AddressOfEntryPoint">
      <summary>Gets the address of the entry point relative to the image base when the PE file is loaded into memory.</summary>
      <returns>The address of the entry point relative to the image base.</returns>
    </member>
    <member name="P:System.Reflection.PortableExecutable.PEHeader.BaseOfCode">
      <summary>Gets the address of the beginning-of-code section relative to the image base when the image is loaded into memory.</summary>
      <returns>The address of the beginning-of-code section relative to the image base.</returns>
    </member>
    <member name="P:System.Reflection.PortableExecutable.PEHeader.BaseOfData">
      <summary>Gets the address of the beginning-of-data section relative to the image base when the image is loaded into memory.</summary>
      <returns>The address of the beginning-of-data section relative to the image base.</returns>
    </member>
    <member name="P:System.Reflection.PortableExecutable.PEHeader.BaseRelocationTableDirectory" />
    <member name="P:System.Reflection.PortableExecutable.PEHeader.BoundImportTableDirectory" />
    <member name="P:System.Reflection.PortableExecutable.PEHeader.CertificateTableDirectory">
      <summary>Gets the Certificate Table entry, which points to a table of attribute certificates.</summary>
    </member>
    <member name="P:System.Reflection.PortableExecutable.PEHeader.CheckSum">
      <summary>Gets the image file checksum.</summary>
      <returns>The image file checksum.</returns>
    </member>
    <member name="P:System.Reflection.PortableExecutable.PEHeader.CopyrightTableDirectory" />
    <member name="P:System.Reflection.PortableExecutable.PEHeader.CorHeaderTableDirectory" />
    <member name="P:System.Reflection.PortableExecutable.PEHeader.DebugTableDirectory" />
    <member name="P:System.Reflection.PortableExecutable.PEHeader.DelayImportTableDirectory" />
    <member name="P:System.Reflection.PortableExecutable.PEHeader.DllCharacteristics" />
    <member name="P:System.Reflection.PortableExecutable.PEHeader.ExceptionTableDirectory" />
    <member name="P:System.Reflection.PortableExecutable.PEHeader.ExportTableDirectory" />
    <member name="P:System.Reflection.PortableExecutable.PEHeader.FileAlignment">
      <summary>Gets the alignment factor (in bytes) that is used to align the raw data of sections in the image file.</summary>
      <returns>A power of 2 between 512 and 64K, inclusive. The default is 512.</returns>
    </member>
    <member name="P:System.Reflection.PortableExecutable.PEHeader.GlobalPointerTableDirectory" />
    <member name="P:System.Reflection.PortableExecutable.PEHeader.ImageBase">
      <summary>Gets the preferred address of the first byte of the image when it is loaded into memory.</summary>
      <returns>The preferred address, which is a multiple of 64K.</returns>
    </member>
    <member name="P:System.Reflection.PortableExecutable.PEHeader.ImportAddressTableDirectory" />
    <member name="P:System.Reflection.PortableExecutable.PEHeader.ImportTableDirectory" />
    <member name="P:System.Reflection.PortableExecutable.PEHeader.LoadConfigTableDirectory" />
    <member name="P:System.Reflection.PortableExecutable.PEHeader.Magic">
      <summary>Gets a value that identifies the format of the image file.</summary>
      <returns>The format of the image file.</returns>
    </member>
    <member name="P:System.Reflection.PortableExecutable.PEHeader.MajorImageVersion">
      <summary>Gets the major version number of the image.</summary>
      <returns>The major version number of the image.</returns>
    </member>
    <member name="P:System.Reflection.PortableExecutable.PEHeader.MajorLinkerVersion">
      <summary>Gets the linker major version number.</summary>
      <returns>The linker major version number.</returns>
    </member>
    <member name="P:System.Reflection.PortableExecutable.PEHeader.MajorOperatingSystemVersion">
      <summary>Gets the major version number of the required operating system.</summary>
      <returns>The major version number of the required operating system.</returns>
    </member>
    <member name="P:System.Reflection.PortableExecutable.PEHeader.MajorSubsystemVersion">
      <summary>Gets the major version number of the subsystem.</summary>
      <returns>The major version number of the subsystem.</returns>
    </member>
    <member name="P:System.Reflection.PortableExecutable.PEHeader.MinorImageVersion">
      <summary>Gets the minor version number of the image.</summary>
      <returns>The minor version number of the image.</returns>
    </member>
    <member name="P:System.Reflection.PortableExecutable.PEHeader.MinorLinkerVersion">
      <summary>Gets the linker minor version number.</summary>
      <returns>The linker minor version number.</returns>
    </member>
    <member name="P:System.Reflection.PortableExecutable.PEHeader.MinorOperatingSystemVersion">
      <summary>Gets the minor version number of the required operating system.</summary>
      <returns>The minor version number of the required operating system.</returns>
    </member>
    <member name="P:System.Reflection.PortableExecutable.PEHeader.MinorSubsystemVersion">
      <summary>Gets the minor version number of the subsystem.</summary>
      <returns>The minor version number of the subsystem.</returns>
    </member>
    <member name="P:System.Reflection.PortableExecutable.PEHeader.NumberOfRvaAndSizes">
      <summary>Gets the number of data-directory entries in the remainder of the <see cref="T:System.Reflection.PortableExecutable.PEHeader" />. Each describes a location and size.</summary>
      <returns>The number of data-directory entries in the remainder of the <see cref="T:System.Reflection.PortableExecutable.PEHeader" />.</returns>
    </member>
    <member name="P:System.Reflection.PortableExecutable.PEHeader.ResourceTableDirectory" />
    <member name="P:System.Reflection.PortableExecutable.PEHeader.SectionAlignment">
      <summary>Gets the alignment (in bytes) of sections when they are loaded into memory.</summary>
      <returns>A number greater than or equal to <see cref="P:System.Reflection.PortableExecutable.PEHeader.FileAlignment" />. The default is the page size for the architecture.</returns>
    </member>
    <member name="P:System.Reflection.PortableExecutable.PEHeader.SizeOfCode">
      <summary>Gets the size of the code (text) section, or the sum of all code sections if there are multiple sections.</summary>
      <returns>the size of the code (text) section, or the sum of all code sections if there are multiple sections.</returns>
    </member>
    <member name="P:System.Reflection.PortableExecutable.PEHeader.SizeOfHeaders">
      <summary>Gets the combined size of an MS DOS stub, PE header, and section headers rounded up to a multiple of FileAlignment.</summary>
      <returns>The combined size of an MS DOS stub, PE header, and section headers rounded up to a multiple of FileAlignment.</returns>
    </member>
    <member name="P:System.Reflection.PortableExecutable.PEHeader.SizeOfHeapCommit">
      <summary>Gets the size of the local heap space to commit.</summary>
      <returns>the size of the local heap space to commit.</returns>
    </member>
    <member name="P:System.Reflection.PortableExecutable.PEHeader.SizeOfHeapReserve">
      <summary>Gets the size of the local heap space to reserve. Only <see cref="P:System.Reflection.PortableExecutable.PEHeader.SizeOfHeapCommit" /> is committed; the rest is made available one page at a time until the reserve size is reached.</summary>
      <returns>The size of the local heap space to reserve.</returns>
    </member>
    <member name="P:System.Reflection.PortableExecutable.PEHeader.SizeOfImage">
      <summary>Gets the size (in bytes) of the image, including all headers, as the image is loaded in memory.</summary>
      <returns>The size (in bytes) of the image, which is a multiple of <see cref="P:System.Reflection.PortableExecutable.PEHeader.SectionAlignment" />.</returns>
    </member>
    <member name="P:System.Reflection.PortableExecutable.PEHeader.SizeOfInitializedData">
      <summary>Gets the size of the initialized data section, or the sum of all such sections if there are multiple data sections.</summary>
    </member>
    <member name="P:System.Reflection.PortableExecutable.PEHeader.SizeOfStackCommit">
      <summary>Gets the size of the stack to commit.</summary>
      <returns>The size of the stack to commit.</returns>
    </member>
    <member name="P:System.Reflection.PortableExecutable.PEHeader.SizeOfStackReserve">
      <summary>Gets the size of the stack to reserve. Only <see cref="P:System.Reflection.PortableExecutable.PEHeader.SizeOfStackCommit" /> is committed; the rest is made available one page at a time until the reserve size is reached.</summary>
      <returns>The size of the stack to reserve.</returns>
    </member>
    <member name="P:System.Reflection.PortableExecutable.PEHeader.SizeOfUninitializedData">
      <summary>Gets the size of the uninitialized data section (BSS), or the sum of all such sections if there are multiple BSS sections.</summary>
      <returns>The size of the uninitialized data section (BSS) or the sum of all such sections.</returns>
    </member>
    <member name="P:System.Reflection.PortableExecutable.PEHeader.Subsystem">
      <summary>Gets the name of the subsystem that is required to run this image.</summary>
      <returns>the name of the subsystem that is required to run this image.</returns>
    </member>
    <member name="P:System.Reflection.PortableExecutable.PEHeader.ThreadLocalStorageTableDirectory" />
    <member name="T:System.Reflection.PortableExecutable.PEHeaderBuilder">
      <summary>Defines the header for a portable executable (PE) file.</summary>
    </member>
    <member name="M:System.Reflection.PortableExecutable.PEHeaderBuilder.#ctor(System.Reflection.PortableExecutable.Machine,System.Int32,System.UInt16,System.UInt16,System.Reflection.PortableExecutable.Subsystem,System.Reflection.PortableExecutable.DllCharacteristics,System.Reflection.PortableExecutable.Characteristics,System.UInt64,System.UInt64,System.UInt64,System.UInt64,System.Int32,System.UInt64,System.Byte,System.Byte,System.UInt16,System.UInt16,System.UInt16,System.UInt16)">
      <summary>Initializes a new instance of the <see cref="T:System.Reflection.PortableExecutable.PEHeader" /> class.</summary>
      <param name="machine">The target machine's CPU architecture.</param>
      <param name="sectionAlignment">The alignment (in bytes) of sections when they are loaded into memory. It must be greater than or equal to <paramref name="fileAlignment" />. The default is the page size for the architecture.</param>
      <param name="majorSubsystemVersion">The major version number of the subsystem.</param>
      <param name="minorSubsystemVersion">The minor version number of the subsystem.</param>
      <param name="subsystem">The subsystem required to run the image.</param>
      <param name="dllCharacteristics">An object describing the characteristics of the dynamic link library.</param>
      <param name="imageCharacteristics">An object describing the characteristics of the image.</param>
      <param name="sizeOfStackReserve">The size of the stack to reserve. Only <paramref name="sizeOfStackCommit" /> is committed; the rest is made available one page at a time until the reserve size is reached.</param>
      <param name="sizeOfStackCommit">The size of the stack to commit.</param>
      <param name="sizeOfHeapReserve">The size of the local heap space to reserve. Only <paramref name="sizeOfHeapCommit" /> is committed; the rest is made available one page at a time until the reserve size is reached.</param>
      <param name="sizeOfHeapCommit">The size of the local heap space to commit.</param>
      <param name="fileAlignment">The alignment factor (in bytes) that is used to align the raw data of sections in the image file. The value should be a power of 2 between 512 and 64K, inclusive. The default is 512. If the <paramref name="sectionAlignment" /> is less than the architecture's page size, then <paramref name="fileAlignment" /> must match <paramref name="sectionAlignment" />.</param>
      <param name="imageBase">The preferred address of the first byte of image when loaded into memory; must be a multiple of 64K.</param>
      <param name="majorLinkerVersion">The linker major version number.</param>
      <param name="minorLinkerVersion">The linker minor version number.</param>
      <param name="majorOperatingSystemVersion">The major version number of the required operating system.</param>
      <param name="minorOperatingSystemVersion">The minor version number of the required operating system.</param>
      <param name="majorImageVersion">The major version number of the image.</param>
      <param name="minorImageVersion">The minor version number of the image.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="fileAlignment" /> is not power of 2 between 512 and 64K.
-or-
<paramref name="sectionAlignment" /> not power of 2.
-or-
<paramref name="sectionAlignment" /> is less than <paramref name="fileAlignment" />.</exception>
    </member>
    <member name="M:System.Reflection.PortableExecutable.PEHeaderBuilder.CreateExecutableHeader">
      <summary>Creates an executable header.</summary>
      <returns>A <see cref="T:System.Reflection.PortableExecutable.PEHeaderBuilder" /> instance representing the executable header.</returns>
    </member>
    <member name="M:System.Reflection.PortableExecutable.PEHeaderBuilder.CreateLibraryHeader">
      <summary>Creates a library header.</summary>
      <returns>A <see cref="T:System.Reflection.PortableExecutable.PEHeaderBuilder" /> instance representing the library header.</returns>
    </member>
    <member name="P:System.Reflection.PortableExecutable.PEHeaderBuilder.DllCharacteristics">
      <summary>Returns the dynamic linker library characteristics.</summary>
      <returns>An object that describes the dynamic linker library characteristics.</returns>
    </member>
    <member name="P:System.Reflection.PortableExecutable.PEHeaderBuilder.FileAlignment">
      <summary>The alignment factor (in bytes) that is used to align the raw data of sections in the image file. The value should be a power of 2 between 512 and 64K, inclusive. The default is 512. If the section alignment is less than the architecture's page size, then file alignment must match the section alignment.</summary>
    </member>
    <member name="P:System.Reflection.PortableExecutable.PEHeaderBuilder.ImageBase">
      <summary>The preferred address of the first byte of image when loaded into memory; must be a multiple of 64K.</summary>
      <returns>A number representing the preferred address of the first byte of image when loaded into memory.</returns>
    </member>
    <member name="P:System.Reflection.PortableExecutable.PEHeaderBuilder.ImageCharacteristics">
      <summary>Returns the image characteristics.</summary>
      <returns>An object representing the image characteristics.</returns>
    </member>
    <member name="P:System.Reflection.PortableExecutable.PEHeaderBuilder.Machine">
      <summary>The target machine's CPU architecture.</summary>
      <returns>One of the enumeration values representing the different CPU architectures.</returns>
    </member>
    <member name="P:System.Reflection.PortableExecutable.PEHeaderBuilder.MajorImageVersion">
      <summary>The major version number of the image.</summary>
      <returns>A number the size of a <see cref="T:System.UInt16" /> representing the the major version number of the image.</returns>
    </member>
    <member name="P:System.Reflection.PortableExecutable.PEHeaderBuilder.MajorLinkerVersion">
      <summary>The linker major version number.</summary>
      <returns>A number the size of a <see cref="T:System.Byte" /> representing the linker major version number.</returns>
    </member>
    <member name="P:System.Reflection.PortableExecutable.PEHeaderBuilder.MajorOperatingSystemVersion">
      <summary>The major version number of the required operating system.</summary>
      <returns>A number the size of a <see cref="T:System.UInt16" /> representing the major version number of the required operating system.</returns>
    </member>
    <member name="P:System.Reflection.PortableExecutable.PEHeaderBuilder.MajorSubsystemVersion">
      <summary>The major version number of the subsystem.</summary>
      <returns>A number the size of a <see cref="T:System.UInt16" /> representing the major version number of the subsystem.</returns>
    </member>
    <member name="P:System.Reflection.PortableExecutable.PEHeaderBuilder.MinorImageVersion">
      <summary>The minor version number of the image.</summary>
      <returns>A number the size of a <see cref="T:System.UInt16" /> representing the minor version number of the image.</returns>
    </member>
    <member name="P:System.Reflection.PortableExecutable.PEHeaderBuilder.MinorLinkerVersion">
      <summary>The linker minor version number.</summary>
      <returns>A number the size of a <see cref="T:System.Byte" /> representing the linker minor version number.</returns>
    </member>
    <member name="P:System.Reflection.PortableExecutable.PEHeaderBuilder.MinorOperatingSystemVersion">
      <summary>The minor version number of the required operating system.</summary>
      <returns>A number the size of a <see cref="T:System.UInt16" /> representing the minor version number of the required operating system.</returns>
    </member>
    <member name="P:System.Reflection.PortableExecutable.PEHeaderBuilder.MinorSubsystemVersion">
      <summary>The minor version number of the subsystem.</summary>
      <returns>A number the size of a <see cref="T:System.UInt16" /> representing the minor version number of the subsystem.</returns>
    </member>
    <member name="P:System.Reflection.PortableExecutable.PEHeaderBuilder.SectionAlignment">
      <summary>The alignment (in bytes) of sections when they are loaded into memory.</summary>
      <returns>A number representing the alignment (in bytes) of sections when they are loaded into memory. It must be greater than or equal to the file alignment. The default is the page size for the architecture.</returns>
    </member>
    <member name="P:System.Reflection.PortableExecutable.PEHeaderBuilder.SizeOfHeapCommit">
      <summary>The size of the local heap space to commit.</summary>
      <returns>A number representing the size of the local heap space to commit.</returns>
    </member>
    <member name="P:System.Reflection.PortableExecutable.PEHeaderBuilder.SizeOfHeapReserve">
      <summary>The size of the local heap space to reserve. Only <see cref="P:System.Reflection.PortableExecutable.PEHeaderBuilder.SizeOfHeapCommit" /> is committed; the rest is made available one page at a time until the reserve size is reached.</summary>
      <returns>A number representing the size of the local heap space to reserve.</returns>
    </member>
    <member name="P:System.Reflection.PortableExecutable.PEHeaderBuilder.SizeOfStackCommit">
      <summary>The size of the stack to commit.</summary>
      <returns>A number representing the size of the stack to commit.</returns>
    </member>
    <member name="P:System.Reflection.PortableExecutable.PEHeaderBuilder.SizeOfStackReserve">
      <summary>The size of the stack to reserve. Only <see cref="P:System.Reflection.PortableExecutable.PEHeaderBuilder.SizeOfStackCommit" /> is committed; the rest is made available one page at a time until the reserve size is reached.</summary>
      <returns>A number representing the size of the stack to reserve.</returns>
    </member>
    <member name="P:System.Reflection.PortableExecutable.PEHeaderBuilder.Subsystem">
      <summary>The subsystem that is required to run this image.</summary>
      <returns>A <see cref="T:System.Reflection.PortableExecutable.Subsystem" /> instance.</returns>
    </member>
    <member name="T:System.Reflection.PortableExecutable.PEHeaders">
      <summary>Defines a type that reads PE (Portable Executable) and COFF (Common Object File Format) headers from a stream.</summary>
    </member>
    <member name="M:System.Reflection.PortableExecutable.PEHeaders.#ctor(System.IO.Stream)">
      <summary>Instantiates a new instance of the <see cref="T:System.Reflection.PortableExecutable.PEHeaders" /> class that reads the PE headers from the current location in the specified stream.</summary>
      <param name="peStream">A stream containing the PE image starting at the stream's current position and ending at the end of the stream.</param>
      <exception cref="T:System.BadImageFormatException">The data read from the stream has an invalid format.</exception>
      <exception cref="T:System.IO.IOException">Error reading from the stream.</exception>
      <exception cref="T:System.ArgumentException">The stream does not support seek operations.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="peStream" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Reflection.PortableExecutable.PEHeaders.#ctor(System.IO.Stream,System.Int32)">
      <summary>Instantiates a new instance of the <see cref="T:System.Reflection.PortableExecutable.PEHeaders" /> class that reads the PE headers from a stream that represents a PE image of a specified size.</summary>
      <param name="peStream">A stream containing PE image of the given size starting at its current position.</param>
      <param name="size">The size of the PE image.</param>
      <exception cref="T:System.BadImageFormatException">The data read from the stream has an invalid format.</exception>
      <exception cref="T:System.IO.IOException">Error reading from the stream.</exception>
      <exception cref="T:System.ArgumentException">The stream does not support seek operations.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="peStream" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="size" /> is negative or extends past the end of the stream.</exception>
    </member>
    <member name="M:System.Reflection.PortableExecutable.PEHeaders.#ctor(System.IO.Stream,System.Int32,System.Boolean)">
      <summary>Instantiates a new instance of the <see cref="T:System.Reflection.PortableExecutable.PEHeaders" /> class that reads the PE headers from a stream that represents a PE image of a specified size and indicates whether the PE image has been loaded into memory.</summary>
      <param name="peStream">The stream containing PE image of the given size starting at its current position.</param>
      <param name="size">The size of the PE image.</param>
      <param name="isLoadedImage">
        <see langword="true" /> if the PE image has been loaded into memory by the OS loader; otherwise, <see langword="false" />.</param>
      <exception cref="T:System.BadImageFormatException">The data read from the stream has invalid format.</exception>
      <exception cref="T:System.IO.IOException">Error reading from the stream.</exception>
      <exception cref="T:System.ArgumentException">The stream does not support seek operations.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="peStream" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="size" /> is negative or extends past the end of the stream.</exception>
    </member>
    <member name="P:System.Reflection.PortableExecutable.PEHeaders.CoffHeader">
      <summary>Gets the COFF header of the image.</summary>
      <returns>The COFF header of the image.</returns>
    </member>
    <member name="P:System.Reflection.PortableExecutable.PEHeaders.CoffHeaderStartOffset">
      <summary>Gets the byte offset from the start of the PE image to the start of the COFF header.</summary>
      <returns>The byte offset from the start of the PE image to the start of the COFF header.</returns>
    </member>
    <member name="P:System.Reflection.PortableExecutable.PEHeaders.CorHeader">
      <summary>Gets the COR header.</summary>
      <returns>The COR header, or <see langword="null" /> if the image does not have one.</returns>
    </member>
    <member name="P:System.Reflection.PortableExecutable.PEHeaders.CorHeaderStartOffset">
      <summary>Gets the byte offset from the start of the image to the COR header.</summary>
      <returns>The byte offset from the start of the image to the COR header, or -1 if the image does not have a COR header.</returns>
    </member>
    <member name="M:System.Reflection.PortableExecutable.PEHeaders.GetContainingSectionIndex(System.Int32)">
      <summary>Searches sections of the PE image for the section that contains the specified Relative Virtual Address.</summary>
      <param name="relativeVirtualAddress">The relative virtual address to search for.</param>
      <returns>The index of the section that contains <paramref name="relativeVirtualAddress" />, or -1 if there the search is unsuccessful.</returns>
    </member>
    <member name="P:System.Reflection.PortableExecutable.PEHeaders.IsCoffOnly">
      <summary>Gets a value that indicates whether the image is Coff only.</summary>
      <returns>
        <see langword="true" /> if the image is Coff only; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Reflection.PortableExecutable.PEHeaders.IsConsoleApplication">
      <summary>Gets a value that indicates whether the image represents a Windows console application.</summary>
      <returns>
        <see langword="true" /> if the image is a Windows console applications; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Reflection.PortableExecutable.PEHeaders.IsDll">
      <summary>Gets a value that indicates whether the image represents a dynamic link library.</summary>
      <returns>
        <see langword="true" /> if the image is a DLL; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Reflection.PortableExecutable.PEHeaders.IsExe">
      <summary>Gets a value that indicates whether the image represents an executable.</summary>
      <returns>
        <see langword="true" /> if the image is an executable; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Reflection.PortableExecutable.PEHeaders.MetadataSize">
      <summary>Gets the size of the CLI metadata.</summary>
      <returns>the size of the CLI metadata, or 0 if the image does not contain metadata.</returns>
    </member>
    <member name="P:System.Reflection.PortableExecutable.PEHeaders.MetadataStartOffset">
      <summary>Gets the offset (in bytes) from the start of the PE image to the start of the CLI metadata.</summary>
      <returns>The offset (in bytes) from the start of the PE image to the start of the CLI metadata, or -1 if the image does not contain metadata.</returns>
    </member>
    <member name="P:System.Reflection.PortableExecutable.PEHeaders.PEHeader">
      <summary>Gets the image's PE header.</summary>
      <returns>The image's PE header, or <see langword="null" /> if the image is COFF only.</returns>
    </member>
    <member name="P:System.Reflection.PortableExecutable.PEHeaders.PEHeaderStartOffset">
      <summary>Gets the byte offset of the header from the start of the image.</summary>
      <returns>The byte offset of the header from the start of the image.</returns>
    </member>
    <member name="P:System.Reflection.PortableExecutable.PEHeaders.SectionHeaders">
      <summary>Gets the PE section headers.</summary>
      <returns>An array containing the PE section headers.</returns>
    </member>
    <member name="M:System.Reflection.PortableExecutable.PEHeaders.TryGetDirectoryOffset(System.Reflection.PortableExecutable.DirectoryEntry,System.Int32@)">
      <summary>Gets the offset (in bytes) from the start of the image to the given directory data.</summary>
      <param name="directory">The PE directory entry.</param>
      <param name="offset">When the method returns, contains the offset from the start of the image to the given directory data.</param>
      <returns>
        <see langword="true" /> if the directory data is found; <see langword="false" /> otherwise.</returns>
    </member>
    <member name="T:System.Reflection.PortableExecutable.PEMagic" />
    <member name="F:System.Reflection.PortableExecutable.PEMagic.PE32" />
    <member name="F:System.Reflection.PortableExecutable.PEMagic.PE32Plus" />
    <member name="T:System.Reflection.PortableExecutable.PEMemoryBlock" />
    <member name="M:System.Reflection.PortableExecutable.PEMemoryBlock.GetContent">
      <summary>Reads the contents of the entire block into an array.</summary>
      <returns>An immutable byte array.</returns>
    </member>
    <member name="M:System.Reflection.PortableExecutable.PEMemoryBlock.GetContent(System.Int32,System.Int32)">
      <summary>Reads the contents of a part of the block into an array.</summary>
      <param name="start">The starting position in the block.</param>
      <param name="length">The number of bytes to read.</param>
      <returns>An immutable array of bytes.</returns>
      <exception cref="T:System.ArgumentOutOfRangeException">The specified range is not contained within the block.</exception>
    </member>
    <member name="M:System.Reflection.PortableExecutable.PEMemoryBlock.GetReader">
      <summary>Creates a <see cref="T:System.Reflection.Metadata.BlobReader" /> for a blob spanning the entire block.</summary>
      <returns>A reader for a blob spanning the entire block.</returns>
    </member>
    <member name="M:System.Reflection.PortableExecutable.PEMemoryBlock.GetReader(System.Int32,System.Int32)">
      <summary>Creates a <see cref="T:System.Reflection.Metadata.BlobReader" /> for a blob spanning a part of the block.</summary>
      <param name="start">The starting position in the block.</param>
      <param name="length">The number of bytes in the portion of the block.</param>
      <returns>A reader for a blob spanning a portion of the block.</returns>
      <exception cref="T:System.ArgumentOutOfRangeException">The specified range is not contained within the block.</exception>
    </member>
    <member name="P:System.Reflection.PortableExecutable.PEMemoryBlock.Length">
      <summary>Gets the length of the block.</summary>
      <returns>The length of the block.</returns>
    </member>
    <member name="P:System.Reflection.PortableExecutable.PEMemoryBlock.Pointer">
      <summary>Gets a pointer to the first byte of the block.</summary>
      <returns>A pointer to the first byte of the block.</returns>
    </member>
    <member name="T:System.Reflection.PortableExecutable.PEReader">
      <summary>Provides a reader for Portable Executable format (PE) files.</summary>
    </member>
    <member name="M:System.Reflection.PortableExecutable.PEReader.#ctor(System.Byte*,System.Int32)">
      <summary>Creates a Portable Executable reader over a PE image stored in memory.</summary>
      <param name="peImage">A pointer to the start of the PE image.</param>
      <param name="size">The size of the PE image.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="peImage" /> is <see cref="F:System.IntPtr.Zero" />.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="size" /> is negative.</exception>
    </member>
    <member name="M:System.Reflection.PortableExecutable.PEReader.#ctor(System.Byte*,System.Int32,System.Boolean)">
      <summary>Creates a Portable Executable reader over a PE image stored in memory. A flag indicates whether the image has already been loaded into memory.</summary>
      <param name="peImage">A pointer to the start of the PE image.</param>
      <param name="size">The size of the PE image.</param>
      <param name="isLoadedImage">
        <see langword="true" /> if the PE image has been loaded into memory by the OS loader; otherwise, <see langword="false" />.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="peImage" /> is <see cref="F:System.IntPtr.Zero" />.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="size" /> is negative.</exception>
    </member>
    <member name="M:System.Reflection.PortableExecutable.PEReader.#ctor(System.Collections.Immutable.ImmutableArray{System.Byte})">
      <summary>Creates a Portable Executable reader over a PE image stored in a byte array.</summary>
      <param name="peImage">An immutable array of bytes representing the PE image.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="peImage" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Reflection.PortableExecutable.PEReader.#ctor(System.IO.Stream)">
      <summary>Creates a Portable Executable reader over a PE image stored in a stream.</summary>
      <param name="peStream">PE image stream.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="peStream" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Reflection.PortableExecutable.PEReader.#ctor(System.IO.Stream,System.Reflection.PortableExecutable.PEStreamOptions)">
      <summary>Creates a Portable Executable reader over a PE image stored in a stream beginning at its current position and ending at the end of the stream.</summary>
      <param name="peStream">A PE image stream.</param>
      <param name="options">Options specifying how sections of the PE image are read from the stream.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="peStream" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="options" /> has an invalid value.</exception>
      <exception cref="T:System.IO.IOException">Error reading from the stream (only when prefetching data).</exception>
      <exception cref="T:System.BadImageFormatException">
        <see cref="F:System.Reflection.PortableExecutable.PEStreamOptions.PrefetchMetadata" /> is specified, and the PE headers of the image are invalid.</exception>
    </member>
    <member name="M:System.Reflection.PortableExecutable.PEReader.#ctor(System.IO.Stream,System.Reflection.PortableExecutable.PEStreamOptions,System.Int32)">
      <summary>Creates a Portable Executable reader over a PE image of the given size beginning at the stream's current position.</summary>
      <param name="peStream">A PE image stream.</param>
      <param name="options">Options specifying how sections of the PE image are read from the stream.</param>
      <param name="size">The PE image size.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="size" /> is negative or extends past the end of the stream.</exception>
      <exception cref="T:System.IO.IOException">Error reading from the stream (only when prefetching data).</exception>
      <exception cref="T:System.BadImageFormatException">
        <see cref="F:System.Reflection.PortableExecutable.PEStreamOptions.PrefetchMetadata" /> is specified, and the PE headers of the image are invalid.</exception>
    </member>
    <member name="M:System.Reflection.PortableExecutable.PEReader.Dispose">
      <summary>Disposes all memory allocated by the reader.</summary>
    </member>
    <member name="M:System.Reflection.PortableExecutable.PEReader.GetEntireImage">
      <summary>Gets a <see cref="T:System.Reflection.PortableExecutable.PEMemoryBlock" /> object containing the entire PE image.</summary>
      <returns>A memory block that contains the entire PE image.</returns>
      <exception cref="T:System.InvalidOperationException">The entire PE image is not available.</exception>
    </member>
    <member name="M:System.Reflection.PortableExecutable.PEReader.GetMetadata">
      <summary>Loads a PE section that contains CLI metadata.</summary>
      <returns>A memory block that contains the CLI metadata.</returns>
      <exception cref="T:System.InvalidOperationException">The PE image doesn't contain metadata (<see cref="P:System.Reflection.PortableExecutable.PEReader.HasMetadata" /> returns <see langword="false" />).</exception>
      <exception cref="T:System.BadImageFormatException">The PE headers contain invalid data.</exception>
      <exception cref="T:System.IO.IOException">IO error while reading from the underlying stream.</exception>
    </member>
    <member name="M:System.Reflection.PortableExecutable.PEReader.GetSectionData(System.Int32)">
      <summary>Loads the PE section that contains the specified relative virtual address into memory and returns a memory block that starts at that address and ends at the end of the containing section.</summary>
      <param name="relativeVirtualAddress">The Relative Virtual Address of the data to read.</param>
      <returns>A memory block that starats at <paramref name="relativeVirtualAddress" /> and ends at the end of the containing section, or an empty block if <paramref name="relativeVirtualAddress" /> doesn't represent a location in any of the PE sections of this PE image.</returns>
      <exception cref="T:System.BadImageFormatException">The PE headers contain invalid data.</exception>
      <exception cref="T:System.IO.IOException">An IO error occurred while reading from the underlying stream.</exception>
      <exception cref="T:System.InvalidOperationException">The PE image is not available.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="relativeVirtualAddress" /> is negative.</exception>
    </member>
    <member name="M:System.Reflection.PortableExecutable.PEReader.GetSectionData(System.String)">
      <summary>Loads the PE section with the specified name into memory and returns a memory block that spans the section.</summary>
      <param name="sectionName">The name of the section.</param>
      <returns>A memory block that spans the section, or an empty block if no section of the given <paramref name="sectionName" /> exists in this PE image.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="sectionName" /> is <see langword="null" />.</exception>
      <exception cref="T:System.InvalidOperationException">The PE image is not available.</exception>
    </member>
    <member name="P:System.Reflection.PortableExecutable.PEReader.HasMetadata">
      <summary>Gets a value that indicates if the PE image contains CLI metadata.</summary>
      <returns>
        <see langword="true" /> if the PE image contains CLI metadata; otherwise, <see langword="false" />.</returns>
      <exception cref="T:System.BadImageFormatException">The PE headers contain invalid data.</exception>
      <exception cref="T:System.IO.IOException">Error reading from the underlying stream.</exception>
    </member>
    <member name="P:System.Reflection.PortableExecutable.PEReader.IsEntireImageAvailable">
      <summary>Gets a value that indicates if the reader can access the entire PE image.</summary>
      <returns>
        <see langword="true" /> if the reader can access the entire PE image; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Reflection.PortableExecutable.PEReader.IsLoadedImage">
      <summary>Gets a value that indicates if the PE image has been loaded into memory by the OS loader.</summary>
      <returns>
        <see langword="true" /> if the PE image has been loaded into memory by the OS loader; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Reflection.PortableExecutable.PEReader.PEHeaders">
      <summary>Gets the PE headers.</summary>
      <returns>The PE headers for this PE image.</returns>
      <exception cref="T:System.BadImageFormatException">The headers contain invalid data.</exception>
      <exception cref="T:System.IO.IOException">Error reading from the stream.</exception>
    </member>
    <member name="M:System.Reflection.PortableExecutable.PEReader.ReadCodeViewDebugDirectoryData(System.Reflection.PortableExecutable.DebugDirectoryEntry)">
      <summary>Reads the data pointed to by the specified Debug Directory entry and interprets it as CodeView.</summary>
      <param name="entry">A Debug Directory entry instance.</param>
      <returns>A code view debug directory data instance.</returns>
      <exception cref="T:System.ArgumentException">
        <paramref name="entry" /> is not a CodeView entry.</exception>
      <exception cref="T:System.BadImageFormatException">Bad format of the data.</exception>
      <exception cref="T:System.IO.IOException">IO error while reading from the underlying stream.</exception>
      <exception cref="T:System.InvalidOperationException">The PE image is not available.</exception>
    </member>
    <member name="M:System.Reflection.PortableExecutable.PEReader.ReadDebugDirectory">
      <summary>Reads all Debug Directory table entries.</summary>
      <returns>An array of Debug Directory table entries.</returns>
      <exception cref="T:System.BadImageFormatException">Bad format of the entry.</exception>
      <exception cref="T:System.IO.IOException">IO error while reading from the underlying stream.</exception>
      <exception cref="T:System.InvalidOperationException">The PE image is not available.</exception>
    </member>
    <member name="M:System.Reflection.PortableExecutable.PEReader.ReadEmbeddedPortablePdbDebugDirectoryData(System.Reflection.PortableExecutable.DebugDirectoryEntry)">
      <summary>Reads the data pointed to by the specified Debug Directory entry and interprets it as an Embedded Portable PDB blob.</summary>
      <param name="entry">The Debug Directory entry whose data is to be read.</param>
      <returns>The provider of a metadata reader for reading a Portable PDB image.</returns>
      <exception cref="T:System.ArgumentException">
        <paramref name="entry" /> is not a <see cref="F:System.Reflection.PortableExecutable.DebugDirectoryEntryType.EmbeddedPortablePdb" /> entry.</exception>
      <exception cref="T:System.BadImageFormatException">Bad format of the data.</exception>
      <exception cref="T:System.InvalidOperationException">PE image not available.</exception>
    </member>
    <member name="M:System.Reflection.PortableExecutable.PEReader.ReadPdbChecksumDebugDirectoryData(System.Reflection.PortableExecutable.DebugDirectoryEntry)">
      <summary>Reads the data pointed to by the specified Debug Directory entry and interprets it as a PDB Checksum entry.</summary>
      <param name="entry">The Debug Directory entry whose data is to be read.</param>
      <returns>The PDB Checksum entry.</returns>
      <exception cref="T:System.ArgumentException">
        <paramref name="entry" /> is not a PDB Checksum entry.</exception>
      <exception cref="T:System.BadImageFormatException">Bad format of the data.</exception>
      <exception cref="T:System.IO.IOException">IO error while reading from the underlying stream.</exception>
      <exception cref="T:System.InvalidOperationException">The PE image is not available.</exception>
    </member>
    <member name="M:System.Reflection.PortableExecutable.PEReader.TryOpenAssociatedPortablePdb(System.String,System.Func{System.String,System.IO.Stream},System.Reflection.Metadata.MetadataReaderProvider@,System.String@)">
      <summary>Opens a Portable PDB associated with this PE image.</summary>
      <param name="peImagePath">The path to the PE image. The path is used to locate the PDB file located in the directory containing the PE file.</param>
      <param name="pdbFileStreamProvider">If specified, called to open a <see cref="T:System.IO.Stream" /> for a given file path. The provider is expected to either return a readable and seekable <see cref="T:System.IO.Stream" />, or <see langword="null" /> if the target file doesn't exist or should be ignored for some reason. The provider should throw <see cref="T:System.IO.IOException" /> if it fails to open the file due to an unexpected IO error.</param>
      <param name="pdbReaderProvider">If successful, a new instance of <see cref="T:System.Reflection.Metadata.MetadataReaderProvider" /> to be used to read the Portable PDB,.</param>
      <param name="pdbPath">If successful and the PDB is found in a file, the path to the file, or <see langword="null" /> if the PDB is embedded in the PE image itself.</param>
      <returns>
        <see langword="true" /> if the PE image has a PDB associated with it and the PDB has been successfully opened; otherwise, <see langword="false" />.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="peImagePath" /> or <paramref name="pdbFileStreamProvider" /> is <see langword="null" />.</exception>
      <exception cref="T:System.InvalidOperationException">The stream returned from <paramref name="pdbFileStreamProvider" /> doesn't support read and seek operations.</exception>
      <exception cref="T:System.BadImageFormatException">No matching PDB file was found due to an error: The PE image or the PDB is invalid.</exception>
      <exception cref="T:System.IO.IOException">No matching PDB file was found due to an error: An IO error occurred while reading the PE image or the PDB.</exception>
    </member>
    <member name="T:System.Reflection.PortableExecutable.PEStreamOptions">
      <summary>Provides options that specify how sections of a PE image are read from a stream.</summary>
    </member>
    <member name="F:System.Reflection.PortableExecutable.PEStreamOptions.Default">
      <summary>By default, the stream is disposed when the <see cref="T:System.Reflection.PortableExecutable.PEReader" /> is disposed, and sections of the PE image are read lazily.</summary>
    </member>
    <member name="F:System.Reflection.PortableExecutable.PEStreamOptions.IsLoadedImage">
      <summary>Indicates that the underlying PE image has been loaded into memory by the OS loader.</summary>
    </member>
    <member name="F:System.Reflection.PortableExecutable.PEStreamOptions.LeaveOpen">
      <summary>Keeps the stream open when the <see cref="T:System.Reflection.PortableExecutable.PEReader" /> is disposed.</summary>
    </member>
    <member name="F:System.Reflection.PortableExecutable.PEStreamOptions.PrefetchEntireImage">
      <summary>Reads the entire image into memory right away. <see cref="T:System.Reflection.PortableExecutable.PEReader" /> closes the stream automatically by the time the constructor returns unless <see cref="F:System.Reflection.PortableExecutable.PEStreamOptions.LeaveOpen" /> is specified.</summary>
    </member>
    <member name="F:System.Reflection.PortableExecutable.PEStreamOptions.PrefetchMetadata">
      <summary>Reads the metadata section into memory right away.Reading from other sections of the file is not allowed (<see cref="T:System.InvalidOperationException" /> is thrown by the <see cref="T:System.Reflection.PortableExecutable.PEReader" />).<see cref="T:System.Reflection.PortableExecutable.PEReader" /> closes the stream automatically by the time the constructor returns unless <see cref="F:System.Reflection.PortableExecutable.PEStreamOptions.LeaveOpen" /> is specified. The underlying file may be closed and even deleted after <see cref="T:System.Reflection.PortableExecutable.PEReader" /> is constructed.</summary>
    </member>
    <member name="T:System.Reflection.PortableExecutable.ResourceSectionBuilder">
      <summary>Defines the base class for a PE resource section builder. Derive from <see cref="T:System.Reflection.PortableExecutable.ResourceSectionBuilder" /> to provide serialization logic for native resources.</summary>
    </member>
    <member name="M:System.Reflection.PortableExecutable.ResourceSectionBuilder.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Reflection.PortableExecutable.ResourceSectionBuilder" /> class.</summary>
    </member>
    <member name="M:System.Reflection.PortableExecutable.ResourceSectionBuilder.Serialize(System.Reflection.Metadata.BlobBuilder,System.Reflection.PortableExecutable.SectionLocation)">
      <summary>Serializes the specified resource.</summary>
      <param name="builder">A blob that contains the data to serialize.</param>
      <param name="location">The location to which to serialize <paramref name="builder" />.</param>
    </member>
    <member name="T:System.Reflection.PortableExecutable.SectionCharacteristics" />
    <member name="F:System.Reflection.PortableExecutable.SectionCharacteristics.Align1024Bytes" />
    <member name="F:System.Reflection.PortableExecutable.SectionCharacteristics.Align128Bytes" />
    <member name="F:System.Reflection.PortableExecutable.SectionCharacteristics.Align16Bytes" />
    <member name="F:System.Reflection.PortableExecutable.SectionCharacteristics.Align1Bytes" />
    <member name="F:System.Reflection.PortableExecutable.SectionCharacteristics.Align2048Bytes" />
    <member name="F:System.Reflection.PortableExecutable.SectionCharacteristics.Align256Bytes" />
    <member name="F:System.Reflection.PortableExecutable.SectionCharacteristics.Align2Bytes" />
    <member name="F:System.Reflection.PortableExecutable.SectionCharacteristics.Align32Bytes" />
    <member name="F:System.Reflection.PortableExecutable.SectionCharacteristics.Align4096Bytes" />
    <member name="F:System.Reflection.PortableExecutable.SectionCharacteristics.Align4Bytes" />
    <member name="F:System.Reflection.PortableExecutable.SectionCharacteristics.Align512Bytes" />
    <member name="F:System.Reflection.PortableExecutable.SectionCharacteristics.Align64Bytes" />
    <member name="F:System.Reflection.PortableExecutable.SectionCharacteristics.Align8192Bytes" />
    <member name="F:System.Reflection.PortableExecutable.SectionCharacteristics.Align8Bytes" />
    <member name="F:System.Reflection.PortableExecutable.SectionCharacteristics.AlignMask" />
    <member name="F:System.Reflection.PortableExecutable.SectionCharacteristics.ContainsCode" />
    <member name="F:System.Reflection.PortableExecutable.SectionCharacteristics.ContainsInitializedData" />
    <member name="F:System.Reflection.PortableExecutable.SectionCharacteristics.ContainsUninitializedData" />
    <member name="F:System.Reflection.PortableExecutable.SectionCharacteristics.GPRel" />
    <member name="F:System.Reflection.PortableExecutable.SectionCharacteristics.LinkerComdat" />
    <member name="F:System.Reflection.PortableExecutable.SectionCharacteristics.LinkerInfo" />
    <member name="F:System.Reflection.PortableExecutable.SectionCharacteristics.LinkerNRelocOvfl" />
    <member name="F:System.Reflection.PortableExecutable.SectionCharacteristics.LinkerOther" />
    <member name="F:System.Reflection.PortableExecutable.SectionCharacteristics.LinkerRemove" />
    <member name="F:System.Reflection.PortableExecutable.SectionCharacteristics.Mem16Bit" />
    <member name="F:System.Reflection.PortableExecutable.SectionCharacteristics.MemDiscardable" />
    <member name="F:System.Reflection.PortableExecutable.SectionCharacteristics.MemExecute" />
    <member name="F:System.Reflection.PortableExecutable.SectionCharacteristics.MemFardata" />
    <member name="F:System.Reflection.PortableExecutable.SectionCharacteristics.MemLocked" />
    <member name="F:System.Reflection.PortableExecutable.SectionCharacteristics.MemNotCached" />
    <member name="F:System.Reflection.PortableExecutable.SectionCharacteristics.MemNotPaged" />
    <member name="F:System.Reflection.PortableExecutable.SectionCharacteristics.MemPreload" />
    <member name="F:System.Reflection.PortableExecutable.SectionCharacteristics.MemProtected" />
    <member name="F:System.Reflection.PortableExecutable.SectionCharacteristics.MemPurgeable" />
    <member name="F:System.Reflection.PortableExecutable.SectionCharacteristics.MemRead" />
    <member name="F:System.Reflection.PortableExecutable.SectionCharacteristics.MemShared" />
    <member name="F:System.Reflection.PortableExecutable.SectionCharacteristics.MemSysheap" />
    <member name="F:System.Reflection.PortableExecutable.SectionCharacteristics.MemWrite" />
    <member name="F:System.Reflection.PortableExecutable.SectionCharacteristics.NoDeferSpecExc" />
    <member name="F:System.Reflection.PortableExecutable.SectionCharacteristics.TypeCopy" />
    <member name="F:System.Reflection.PortableExecutable.SectionCharacteristics.TypeDSect" />
    <member name="F:System.Reflection.PortableExecutable.SectionCharacteristics.TypeGroup" />
    <member name="F:System.Reflection.PortableExecutable.SectionCharacteristics.TypeNoLoad" />
    <member name="F:System.Reflection.PortableExecutable.SectionCharacteristics.TypeNoPad" />
    <member name="F:System.Reflection.PortableExecutable.SectionCharacteristics.TypeOver" />
    <member name="F:System.Reflection.PortableExecutable.SectionCharacteristics.TypeReg" />
    <member name="T:System.Reflection.PortableExecutable.SectionHeader">
      <summary>Provides information about the section header of a PE/COFF file.</summary>
    </member>
    <member name="P:System.Reflection.PortableExecutable.SectionHeader.Name">
      <summary>Gets the name of the section.</summary>
      <returns>The name of the section.</returns>
    </member>
    <member name="P:System.Reflection.PortableExecutable.SectionHeader.NumberOfLineNumbers">
      <summary>Gets the number of line-number entries for the section.</summary>
      <returns>The number of line-number entries for the section.</returns>
    </member>
    <member name="P:System.Reflection.PortableExecutable.SectionHeader.NumberOfRelocations">
      <summary>Gets the number of relocation entries for the section.</summary>
      <returns>The number of relocation entries for the section. Its value is zero for PE images.</returns>
    </member>
    <member name="P:System.Reflection.PortableExecutable.SectionHeader.PointerToLineNumbers">
      <summary>Gets the file pointer to the beginning of line-number entries for the section.</summary>
      <returns>The file pointer to the beginning of line-number entries for the section, or zero if there are no COFF line numbers.</returns>
    </member>
    <member name="P:System.Reflection.PortableExecutable.SectionHeader.PointerToRawData">
      <summary>Gets the file pointer to the first page of the section within the COFF file.</summary>
      <returns>The file pointer to the first page of the section within the COFF file.</returns>
    </member>
    <member name="P:System.Reflection.PortableExecutable.SectionHeader.PointerToRelocations">
      <summary>Gets the file pointer to the beginning of relocation entries for the section.</summary>
      <returns>The file pointer to the beginning of relocation entries for the section. It is set to zero for PE images or if there are no relocations.</returns>
    </member>
    <member name="P:System.Reflection.PortableExecutable.SectionHeader.SectionCharacteristics">
      <summary>Gets the flags that describe the characteristics of the section.</summary>
      <returns>The flags that describe the characteristics of the section.</returns>
    </member>
    <member name="P:System.Reflection.PortableExecutable.SectionHeader.SizeOfRawData">
      <summary>Gets the size of the section (for object files) or the size of the initialized data on disk (for image files).</summary>
      <returns>The size of the section (for object files) or the size of the initialized data on disk (for image files).</returns>
    </member>
    <member name="P:System.Reflection.PortableExecutable.SectionHeader.VirtualAddress">
      <summary>Gets the virtual addess of the section.</summary>
      <returns>The virtual address of the section.</returns>
    </member>
    <member name="P:System.Reflection.PortableExecutable.SectionHeader.VirtualSize">
      <summary>Gets the total size of the section when loaded into memory.</summary>
      <returns>The total size of the section when loaded into memory.</returns>
    </member>
    <member name="T:System.Reflection.PortableExecutable.SectionLocation" />
    <member name="M:System.Reflection.PortableExecutable.SectionLocation.#ctor(System.Int32,System.Int32)">
      <param name="relativeVirtualAddress" />
      <param name="pointerToRawData" />
    </member>
    <member name="P:System.Reflection.PortableExecutable.SectionLocation.PointerToRawData" />
    <member name="P:System.Reflection.PortableExecutable.SectionLocation.RelativeVirtualAddress" />
    <member name="T:System.Reflection.PortableExecutable.Subsystem" />
    <member name="F:System.Reflection.PortableExecutable.Subsystem.EfiApplication" />
    <member name="F:System.Reflection.PortableExecutable.Subsystem.EfiBootServiceDriver" />
    <member name="F:System.Reflection.PortableExecutable.Subsystem.EfiRom" />
    <member name="F:System.Reflection.PortableExecutable.Subsystem.EfiRuntimeDriver" />
    <member name="F:System.Reflection.PortableExecutable.Subsystem.Native" />
    <member name="F:System.Reflection.PortableExecutable.Subsystem.NativeWindows" />
    <member name="F:System.Reflection.PortableExecutable.Subsystem.OS2Cui" />
    <member name="F:System.Reflection.PortableExecutable.Subsystem.PosixCui" />
    <member name="F:System.Reflection.PortableExecutable.Subsystem.Unknown" />
    <member name="F:System.Reflection.PortableExecutable.Subsystem.WindowsBootApplication" />
    <member name="F:System.Reflection.PortableExecutable.Subsystem.WindowsCEGui" />
    <member name="F:System.Reflection.PortableExecutable.Subsystem.WindowsCui" />
    <member name="F:System.Reflection.PortableExecutable.Subsystem.WindowsGui" />
    <member name="F:System.Reflection.PortableExecutable.Subsystem.Xbox" />
  </members>
</doc>