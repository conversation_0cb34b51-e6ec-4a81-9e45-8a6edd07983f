﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>System.Reflection.TypeExtensions</name>
  </assembly>
  <members>
    <member name="T:System.Reflection.AssemblyExtensions" />
    <member name="M:System.Reflection.AssemblyExtensions.GetExportedTypes(System.Reflection.Assembly)">
      <param name="assembly" />
    </member>
    <member name="M:System.Reflection.AssemblyExtensions.GetModules(System.Reflection.Assembly)">
      <param name="assembly" />
    </member>
    <member name="M:System.Reflection.AssemblyExtensions.GetTypes(System.Reflection.Assembly)">
      <param name="assembly" />
    </member>
    <member name="T:System.Reflection.EventInfoExtensions" />
    <member name="M:System.Reflection.EventInfoExtensions.GetAddMethod(System.Reflection.EventInfo)">
      <param name="eventInfo" />
    </member>
    <member name="M:System.Reflection.EventInfoExtensions.GetAddMethod(System.Reflection.EventInfo,System.Boolean)">
      <param name="eventInfo" />
      <param name="nonPublic" />
    </member>
    <member name="M:System.Reflection.EventInfoExtensions.GetRaiseMethod(System.Reflection.EventInfo)">
      <param name="eventInfo" />
    </member>
    <member name="M:System.Reflection.EventInfoExtensions.GetRaiseMethod(System.Reflection.EventInfo,System.Boolean)">
      <param name="eventInfo" />
      <param name="nonPublic" />
    </member>
    <member name="M:System.Reflection.EventInfoExtensions.GetRemoveMethod(System.Reflection.EventInfo)">
      <param name="eventInfo" />
    </member>
    <member name="M:System.Reflection.EventInfoExtensions.GetRemoveMethod(System.Reflection.EventInfo,System.Boolean)">
      <param name="eventInfo" />
      <param name="nonPublic" />
    </member>
    <member name="T:System.Reflection.MemberInfoExtensions" />
    <member name="M:System.Reflection.MemberInfoExtensions.GetMetadataToken(System.Reflection.MemberInfo)">
      <summary>Gets a metadata token for the given member, if available.</summary>
      <param name="member">The member from which to retrieve the token, as reftype.</param>
      <returns>An integer representing the metadata token. The returned token is never nil. If unavailable, an exception is thrown.</returns>
      <exception cref="T:System.InvalidOperationException">There is no metadata token available.</exception>
    </member>
    <member name="M:System.Reflection.MemberInfoExtensions.HasMetadataToken(System.Reflection.MemberInfo)">
      <summary>Returns a value that indicates whether a metadata token is available for the specified member.</summary>
      <param name="member">The member to analyze, as reftype.</param>
      <returns>
        <see langword="true" /> if there is a metadata token available for the given member; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="T:System.Reflection.MethodInfoExtensions" />
    <member name="M:System.Reflection.MethodInfoExtensions.GetBaseDefinition(System.Reflection.MethodInfo)">
      <param name="method" />
    </member>
    <member name="T:System.Reflection.ModuleExtensions" />
    <member name="M:System.Reflection.ModuleExtensions.GetModuleVersionId(System.Reflection.Module)">
      <param name="module" />
    </member>
    <member name="M:System.Reflection.ModuleExtensions.HasModuleVersionId(System.Reflection.Module)">
      <param name="module" />
    </member>
    <member name="T:System.Reflection.PropertyInfoExtensions" />
    <member name="M:System.Reflection.PropertyInfoExtensions.GetAccessors(System.Reflection.PropertyInfo)">
      <param name="property" />
    </member>
    <member name="M:System.Reflection.PropertyInfoExtensions.GetAccessors(System.Reflection.PropertyInfo,System.Boolean)">
      <param name="property" />
      <param name="nonPublic" />
    </member>
    <member name="M:System.Reflection.PropertyInfoExtensions.GetGetMethod(System.Reflection.PropertyInfo)">
      <param name="property" />
    </member>
    <member name="M:System.Reflection.PropertyInfoExtensions.GetGetMethod(System.Reflection.PropertyInfo,System.Boolean)">
      <param name="property" />
      <param name="nonPublic" />
    </member>
    <member name="M:System.Reflection.PropertyInfoExtensions.GetSetMethod(System.Reflection.PropertyInfo)">
      <param name="property" />
    </member>
    <member name="M:System.Reflection.PropertyInfoExtensions.GetSetMethod(System.Reflection.PropertyInfo,System.Boolean)">
      <param name="property" />
      <param name="nonPublic" />
    </member>
    <member name="T:System.Reflection.TypeExtensions" />
    <member name="M:System.Reflection.TypeExtensions.GetConstructor(System.Type,System.Type[])">
      <param name="type" />
      <param name="types" />
    </member>
    <member name="M:System.Reflection.TypeExtensions.GetConstructors(System.Type)">
      <param name="type" />
    </member>
    <member name="M:System.Reflection.TypeExtensions.GetConstructors(System.Type,System.Reflection.BindingFlags)">
      <param name="type" />
      <param name="bindingAttr" />
    </member>
    <member name="M:System.Reflection.TypeExtensions.GetDefaultMembers(System.Type)">
      <param name="type" />
    </member>
    <member name="M:System.Reflection.TypeExtensions.GetEvent(System.Type,System.String)">
      <param name="type" />
      <param name="name" />
    </member>
    <member name="M:System.Reflection.TypeExtensions.GetEvent(System.Type,System.String,System.Reflection.BindingFlags)">
      <param name="type" />
      <param name="name" />
      <param name="bindingAttr" />
    </member>
    <member name="M:System.Reflection.TypeExtensions.GetEvents(System.Type)">
      <param name="type" />
    </member>
    <member name="M:System.Reflection.TypeExtensions.GetEvents(System.Type,System.Reflection.BindingFlags)">
      <param name="type" />
      <param name="bindingAttr" />
    </member>
    <member name="M:System.Reflection.TypeExtensions.GetField(System.Type,System.String)">
      <param name="type" />
      <param name="name" />
    </member>
    <member name="M:System.Reflection.TypeExtensions.GetField(System.Type,System.String,System.Reflection.BindingFlags)">
      <param name="type" />
      <param name="name" />
      <param name="bindingAttr" />
    </member>
    <member name="M:System.Reflection.TypeExtensions.GetFields(System.Type)">
      <param name="type" />
    </member>
    <member name="M:System.Reflection.TypeExtensions.GetFields(System.Type,System.Reflection.BindingFlags)">
      <param name="type" />
      <param name="bindingAttr" />
    </member>
    <member name="M:System.Reflection.TypeExtensions.GetGenericArguments(System.Type)">
      <param name="type" />
    </member>
    <member name="M:System.Reflection.TypeExtensions.GetInterfaces(System.Type)">
      <param name="type" />
    </member>
    <member name="M:System.Reflection.TypeExtensions.GetMember(System.Type,System.String)">
      <param name="type" />
      <param name="name" />
    </member>
    <member name="M:System.Reflection.TypeExtensions.GetMember(System.Type,System.String,System.Reflection.BindingFlags)">
      <param name="type" />
      <param name="name" />
      <param name="bindingAttr" />
    </member>
    <member name="M:System.Reflection.TypeExtensions.GetMembers(System.Type)">
      <param name="type" />
    </member>
    <member name="M:System.Reflection.TypeExtensions.GetMembers(System.Type,System.Reflection.BindingFlags)">
      <param name="type" />
      <param name="bindingAttr" />
    </member>
    <member name="M:System.Reflection.TypeExtensions.GetMethod(System.Type,System.String)">
      <param name="type" />
      <param name="name" />
    </member>
    <member name="M:System.Reflection.TypeExtensions.GetMethod(System.Type,System.String,System.Reflection.BindingFlags)">
      <param name="type" />
      <param name="name" />
      <param name="bindingAttr" />
    </member>
    <member name="M:System.Reflection.TypeExtensions.GetMethod(System.Type,System.String,System.Type[])">
      <param name="type" />
      <param name="name" />
      <param name="types" />
    </member>
    <member name="M:System.Reflection.TypeExtensions.GetMethods(System.Type)">
      <param name="type" />
    </member>
    <member name="M:System.Reflection.TypeExtensions.GetMethods(System.Type,System.Reflection.BindingFlags)">
      <param name="type" />
      <param name="bindingAttr" />
    </member>
    <member name="M:System.Reflection.TypeExtensions.GetNestedType(System.Type,System.String,System.Reflection.BindingFlags)">
      <param name="type" />
      <param name="name" />
      <param name="bindingAttr" />
    </member>
    <member name="M:System.Reflection.TypeExtensions.GetNestedTypes(System.Type,System.Reflection.BindingFlags)">
      <param name="type" />
      <param name="bindingAttr" />
    </member>
    <member name="M:System.Reflection.TypeExtensions.GetProperties(System.Type)">
      <param name="type" />
    </member>
    <member name="M:System.Reflection.TypeExtensions.GetProperties(System.Type,System.Reflection.BindingFlags)">
      <param name="type" />
      <param name="bindingAttr" />
    </member>
    <member name="M:System.Reflection.TypeExtensions.GetProperty(System.Type,System.String)">
      <param name="type" />
      <param name="name" />
    </member>
    <member name="M:System.Reflection.TypeExtensions.GetProperty(System.Type,System.String,System.Reflection.BindingFlags)">
      <param name="type" />
      <param name="name" />
      <param name="bindingAttr" />
    </member>
    <member name="M:System.Reflection.TypeExtensions.GetProperty(System.Type,System.String,System.Type)">
      <param name="type" />
      <param name="name" />
      <param name="returnType" />
    </member>
    <member name="M:System.Reflection.TypeExtensions.GetProperty(System.Type,System.String,System.Type,System.Type[])">
      <param name="type" />
      <param name="name" />
      <param name="returnType" />
      <param name="types" />
    </member>
    <member name="M:System.Reflection.TypeExtensions.IsAssignableFrom(System.Type,System.Type)">
      <param name="type" />
      <param name="c" />
    </member>
    <member name="M:System.Reflection.TypeExtensions.IsInstanceOfType(System.Type,System.Object)">
      <param name="type" />
      <param name="o" />
    </member>
  </members>
</doc>