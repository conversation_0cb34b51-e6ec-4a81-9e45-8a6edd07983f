﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>System.Runtime.CompilerServices.VisualC</name>
  </assembly>
  <members>
    <member name="T:System.Runtime.CompilerServices.CallConvCdecl">
      <summary>Indicates that a method should use the <see langword="Cdecl" /> calling convention.</summary>
    </member>
    <member name="M:System.Runtime.CompilerServices.CallConvCdecl.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Runtime.CompilerServices.CallConvCdecl" /> class.</summary>
    </member>
    <member name="T:System.Runtime.CompilerServices.CallConvFastcall">
      <summary>This calling convention is not supported in this version of the .NET Framework.</summary>
    </member>
    <member name="M:System.Runtime.CompilerServices.CallConvFastcall.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Runtime.CompilerServices.CallConvFastcall" /> class.</summary>
    </member>
    <member name="T:System.Runtime.CompilerServices.CallConvStdcall">
      <summary>Indicates that a method should use the <see langword="StdCall" /> calling convention.</summary>
    </member>
    <member name="M:System.Runtime.CompilerServices.CallConvStdcall.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Runtime.CompilerServices.CallConvStdcall" /> class.</summary>
    </member>
    <member name="T:System.Runtime.CompilerServices.CallConvThiscall">
      <summary>Indicates that a method should use the <see langword="ThisCall" /> calling convention.</summary>
    </member>
    <member name="M:System.Runtime.CompilerServices.CallConvThiscall.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Runtime.CompilerServices.CallConvThiscall" /> class.</summary>
    </member>
    <member name="T:System.Runtime.CompilerServices.CompilerMarshalOverride">
      <summary>Indicates that the modified instance of a variable differs from its true type when marshaling. This class cannot be inherited.</summary>
    </member>
    <member name="T:System.Runtime.CompilerServices.HasCopySemanticsAttribute">
      <summary>This class is not used in the .NET Framework version 2.0 and is reserved for future use. This class cannot be inherited.</summary>
    </member>
    <member name="M:System.Runtime.CompilerServices.HasCopySemanticsAttribute.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Runtime.CompilerServices.HasCopySemanticsAttribute" /> class.</summary>
    </member>
    <member name="T:System.Runtime.CompilerServices.IsBoxed">
      <summary>Indicates that the modified reference type is a boxed value type. This class cannot be inherited.</summary>
    </member>
    <member name="T:System.Runtime.CompilerServices.IsByValue">
      <summary>Indicates that a modified method argument should be interpreted as having object passed-by-value semantics. This modifier is applied to reference types.  This class cannot be inherited.</summary>
    </member>
    <member name="T:System.Runtime.CompilerServices.IsCopyConstructed">
      <summary>Indicates that any copying of values of this type must use the copy constructor provided by the type.  This class cannot be inherited.</summary>
    </member>
    <member name="T:System.Runtime.CompilerServices.IsExplicitlyDereferenced">
      <summary>Indicates that a managed pointer represents a pointer parameter within a method signature. This class cannot be inherited.</summary>
    </member>
    <member name="T:System.Runtime.CompilerServices.IsImplicitlyDereferenced">
      <summary>Indicates that the modified garbage collection reference represents a reference parameter within a method signature. This class cannot be inherited.</summary>
    </member>
    <member name="T:System.Runtime.CompilerServices.IsJitIntrinsic">
      <summary>Indicates that a modified method is an intrinsic value for which the just-in-time (JIT) compiler  can perform special code generation. This class cannot be inherited.</summary>
    </member>
    <member name="T:System.Runtime.CompilerServices.IsLong">
      <summary>Indicates that a modified integer is a standard C++ <see langword="long" /> value. This class cannot be inherited.</summary>
    </member>
    <member name="T:System.Runtime.CompilerServices.IsPinned">
      <summary>Indicates that a modified instance is pinned in memory. This class cannot be inherited.</summary>
    </member>
    <member name="T:System.Runtime.CompilerServices.IsSignUnspecifiedByte">
      <summary>Indicates that a modifier is neither signed nor unsigned. This class cannot be inherited.</summary>
    </member>
    <member name="T:System.Runtime.CompilerServices.IsUdtReturn">
      <summary>Indicates that a return type is a user-defined type. This class cannot be inherited.</summary>
    </member>
    <member name="T:System.Runtime.CompilerServices.NativeCppClassAttribute">
      <summary>Applies metadata to an assembly that indicates that a type is an unmanaged type.  This class cannot be inherited.</summary>
    </member>
    <member name="M:System.Runtime.CompilerServices.NativeCppClassAttribute.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Runtime.CompilerServices.NativeCppClassAttribute" /> class.</summary>
    </member>
    <member name="T:System.Runtime.CompilerServices.RequiredAttributeAttribute">
      <summary>Specifies that an importing compiler must fully understand the semantics of a type definition, or refuse to use it.  This class cannot be inherited.</summary>
    </member>
    <member name="M:System.Runtime.CompilerServices.RequiredAttributeAttribute.#ctor(System.Type)">
      <summary>Initializes a new instance of the <see cref="T:System.Runtime.CompilerServices.RequiredAttributeAttribute" /> class.</summary>
      <param name="requiredContract">A type that an importing compiler must fully understand.
This parameter is not supported in the .NET Framework version 2.0 and later.</param>
    </member>
    <member name="P:System.Runtime.CompilerServices.RequiredAttributeAttribute.RequiredContract">
      <summary>Gets a type that an importing compiler must fully understand.</summary>
      <returns>A type that an importing compiler must fully understand.</returns>
    </member>
    <member name="T:System.Runtime.CompilerServices.ScopelessEnumAttribute">
      <summary>Indicates that a native enumeration is not qualified by the enumeration type name. This class cannot be inherited.</summary>
    </member>
    <member name="M:System.Runtime.CompilerServices.ScopelessEnumAttribute.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Runtime.CompilerServices.ScopelessEnumAttribute" /> class.</summary>
    </member>
  </members>
</doc>