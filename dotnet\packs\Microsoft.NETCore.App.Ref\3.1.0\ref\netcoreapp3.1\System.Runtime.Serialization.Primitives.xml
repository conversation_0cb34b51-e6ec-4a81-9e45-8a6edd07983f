﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>System.Runtime.Serialization.Primitives</name>
  </assembly>
  <members>
    <member name="T:System.Runtime.Serialization.CollectionDataContractAttribute">
      <summary>When applied to a collection type, enables custom specification of the collection item elements. This attribute can be applied only to types that are recognized by the <see cref="T:System.Runtime.Serialization.DataContractSerializer" /> as valid, serializable collections.</summary>
    </member>
    <member name="M:System.Runtime.Serialization.CollectionDataContractAttribute.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Runtime.Serialization.CollectionDataContractAttribute" /> class.</summary>
    </member>
    <member name="P:System.Runtime.Serialization.CollectionDataContractAttribute.IsItemNameSetExplicitly">
      <summary>Gets whether <see cref="P:System.Runtime.Serialization.CollectionDataContractAttribute.ItemName" /> has been explicitly set.</summary>
      <returns>
        <see langword="true" /> if the item name has been explicitly set; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Runtime.Serialization.CollectionDataContractAttribute.IsKeyNameSetExplicitly">
      <summary>Gets whether <see cref="P:System.Runtime.Serialization.CollectionDataContractAttribute.KeyName" /> has been explicitly set.</summary>
      <returns>
        <see langword="true" /> if the key name has been explicitly set; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Runtime.Serialization.CollectionDataContractAttribute.IsNameSetExplicitly">
      <summary>Gets whether <see cref="P:System.Runtime.Serialization.CollectionDataContractAttribute.Name" /> has been explicitly set.</summary>
      <returns>
        <see langword="true" /> if the name has been explicitly set; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Runtime.Serialization.CollectionDataContractAttribute.IsNamespaceSetExplicitly">
      <summary>Gets whether <see cref="P:System.Runtime.Serialization.CollectionDataContractAttribute.Namespace" /> has been explicitly set.</summary>
      <returns>
        <see langword="true" /> if the item namespace has been explicitly set; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Runtime.Serialization.CollectionDataContractAttribute.IsReference">
      <summary>Gets or sets a value that indicates whether to preserve object reference data.</summary>
      <returns>
        <see langword="true" /> to keep object reference data; otherwise, <see langword="false" />. The default is <see langword="false" />.</returns>
    </member>
    <member name="P:System.Runtime.Serialization.CollectionDataContractAttribute.IsReferenceSetExplicitly">
      <summary>Gets whether reference has been explicitly set.</summary>
      <returns>
        <see langword="true" /> if the reference has been explicitly set; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Runtime.Serialization.CollectionDataContractAttribute.IsValueNameSetExplicitly">
      <summary>Gets whether <see cref="P:System.Runtime.Serialization.CollectionDataContractAttribute.ValueName" /> has been explicitly set.</summary>
      <returns>
        <see langword="true" /> if the value name has been explicitly set; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Runtime.Serialization.CollectionDataContractAttribute.ItemName">
      <summary>Gets or sets a custom name for a collection element.</summary>
      <returns>The name to apply to collection elements.</returns>
    </member>
    <member name="P:System.Runtime.Serialization.CollectionDataContractAttribute.KeyName">
      <summary>Gets or sets the custom name for a dictionary key name.</summary>
      <returns>The name to use instead of the default dictionary key name.</returns>
    </member>
    <member name="P:System.Runtime.Serialization.CollectionDataContractAttribute.Name">
      <summary>Gets or sets the data contract name for the collection type.</summary>
      <returns>The data contract name for the collection type.</returns>
    </member>
    <member name="P:System.Runtime.Serialization.CollectionDataContractAttribute.Namespace">
      <summary>Gets or sets the namespace for the data contract.</summary>
      <returns>The namespace of the data contract.</returns>
    </member>
    <member name="P:System.Runtime.Serialization.CollectionDataContractAttribute.ValueName">
      <summary>Gets or sets the custom name for a dictionary value name.</summary>
      <returns>The name to use instead of the default dictionary value name.</returns>
    </member>
    <member name="T:System.Runtime.Serialization.ContractNamespaceAttribute">
      <summary>Specifies the CLR namespace and XML namespace of the data contract.</summary>
    </member>
    <member name="M:System.Runtime.Serialization.ContractNamespaceAttribute.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Runtime.Serialization.ContractNamespaceAttribute" /> class using the supplied namespace.</summary>
      <param name="contractNamespace">The namespace of the contract.</param>
    </member>
    <member name="P:System.Runtime.Serialization.ContractNamespaceAttribute.ClrNamespace">
      <summary>Gets or sets the CLR namespace of the data contract type.</summary>
      <returns>The CLR-legal namespace of a type.</returns>
    </member>
    <member name="P:System.Runtime.Serialization.ContractNamespaceAttribute.ContractNamespace">
      <summary>Gets the namespace of the data contract members.</summary>
      <returns>The namespace of the data contract members.</returns>
    </member>
    <member name="T:System.Runtime.Serialization.DataContractAttribute">
      <summary>Specifies that the type defines or implements a data contract and is serializable by a serializer, such as the <see cref="T:System.Runtime.Serialization.DataContractSerializer" />. To make their type serializable, type authors must define a data contract for their type.</summary>
    </member>
    <member name="M:System.Runtime.Serialization.DataContractAttribute.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Runtime.Serialization.DataContractAttribute" /> class.</summary>
    </member>
    <member name="P:System.Runtime.Serialization.DataContractAttribute.IsNameSetExplicitly">
      <summary>Gets whether <see cref="P:System.Runtime.Serialization.DataContractAttribute.Name" /> has been explicitly set.</summary>
      <returns>
        <see langword="true" /> if the name has been explicitly set; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Runtime.Serialization.DataContractAttribute.IsNamespaceSetExplicitly">
      <summary>Gets whether <see cref="P:System.Runtime.Serialization.DataContractAttribute.Namespace" /> has been explicitly set.</summary>
      <returns>
        <see langword="true" /> if the namespace has been explicitly set; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Runtime.Serialization.DataContractAttribute.IsReference">
      <summary>Gets or sets a value that indicates whether to preserve object reference data.</summary>
      <returns>
        <see langword="true" /> to keep object reference data using standard XML; otherwise, <see langword="false" />. The default is <see langword="false" />.</returns>
    </member>
    <member name="P:System.Runtime.Serialization.DataContractAttribute.IsReferenceSetExplicitly">
      <summary>Gets whether <see cref="P:System.Runtime.Serialization.DataContractAttribute.IsReference" /> has been explicitly set.</summary>
      <returns>
        <see langword="true" /> if the reference has been explicitly set; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Runtime.Serialization.DataContractAttribute.Name">
      <summary>Gets or sets the name of the data contract for the type.</summary>
      <returns>The local name of a data contract. The default is the name of the class that the attribute is applied to.</returns>
    </member>
    <member name="P:System.Runtime.Serialization.DataContractAttribute.Namespace">
      <summary>Gets or sets the namespace for the data contract for the type.</summary>
      <returns>The namespace of the contract.</returns>
    </member>
    <member name="T:System.Runtime.Serialization.DataMemberAttribute">
      <summary>When applied to the member of a type, specifies that the member is part of a data contract and is serializable by the <see cref="T:System.Runtime.Serialization.DataContractSerializer" />.</summary>
    </member>
    <member name="M:System.Runtime.Serialization.DataMemberAttribute.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Runtime.Serialization.DataMemberAttribute" /> class.</summary>
    </member>
    <member name="P:System.Runtime.Serialization.DataMemberAttribute.EmitDefaultValue">
      <summary>Gets or sets a value that specifies whether to serialize the default value for a field or property being serialized.</summary>
      <returns>
        <see langword="true" /> if the default value for a member should be generated in the serialization stream; otherwise, <see langword="false" />. The default is <see langword="true" />.</returns>
    </member>
    <member name="P:System.Runtime.Serialization.DataMemberAttribute.IsNameSetExplicitly">
      <summary>Gets whether <see cref="P:System.Runtime.Serialization.DataMemberAttribute.Name" /> has been explicitly set.</summary>
      <returns>
        <see langword="true" /> if the name has been explicitly set; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Runtime.Serialization.DataMemberAttribute.IsRequired">
      <summary>Gets or sets a value that instructs the serialization engine that the member must be present when reading or deserializing.</summary>
      <returns>
        <see langword="true" />, if the member is required; otherwise, <see langword="false" />.</returns>
      <exception cref="T:System.Runtime.Serialization.SerializationException">the member is not present.</exception>
    </member>
    <member name="P:System.Runtime.Serialization.DataMemberAttribute.Name">
      <summary>Gets or sets a data member name.</summary>
      <returns>The name of the data member. The default is the name of the target that the attribute is applied to.</returns>
    </member>
    <member name="P:System.Runtime.Serialization.DataMemberAttribute.Order">
      <summary>Gets or sets the order of serialization and deserialization of a member.</summary>
      <returns>The numeric order of serialization or deserialization.</returns>
    </member>
    <member name="T:System.Runtime.Serialization.EnumMemberAttribute">
      <summary>Specifies that the field is an enumeration member and should be serialized.</summary>
    </member>
    <member name="M:System.Runtime.Serialization.EnumMemberAttribute.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Runtime.Serialization.EnumMemberAttribute" /> class.</summary>
    </member>
    <member name="P:System.Runtime.Serialization.EnumMemberAttribute.IsValueSetExplicitly">
      <summary>Gets whether the <see cref="P:System.Runtime.Serialization.EnumMemberAttribute.Value" /> has been explicitly set.</summary>
      <returns>
        <see langword="true" /> if the value has been explicitly set; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Runtime.Serialization.EnumMemberAttribute.Value">
      <summary>Gets or sets the value associated with the enumeration member the attribute is applied to.</summary>
      <returns>The value associated with the enumeration member.</returns>
    </member>
    <member name="T:System.Runtime.Serialization.IgnoreDataMemberAttribute">
      <summary>When applied to the member of a type, specifies that the member is not part of a data contract and is not serialized.</summary>
    </member>
    <member name="M:System.Runtime.Serialization.IgnoreDataMemberAttribute.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Runtime.Serialization.IgnoreDataMemberAttribute" /> class.</summary>
    </member>
    <member name="T:System.Runtime.Serialization.InvalidDataContractException">
      <summary>The exception that is thrown when the <see cref="T:System.Runtime.Serialization.DataContractSerializer" /> or <see cref="T:System.Runtime.Serialization.NetDataContractSerializer" /> encounters an invalid data contract during serialization and deserialization.</summary>
    </member>
    <member name="M:System.Runtime.Serialization.InvalidDataContractException.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Runtime.Serialization.InvalidDataContractException" /> class.</summary>
    </member>
    <member name="M:System.Runtime.Serialization.InvalidDataContractException.#ctor(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
      <summary>Initializes a new instance of the <see cref="T:System.Runtime.Serialization.InvalidDataContractException" /> class with the specified <see cref="T:System.Runtime.Serialization.SerializationInfo" /> and <see cref="T:System.Runtime.Serialization.StreamingContext" />.</summary>
      <param name="info">A <see cref="T:System.Runtime.Serialization.SerializationInfo" /> that contains data needed to serialize and deserialize an object.</param>
      <param name="context">A <see cref="T:System.Runtime.Serialization.StreamingContext" /> that specifies user context during serialization and deserialization.</param>
    </member>
    <member name="M:System.Runtime.Serialization.InvalidDataContractException.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Runtime.Serialization.InvalidDataContractException" /> class with the specified error message.</summary>
      <param name="message">A description of the error.</param>
    </member>
    <member name="M:System.Runtime.Serialization.InvalidDataContractException.#ctor(System.String,System.Exception)">
      <summary>Initializes a new instance of the <see cref="T:System.Runtime.Serialization.InvalidDataContractException" /> class with the specified error message and inner exception.</summary>
      <param name="message">A description of the error.</param>
      <param name="innerException">The original <see cref="T:System.Exception" />.</param>
    </member>
    <member name="T:System.Runtime.Serialization.ISerializationSurrogateProvider">
      <summary>Provides the methods needed to construct a serialization surrogate that extends the <see cref="T:System.Runtime.Serialization.DataContractSerializer" />. A serialization surrogate is used during serialization and deserialization to substitute one type for another.</summary>
    </member>
    <member name="M:System.Runtime.Serialization.ISerializationSurrogateProvider.GetDeserializedObject(System.Object,System.Type)">
      <summary>During deserialization, returns an object that is a substitute for the specified object.</summary>
      <param name="obj">The deserialized object to be substituted.</param>
      <param name="targetType">The <see cref="T:System.Type" /> that the substituted object should be assigned to.</param>
      <returns>The substituted deserialized object.</returns>
    </member>
    <member name="M:System.Runtime.Serialization.ISerializationSurrogateProvider.GetObjectToSerialize(System.Object,System.Type)">
      <summary>During serialization, returns an object that substitutes the specified object.</summary>
      <param name="obj">The object to substitute.</param>
      <param name="targetType">The <see cref="T:System.Type" /> that the substituted object should be assigned to.</param>
      <returns>The substituted object that will be serialized.</returns>
    </member>
    <member name="M:System.Runtime.Serialization.ISerializationSurrogateProvider.GetSurrogateType(System.Type)">
      <summary>During serialization, deserialization, and schema import and export, returns a data contract type that substitutes the specified type.</summary>
      <param name="type">The type to substitute.</param>
      <returns>The <see cref="T:System.Type" /> to substitute for the <paramref name="type" /> value.</returns>
    </member>
    <member name="T:System.Runtime.Serialization.KnownTypeAttribute">
      <summary>Specifies types that should be recognized by the <see cref="T:System.Runtime.Serialization.DataContractSerializer" /> when serializing or deserializing a given type.</summary>
    </member>
    <member name="M:System.Runtime.Serialization.KnownTypeAttribute.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Runtime.Serialization.KnownTypeAttribute" /> class with the name of a method that returns an <see cref="T:System.Collections.IEnumerable" /> of known types.</summary>
      <param name="methodName">The name of the method that returns an <see cref="T:System.Collections.IEnumerable" /> of types used when serializing or deserializing data.</param>
    </member>
    <member name="M:System.Runtime.Serialization.KnownTypeAttribute.#ctor(System.Type)">
      <summary>Initializes a new instance of the <see cref="T:System.Runtime.Serialization.KnownTypeAttribute" /> class with the specified type.</summary>
      <param name="type">The <see cref="T:System.Type" /> that is included as a known type when serializing or deserializing data.</param>
    </member>
    <member name="P:System.Runtime.Serialization.KnownTypeAttribute.MethodName">
      <summary>Gets the name of a method that will return a list of types that should be recognized during serialization or deserialization.</summary>
      <returns>A <see cref="T:System.String" /> that contains the name of the method on the type defined by the <see cref="T:System.Runtime.Serialization.KnownTypeAttribute" /> class.</returns>
    </member>
    <member name="P:System.Runtime.Serialization.KnownTypeAttribute.Type">
      <summary>Gets the type that should be recognized during serialization or deserialization by the <see cref="T:System.Runtime.Serialization.DataContractSerializer" />.</summary>
      <returns>The <see cref="T:System.Type" /> that is used during serialization or deserialization.</returns>
    </member>
  </members>
</doc>