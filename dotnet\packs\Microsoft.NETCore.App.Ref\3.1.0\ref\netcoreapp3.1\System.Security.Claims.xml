﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>System.Security.Claims</name>
  </assembly>
  <members>
    <member name="T:System.Security.Claims.Claim">
      <summary>Represents a claim.</summary>
    </member>
    <member name="M:System.Security.Claims.Claim.#ctor(System.IO.BinaryReader)">
      <summary>Initializes an instance of <see cref="T:System.Security.Claims.Claim" /> with the specified <see cref="T:System.IO.BinaryReader" />.</summary>
      <param name="reader">A <see cref="T:System.IO.BinaryReader" /> pointing to a <see cref="T:System.Security.Claims.Claim" />.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="reader" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Security.Claims.Claim.#ctor(System.IO.BinaryReader,System.Security.Claims.ClaimsIdentity)">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Claims.Claim" /> class with the specified reader and subject.</summary>
      <param name="reader">The binary reader.</param>
      <param name="subject">The subject that this claim describes.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="reader" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Security.Claims.Claim.#ctor(System.Security.Claims.Claim)">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Claims.Claim" /> class.</summary>
      <param name="other">The security claim.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="other" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Security.Claims.Claim.#ctor(System.Security.Claims.Claim,System.Security.Claims.ClaimsIdentity)">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Claims.Claim" /> class with the specified security claim and subject.</summary>
      <param name="other">The security claim.</param>
      <param name="subject">The subject that this claim describes.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="other" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Security.Claims.Claim.#ctor(System.String,System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Claims.Claim" /> class with the specified claim type, and value.</summary>
      <param name="type">The claim type.</param>
      <param name="value">The claim value.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="type" /> or <paramref name="value" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Security.Claims.Claim.#ctor(System.String,System.String,System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Claims.Claim" /> class with the specified claim type, value, and value type.</summary>
      <param name="type">The claim type.</param>
      <param name="value">The claim value.</param>
      <param name="valueType">The claim value type. If this parameter is <see langword="null" />, then <see cref="F:System.Security.Claims.ClaimValueTypes.String" /> is used.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="type" /> or <paramref name="value" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Security.Claims.Claim.#ctor(System.String,System.String,System.String,System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Claims.Claim" /> class with the specified claim type, value, value type, and issuer.</summary>
      <param name="type">The claim type.</param>
      <param name="value">The claim value.</param>
      <param name="valueType">The claim value type. If this parameter is <see langword="null" />, then <see cref="F:System.Security.Claims.ClaimValueTypes.String" /> is used.</param>
      <param name="issuer">The claim issuer. If this parameter is empty or <see langword="null" />, then <see cref="F:System.Security.Claims.ClaimsIdentity.DefaultIssuer" /> is used.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="type" /> or <paramref name="value" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Security.Claims.Claim.#ctor(System.String,System.String,System.String,System.String,System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Claims.Claim" /> class with the specified claim type, value, value type, issuer,  and original issuer.</summary>
      <param name="type">The claim type.</param>
      <param name="value">The claim value.</param>
      <param name="valueType">The claim value type. If this parameter is <see langword="null" />, then <see cref="F:System.Security.Claims.ClaimValueTypes.String" /> is used.</param>
      <param name="issuer">The claim issuer. If this parameter is empty or <see langword="null" />, then <see cref="F:System.Security.Claims.ClaimsIdentity.DefaultIssuer" /> is used.</param>
      <param name="originalIssuer">The original issuer of the claim. If this parameter is empty or <see langword="null" />, then the <see cref="P:System.Security.Claims.Claim.OriginalIssuer" /> property is set to the value of the <see cref="P:System.Security.Claims.Claim.Issuer" /> property.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="type" /> or <paramref name="value" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Security.Claims.Claim.#ctor(System.String,System.String,System.String,System.String,System.String,System.Security.Claims.ClaimsIdentity)">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Claims.Claim" /> class with the specified claim type, value, value type, issuer, original issuer and subject.</summary>
      <param name="type">The claim type.</param>
      <param name="value">The claim value.</param>
      <param name="valueType">The claim value type. If this parameter is <see langword="null" />, then <see cref="F:System.Security.Claims.ClaimValueTypes.String" /> is used.</param>
      <param name="issuer">The claim issuer. If this parameter is empty or <see langword="null" />, then <see cref="F:System.Security.Claims.ClaimsIdentity.DefaultIssuer" /> is used.</param>
      <param name="originalIssuer">The original issuer of the claim. If this parameter is empty or <see langword="null" />, then the <see cref="P:System.Security.Claims.Claim.OriginalIssuer" /> property is set to the value of the <see cref="P:System.Security.Claims.Claim.Issuer" /> property.</param>
      <param name="subject">The subject that this claim describes.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="type" /> or <paramref name="value" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Security.Claims.Claim.Clone">
      <summary>Returns a new <see cref="T:System.Security.Claims.Claim" /> object copied from this object. The new claim does not have a subject.</summary>
      <returns>The new claim object.</returns>
    </member>
    <member name="M:System.Security.Claims.Claim.Clone(System.Security.Claims.ClaimsIdentity)">
      <summary>Returns a new <see cref="T:System.Security.Claims.Claim" /> object copied from this object. The subject of the new claim is set to the specified ClaimsIdentity.</summary>
      <param name="identity">The intended subject of the new claim.</param>
      <returns>The new claim object.</returns>
    </member>
    <member name="P:System.Security.Claims.Claim.CustomSerializationData">
      <summary>Contains any additional data provided by a derived type.</summary>
      <returns>A <see cref="T:System.Byte" /> array representing the additional serialized data.</returns>
    </member>
    <member name="P:System.Security.Claims.Claim.Issuer">
      <summary>Gets the issuer of the claim.</summary>
      <returns>A name that refers to the issuer of the claim.</returns>
    </member>
    <member name="P:System.Security.Claims.Claim.OriginalIssuer">
      <summary>Gets the original issuer of the claim.</summary>
      <returns>A name that refers to the original issuer of the claim.</returns>
    </member>
    <member name="P:System.Security.Claims.Claim.Properties">
      <summary>Gets a dictionary that contains additional properties associated with this claim.</summary>
      <returns>A dictionary that contains additional properties associated with the claim. The properties are represented as name-value pairs.</returns>
    </member>
    <member name="P:System.Security.Claims.Claim.Subject">
      <summary>Gets the subject of the claim.</summary>
      <returns>The subject of the claim.</returns>
    </member>
    <member name="M:System.Security.Claims.Claim.ToString">
      <summary>Returns a string representation of this <see cref="T:System.Security.Claims.Claim" /> object.</summary>
      <returns>The string representation of this <see cref="T:System.Security.Claims.Claim" /> object.</returns>
    </member>
    <member name="P:System.Security.Claims.Claim.Type">
      <summary>Gets the claim type of the claim.</summary>
      <returns>The claim type.</returns>
    </member>
    <member name="P:System.Security.Claims.Claim.Value">
      <summary>Gets the value of the claim.</summary>
      <returns>The claim value.</returns>
    </member>
    <member name="P:System.Security.Claims.Claim.ValueType">
      <summary>Gets the value type of the claim.</summary>
      <returns>The claim value type.</returns>
    </member>
    <member name="M:System.Security.Claims.Claim.WriteTo(System.IO.BinaryWriter)">
      <summary>Writes this <see cref="T:System.Security.Claims.Claim" /> to the writer.</summary>
      <param name="writer">The writer to use for data storage.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="writer" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Security.Claims.Claim.WriteTo(System.IO.BinaryWriter,System.Byte[])">
      <summary>Writes this <see cref="T:System.Security.Claims.Claim" /> to the writer.</summary>
      <param name="writer">The writer to write this claim.</param>
      <param name="userData">The user data to claim.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="writer" /> is <see langword="null" />.</exception>
    </member>
    <member name="T:System.Security.Claims.ClaimsIdentity">
      <summary>Represents a claims-based identity.</summary>
    </member>
    <member name="M:System.Security.Claims.ClaimsIdentity.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Claims.ClaimsIdentity" /> class with an empty claims collection.</summary>
    </member>
    <member name="M:System.Security.Claims.ClaimsIdentity.#ctor(System.Collections.Generic.IEnumerable{System.Security.Claims.Claim})">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Claims.ClaimsIdentity" /> class using an enumerated collection of <see cref="T:System.Security.Claims.Claim" /> objects.</summary>
      <param name="claims">The claims with which to populate the claims identity.</param>
    </member>
    <member name="M:System.Security.Claims.ClaimsIdentity.#ctor(System.Collections.Generic.IEnumerable{System.Security.Claims.Claim},System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Claims.ClaimsIdentity" /> class with the specified claims and authentication type.</summary>
      <param name="claims">The claims with which to populate the claims identity.</param>
      <param name="authenticationType">The type of authentication used.</param>
    </member>
    <member name="M:System.Security.Claims.ClaimsIdentity.#ctor(System.Collections.Generic.IEnumerable{System.Security.Claims.Claim},System.String,System.String,System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Claims.ClaimsIdentity" /> class with the specified claims, authentication type, name claim type, and role claim type.</summary>
      <param name="claims">The claims with which to populate the claims identity.</param>
      <param name="authenticationType">The type of authentication used.</param>
      <param name="nameType">The claim type to use for name claims.</param>
      <param name="roleType">The claim type to use for role claims.</param>
    </member>
    <member name="M:System.Security.Claims.ClaimsIdentity.#ctor(System.IO.BinaryReader)">
      <summary>Initializes an instance of <see cref="T:System.Security.Claims.ClaimsIdentity" /> with the specified <see cref="T:System.IO.BinaryReader" />.</summary>
      <param name="reader">A <see cref="T:System.IO.BinaryReader" /> pointing to a <see cref="T:System.Security.Claims.ClaimsIdentity" />.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="reader" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Security.Claims.ClaimsIdentity.#ctor(System.Runtime.Serialization.SerializationInfo)">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Claims.ClaimsIdentity" /> class from a serialized stream created by using <see cref="T:System.Runtime.Serialization.ISerializable" />.</summary>
      <param name="info">The serialized data.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="info" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Security.Claims.ClaimsIdentity.#ctor(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Claims.ClaimsIdentity" /> class from a serialized stream created by using <see cref="T:System.Runtime.Serialization.ISerializable" />.</summary>
      <param name="info">The serialized data.</param>
      <param name="context">The context for serialization.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="info" /> is null.</exception>
    </member>
    <member name="M:System.Security.Claims.ClaimsIdentity.#ctor(System.Security.Claims.ClaimsIdentity)">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Claims.ClaimsIdentity" /> class from an existing <see cref="T:System.Security.Claims.ClaimsIdentity" /> instance.</summary>
      <param name="other">The <see cref="T:System.Security.Claims.ClaimsIdentity" /> to copy.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="other" /> is null.</exception>
    </member>
    <member name="M:System.Security.Claims.ClaimsIdentity.#ctor(System.Security.Principal.IIdentity)">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Claims.ClaimsIdentity" /> class using the name and authentication type from the specified <see cref="T:System.Security.Principal.IIdentity" />.</summary>
      <param name="identity">The identity from which to base the new claims identity.</param>
    </member>
    <member name="M:System.Security.Claims.ClaimsIdentity.#ctor(System.Security.Principal.IIdentity,System.Collections.Generic.IEnumerable{System.Security.Claims.Claim})">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Claims.ClaimsIdentity" /> class using the specified claims and the specified <see cref="T:System.Security.Principal.IIdentity" />.</summary>
      <param name="identity">The identity from which to base the new claims identity.</param>
      <param name="claims">The claims with which to populate the claims identity.</param>
    </member>
    <member name="M:System.Security.Claims.ClaimsIdentity.#ctor(System.Security.Principal.IIdentity,System.Collections.Generic.IEnumerable{System.Security.Claims.Claim},System.String,System.String,System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Claims.ClaimsIdentity" /> class from the specified <see cref="T:System.Security.Principal.IIdentity" /> using the specified claims, authentication type, name claim type, and role claim type.</summary>
      <param name="identity">The identity from which to base the new claims identity.</param>
      <param name="claims">The claims with which to populate the new claims identity.</param>
      <param name="authenticationType">The type of authentication used.</param>
      <param name="nameType">The claim type to use for name claims.</param>
      <param name="roleType">The claim type to use for role claims.</param>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="identity" /> is a <see cref="T:System.Security.Claims.ClaimsIdentity" /> and <see cref="P:System.Security.Claims.ClaimsIdentity.Actor" /> results in a circular reference back to <see langword="this" />.</exception>
    </member>
    <member name="M:System.Security.Claims.ClaimsIdentity.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Claims.ClaimsIdentity" /> class with an empty claims collection and the specified authentication type.</summary>
      <param name="authenticationType">The type of authentication used.</param>
    </member>
    <member name="M:System.Security.Claims.ClaimsIdentity.#ctor(System.String,System.String,System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Claims.ClaimsIdentity" /> class with the specified authentication type, name claim type, and role claim type.</summary>
      <param name="authenticationType">The type of authentication used.</param>
      <param name="nameType">The claim type to use for name claims.</param>
      <param name="roleType">The claim type to use for role claims.</param>
    </member>
    <member name="P:System.Security.Claims.ClaimsIdentity.Actor">
      <summary>Gets or sets the identity of the calling party that was granted delegation rights.</summary>
      <returns>The calling party that was granted delegation rights.</returns>
      <exception cref="T:System.InvalidOperationException">An attempt to set the property to the current instance occurs.</exception>
    </member>
    <member name="M:System.Security.Claims.ClaimsIdentity.AddClaim(System.Security.Claims.Claim)">
      <summary>Adds a single claim to this claims identity.</summary>
      <param name="claim">The claim to add.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="claim" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Security.Claims.ClaimsIdentity.AddClaims(System.Collections.Generic.IEnumerable{System.Security.Claims.Claim})">
      <summary>Adds a list of claims to this claims identity.</summary>
      <param name="claims">The claims to add.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="claims" /> is <see langword="null" />.</exception>
    </member>
    <member name="P:System.Security.Claims.ClaimsIdentity.AuthenticationType">
      <summary>Gets the authentication type.</summary>
      <returns>The authentication type.</returns>
    </member>
    <member name="P:System.Security.Claims.ClaimsIdentity.BootstrapContext">
      <summary>Gets or sets the token that was used to create this claims identity.</summary>
      <returns>The bootstrap context.</returns>
    </member>
    <member name="P:System.Security.Claims.ClaimsIdentity.Claims">
      <summary>Gets the claims associated with this claims identity.</summary>
      <returns>The collection of claims associated with this claims identity.</returns>
    </member>
    <member name="M:System.Security.Claims.ClaimsIdentity.Clone">
      <summary>Returns a new <see cref="T:System.Security.Claims.ClaimsIdentity" /> copied from this claims identity.</summary>
      <returns>A copy of the current instance.</returns>
    </member>
    <member name="M:System.Security.Claims.ClaimsIdentity.CreateClaim(System.IO.BinaryReader)">
      <summary>Provides an extensibility point for derived types to create a custom <see cref="T:System.Security.Claims.Claim" />.</summary>
      <param name="reader">The <see cref="T:System.IO.BinaryReader" /> that points to the claim.</param>
      <returns>A new <see cref="T:System.Security.Claims.Claim" />.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="reader" /> is <see langword="null" />.</exception>
    </member>
    <member name="P:System.Security.Claims.ClaimsIdentity.CustomSerializationData">
      <summary>Contains any additional data provided by a derived type. Typically set when calling <see cref="M:System.Security.Claims.ClaimsIdentity.WriteTo(System.IO.BinaryWriter,System.Byte[])" />.</summary>
      <returns>A <see cref="T:System.Byte" /> array representing the additional serialized data.</returns>
    </member>
    <member name="F:System.Security.Claims.ClaimsIdentity.DefaultIssuer">
      <summary>The default issuer; "LOCAL AUTHORITY".</summary>
    </member>
    <member name="F:System.Security.Claims.ClaimsIdentity.DefaultNameClaimType">
      <summary>The default name claim type; <see cref="F:System.Security.Claims.ClaimTypes.Name" />.</summary>
    </member>
    <member name="F:System.Security.Claims.ClaimsIdentity.DefaultRoleClaimType">
      <summary>The default role claim type; <see cref="F:System.Security.Claims.ClaimTypes.Role" />.</summary>
    </member>
    <member name="M:System.Security.Claims.ClaimsIdentity.FindAll(System.Predicate{System.Security.Claims.Claim})">
      <summary>Retrieves all of the claims that are matched by the specified predicate.</summary>
      <param name="match">The function that performs the matching logic.</param>
      <returns>The matching claims. The list is read-only.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="match" /> is null.</exception>
    </member>
    <member name="M:System.Security.Claims.ClaimsIdentity.FindAll(System.String)">
      <summary>Retrieves all of the claims that have the specified claim type.</summary>
      <param name="type">The claim type against which to match claims.</param>
      <returns>The matching claims. The list is read-only.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="type" /> is null.</exception>
    </member>
    <member name="M:System.Security.Claims.ClaimsIdentity.FindFirst(System.Predicate{System.Security.Claims.Claim})">
      <summary>Retrieves the first claim that is matched by the specified predicate.</summary>
      <param name="match">The function that performs the matching logic.</param>
      <returns>The first matching claim or <see langword="null" /> if no match is found.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="match" /> is null.</exception>
    </member>
    <member name="M:System.Security.Claims.ClaimsIdentity.FindFirst(System.String)">
      <summary>Retrieves the first claim with the specified claim type.</summary>
      <param name="type">The claim type to match.</param>
      <returns>The first matching claim or <see langword="null" /> if no match is found.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="type" /> is null.</exception>
    </member>
    <member name="M:System.Security.Claims.ClaimsIdentity.GetObjectData(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
      <summary>Populates the <see cref="T:System.Runtime.Serialization.SerializationInfo" /> with data needed to serialize the current <see cref="T:System.Security.Claims.ClaimsIdentity" /> object.</summary>
      <param name="info">The object to populate with data.</param>
      <param name="context">The destination for this serialization. Can be <see langword="null" />.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="info" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Security.Claims.ClaimsIdentity.HasClaim(System.Predicate{System.Security.Claims.Claim})">
      <summary>Determines whether this claims identity has a claim that is matched by the specified predicate.</summary>
      <param name="match">The function that performs the matching logic.</param>
      <returns>
        <see langword="true" /> if a matching claim exists; otherwise, <see langword="false" />.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="match" /> is null.</exception>
    </member>
    <member name="M:System.Security.Claims.ClaimsIdentity.HasClaim(System.String,System.String)">
      <summary>Determines whether this claims identity has a claim with the specified claim type and value.</summary>
      <param name="type">The type of the claim to match.</param>
      <param name="value">The value of the claim to match.</param>
      <returns>
        <see langword="true" /> if a match is found; otherwise, <see langword="false" />.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="type" /> is null.
-or-
<paramref name="value" /> is null.</exception>
    </member>
    <member name="P:System.Security.Claims.ClaimsIdentity.IsAuthenticated">
      <summary>Gets a value that indicates whether the identity has been authenticated.</summary>
      <returns>
        <see langword="true" /> if the identity has been authenticated; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Security.Claims.ClaimsIdentity.Label">
      <summary>Gets or sets the label for this claims identity.</summary>
      <returns>The label.</returns>
    </member>
    <member name="P:System.Security.Claims.ClaimsIdentity.Name">
      <summary>Gets the name of this claims identity.</summary>
      <returns>The name or <see langword="null" />.</returns>
    </member>
    <member name="P:System.Security.Claims.ClaimsIdentity.NameClaimType">
      <summary>Gets the claim type that is used to determine which claims provide the value for the <see cref="P:System.Security.Claims.ClaimsIdentity.Name" /> property of this claims identity.</summary>
      <returns>The name claim type.</returns>
    </member>
    <member name="M:System.Security.Claims.ClaimsIdentity.RemoveClaim(System.Security.Claims.Claim)">
      <summary>Attempts to remove a claim from the claims identity.</summary>
      <param name="claim">The claim to remove.</param>
      <exception cref="T:System.InvalidOperationException">The claim cannot be removed.</exception>
    </member>
    <member name="P:System.Security.Claims.ClaimsIdentity.RoleClaimType">
      <summary>Gets the claim type that will be interpreted as a .NET Framework role among the claims in this claims identity.</summary>
      <returns>The role claim type.</returns>
    </member>
    <member name="M:System.Security.Claims.ClaimsIdentity.TryRemoveClaim(System.Security.Claims.Claim)">
      <summary>Attempts to remove a claim from the claims identity.</summary>
      <param name="claim">The claim to remove.</param>
      <returns>
        <see langword="true" /> if the claim was successfully removed; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Security.Claims.ClaimsIdentity.WriteTo(System.IO.BinaryWriter)">
      <summary>Serializes using a <see cref="T:System.IO.BinaryWriter" />.</summary>
      <param name="writer">The writer to use for data storage.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="writer" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Security.Claims.ClaimsIdentity.WriteTo(System.IO.BinaryWriter,System.Byte[])">
      <summary>Serializes using a <see cref="T:System.IO.BinaryWriter" />.</summary>
      <param name="writer">The writer to use for data storage.</param>
      <param name="userData">Additional data provided by the derived type.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="writer" /> is <see langword="null" />.</exception>
    </member>
    <member name="T:System.Security.Claims.ClaimsPrincipal">
      <summary>An <see cref="T:System.Security.Principal.IPrincipal" /> implementation that supports multiple claims-based identities.</summary>
    </member>
    <member name="M:System.Security.Claims.ClaimsPrincipal.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Claims.ClaimsPrincipal" /> class.</summary>
    </member>
    <member name="M:System.Security.Claims.ClaimsPrincipal.#ctor(System.Collections.Generic.IEnumerable{System.Security.Claims.ClaimsIdentity})">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Claims.ClaimsPrincipal" /> class using the specified claims identities.</summary>
      <param name="identities">The identities from which to initialize the new claims principal.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="identities" /> is null.</exception>
    </member>
    <member name="M:System.Security.Claims.ClaimsPrincipal.#ctor(System.IO.BinaryReader)">
      <summary>Initializes an instance of <see cref="T:System.Security.Claims.ClaimsPrincipal" /> with the specified <see cref="T:System.IO.BinaryReader" />.</summary>
      <param name="reader">A <see cref="T:System.IO.BinaryReader" /> pointing to a <see cref="T:System.Security.Claims.ClaimsPrincipal" />.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="reader" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Security.Claims.ClaimsPrincipal.#ctor(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Claims.ClaimsPrincipal" /> class from a serialized stream created by using <see cref="T:System.Runtime.Serialization.ISerializable" />.</summary>
      <param name="info">The serialized data.</param>
      <param name="context">The context for serialization.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="info" /> is null.</exception>
    </member>
    <member name="M:System.Security.Claims.ClaimsPrincipal.#ctor(System.Security.Principal.IIdentity)">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Claims.ClaimsPrincipal" /> class from the specified identity.</summary>
      <param name="identity">The identity from which to initialize the new claims principal.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="identity" /> is null.</exception>
    </member>
    <member name="M:System.Security.Claims.ClaimsPrincipal.#ctor(System.Security.Principal.IPrincipal)">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Claims.ClaimsPrincipal" /> class from the specified principal.</summary>
      <param name="principal">The principal from which to initialize the new claims principal.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="principal" /> is null.</exception>
    </member>
    <member name="M:System.Security.Claims.ClaimsPrincipal.AddIdentities(System.Collections.Generic.IEnumerable{System.Security.Claims.ClaimsIdentity})">
      <summary>Adds the specified claims identities to this claims principal.</summary>
      <param name="identities">The claims identities to add.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="identities" /> is null.</exception>
    </member>
    <member name="M:System.Security.Claims.ClaimsPrincipal.AddIdentity(System.Security.Claims.ClaimsIdentity)">
      <summary>Adds the specified claims identity to this claims principal.</summary>
      <param name="identity">The claims identity to add.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="identity" /> is null.</exception>
    </member>
    <member name="P:System.Security.Claims.ClaimsPrincipal.Claims">
      <summary>Gets a collection that contains all of the claims from all of the claims identities associated with this claims principal.</summary>
      <returns>The claims associated with this principal.</returns>
    </member>
    <member name="P:System.Security.Claims.ClaimsPrincipal.ClaimsPrincipalSelector">
      <summary>Gets or sets the delegate used to select the claims principal returned by the <see cref="P:System.Security.Claims.ClaimsPrincipal.Current" /> property.</summary>
      <returns>The delegate. The default is <see langword="null" />.</returns>
    </member>
    <member name="M:System.Security.Claims.ClaimsPrincipal.Clone">
      <summary>Returns a copy of this instance.</summary>
      <returns>A new copy of the <see cref="T:System.Security.Claims.ClaimsPrincipal" /> object.</returns>
    </member>
    <member name="M:System.Security.Claims.ClaimsPrincipal.CreateClaimsIdentity(System.IO.BinaryReader)">
      <summary>Creates a new claims identity.</summary>
      <param name="reader">The binary reader.</param>
      <returns>The created claims identity.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="reader" /> is <see langword="null" />.</exception>
    </member>
    <member name="P:System.Security.Claims.ClaimsPrincipal.Current">
      <summary>Gets the current claims principal.</summary>
      <returns>The current claims principal.</returns>
    </member>
    <member name="P:System.Security.Claims.ClaimsPrincipal.CustomSerializationData">
      <summary>Contains any additional data provided by a derived type. Typically set when calling <see cref="M:System.Security.Claims.ClaimsIdentity.WriteTo(System.IO.BinaryWriter,System.Byte[])" />.</summary>
      <returns>A <see cref="T:System.Byte" /> array representing the additional serialized data.</returns>
    </member>
    <member name="M:System.Security.Claims.ClaimsPrincipal.FindAll(System.Predicate{System.Security.Claims.Claim})">
      <summary>Retrieves all of the claims that are matched by the specified predicate.</summary>
      <param name="match">The function that performs the matching logic.</param>
      <returns>The matching claims.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="match" /> is null.</exception>
    </member>
    <member name="M:System.Security.Claims.ClaimsPrincipal.FindAll(System.String)">
      <summary>Retrieves all or the claims that have the specified claim type.</summary>
      <param name="type">The claim type against which to match claims.</param>
      <returns>The matching claims.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="type" /> is null.</exception>
    </member>
    <member name="M:System.Security.Claims.ClaimsPrincipal.FindFirst(System.Predicate{System.Security.Claims.Claim})">
      <summary>Retrieves the first claim that is matched by the specified predicate.</summary>
      <param name="match">The function that performs the matching logic.</param>
      <returns>The first matching claim or <see langword="null" /> if no match is found.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="match" /> is null.</exception>
    </member>
    <member name="M:System.Security.Claims.ClaimsPrincipal.FindFirst(System.String)">
      <summary>Retrieves the first claim with the specified claim type.</summary>
      <param name="type">The claim type to match.</param>
      <returns>The first matching claim or <see langword="null" /> if no match is found.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="type" /> is null.</exception>
    </member>
    <member name="M:System.Security.Claims.ClaimsPrincipal.GetObjectData(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
      <summary>Populates the <see cref="T:System.Runtime.Serialization.SerializationInfo" /> with data needed to serialize the current <see cref="T:System.Security.Claims.ClaimsPrincipal" /> object.</summary>
      <param name="info">The object to populate with data.</param>
      <param name="context">The destination for this serialization. Can be <see langword="null" />.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="info" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Security.Claims.ClaimsPrincipal.HasClaim(System.Predicate{System.Security.Claims.Claim})">
      <summary>Determines whether any of the claims identities associated with this claims principal contains a claim that is matched by the specified predicate.</summary>
      <param name="match">The function that performs the matching logic.</param>
      <returns>
        <see langword="true" /> if a matching claim exists; otherwise, <see langword="false" />.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="match" /> is null.</exception>
    </member>
    <member name="M:System.Security.Claims.ClaimsPrincipal.HasClaim(System.String,System.String)">
      <summary>Determines whether any of the claims identities associated with this claims principal contains a claim with the specified claim type and value.</summary>
      <param name="type">The type of the claim to match.</param>
      <param name="value">The value of the claim to match.</param>
      <returns>
        <see langword="true" /> if a matching claim exists; otherwise, <see langword="false" />.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="type" /> is null.
-or-
<paramref name="value" /> is null.</exception>
    </member>
    <member name="P:System.Security.Claims.ClaimsPrincipal.Identities">
      <summary>Gets a collection that contains all of the claims identities associated with this claims principal.</summary>
      <returns>The collection of claims identities.</returns>
    </member>
    <member name="P:System.Security.Claims.ClaimsPrincipal.Identity">
      <summary>Gets the primary claims identity associated with this claims principal.</summary>
      <returns>The primary claims identity associated with this claims principal.</returns>
    </member>
    <member name="M:System.Security.Claims.ClaimsPrincipal.IsInRole(System.String)">
      <summary>Returns a value that indicates whether the entity (user) represented by this claims principal is in the specified role.</summary>
      <param name="role">The role for which to check.</param>
      <returns>
        <see langword="true" /> if claims principal is in the specified role; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Security.Claims.ClaimsPrincipal.PrimaryIdentitySelector">
      <summary>Gets or sets the delegate used to select the claims identity returned by the <see cref="P:System.Security.Claims.ClaimsPrincipal.Identity" /> property.</summary>
      <returns>The delegate. The default is <see langword="null" />.</returns>
    </member>
    <member name="M:System.Security.Claims.ClaimsPrincipal.WriteTo(System.IO.BinaryWriter)">
      <summary>Serializes using a <see cref="T:System.IO.BinaryWriter" />.</summary>
      <param name="writer">The writer to use for data storage.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="writer" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Security.Claims.ClaimsPrincipal.WriteTo(System.IO.BinaryWriter,System.Byte[])">
      <summary>Serializes using a <see cref="T:System.IO.BinaryWriter" />.</summary>
      <param name="writer">The writer to use for data storage.</param>
      <param name="userData">Additional data provided by the derived type.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="writer" /> is <see langword="null" />.</exception>
    </member>
    <member name="T:System.Security.Claims.ClaimTypes">
      <summary>Defines constants for the well-known claim types that can be assigned to a subject. This class cannot be inherited.</summary>
    </member>
    <member name="F:System.Security.Claims.ClaimTypes.Actor">
      <summary>The URI for a claim that specifies the actor, <c>http://schemas.xmlsoap.org/ws/2009/09/identity/claims/actor</c>.</summary>
    </member>
    <member name="F:System.Security.Claims.ClaimTypes.Anonymous">
      <summary>The URI for a claim that specifies the anonymous user; <c>http://schemas.xmlsoap.org/ws/2005/05/identity/claims/anonymous</c>.</summary>
    </member>
    <member name="F:System.Security.Claims.ClaimTypes.Authentication">
      <summary>The URI for a claim that specifies details about whether an identity is authenticated, <c>http://schemas.xmlsoap.org/ws/2005/05/identity/claims/authenticated</c>.</summary>
    </member>
    <member name="F:System.Security.Claims.ClaimTypes.AuthenticationInstant">
      <summary>The URI for a claim that specifies the instant at which an entity was authenticated; <c>http://schemas.microsoft.com/ws/2008/06/identity/claims/authenticationinstant</c>.</summary>
    </member>
    <member name="F:System.Security.Claims.ClaimTypes.AuthenticationMethod">
      <summary>The URI for a claim that specifies the method with which an entity was authenticated; <c>http://schemas.microsoft.com/ws/2008/06/identity/claims/authenticationmethod</c>.</summary>
    </member>
    <member name="F:System.Security.Claims.ClaimTypes.AuthorizationDecision">
      <summary>The URI for a claim that specifies an authorization decision on an entity; <c>http://schemas.xmlsoap.org/ws/2005/05/identity/claims/authorizationdecision</c>.</summary>
    </member>
    <member name="F:System.Security.Claims.ClaimTypes.CookiePath">
      <summary>The URI for a claim that specifies the cookie path; <c>http://schemas.microsoft.com/ws/2008/06/identity/claims/cookiepath</c>.</summary>
    </member>
    <member name="F:System.Security.Claims.ClaimTypes.Country">
      <summary>The URI for a claim that specifies the country/region in which an entity resides, <c>http://schemas.xmlsoap.org/ws/2005/05/identity/claims/country</c>.</summary>
    </member>
    <member name="F:System.Security.Claims.ClaimTypes.DateOfBirth">
      <summary>The URI for a claim that specifies the date of birth of an entity, <c>http://schemas.xmlsoap.org/ws/2005/05/identity/claims/dateofbirth</c>.</summary>
    </member>
    <member name="F:System.Security.Claims.ClaimTypes.DenyOnlyPrimaryGroupSid">
      <summary>The URI for a claim that specifies the deny-only primary group SID on an entity; <c>http://schemas.microsoft.com/ws/2008/06/identity/claims/denyonlyprimarygroupsid</c>. A deny-only SID denies the specified entity to a securable object.</summary>
    </member>
    <member name="F:System.Security.Claims.ClaimTypes.DenyOnlyPrimarySid">
      <summary>The URI for a claim that specifies the deny-only primary SID on an entity; <c>http://schemas.microsoft.com/ws/2008/06/identity/claims/denyonlyprimarysid</c>. A deny-only SID denies the specified entity to a securable object.</summary>
    </member>
    <member name="F:System.Security.Claims.ClaimTypes.DenyOnlySid">
      <summary>The URI for a claim that specifies a deny-only security identifier (SID) for an entity, <c>http://schemas.xmlsoap.org/ws/2005/05/identity/claims/denyonlysid</c>. A deny-only SID denies the specified entity to a securable object.</summary>
    </member>
    <member name="F:System.Security.Claims.ClaimTypes.DenyOnlyWindowsDeviceGroup">
      <summary>The URI for a claim that specifies the Windows deny-only group SID of the device, <c>http://schemas.microsoft.com/ws/2008/06/identity/claims/denyonlywindowsdevicegroup</c>.</summary>
    </member>
    <member name="F:System.Security.Claims.ClaimTypes.Dns">
      <summary>The URI for a claim that specifies the DNS name associated with the computer name or with the alternative name of either the subject or issuer of an X.509 certificate, <c>http://schemas.xmlsoap.org/ws/2005/05/identity/claims/dns</c>.</summary>
    </member>
    <member name="F:System.Security.Claims.ClaimTypes.Dsa">
      <summary>
        <c>http://schemas.microsoft.com/ws/2008/06/identity/claims/dsa</c>.</summary>
    </member>
    <member name="F:System.Security.Claims.ClaimTypes.Email">
      <summary>The URI for a claim that specifies the email address of an entity, <c>http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress</c>.</summary>
    </member>
    <member name="F:System.Security.Claims.ClaimTypes.Expiration">
      <summary>
        <c>http://schemas.microsoft.com/ws/2008/06/identity/claims/expiration</c>.</summary>
    </member>
    <member name="F:System.Security.Claims.ClaimTypes.Expired">
      <summary>
        <c>http://schemas.microsoft.com/ws/2008/06/identity/claims/expired</c>.</summary>
    </member>
    <member name="F:System.Security.Claims.ClaimTypes.Gender">
      <summary>The URI for a claim that specifies the gender of an entity, <c>http://schemas.xmlsoap.org/ws/2005/05/identity/claims/gender</c>.</summary>
    </member>
    <member name="F:System.Security.Claims.ClaimTypes.GivenName">
      <summary>The URI for a claim that specifies the given name of an entity, <c>http://schemas.xmlsoap.org/ws/2005/05/identity/claims/givenname</c>.</summary>
    </member>
    <member name="F:System.Security.Claims.ClaimTypes.GroupSid">
      <summary>The URI for a claim that specifies the SID for the group of an entity, <c>http://schemas.microsoft.com/ws/2008/06/identity/claims/groupsid</c>.</summary>
    </member>
    <member name="F:System.Security.Claims.ClaimTypes.Hash">
      <summary>The URI for a claim that specifies a hash value, <c>http://schemas.xmlsoap.org/ws/2005/05/identity/claims/hash</c>.</summary>
    </member>
    <member name="F:System.Security.Claims.ClaimTypes.HomePhone">
      <summary>The URI for a claim that specifies the home phone number of an entity, <c>http://schemas.xmlsoap.org/ws/2005/05/identity/claims/homephone</c>.</summary>
    </member>
    <member name="F:System.Security.Claims.ClaimTypes.IsPersistent">
      <summary>
        <c>http://schemas.microsoft.com/ws/2008/06/identity/claims/ispersistent</c>.</summary>
    </member>
    <member name="F:System.Security.Claims.ClaimTypes.Locality">
      <summary>The URI for a claim that specifies the locale in which an entity resides, <c>http://schemas.xmlsoap.org/ws/2005/05/identity/claims/locality</c>.</summary>
    </member>
    <member name="F:System.Security.Claims.ClaimTypes.MobilePhone">
      <summary>The URI for a claim that specifies the mobile phone number of an entity, <c>http://schemas.xmlsoap.org/ws/2005/05/identity/claims/mobilephone</c>.</summary>
    </member>
    <member name="F:System.Security.Claims.ClaimTypes.Name">
      <summary>The URI for a claim that specifies the name of an entity, <c>http://schemas.xmlsoap.org/ws/2005/05/identity/claims/name</c>.</summary>
    </member>
    <member name="F:System.Security.Claims.ClaimTypes.NameIdentifier">
      <summary>The URI for a claim that specifies the name of an entity, <c>http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier</c>.</summary>
    </member>
    <member name="F:System.Security.Claims.ClaimTypes.OtherPhone">
      <summary>The URI for a claim that specifies the alternative phone number of an entity, <c>http://schemas.xmlsoap.org/ws/2005/05/identity/claims/otherphone</c>.</summary>
    </member>
    <member name="F:System.Security.Claims.ClaimTypes.PostalCode">
      <summary>The URI for a claim that specifies the postal code of an entity, <c>http://schemas.xmlsoap.org/ws/2005/05/identity/claims/postalcode</c>.</summary>
    </member>
    <member name="F:System.Security.Claims.ClaimTypes.PrimaryGroupSid">
      <summary>The URI for a claim that specifies the primary group SID of an entity, <c>http://schemas.microsoft.com/ws/2008/06/identity/claims/primarygroupsid</c>.</summary>
    </member>
    <member name="F:System.Security.Claims.ClaimTypes.PrimarySid">
      <summary>The URI for a claim that specifies the primary SID of an entity, <c>http://schemas.microsoft.com/ws/2008/06/identity/claims/primarysid</c>.</summary>
    </member>
    <member name="F:System.Security.Claims.ClaimTypes.Role">
      <summary>The URI for a claim that specifies the role of an entity, <c>http://schemas.microsoft.com/ws/2008/06/identity/claims/role</c>.</summary>
    </member>
    <member name="F:System.Security.Claims.ClaimTypes.Rsa">
      <summary>The URI for a claim that specifies an RSA key, <c>http://schemas.xmlsoap.org/ws/2005/05/identity/claims/rsa</c>.</summary>
    </member>
    <member name="F:System.Security.Claims.ClaimTypes.SerialNumber">
      <summary>The URI for a claim that specifies a serial number, <c>http://schemas.microsoft.com/ws/2008/06/identity/claims/serialnumber</c>.</summary>
    </member>
    <member name="F:System.Security.Claims.ClaimTypes.Sid">
      <summary>The URI for a claim that specifies a security identifier (SID), <c>http://schemas.xmlsoap.org/ws/2005/05/identity/claims/sid</c>.</summary>
    </member>
    <member name="F:System.Security.Claims.ClaimTypes.Spn">
      <summary>The URI for a claim that specifies a service principal name (SPN) claim, <c>http://schemas.xmlsoap.org/ws/2005/05/identity/claims/spn</c>.</summary>
    </member>
    <member name="F:System.Security.Claims.ClaimTypes.StateOrProvince">
      <summary>The URI for a claim that specifies the state or province in which an entity resides, <c>http://schemas.xmlsoap.org/ws/2005/05/identity/claims/stateorprovince</c>.</summary>
    </member>
    <member name="F:System.Security.Claims.ClaimTypes.StreetAddress">
      <summary>The URI for a claim that specifies the street address of an entity, <c>http://schemas.xmlsoap.org/ws/2005/05/identity/claims/streetaddress</c>.</summary>
    </member>
    <member name="F:System.Security.Claims.ClaimTypes.Surname">
      <summary>The URI for a claim that specifies the surname of an entity, <c>http://schemas.xmlsoap.org/ws/2005/05/identity/claims/surname</c>.</summary>
    </member>
    <member name="F:System.Security.Claims.ClaimTypes.System">
      <summary>The URI for a claim that identifies the system entity, <c>http://schemas.xmlsoap.org/ws/2005/05/identity/claims/system</c>.</summary>
    </member>
    <member name="F:System.Security.Claims.ClaimTypes.Thumbprint">
      <summary>The URI for a claim that specifies a thumbprint, <c>http://schemas.xmlsoap.org/ws/2005/05/identity/claims/thumbprint</c>. A thumbprint is a globally unique SHA-1 hash of an X.509 certificate.</summary>
    </member>
    <member name="F:System.Security.Claims.ClaimTypes.Upn">
      <summary>The URI for a claim that specifies a user principal name (UPN), <c>http://schemas.xmlsoap.org/ws/2005/05/identity/claims/upn</c>.</summary>
    </member>
    <member name="F:System.Security.Claims.ClaimTypes.Uri">
      <summary>The URI for a claim that specifies a URI, <c>http://schemas.xmlsoap.org/ws/2005/05/identity/claims/uri</c>.</summary>
    </member>
    <member name="F:System.Security.Claims.ClaimTypes.UserData">
      <summary>The URI for a claim that specifies the user data, <c>http://schemas.microsoft.com/ws/2008/06/identity/claims/userdata</c>.</summary>
    </member>
    <member name="F:System.Security.Claims.ClaimTypes.Version">
      <summary>The URI for a claim that specifies the version, <c>http://schemas.microsoft.com/ws/2008/06/identity/claims/version</c>.</summary>
    </member>
    <member name="F:System.Security.Claims.ClaimTypes.Webpage">
      <summary>The URI for a claim that specifies the webpage of an entity, <c>http://schemas.xmlsoap.org/ws/2005/05/identity/claims/webpage</c>.</summary>
    </member>
    <member name="F:System.Security.Claims.ClaimTypes.WindowsAccountName">
      <summary>The URI for a claim that specifies the Windows domain account name of an entity, <c>http://schemas.microsoft.com/ws/2008/06/identity/claims/windowsaccountname</c>.</summary>
    </member>
    <member name="F:System.Security.Claims.ClaimTypes.WindowsDeviceClaim">
      <summary>
        <c>http://schemas.microsoft.com/ws/2008/06/identity/claims/windowsdeviceclaim</c>.</summary>
    </member>
    <member name="F:System.Security.Claims.ClaimTypes.WindowsDeviceGroup">
      <summary>The URI for a claim that specifies the Windows group SID of the device, <c>http://schemas.microsoft.com/ws/2008/06/identity/claims/windowsdevicegroup</c>.</summary>
    </member>
    <member name="F:System.Security.Claims.ClaimTypes.WindowsFqbnVersion">
      <summary>
        <c>http://schemas.microsoft.com/ws/2008/06/identity/claims/windowsfqbnversion</c>.</summary>
    </member>
    <member name="F:System.Security.Claims.ClaimTypes.WindowsSubAuthority">
      <summary>
        <c>http://schemas.microsoft.com/ws/2008/06/identity/claims/windowssubauthority</c>.</summary>
    </member>
    <member name="F:System.Security.Claims.ClaimTypes.WindowsUserClaim">
      <summary>
        <c>http://schemas.microsoft.com/ws/2008/06/identity/claims/windowsuserclaim</c>.</summary>
    </member>
    <member name="F:System.Security.Claims.ClaimTypes.X500DistinguishedName">
      <summary>The URI for an X.500 distinguished name claim, such as the subject of an X.509 Public Key Certificate or an entry identifier in a directory services Directory Information Tree; <c>http://schemas.xmlsoap.org/ws/2005/05/identity/claims/x500distinguishedname</c>.</summary>
    </member>
    <member name="T:System.Security.Claims.ClaimValueTypes">
      <summary>Defines claim value types according to the type URIs defined by W3C and OASIS. This class cannot be inherited.</summary>
    </member>
    <member name="F:System.Security.Claims.ClaimValueTypes.Base64Binary">
      <summary>A URI that represents the <see langword="base64Binary" /> XML data type.</summary>
    </member>
    <member name="F:System.Security.Claims.ClaimValueTypes.Base64Octet">
      <summary>A URI that represents the <see langword="base64Octet" /> XML data type.</summary>
    </member>
    <member name="F:System.Security.Claims.ClaimValueTypes.Boolean">
      <summary>A URI that represents the <see langword="boolean" /> XML data type.</summary>
    </member>
    <member name="F:System.Security.Claims.ClaimValueTypes.Date">
      <summary>A URI that represents the <see langword="date" /> XML data type.</summary>
    </member>
    <member name="F:System.Security.Claims.ClaimValueTypes.DateTime">
      <summary>A URI that represents the <see langword="dateTime" /> XML data type.</summary>
    </member>
    <member name="F:System.Security.Claims.ClaimValueTypes.DaytimeDuration">
      <summary>A URI that represents the <see langword="daytimeDuration" /> XQuery data type.</summary>
    </member>
    <member name="F:System.Security.Claims.ClaimValueTypes.DnsName">
      <summary>A URI that represents the <see langword="dns" /> SOAP data type.</summary>
    </member>
    <member name="F:System.Security.Claims.ClaimValueTypes.Double">
      <summary>A URI that represents the <see langword="double" /> XML data type.</summary>
    </member>
    <member name="F:System.Security.Claims.ClaimValueTypes.DsaKeyValue">
      <summary>A URI that represents the <see langword="DSAKeyValue" /> XML Signature data type.</summary>
    </member>
    <member name="F:System.Security.Claims.ClaimValueTypes.Email">
      <summary>A URI that represents the <see langword="emailaddress" /> SOAP data type.</summary>
    </member>
    <member name="F:System.Security.Claims.ClaimValueTypes.Fqbn">
      <summary>A URI that represents the <see langword="fqbn" /> XML data type.</summary>
    </member>
    <member name="F:System.Security.Claims.ClaimValueTypes.HexBinary">
      <summary>A URI that represents the <see langword="hexBinary" /> XML data type.</summary>
    </member>
    <member name="F:System.Security.Claims.ClaimValueTypes.Integer">
      <summary>A URI that represents the <see langword="integer" /> XML data type.</summary>
    </member>
    <member name="F:System.Security.Claims.ClaimValueTypes.Integer32">
      <summary>A URI that represents the <see langword="integer32" /> XML data type.</summary>
    </member>
    <member name="F:System.Security.Claims.ClaimValueTypes.Integer64">
      <summary>A URI that represents the <see langword="integer64" /> XML data type.</summary>
    </member>
    <member name="F:System.Security.Claims.ClaimValueTypes.KeyInfo">
      <summary>A URI that represents the <see langword="KeyInfo" /> XML Signature data type.</summary>
    </member>
    <member name="F:System.Security.Claims.ClaimValueTypes.Rfc822Name">
      <summary>A URI that represents the <see langword="rfc822Name" /> XACML 1.0 data type.</summary>
    </member>
    <member name="F:System.Security.Claims.ClaimValueTypes.Rsa">
      <summary>A URI that represents the <see langword="rsa" /> SOAP data type.</summary>
    </member>
    <member name="F:System.Security.Claims.ClaimValueTypes.RsaKeyValue">
      <summary>A URI that represents the <see langword="RSAKeyValue" /> XML Signature data type.</summary>
    </member>
    <member name="F:System.Security.Claims.ClaimValueTypes.Sid">
      <summary>A URI that represents the <see langword="sid" /> XML data type.</summary>
    </member>
    <member name="F:System.Security.Claims.ClaimValueTypes.String">
      <summary>A URI that represents the <see langword="string" /> XML data type.</summary>
    </member>
    <member name="F:System.Security.Claims.ClaimValueTypes.Time">
      <summary>A URI that represents the <see langword="time" /> XML data type.</summary>
    </member>
    <member name="F:System.Security.Claims.ClaimValueTypes.UInteger32">
      <summary>A URI that represents the <see langword="uinteger32" /> XML data type.</summary>
    </member>
    <member name="F:System.Security.Claims.ClaimValueTypes.UInteger64">
      <summary>A URI that represents the <see langword="uinteger64" /> XML data type.</summary>
    </member>
    <member name="F:System.Security.Claims.ClaimValueTypes.UpnName">
      <summary>A URI that represents the <see langword="UPN" /> SOAP data type.</summary>
    </member>
    <member name="F:System.Security.Claims.ClaimValueTypes.X500Name">
      <summary>A URI that represents the <see langword="x500Name" /> XACML 1.0 data type.</summary>
    </member>
    <member name="F:System.Security.Claims.ClaimValueTypes.YearMonthDuration">
      <summary>A URI that represents the <see langword="yearMonthDuration" /> XQuery data type.</summary>
    </member>
    <member name="T:System.Security.Principal.GenericIdentity">
      <summary>Represents a generic user.</summary>
    </member>
    <member name="M:System.Security.Principal.GenericIdentity.#ctor(System.Security.Principal.GenericIdentity)">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Principal.GenericIdentity" /> class by using the specified <see cref="T:System.Security.Principal.GenericIdentity" /> object.</summary>
      <param name="identity">The object from which to construct the new instance of <see cref="T:System.Security.Principal.GenericIdentity" />.</param>
    </member>
    <member name="M:System.Security.Principal.GenericIdentity.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Principal.GenericIdentity" /> class representing the user with the specified name.</summary>
      <param name="name">The name of the user on whose behalf the code is running.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="name" /> parameter is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Security.Principal.GenericIdentity.#ctor(System.String,System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Principal.GenericIdentity" /> class representing the user with the specified name and authentication type.</summary>
      <param name="name">The name of the user on whose behalf the code is running.</param>
      <param name="type">The type of authentication used to identify the user.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="name" /> parameter is <see langword="null" />.
-or-
The <paramref name="type" /> parameter is <see langword="null" />.</exception>
    </member>
    <member name="P:System.Security.Principal.GenericIdentity.AuthenticationType">
      <summary>Gets the type of authentication used to identify the user.</summary>
      <returns>The type of authentication used to identify the user.</returns>
    </member>
    <member name="P:System.Security.Principal.GenericIdentity.Claims">
      <summary>Gets all claims for the user represented by this generic identity.</summary>
      <returns>A collection of claims for this <see cref="T:System.Security.Principal.GenericIdentity" /> object.</returns>
    </member>
    <member name="M:System.Security.Principal.GenericIdentity.Clone">
      <summary>Creates a new object that is a copy of the current instance.</summary>
      <returns>A copy of the current instance.</returns>
    </member>
    <member name="P:System.Security.Principal.GenericIdentity.IsAuthenticated">
      <summary>Gets a value indicating whether the user has been authenticated.</summary>
      <returns>
        <see langword="true" /> if the user was has been authenticated; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Security.Principal.GenericIdentity.Name">
      <summary>Gets the user's name.</summary>
      <returns>The name of the user on whose behalf the code is being run.</returns>
    </member>
    <member name="T:System.Security.Principal.GenericPrincipal">
      <summary>Represents a generic principal.</summary>
    </member>
    <member name="M:System.Security.Principal.GenericPrincipal.#ctor(System.Security.Principal.IIdentity,System.String[])">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Principal.GenericPrincipal" /> class from a user identity and an array of role names to which the user represented by that identity belongs.</summary>
      <param name="identity">A basic implementation of <see cref="T:System.Security.Principal.IIdentity" /> that represents any user.</param>
      <param name="roles">An array of role names to which the user represented by the <paramref name="identity" /> parameter belongs.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="identity" /> parameter is <see langword="null" />.</exception>
    </member>
    <member name="P:System.Security.Principal.GenericPrincipal.Identity">
      <summary>Gets the <see cref="T:System.Security.Principal.GenericIdentity" /> of the user represented by the current <see cref="T:System.Security.Principal.GenericPrincipal" />.</summary>
      <returns>The <see cref="T:System.Security.Principal.GenericIdentity" /> of the user represented by the <see cref="T:System.Security.Principal.GenericPrincipal" />.</returns>
    </member>
    <member name="M:System.Security.Principal.GenericPrincipal.IsInRole(System.String)">
      <summary>Determines whether the current <see cref="T:System.Security.Principal.GenericPrincipal" /> belongs to the specified role.</summary>
      <param name="role">The name of the role for which to check membership.</param>
      <returns>
        <see langword="true" /> if the current <see cref="T:System.Security.Principal.GenericPrincipal" /> is a member of the specified role; otherwise, <see langword="false" />.</returns>
    </member>
  </members>
</doc>