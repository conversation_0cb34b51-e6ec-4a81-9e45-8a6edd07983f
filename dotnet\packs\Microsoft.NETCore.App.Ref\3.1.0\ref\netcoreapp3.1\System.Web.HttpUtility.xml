﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>System.Web.HttpUtility</name>
  </assembly>
  <members>
    <member name="T:System.Web.HttpUtility">
      <summary>Provides methods for encoding and decoding URLs when processing Web requests. This class cannot be inherited.</summary>
    </member>
    <member name="M:System.Web.HttpUtility.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Web.HttpUtility" /> class.</summary>
    </member>
    <member name="M:System.Web.HttpUtility.HtmlAttributeEncode(System.String)">
      <summary>Minimally converts a string to an HTML-encoded string.</summary>
      <param name="s">The string to encode.</param>
      <returns>An encoded string.</returns>
    </member>
    <member name="M:System.Web.HttpUtility.HtmlAttributeEncode(System.String,System.IO.TextWriter)">
      <summary>Minimally converts a string into an HTML-encoded string and sends the encoded string to a <see cref="T:System.IO.TextWriter" /> output stream.</summary>
      <param name="s">The string to encode</param>
      <param name="output">A <see cref="T:System.IO.TextWriter" /> output stream.</param>
    </member>
    <member name="M:System.Web.HttpUtility.HtmlDecode(System.String)">
      <summary>Converts a string that has been HTML-encoded for HTTP transmission into a decoded string.</summary>
      <param name="s">The string to decode.</param>
      <returns>A decoded string.</returns>
    </member>
    <member name="M:System.Web.HttpUtility.HtmlDecode(System.String,System.IO.TextWriter)">
      <summary>Converts a string that has been HTML-encoded into a decoded string, and sends the decoded string to a <see cref="T:System.IO.TextWriter" /> output stream.</summary>
      <param name="s">The string to decode.</param>
      <param name="output">A <see cref="T:System.IO.TextWriter" /> stream of output.</param>
    </member>
    <member name="M:System.Web.HttpUtility.HtmlEncode(System.Object)">
      <summary>Converts an object's string representation into an HTML-encoded string, and returns the encoded string.</summary>
      <param name="value">An object.</param>
      <returns>An encoded string.</returns>
    </member>
    <member name="M:System.Web.HttpUtility.HtmlEncode(System.String)">
      <summary>Converts a string to an HTML-encoded string.</summary>
      <param name="s">The string to encode.</param>
      <returns>An encoded string.</returns>
    </member>
    <member name="M:System.Web.HttpUtility.HtmlEncode(System.String,System.IO.TextWriter)">
      <summary>Converts a string into an HTML-encoded string, and returns the output as a <see cref="T:System.IO.TextWriter" /> stream of output.</summary>
      <param name="s">The string to encode</param>
      <param name="output">A <see cref="T:System.IO.TextWriter" /> output stream.</param>
    </member>
    <member name="M:System.Web.HttpUtility.JavaScriptStringEncode(System.String)">
      <summary>Encodes a string.</summary>
      <param name="value">A string to encode.</param>
      <returns>An encoded string.</returns>
    </member>
    <member name="M:System.Web.HttpUtility.JavaScriptStringEncode(System.String,System.Boolean)">
      <summary>Encodes a string.</summary>
      <param name="value">A string to encode.</param>
      <param name="addDoubleQuotes">A value that indicates whether double quotation marks will be included around the encoded string.</param>
      <returns>An encoded string.</returns>
    </member>
    <member name="M:System.Web.HttpUtility.ParseQueryString(System.String)">
      <summary>Parses a query string into a <see cref="T:System.Collections.Specialized.NameValueCollection" /> using <see cref="P:System.Text.Encoding.UTF8" /> encoding.</summary>
      <param name="query">The query string to parse.</param>
      <returns>A <see cref="T:System.Collections.Specialized.NameValueCollection" /> of query parameters and values.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="query" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Web.HttpUtility.ParseQueryString(System.String,System.Text.Encoding)">
      <summary>Parses a query string into a <see cref="T:System.Collections.Specialized.NameValueCollection" /> using the specified <see cref="T:System.Text.Encoding" />.</summary>
      <param name="query">The query string to parse.</param>
      <param name="encoding">The <see cref="T:System.Text.Encoding" /> to use.</param>
      <returns>A <see cref="T:System.Collections.Specialized.NameValueCollection" /> of query parameters and values.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="query" /> is <see langword="null" />.
-or-
<paramref name="encoding" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Web.HttpUtility.UrlDecode(System.Byte[],System.Int32,System.Int32,System.Text.Encoding)">
      <summary>Converts a URL-encoded byte array into a decoded string using the specified encoding object, starting at the specified position in the array, and continuing for the specified number of bytes.</summary>
      <param name="bytes">The array of bytes to decode.</param>
      <param name="offset">The position in the byte to begin decoding.</param>
      <param name="count">The number of bytes to decode.</param>
      <param name="e">The <see cref="T:System.Text.Encoding" /> object that specifies the decoding scheme.</param>
      <returns>A decoded string.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="bytes" /> is <see langword="null" />, but <paramref name="count" /> does not equal <see langword="0" />.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="offset" /> is less than <see langword="0" /> or greater than the length of the <paramref name="bytes" /> array.
-or-
<paramref name="count" /> is less than <see langword="0" />, or <paramref name="count" /> + <paramref name="offset" /> is greater than the length of the <paramref name="bytes" /> array.</exception>
    </member>
    <member name="M:System.Web.HttpUtility.UrlDecode(System.Byte[],System.Text.Encoding)">
      <summary>Converts a URL-encoded byte array into a decoded string using the specified decoding object.</summary>
      <param name="bytes">The array of bytes to decode.</param>
      <param name="e">The <see cref="T:System.Text.Encoding" /> that specifies the decoding scheme.</param>
      <returns>A decoded string.</returns>
    </member>
    <member name="M:System.Web.HttpUtility.UrlDecode(System.String)">
      <summary>Converts a string that has been encoded for transmission in a URL into a decoded string.</summary>
      <param name="str">The string to decode.</param>
      <returns>A decoded string.</returns>
    </member>
    <member name="M:System.Web.HttpUtility.UrlDecode(System.String,System.Text.Encoding)">
      <summary>Converts a URL-encoded string into a decoded string, using the specified encoding object.</summary>
      <param name="str">The string to decode.</param>
      <param name="e">The <see cref="T:System.Text.Encoding" /> that specifies the decoding scheme.</param>
      <returns>A decoded string.</returns>
    </member>
    <member name="M:System.Web.HttpUtility.UrlDecodeToBytes(System.Byte[])">
      <summary>Converts a URL-encoded array of bytes into a decoded array of bytes.</summary>
      <param name="bytes">The array of bytes to decode.</param>
      <returns>A decoded array of bytes.</returns>
    </member>
    <member name="M:System.Web.HttpUtility.UrlDecodeToBytes(System.Byte[],System.Int32,System.Int32)">
      <summary>Converts a URL-encoded array of bytes into a decoded array of bytes, starting at the specified position in the array and continuing for the specified number of bytes.</summary>
      <param name="bytes">The array of bytes to decode.</param>
      <param name="offset">The position in the byte array at which to begin decoding.</param>
      <param name="count">The number of bytes to decode.</param>
      <returns>A decoded array of bytes.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="bytes" /> is <see langword="null" />, but <paramref name="count" /> does not equal <see langword="0" />.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="offset" /> is less than <see langword="0" /> or greater than the length of the <paramref name="bytes" /> array.
-or-
<paramref name="count" /> is less than <see langword="0" />, or <paramref name="count" /> + <paramref name="offset" /> is greater than the length of the <paramref name="bytes" /> array.</exception>
    </member>
    <member name="M:System.Web.HttpUtility.UrlDecodeToBytes(System.String)">
      <summary>Converts a URL-encoded string into a decoded array of bytes.</summary>
      <param name="str">The string to decode.</param>
      <returns>A decoded array of bytes.</returns>
    </member>
    <member name="M:System.Web.HttpUtility.UrlDecodeToBytes(System.String,System.Text.Encoding)">
      <summary>Converts a URL-encoded string into a decoded array of bytes using the specified decoding object.</summary>
      <param name="str">The string to decode.</param>
      <param name="e">The <see cref="T:System.Text.Encoding" /> object that specifies the decoding scheme.</param>
      <returns>A decoded array of bytes.</returns>
    </member>
    <member name="M:System.Web.HttpUtility.UrlEncode(System.Byte[])">
      <summary>Converts a byte array into an encoded URL string.</summary>
      <param name="bytes">The array of bytes to encode.</param>
      <returns>An encoded string.</returns>
    </member>
    <member name="M:System.Web.HttpUtility.UrlEncode(System.Byte[],System.Int32,System.Int32)">
      <summary>Converts a byte array into a URL-encoded string, starting at the specified position in the array and continuing for the specified number of bytes.</summary>
      <param name="bytes">The array of bytes to encode.</param>
      <param name="offset">The position in the byte array at which to begin encoding.</param>
      <param name="count">The number of bytes to encode.</param>
      <returns>An encoded string.</returns>
    </member>
    <member name="M:System.Web.HttpUtility.UrlEncode(System.String)">
      <summary>Encodes a URL string.</summary>
      <param name="str">The text to encode.</param>
      <returns>An encoded string.</returns>
    </member>
    <member name="M:System.Web.HttpUtility.UrlEncode(System.String,System.Text.Encoding)">
      <summary>Encodes a URL string using the specified encoding object.</summary>
      <param name="str">The text to encode.</param>
      <param name="e">The <see cref="T:System.Text.Encoding" /> object that specifies the encoding scheme.</param>
      <returns>An encoded string.</returns>
    </member>
    <member name="M:System.Web.HttpUtility.UrlEncodeToBytes(System.Byte[])">
      <summary>Converts an array of bytes into a URL-encoded array of bytes.</summary>
      <param name="bytes">The array of bytes to encode.</param>
      <returns>An encoded array of bytes.</returns>
    </member>
    <member name="M:System.Web.HttpUtility.UrlEncodeToBytes(System.Byte[],System.Int32,System.Int32)">
      <summary>Converts an array of bytes into a URL-encoded array of bytes, starting at the specified position in the array and continuing for the specified number of bytes.</summary>
      <param name="bytes">The array of bytes to encode.</param>
      <param name="offset">The position in the byte array at which to begin encoding.</param>
      <param name="count">The number of bytes to encode.</param>
      <returns>An encoded array of bytes.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="bytes" /> is <see langword="null" />, but <paramref name="count" /> does not equal <see langword="0" />.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="offset" /> is less than <see langword="0" /> or greater than the length of the <paramref name="bytes" /> array.
-or-
<paramref name="count" /> is less than <see langword="0" />, or <paramref name="count" /> + <paramref name="offset" /> is greater than the length of the <paramref name="bytes" /> array.</exception>
    </member>
    <member name="M:System.Web.HttpUtility.UrlEncodeToBytes(System.String)">
      <summary>Converts a string into a URL-encoded array of bytes.</summary>
      <param name="str">The string to encode.</param>
      <returns>An encoded array of bytes.</returns>
    </member>
    <member name="M:System.Web.HttpUtility.UrlEncodeToBytes(System.String,System.Text.Encoding)">
      <summary>Converts a string into a URL-encoded array of bytes using the specified encoding object.</summary>
      <param name="str">The string to encode</param>
      <param name="e">The <see cref="T:System.Text.Encoding" /> that specifies the encoding scheme.</param>
      <returns>An encoded array of bytes.</returns>
    </member>
    <member name="M:System.Web.HttpUtility.UrlEncodeUnicode(System.String)">
      <summary>Converts a string into a Unicode string.</summary>
      <param name="str">The string to convert.</param>
      <returns>A Unicode string in %<paramref name="UnicodeValue" /> notation.</returns>
    </member>
    <member name="M:System.Web.HttpUtility.UrlEncodeUnicodeToBytes(System.String)">
      <summary>Converts a Unicode string into an array of bytes.</summary>
      <param name="str">The string to convert.</param>
      <returns>A byte array.</returns>
    </member>
    <member name="M:System.Web.HttpUtility.UrlPathEncode(System.String)">
      <summary>Do not use; intended only for browser compatibility. Use <see cref="M:System.Web.HttpUtility.UrlEncode(System.String)" />.</summary>
      <param name="str">The text to encode.</param>
      <returns>The encoded text.</returns>
    </member>
  </members>
</doc>