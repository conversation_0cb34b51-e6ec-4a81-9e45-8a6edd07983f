﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>System.Xml.XPath</name>
  </assembly>
  <members>
    <member name="T:System.Xml.XPath.XPathDocument">
      <summary>Provides a fast, read-only, in-memory representation of an XML document by using the XPath data model.</summary>
    </member>
    <member name="M:System.Xml.XPath.XPathDocument.#ctor(System.IO.Stream)">
      <summary>Initializes a new instance of the <see cref="T:System.Xml.XPath.XPathDocument" /> class from the XML data in the specified <see cref="T:System.IO.Stream" /> object.</summary>
      <param name="stream">The <see cref="T:System.IO.Stream" /> object that contains the XML data.</param>
      <exception cref="T:System.Xml.XmlException">An error was encountered in the XML data. The <see cref="T:System.Xml.XPath.XPathDocument" /> remains empty.</exception>
      <exception cref="T:System.ArgumentNullException">The <see cref="T:System.IO.Stream" /> object passed as a parameter is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Xml.XPath.XPathDocument.#ctor(System.IO.TextReader)">
      <summary>Initializes a new instance of the <see cref="T:System.Xml.XPath.XPathDocument" /> class from the XML data that is contained in the specified <see cref="T:System.IO.TextReader" /> object.</summary>
      <param name="textReader">The <see cref="T:System.IO.TextReader" /> object that contains the XML data.</param>
      <exception cref="T:System.Xml.XmlException">An error was encountered in the XML data. The <see cref="T:System.Xml.XPath.XPathDocument" /> remains empty.</exception>
      <exception cref="T:System.ArgumentNullException">The <see cref="T:System.IO.TextReader" /> object passed as a parameter is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Xml.XPath.XPathDocument.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Xml.XPath.XPathDocument" /> class from the XML data in the specified file.</summary>
      <param name="uri">The path of the file that contains the XML data.</param>
      <exception cref="T:System.Xml.XmlException">An error was encountered in the XML data. The <see cref="T:System.Xml.XPath.XPathDocument" /> remains empty.</exception>
      <exception cref="T:System.ArgumentNullException">The file path parameter is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Xml.XPath.XPathDocument.#ctor(System.String,System.Xml.XmlSpace)">
      <summary>Initializes a new instance of the <see cref="T:System.Xml.XPath.XPathDocument" /> class from the XML data in the file specified with the white space handling specified.</summary>
      <param name="uri">The path of the file that contains the XML data.</param>
      <param name="space">An <see cref="T:System.Xml.XmlSpace" /> object.</param>
      <exception cref="T:System.Xml.XmlException">An error was encountered in the XML data. The <see cref="T:System.Xml.XPath.XPathDocument" /> remains empty.</exception>
      <exception cref="T:System.ArgumentNullException">The file path parameter or <see cref="T:System.Xml.XmlSpace" /> object parameter is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Xml.XPath.XPathDocument.#ctor(System.Xml.XmlReader)">
      <summary>Initializes a new instance of the <see cref="T:System.Xml.XPath.XPathDocument" /> class from the XML data that is contained in the specified <see cref="T:System.Xml.XmlReader" /> object.</summary>
      <param name="reader">The <see cref="T:System.Xml.XmlReader" /> object that contains the XML data.</param>
      <exception cref="T:System.Xml.XmlException">An error was encountered in the XML data. The <see cref="T:System.Xml.XPath.XPathDocument" /> remains empty.</exception>
      <exception cref="T:System.ArgumentNullException">The <see cref="T:System.Xml.XmlReader" /> object passed as a parameter is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Xml.XPath.XPathDocument.#ctor(System.Xml.XmlReader,System.Xml.XmlSpace)">
      <summary>Initializes a new instance of the <see cref="T:System.Xml.XPath.XPathDocument" /> class from the XML data that is contained in the specified <see cref="T:System.Xml.XmlReader" /> object with the specified white space handling.</summary>
      <param name="reader">The <see cref="T:System.Xml.XmlReader" /> object that contains the XML data.</param>
      <param name="space">An <see cref="T:System.Xml.XmlSpace" /> object.</param>
      <exception cref="T:System.Xml.XmlException">An error was encountered in the XML data. The <see cref="T:System.Xml.XPath.XPathDocument" /> remains empty.</exception>
      <exception cref="T:System.ArgumentNullException">The <see cref="T:System.Xml.XmlReader" /> object parameter or <see cref="T:System.Xml.XmlSpace" /> object parameter is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Xml.XPath.XPathDocument.CreateNavigator">
      <summary>Initializes a read-only <see cref="T:System.Xml.XPath.XPathNavigator" /> object for navigating through nodes in this <see cref="T:System.Xml.XPath.XPathDocument" />.</summary>
      <returns>A read-only <see cref="T:System.Xml.XPath.XPathNavigator" /> object.</returns>
    </member>
    <member name="T:System.Xml.XPath.XPathException">
      <summary>Provides the exception thrown when an error occurs while processing an XPath expression.</summary>
    </member>
    <member name="M:System.Xml.XPath.XPathException.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Xml.XPath.XPathException" /> class.</summary>
    </member>
    <member name="M:System.Xml.XPath.XPathException.#ctor(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
      <summary>Uses the information in the <see cref="T:System.Runtime.Serialization.SerializationInfo" /> and <see cref="T:System.Runtime.Serialization.StreamingContext" /> objects to initialize a new instance of the <see cref="T:System.Xml.XPath.XPathException" /> class.</summary>
      <param name="info">The <see cref="T:System.Runtime.Serialization.SerializationInfo" /> object that contains all the properties of an <see cref="T:System.Xml.XPath.XPathException" />.</param>
      <param name="context">The <see cref="T:System.Runtime.Serialization.StreamingContext" /> object.</param>
    </member>
    <member name="M:System.Xml.XPath.XPathException.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Xml.XPath.XPathException" /> class with the specified exception message.</summary>
      <param name="message">The description of the error condition.</param>
    </member>
    <member name="M:System.Xml.XPath.XPathException.#ctor(System.String,System.Exception)">
      <summary>Initializes a new instance of the <see cref="T:System.Xml.XPath.XPathException" /> class using the specified exception message and <see cref="T:System.Exception" /> object.</summary>
      <param name="message">The description of the error condition.</param>
      <param name="innerException">The <see cref="T:System.Exception" /> that threw the <see cref="T:System.Xml.XPath.XPathException" />, if any. This value can be <see langword="null" />.</param>
    </member>
    <member name="M:System.Xml.XPath.XPathException.GetObjectData(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
      <summary>Streams all the <see cref="T:System.Xml.XPath.XPathException" /> properties into the <see cref="T:System.Runtime.Serialization.SerializationInfo" /> class for the specified <see cref="T:System.Runtime.Serialization.StreamingContext" />.</summary>
      <param name="info">The <see cref="T:System.Runtime.Serialization.SerializationInfo" /> object.</param>
      <param name="context">The <see cref="T:System.Runtime.Serialization.StreamingContext" /> object.</param>
    </member>
    <member name="P:System.Xml.XPath.XPathException.Message">
      <summary>Gets the description of the error condition for this exception.</summary>
      <returns>The <see langword="string" /> description of the error condition for this exception.</returns>
    </member>
  </members>
</doc>