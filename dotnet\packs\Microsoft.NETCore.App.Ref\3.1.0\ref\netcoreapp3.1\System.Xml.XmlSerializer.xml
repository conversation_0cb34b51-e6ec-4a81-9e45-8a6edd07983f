﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>System.Xml.XmlSerializer</name>
  </assembly>
  <members>
    <member name="T:System.Xml.Serialization.CodeGenerationOptions">
      <summary>Specifies various options to use when generating .NET types for use with an XML Web Service.</summary>
    </member>
    <member name="F:System.Xml.Serialization.CodeGenerationOptions.EnableDataBinding">
      <summary>Enables data binding.</summary>
    </member>
    <member name="F:System.Xml.Serialization.CodeGenerationOptions.GenerateNewAsync">
      <summary>Creates events for the asynchronous invocation of Web methods.</summary>
    </member>
    <member name="F:System.Xml.Serialization.CodeGenerationOptions.GenerateOldAsync">
      <summary>Creates Begin and End methods for the asynchronous invocation of Web methods.</summary>
    </member>
    <member name="F:System.Xml.Serialization.CodeGenerationOptions.GenerateOrder">
      <summary>Generates explicitly ordered serialization code as specified through the <see langword="Order" /> property of the <see cref="T:System.Xml.Serialization.XmlAnyElementAttribute" />, <see cref="T:System.Xml.Serialization.XmlArrayAttribute" />, and <see cref="T:System.Xml.Serialization.XmlElementAttribute" /> attributes.</summary>
    </member>
    <member name="F:System.Xml.Serialization.CodeGenerationOptions.GenerateProperties">
      <summary>Represents primitive types by properties.</summary>
    </member>
    <member name="F:System.Xml.Serialization.CodeGenerationOptions.None">
      <summary>Represents primitive types by fields and primitive types by <see cref="N:System" /> namespace types.</summary>
    </member>
    <member name="T:System.Xml.Serialization.CodeIdentifier">
      <summary>Provides static methods to convert input text into names for code entities.</summary>
    </member>
    <member name="M:System.Xml.Serialization.CodeIdentifier.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Xml.Serialization.CodeIdentifier" /> class.</summary>
    </member>
    <member name="M:System.Xml.Serialization.CodeIdentifier.MakeCamel(System.String)">
      <summary>Produces a camel-case string from an input string.</summary>
      <param name="identifier">The name of a code entity, such as a method parameter, typically taken from an XML element or attribute name.</param>
      <returns>A camel-case version of the parameter string.</returns>
    </member>
    <member name="M:System.Xml.Serialization.CodeIdentifier.MakePascal(System.String)">
      <summary>Produces a Pascal-case string from an input string.</summary>
      <param name="identifier">The name of a code entity, such as a method parameter, typically taken from an XML element or attribute name.</param>
      <returns>A Pascal-case version of the parameter string.</returns>
    </member>
    <member name="M:System.Xml.Serialization.CodeIdentifier.MakeValid(System.String)">
      <summary>Produces a valid code entity name from an input string.</summary>
      <param name="identifier">The name of a code entity, such as a method parameter, typically taken from an XML element or attribute name.</param>
      <returns>A string that can be used as a code identifier, such as the name of a method parameter.</returns>
    </member>
    <member name="T:System.Xml.Serialization.CodeIdentifiers">
      <summary>Maintains a group of names for related code entities or type mappings that are generated by the .NET Framework's XML serialization infrastructure.</summary>
    </member>
    <member name="M:System.Xml.Serialization.CodeIdentifiers.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Xml.Serialization.CodeIdentifiers" /> class.</summary>
    </member>
    <member name="M:System.Xml.Serialization.CodeIdentifiers.#ctor(System.Boolean)">
      <summary>Initializes a new instance of the <see cref="T:System.Xml.Serialization.CodeIdentifiers" /> class and determines whether code identifiers are case sensitive.</summary>
      <param name="caseSensitive">
        <see langword="true" /> if identifiers are case sensitive; otherwise, <see langword="false" />.</param>
    </member>
    <member name="M:System.Xml.Serialization.CodeIdentifiers.Add(System.String,System.Object)">
      <summary>Adds a named code entity or type mapping to the <see cref="T:System.Xml.Serialization.CodeIdentifiers" /> instance's scope.</summary>
      <param name="identifier">A name that is used to index the code entity.</param>
      <param name="value">The code entity or type mapping information.</param>
    </member>
    <member name="M:System.Xml.Serialization.CodeIdentifiers.AddReserved(System.String)">
      <summary>Prevents a specified name from being used within the <see cref="T:System.Xml.Serialization.CodeIdentifiers" /> instance's scope.</summary>
      <param name="identifier">A name that will not be available within the <see cref="T:System.Xml.Serialization.CodeIdentifiers" /> instance's scope.</param>
    </member>
    <member name="M:System.Xml.Serialization.CodeIdentifiers.AddUnique(System.String,System.Object)">
      <summary>Adds a named code entity or type mapping to the <see cref="T:System.Xml.Serialization.CodeIdentifiers" /> instance's scope, ensuring that the name is unique within the scope.</summary>
      <param name="identifier">A candidate name that is used to index the code entity.</param>
      <param name="value">The code entity or type mapping information.</param>
      <returns>The identifier parameter value or a modified string if there is a name collision.</returns>
    </member>
    <member name="M:System.Xml.Serialization.CodeIdentifiers.Clear">
      <summary>Removes all code entities or type mappings, including their names, from the <see cref="T:System.Xml.Serialization.CodeIdentifiers" /> instance's scope.</summary>
    </member>
    <member name="M:System.Xml.Serialization.CodeIdentifiers.IsInUse(System.String)">
      <summary>Determines whether a specified name is already being used within the <see cref="T:System.Xml.Serialization.CodeIdentifiers" /> instance's scope.</summary>
      <param name="identifier">The name to check for use within the <see cref="T:System.Xml.Serialization.CodeIdentifiers" /> instance's scope.</param>
      <returns>
        <see langword="true" /> if the name is already in use; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Xml.Serialization.CodeIdentifiers.MakeRightCase(System.String)">
      <summary>Ensures that the input is of the correct case by modifying the name according to the value of the <see cref="P:System.Xml.Serialization.CodeIdentifiers.UseCamelCasing" /> property.</summary>
      <param name="identifier">The name to possibly modify.</param>
      <returns>A string that is the same as the input identifier or has the first letter's case modified.</returns>
    </member>
    <member name="M:System.Xml.Serialization.CodeIdentifiers.MakeUnique(System.String)">
      <summary>Determines whether the input name conflicts with another name within the <see cref="T:System.Xml.Serialization.CodeIdentifiers" /> instance's scope and, if it does, generates a modified string.</summary>
      <param name="identifier">The name to modify if it is not already unique.</param>
      <returns>The identifier parameter value or a modified string if there is a name collision.</returns>
    </member>
    <member name="M:System.Xml.Serialization.CodeIdentifiers.Remove(System.String)">
      <summary>Removes from the <see cref="T:System.Xml.Serialization.CodeIdentifiers" /> instance's scope the code entity or type mapping identified by the input name.</summary>
      <param name="identifier">The name of the code entity or type mapping information to remove.</param>
    </member>
    <member name="M:System.Xml.Serialization.CodeIdentifiers.RemoveReserved(System.String)">
      <summary>Removes the input name from the <see cref="T:System.Xml.Serialization.CodeIdentifiers" /> instance's reserved names.</summary>
      <param name="identifier">A name that will be made available within the <see cref="T:System.Xml.Serialization.CodeIdentifiers" /> instance's scope.</param>
    </member>
    <member name="M:System.Xml.Serialization.CodeIdentifiers.ToArray(System.Type)">
      <summary>Returns an array of the code entities or type mappings within the <see cref="T:System.Xml.Serialization.CodeIdentifiers" /> instance's scope.</summary>
      <param name="type">The type of all the code entities or type mappings for the instance.</param>
      <returns>An array of the code entities or type mappings that is stored by the <see cref="T:System.Xml.Serialization.CodeIdentifiers" /> object.</returns>
    </member>
    <member name="P:System.Xml.Serialization.CodeIdentifiers.UseCamelCasing">
      <summary>Gets or sets a value that indicates whether the instance uses camel case.</summary>
      <returns>
        <see langword="true" /> to indicate that the instance uses camel case; <see langword="false" /> to indicate that the instance uses Pascal case.</returns>
    </member>
    <member name="T:System.Xml.Serialization.ImportContext">
      <summary>Describes the context in which a set of schema is bound to .NET Framework code entities.</summary>
    </member>
    <member name="M:System.Xml.Serialization.ImportContext.#ctor(System.Xml.Serialization.CodeIdentifiers,System.Boolean)">
      <summary>Initializes a new instance of the <see cref="T:System.Xml.Serialization.ImportContext" /> class for the given code identifiers, with the given type-sharing option.</summary>
      <param name="identifiers">The code entities to which the context applies.</param>
      <param name="shareTypes">A <see cref="T:System.Boolean" /> value that determines whether custom types are shared among schema.</param>
    </member>
    <member name="P:System.Xml.Serialization.ImportContext.ShareTypes">
      <summary>Gets a value that determines whether custom types are shared.</summary>
      <returns>
        <see langword="true" />, if custom types are shared among schema; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Xml.Serialization.ImportContext.TypeIdentifiers">
      <summary>Gets a set of code entities to which the context applies.</summary>
      <returns>A <see cref="T:System.Xml.Serialization.CodeIdentifiers" /> that specifies the code entities to which the context applies.</returns>
    </member>
    <member name="P:System.Xml.Serialization.ImportContext.Warnings">
      <summary>Gets a collection of warnings that are generated when importing the code entity descriptions.</summary>
      <returns>A <see cref="T:System.Collections.Specialized.StringCollection" /> that contains warnings that were generated when importing the code entity descriptions.</returns>
    </member>
    <member name="T:System.Xml.Serialization.IXmlTextParser">
      <summary>Establishes a <see cref="P:System.Xml.Serialization.IXmlTextParser.Normalized" /> property for use by the .NET Framework infrastructure.</summary>
    </member>
    <member name="P:System.Xml.Serialization.IXmlTextParser.Normalized">
      <summary>Gets or sets whether white space and attribute values are normalized.</summary>
      <returns>
        <see langword="true" /> if white space attributes values are normalized; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Xml.Serialization.IXmlTextParser.WhitespaceHandling">
      <summary>Gets or sets how white space is handled when parsing XML.</summary>
      <returns>A member of the <see cref="T:System.Xml.WhitespaceHandling" /> enumeration that describes how whites pace is handled when parsing XML.</returns>
    </member>
    <member name="T:System.Xml.Serialization.SchemaImporter">
      <summary>Describes a schema importer.</summary>
    </member>
    <member name="T:System.Xml.Serialization.SoapAttributeAttribute">
      <summary>Specifies that the <see cref="T:System.Xml.Serialization.XmlSerializer" /> must serialize the class member as an encoded SOAP attribute.</summary>
    </member>
    <member name="M:System.Xml.Serialization.SoapAttributeAttribute.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Xml.Serialization.SoapAttributeAttribute" /> class.</summary>
    </member>
    <member name="M:System.Xml.Serialization.SoapAttributeAttribute.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Xml.Serialization.SoapAttributeAttribute" /> class using the specified value as the name of the XML attribute.</summary>
      <param name="attributeName">The name of the XML attribute.</param>
    </member>
    <member name="P:System.Xml.Serialization.SoapAttributeAttribute.AttributeName">
      <summary>Gets or sets the name of the XML attribute generated by the <see cref="T:System.Xml.Serialization.XmlSerializer" />.</summary>
      <returns>The name of the XML attribute. The default is the member identifier.</returns>
    </member>
    <member name="P:System.Xml.Serialization.SoapAttributeAttribute.DataType">
      <summary>Gets or sets the XML Schema definition language (XSD) data type of the SOAP attribute generated by the <see cref="T:System.Xml.Serialization.XmlSerializer" />.</summary>
      <returns>An XML Schema data type.</returns>
      <exception cref="T:System.Exception">The XML Schema data type you have specified cannot be mapped to the .NET data type.</exception>
    </member>
    <member name="P:System.Xml.Serialization.SoapAttributeAttribute.Namespace">
      <summary>Gets or sets the XML namespace of the XML attribute.</summary>
      <returns>The XML namespace of the XML attribute.</returns>
    </member>
    <member name="T:System.Xml.Serialization.SoapAttributeOverrides">
      <summary>Allows you to override attributes applied to properties, fields, and classes when you use an <see cref="T:System.Xml.Serialization.XmlSerializer" /> to serialize or deserialize an object as encoded SOAP.</summary>
    </member>
    <member name="M:System.Xml.Serialization.SoapAttributeOverrides.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Xml.Serialization.SoapAttributeOverrides" /> class.</summary>
    </member>
    <member name="M:System.Xml.Serialization.SoapAttributeOverrides.Add(System.Type,System.String,System.Xml.Serialization.SoapAttributes)">
      <summary>Adds a <see cref="T:System.Xml.Serialization.SoapAttributes" /> to the collection of <see cref="T:System.Xml.Serialization.SoapAttributes" /> objects contained by the <see cref="T:System.Xml.Serialization.SoapAttributeOverrides" />. The <paramref name="type" /> parameter specifies the object to be overridden by the <see cref="T:System.Xml.Serialization.SoapAttributes" />. The <paramref name="member" /> parameter specifies the name of a member that is overridden.</summary>
      <param name="type">The <see cref="T:System.Type" /> of the object to override.</param>
      <param name="member">The name of the member to override.</param>
      <param name="attributes">A <see cref="T:System.Xml.Serialization.SoapAttributes" /> that represents the overriding attributes.</param>
    </member>
    <member name="M:System.Xml.Serialization.SoapAttributeOverrides.Add(System.Type,System.Xml.Serialization.SoapAttributes)">
      <summary>Adds a <see cref="T:System.Xml.Serialization.SoapAttributes" /> to a collection of <see cref="T:System.Xml.Serialization.SoapAttributes" /> objects. The <paramref name="type" /> parameter specifies an object to be overridden by the <see cref="T:System.Xml.Serialization.SoapAttributes" />.</summary>
      <param name="type">The <see cref="T:System.Type" /> of the object that is overridden.</param>
      <param name="attributes">A <see cref="T:System.Xml.Serialization.SoapAttributes" /> that represents the overriding attributes.</param>
    </member>
    <member name="P:System.Xml.Serialization.SoapAttributeOverrides.Item(System.Type)">
      <summary>Gets the object associated with the specified (base class) type.</summary>
      <param name="type">The base class <see cref="T:System.Type" /> that is associated with the collection of attributes you want to retrieve.</param>
      <returns>A <see cref="T:System.Xml.Serialization.SoapAttributes" /> that represents the collection of overriding attributes.</returns>
    </member>
    <member name="P:System.Xml.Serialization.SoapAttributeOverrides.Item(System.Type,System.String)">
      <summary>Gets the object associated with the specified (base class) type. The <paramref name="member" /> parameter specifies the base class member that is overridden.</summary>
      <param name="type">The base class <see cref="T:System.Type" /> that is associated with the collection of attributes you want to override.</param>
      <param name="member">The name of the overridden member that specifies the <see cref="T:System.Xml.Serialization.SoapAttributes" /> to return.</param>
      <returns>A <see cref="T:System.Xml.Serialization.SoapAttributes" /> that represents the collection of overriding attributes.</returns>
    </member>
    <member name="T:System.Xml.Serialization.SoapAttributes">
      <summary>Represents a collection of attribute objects that control how the <see cref="T:System.Xml.Serialization.XmlSerializer" /> serializes and deserializes SOAP methods.</summary>
    </member>
    <member name="M:System.Xml.Serialization.SoapAttributes.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Xml.Serialization.SoapAttributes" /> class.</summary>
    </member>
    <member name="M:System.Xml.Serialization.SoapAttributes.#ctor(System.Reflection.ICustomAttributeProvider)">
      <summary>Initializes a new instance of the <see cref="T:System.Xml.Serialization.SoapAttributes" /> class using the specified custom type.</summary>
      <param name="provider">Any object that implements the <see cref="T:System.Reflection.ICustomAttributeProvider" /> interface, such as the <see cref="T:System.Type" /> class.</param>
    </member>
    <member name="P:System.Xml.Serialization.SoapAttributes.SoapAttribute">
      <summary>Gets or sets the <see cref="T:System.Xml.Serialization.SoapAttributeAttribute" /> to override.</summary>
      <returns>A <see cref="T:System.Xml.Serialization.SoapAttributeAttribute" /> that overrides the behavior of the <see cref="T:System.Xml.Serialization.XmlSerializer" /> when the member is serialized.</returns>
    </member>
    <member name="P:System.Xml.Serialization.SoapAttributes.SoapDefaultValue">
      <summary>Gets or sets the default value of an XML element or attribute.</summary>
      <returns>An object that represents the default value of an XML element or attribute.</returns>
    </member>
    <member name="P:System.Xml.Serialization.SoapAttributes.SoapElement">
      <summary>Gets or sets a <see cref="T:System.Xml.Serialization.SoapElementAttribute" /> to override.</summary>
      <returns>The <see cref="T:System.Xml.Serialization.SoapElementAttribute" /> to override.</returns>
    </member>
    <member name="P:System.Xml.Serialization.SoapAttributes.SoapEnum">
      <summary>Gets or sets an object that specifies how the <see cref="T:System.Xml.Serialization.XmlSerializer" /> serializes a SOAP enumeration.</summary>
      <returns>An object that specifies how the <see cref="T:System.Xml.Serialization.XmlSerializer" /> serializes an enumeration member.</returns>
    </member>
    <member name="P:System.Xml.Serialization.SoapAttributes.SoapIgnore">
      <summary>Gets or sets a value that specifies whether the <see cref="T:System.Xml.Serialization.XmlSerializer" /> serializes a public field or property as encoded SOAP XML.</summary>
      <returns>
        <see langword="true" /> if the <see cref="T:System.Xml.Serialization.XmlSerializer" /> must not serialize the field or property; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Xml.Serialization.SoapAttributes.SoapType">
      <summary>Gets or sets an object that instructs the <see cref="T:System.Xml.Serialization.XmlSerializer" /> how to serialize an object type into encoded SOAP XML.</summary>
      <returns>A <see cref="T:System.Xml.Serialization.SoapTypeAttribute" /> that either overrides a <see cref="T:System.Xml.Serialization.SoapTypeAttribute" /> applied to a class declaration, or is applied to a class declaration.</returns>
    </member>
    <member name="T:System.Xml.Serialization.SoapElementAttribute">
      <summary>Specifies that the public member value be serialized by the <see cref="T:System.Xml.Serialization.XmlSerializer" /> as an encoded SOAP XML element.</summary>
    </member>
    <member name="M:System.Xml.Serialization.SoapElementAttribute.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Xml.Serialization.SoapElementAttribute" /> class.</summary>
    </member>
    <member name="M:System.Xml.Serialization.SoapElementAttribute.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Xml.Serialization.SoapElementAttribute" /> class and specifies the name of the XML element.</summary>
      <param name="elementName">The XML element name of the serialized member.</param>
    </member>
    <member name="P:System.Xml.Serialization.SoapElementAttribute.DataType">
      <summary>Gets or sets the XML Schema definition language (XSD) data type of the generated XML element.</summary>
      <returns>One of the XML Schema data types.</returns>
    </member>
    <member name="P:System.Xml.Serialization.SoapElementAttribute.ElementName">
      <summary>Gets or sets the name of the generated XML element.</summary>
      <returns>The name of the generated XML element. The default is the member identifier.</returns>
    </member>
    <member name="P:System.Xml.Serialization.SoapElementAttribute.IsNullable">
      <summary>Gets or sets a value that indicates whether the <see cref="T:System.Xml.Serialization.XmlSerializer" /> must serialize a member that has the <see langword="xsi:null" /> attribute set to "1".</summary>
      <returns>
        <see langword="true" /> if the <see cref="T:System.Xml.Serialization.XmlSerializer" /> generates the <see langword="xsi:null" /> attribute; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="T:System.Xml.Serialization.SoapEnumAttribute">
      <summary>Controls how the <see cref="T:System.Xml.Serialization.XmlSerializer" /> serializes an enumeration member.</summary>
    </member>
    <member name="M:System.Xml.Serialization.SoapEnumAttribute.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Xml.Serialization.SoapEnumAttribute" /> class.</summary>
    </member>
    <member name="M:System.Xml.Serialization.SoapEnumAttribute.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Xml.Serialization.SoapEnumAttribute" /> class using the specified element name.</summary>
      <param name="name">The XML element name generated by the <see cref="T:System.Xml.Serialization.XmlSerializer" />.</param>
    </member>
    <member name="P:System.Xml.Serialization.SoapEnumAttribute.Name">
      <summary>Gets or sets the value generated in an XML document when the <see cref="T:System.Xml.Serialization.XmlSerializer" /> serializes an enumeration, or the value recognized when it deserializes the enumeration member.</summary>
      <returns>The value generated in an XML document when the <see cref="T:System.Xml.Serialization.XmlSerializer" /> serializes the enumeration, or the value recognized when it deserializes the enumeration member.</returns>
    </member>
    <member name="T:System.Xml.Serialization.SoapIgnoreAttribute">
      <summary>Instructs the <see cref="T:System.Xml.Serialization.XmlSerializer" /> not to serialize the public field or public read/write property value.</summary>
    </member>
    <member name="M:System.Xml.Serialization.SoapIgnoreAttribute.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Xml.Serialization.SoapIgnoreAttribute" /> class.</summary>
    </member>
    <member name="T:System.Xml.Serialization.SoapIncludeAttribute">
      <summary>Allows the <see cref="T:System.Xml.Serialization.XmlSerializer" /> to recognize a type when it serializes or deserializes an object as encoded SOAP XML.</summary>
    </member>
    <member name="M:System.Xml.Serialization.SoapIncludeAttribute.#ctor(System.Type)">
      <summary>Initializes a new instance of the <see cref="T:System.Xml.Serialization.SoapIncludeAttribute" /> class using the specified type.</summary>
      <param name="type">The type of the object to include.</param>
    </member>
    <member name="P:System.Xml.Serialization.SoapIncludeAttribute.Type">
      <summary>Gets or sets the type of the object to use when serializing or deserializing an object.</summary>
      <returns>The type of the object to include.</returns>
    </member>
    <member name="T:System.Xml.Serialization.SoapReflectionImporter">
      <summary>Generates mappings to SOAP-encoded messages from .NET Framework types or Web service method information.</summary>
    </member>
    <member name="M:System.Xml.Serialization.SoapReflectionImporter.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Xml.Serialization.SoapReflectionImporter" /> class.</summary>
    </member>
    <member name="M:System.Xml.Serialization.SoapReflectionImporter.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Xml.Serialization.SoapReflectionImporter" /> class, specifying a default XML namespace for imported type mappings.</summary>
      <param name="defaultNamespace">The default XML namespace to use for imported type mappings.</param>
    </member>
    <member name="M:System.Xml.Serialization.SoapReflectionImporter.#ctor(System.Xml.Serialization.SoapAttributeOverrides)">
      <summary>Initializes a new instance of the <see cref="T:System.Xml.Serialization.SoapReflectionImporter" /> class, specifying overrides for XML serialization.</summary>
      <param name="attributeOverrides">A <see cref="T:System.Xml.Serialization.SoapAttributeOverrides" /> object that overrides how the <see cref="T:System.Xml.Serialization.XmlSerializer" /> class serializes mapped types using SOAP encoding.</param>
    </member>
    <member name="M:System.Xml.Serialization.SoapReflectionImporter.#ctor(System.Xml.Serialization.SoapAttributeOverrides,System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Xml.Serialization.SoapReflectionImporter" /> class, specifying XML serialization overrides and a default XML namespace.</summary>
      <param name="attributeOverrides">A <see cref="T:System.Xml.Serialization.SoapAttributeOverrides" /> object that overrides how the <see cref="T:System.Xml.Serialization.XmlSerializer" /> class serializes mapped types using SOAP encoding.</param>
      <param name="defaultNamespace">The default XML namespace to use for imported type mappings.</param>
    </member>
    <member name="M:System.Xml.Serialization.SoapReflectionImporter.ImportMembersMapping(System.String,System.String,System.Xml.Serialization.XmlReflectionMember[])">
      <summary>Generates internal type mappings for information that is gathered from a Web service method.</summary>
      <param name="elementName">An XML element name produced from the Web service method.</param>
      <param name="ns">An XML element namespace produced from the Web service method.</param>
      <param name="members">An array of .NET Framework code entities that belong to a Web service method.</param>
      <returns>Internal .NET Framework type mappings to the element parts of a WSDL message definition.</returns>
    </member>
    <member name="M:System.Xml.Serialization.SoapReflectionImporter.ImportMembersMapping(System.String,System.String,System.Xml.Serialization.XmlReflectionMember[],System.Boolean,System.Boolean)">
      <summary>Generates internal type mappings for information that is gathered from a Web service method.</summary>
      <param name="elementName">An XML element name produced from the Web service method.</param>
      <param name="ns">An XML element namespace produced from the Web service method.</param>
      <param name="members">An array of .NET Framework code entities that belong to a Web service method.</param>
      <param name="hasWrapperElement">
        <see langword="true" /> to indicate that elements that correspond to WSDL message parts should be enclosed in an extra wrapper element in a SOAP message; otherwise, <see langword="false" />.</param>
      <param name="writeAccessors">
        <see langword="true" /> to indicate an RPC-style Web service binding; <see langword="false" /> to indicate a document-style Web service binding or a SOAP header.</param>
      <returns>Internal .NET Framework type mappings to the element parts of a WSDL message definition.</returns>
    </member>
    <member name="M:System.Xml.Serialization.SoapReflectionImporter.ImportMembersMapping(System.String,System.String,System.Xml.Serialization.XmlReflectionMember[],System.Boolean,System.Boolean,System.Boolean)">
      <summary>Generates internal type mappings for information that is gathered from a Web service method.</summary>
      <param name="elementName">An XML element name produced from the Web service method.</param>
      <param name="ns">An XML element namespace produced from the Web service method.</param>
      <param name="members">An array of .NET Framework code entities that belong to a Web service method.</param>
      <param name="hasWrapperElement">
        <see langword="true" /> to indicate that elements that correspond to WSDL message parts should be enclosed in an extra wrapper element in a SOAP message; otherwise, <see langword="false" />.</param>
      <param name="writeAccessors">
        <see langword="true" /> to indicate an RPC-style Web service binding; <see langword="false" /> to indicate a document-style Web service binding or a SOAP header.</param>
      <param name="validate">
        <see langword="true" /> to indicate that a generated deserializer should check for the expected qualified name of the wrapper element; otherwise, <see langword="false" />. This parameter's value is relevant only if the <paramref name="hasWrapperElement" /> parameter's value is <see langword="true" />.</param>
      <returns>Internal .NET Framework type mappings to the element parts of a WSDL message definition.</returns>
    </member>
    <member name="M:System.Xml.Serialization.SoapReflectionImporter.ImportMembersMapping(System.String,System.String,System.Xml.Serialization.XmlReflectionMember[],System.Boolean,System.Boolean,System.Boolean,System.Xml.Serialization.XmlMappingAccess)">
      <summary>Generates internal type mappings for information that is gathered from a Web service method.</summary>
      <param name="elementName">An XML element name produced from the Web service method.</param>
      <param name="ns">An XML element namespace produced from the Web service method.</param>
      <param name="members">An array of .NET Framework code entities that belong to a Web service method.</param>
      <param name="hasWrapperElement">
        <see langword="true" /> to indicate that elements that correspond to WSDL message parts should be enclosed in an extra wrapper element in a SOAP message; otherwise, <see langword="false" />.</param>
      <param name="writeAccessors">
        <see langword="true" /> to indicate an RPC-style Web service binding; <see langword="false" /> to indicate a document-style Web service binding or a SOAP header.</param>
      <param name="validate">
        <see langword="true" /> to indicate that a generated deserializer should check for the expected qualified name of the wrapper element; otherwise, <see langword="false" />. This parameter's value is relevant only if the <paramref name="hasWrapperElement" /> parameter's value is <see langword="true" />.</param>
      <param name="access">One of the <see cref="T:System.Xml.Serialization.XmlMappingAccess" /> values.</param>
      <returns>Internal .NET Framework type mappings to the element parts of a WSDL message definition.</returns>
    </member>
    <member name="M:System.Xml.Serialization.SoapReflectionImporter.ImportTypeMapping(System.Type)">
      <summary>Generates a mapping to an XML Schema element for a .NET Framework type.</summary>
      <param name="type">The .NET Framework type for which to generate a type mapping.</param>
      <returns>Internal .NET Framework mapping of a type to an XML Schema element.</returns>
    </member>
    <member name="M:System.Xml.Serialization.SoapReflectionImporter.ImportTypeMapping(System.Type,System.String)">
      <summary>Generates a mapping to an XML Schema element for a .NET Framework type.</summary>
      <param name="type">The .NET Framework type for which to generate a type mapping.</param>
      <param name="defaultNamespace">The default XML namespace to use.</param>
      <returns>Internal .NET Framework mapping of a type to an XML Schema element.</returns>
    </member>
    <member name="M:System.Xml.Serialization.SoapReflectionImporter.IncludeType(System.Type)">
      <summary>Places mappings for a type in the <see cref="T:System.Xml.Serialization.SoapReflectionImporter" /> instance's context for later use when import methods are invoked.</summary>
      <param name="type">The .NET Framework type for which to save type mapping information.</param>
    </member>
    <member name="M:System.Xml.Serialization.SoapReflectionImporter.IncludeTypes(System.Reflection.ICustomAttributeProvider)">
      <summary>Places mappings for derived types in the <see cref="T:System.Xml.Serialization.SoapReflectionImporter" /> instance's context for later use when import methods are invoked.</summary>
      <param name="provider">An <see cref="T:System.Reflection.ICustomAttributeProvider" /> reflection object that contains custom attributes that are derived from the <see cref="T:System.Xml.Serialization.SoapIncludeAttribute" /> attribute.</param>
    </member>
    <member name="T:System.Xml.Serialization.SoapSchemaMember">
      <summary>Represents certain attributes of a XSD &lt;<see langword="part" />&gt; element in a WSDL document for generating classes from the document.</summary>
    </member>
    <member name="M:System.Xml.Serialization.SoapSchemaMember.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Xml.Serialization.SoapSchemaMember" /> class.</summary>
    </member>
    <member name="P:System.Xml.Serialization.SoapSchemaMember.MemberName">
      <summary>Gets or sets a value that corresponds to the name attribute of the WSDL part element.</summary>
      <returns>The element name.</returns>
    </member>
    <member name="P:System.Xml.Serialization.SoapSchemaMember.MemberType">
      <summary>Gets or sets a value that corresponds to the type attribute of the WSDL part element.</summary>
      <returns>An <see cref="T:System.Xml.XmlQualifiedName" /> that corresponds to the XML type.</returns>
    </member>
    <member name="T:System.Xml.Serialization.SoapTypeAttribute">
      <summary>Controls the schema generated by the <see cref="T:System.Xml.Serialization.XmlSerializer" /> when a class instance is serialized as SOAP encoded XML.</summary>
    </member>
    <member name="M:System.Xml.Serialization.SoapTypeAttribute.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Xml.Serialization.SoapTypeAttribute" /> class.</summary>
    </member>
    <member name="M:System.Xml.Serialization.SoapTypeAttribute.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Xml.Serialization.SoapTypeAttribute" /> class and specifies the name of the XML type.</summary>
      <param name="typeName">The name of the XML type that the <see cref="T:System.Xml.Serialization.XmlSerializer" /> generates when it serializes the class instance (and recognizes when it deserializes the class instance).</param>
    </member>
    <member name="M:System.Xml.Serialization.SoapTypeAttribute.#ctor(System.String,System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Xml.Serialization.SoapTypeAttribute" /> class and specifies the name and XML namespace of the type.</summary>
      <param name="typeName">The name of the XML type that the <see cref="T:System.Xml.Serialization.XmlSerializer" /> generates when it serializes the class instance (and recognizes when it deserializes the class instance).</param>
      <param name="ns">The XML namespace of the type.</param>
    </member>
    <member name="P:System.Xml.Serialization.SoapTypeAttribute.IncludeInSchema">
      <summary>Gets or sets a value that indicates whether to include the type in SOAP-encoded XML Schema documents.</summary>
      <returns>
        <see langword="true" /> to include the type in SOAP-encoded XML Schema documents; otherwise, <see langword="false" />. The default is <see langword="true" />.</returns>
    </member>
    <member name="P:System.Xml.Serialization.SoapTypeAttribute.Namespace">
      <summary>Gets or sets the namespace of the XML type.</summary>
      <returns>The namespace of the XML type. The default is an empty string ("").</returns>
    </member>
    <member name="P:System.Xml.Serialization.SoapTypeAttribute.TypeName">
      <summary>Gets or sets the name of the XML type.</summary>
      <returns>The name of the XML type. The default is the class name.</returns>
    </member>
    <member name="T:System.Xml.Serialization.UnreferencedObjectEventArgs">
      <summary>Provides data for the known, but unreferenced, object found in an encoded SOAP XML stream during deserialization.</summary>
    </member>
    <member name="M:System.Xml.Serialization.UnreferencedObjectEventArgs.#ctor(System.Object,System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Xml.Serialization.UnreferencedObjectEventArgs" /> class.</summary>
      <param name="o">The unreferenced object.</param>
      <param name="id">A unique string used to identify the unreferenced object.</param>
    </member>
    <member name="P:System.Xml.Serialization.UnreferencedObjectEventArgs.UnreferencedId">
      <summary>Gets the ID of the object.</summary>
      <returns>The ID of the object.</returns>
    </member>
    <member name="P:System.Xml.Serialization.UnreferencedObjectEventArgs.UnreferencedObject">
      <summary>Gets the deserialized, but unreferenced, object.</summary>
      <returns>The deserialized, but unreferenced, object.</returns>
    </member>
    <member name="T:System.Xml.Serialization.UnreferencedObjectEventHandler">
      <summary>Represents the method that handles the <see cref="E:System.Xml.Serialization.XmlSerializer.UnreferencedObject" /> event of an <see cref="T:System.Xml.Serialization.XmlSerializer" />.</summary>
      <param name="sender">The source of the event.</param>
      <param name="e">An <see cref="T:System.Xml.Serialization.UnreferencedObjectEventArgs" /> that contains the event data.</param>
    </member>
    <member name="T:System.Xml.Serialization.XmlAnyElementAttributes">
      <summary>Represents a collection of <see cref="T:System.Xml.Serialization.XmlAnyElementAttribute" /> objects.</summary>
    </member>
    <member name="M:System.Xml.Serialization.XmlAnyElementAttributes.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Xml.Serialization.XmlAnyElementAttributes" /> class.</summary>
    </member>
    <member name="M:System.Xml.Serialization.XmlAnyElementAttributes.Add(System.Xml.Serialization.XmlAnyElementAttribute)">
      <summary>Adds an <see cref="T:System.Xml.Serialization.XmlAnyElementAttribute" /> to the collection.</summary>
      <param name="attribute">The <see cref="T:System.Xml.Serialization.XmlAnyElementAttribute" /> to add.</param>
      <returns>The index of the newly added <see cref="T:System.Xml.Serialization.XmlAnyElementAttribute" />.</returns>
    </member>
    <member name="M:System.Xml.Serialization.XmlAnyElementAttributes.Contains(System.Xml.Serialization.XmlAnyElementAttribute)">
      <summary>Gets a value that indicates whether the specified <see cref="T:System.Xml.Serialization.XmlAnyElementAttribute" /> exists in the collection.</summary>
      <param name="attribute">The <see cref="T:System.Xml.Serialization.XmlAnyElementAttribute" /> you are interested in.</param>
      <returns>
        <see langword="true" /> if the <see cref="T:System.Xml.Serialization.XmlAnyElementAttribute" /> exists in the collection; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Xml.Serialization.XmlAnyElementAttributes.CopyTo(System.Xml.Serialization.XmlAnyElementAttribute[],System.Int32)">
      <summary>Copies the entire collection to a compatible one-dimensional array of <see cref="T:System.Xml.Serialization.XmlElementAttribute" /> objects, starting at the specified index of the target array.</summary>
      <param name="array">The one-dimensional array of <see cref="T:System.Xml.Serialization.XmlElementAttribute" /> objects that is the destination of the elements copied from the collection. The array must have zero-based indexing.</param>
      <param name="index">The zero-based index in <paramref name="array" /> at which copying begins.</param>
    </member>
    <member name="M:System.Xml.Serialization.XmlAnyElementAttributes.IndexOf(System.Xml.Serialization.XmlAnyElementAttribute)">
      <summary>Gets the index of the specified <see cref="T:System.Xml.Serialization.XmlAnyElementAttribute" />.</summary>
      <param name="attribute">The <see cref="T:System.Xml.Serialization.XmlAnyElementAttribute" /> whose index you want.</param>
      <returns>The index of the specified <see cref="T:System.Xml.Serialization.XmlAnyElementAttribute" />.</returns>
    </member>
    <member name="M:System.Xml.Serialization.XmlAnyElementAttributes.Insert(System.Int32,System.Xml.Serialization.XmlAnyElementAttribute)">
      <summary>Inserts an <see cref="T:System.Xml.Serialization.XmlAnyElementAttribute" /> into the collection at the specified index.</summary>
      <param name="index">The index where the <see cref="T:System.Xml.Serialization.XmlAnyElementAttribute" /> is inserted.</param>
      <param name="attribute">The <see cref="T:System.Xml.Serialization.XmlAnyElementAttribute" /> to insert.</param>
    </member>
    <member name="P:System.Xml.Serialization.XmlAnyElementAttributes.Item(System.Int32)">
      <summary>Gets or sets the <see cref="T:System.Xml.Serialization.XmlAnyElementAttribute" /> at the specified index.</summary>
      <param name="index">The index of the <see cref="T:System.Xml.Serialization.XmlAnyElementAttribute" />.</param>
      <returns>An <see cref="T:System.Xml.Serialization.XmlAnyElementAttribute" /> at the specified index.</returns>
    </member>
    <member name="M:System.Xml.Serialization.XmlAnyElementAttributes.Remove(System.Xml.Serialization.XmlAnyElementAttribute)">
      <summary>Removes the specified <see cref="T:System.Xml.Serialization.XmlAnyElementAttribute" /> from the collection.</summary>
      <param name="attribute">The <see cref="T:System.Xml.Serialization.XmlAnyElementAttribute" /> to remove.</param>
    </member>
    <member name="T:System.Xml.Serialization.XmlArrayAttribute">
      <summary>Specifies that the <see cref="T:System.Xml.Serialization.XmlSerializer" /> must serialize a particular class member as an array of XML elements.</summary>
    </member>
    <member name="M:System.Xml.Serialization.XmlArrayAttribute.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Xml.Serialization.XmlArrayAttribute" /> class.</summary>
    </member>
    <member name="M:System.Xml.Serialization.XmlArrayAttribute.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Xml.Serialization.XmlArrayAttribute" /> class and specifies the XML element name generated in the XML document instance.</summary>
      <param name="elementName">The name of the XML element that the <see cref="T:System.Xml.Serialization.XmlSerializer" /> generates.</param>
    </member>
    <member name="P:System.Xml.Serialization.XmlArrayAttribute.ElementName">
      <summary>Gets or sets the XML element name given to the serialized array.</summary>
      <returns>The XML element name of the serialized array. The default is the name of the member to which the <see cref="T:System.Xml.Serialization.XmlArrayAttribute" /> is assigned.</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlArrayAttribute.Form">
      <summary>Gets or sets a value that indicates whether the XML element name generated by the <see cref="T:System.Xml.Serialization.XmlSerializer" /> is qualified or unqualified.</summary>
      <returns>One of the <see cref="T:System.Xml.Schema.XmlSchemaForm" /> values. The default is <see langword="XmlSchemaForm.None" />.</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlArrayAttribute.IsNullable">
      <summary>Gets or sets a value that indicates whether the <see cref="T:System.Xml.Serialization.XmlSerializer" /> must serialize a member as an empty XML tag with the <see langword="xsi:nil" /> attribute set to <see langword="true" />.</summary>
      <returns>
        <see langword="true" /> if the <see cref="T:System.Xml.Serialization.XmlSerializer" /> generates the <see langword="xsi:nil" /> attribute; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlArrayAttribute.Namespace">
      <summary>Gets or sets the namespace of the XML element.</summary>
      <returns>The namespace of the XML element.</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlArrayAttribute.Order">
      <summary>Gets or sets the explicit order in which the elements are serialized or deserialized.</summary>
      <returns>The order of the code generation.</returns>
    </member>
    <member name="T:System.Xml.Serialization.XmlArrayItemAttribute">
      <summary>Represents an attribute that specifies the derived types that the <see cref="T:System.Xml.Serialization.XmlSerializer" /> can place in a serialized array.</summary>
    </member>
    <member name="M:System.Xml.Serialization.XmlArrayItemAttribute.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Xml.Serialization.XmlArrayItemAttribute" /> class.</summary>
    </member>
    <member name="M:System.Xml.Serialization.XmlArrayItemAttribute.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Xml.Serialization.XmlArrayItemAttribute" /> class and specifies the name of the XML element generated in the XML document.</summary>
      <param name="elementName">The name of the XML element.</param>
    </member>
    <member name="M:System.Xml.Serialization.XmlArrayItemAttribute.#ctor(System.String,System.Type)">
      <summary>Initializes a new instance of the <see cref="T:System.Xml.Serialization.XmlArrayItemAttribute" /> class and specifies the name of the XML element generated in the XML document and the <see cref="T:System.Type" /> that can be inserted into the generated XML document.</summary>
      <param name="elementName">The name of the XML element.</param>
      <param name="type">The <see cref="T:System.Type" /> of the object to serialize.</param>
    </member>
    <member name="M:System.Xml.Serialization.XmlArrayItemAttribute.#ctor(System.Type)">
      <summary>Initializes a new instance of the <see cref="T:System.Xml.Serialization.XmlArrayItemAttribute" /> class and specifies the <see cref="T:System.Type" /> that can be inserted into the serialized array.</summary>
      <param name="type">The <see cref="T:System.Type" /> of the object to serialize.</param>
    </member>
    <member name="P:System.Xml.Serialization.XmlArrayItemAttribute.DataType">
      <summary>Gets or sets the XML data type of the generated XML element.</summary>
      <returns>An XML schema definition (XSD) data type.</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlArrayItemAttribute.ElementName">
      <summary>Gets or sets the name of the generated XML element.</summary>
      <returns>The name of the generated XML element. The default is the member identifier.</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlArrayItemAttribute.Form">
      <summary>Gets or sets a value that indicates whether the name of the generated XML element is qualified.</summary>
      <returns>One of the <see cref="T:System.Xml.Schema.XmlSchemaForm" /> values. The default is <see langword="XmlSchemaForm.None" />.</returns>
      <exception cref="T:System.Exception">The <see cref="P:System.Xml.Serialization.XmlArrayItemAttribute.Form" /> property is set to <see langword="XmlSchemaForm.Unqualified" /> and a <see cref="P:System.Xml.Serialization.XmlArrayItemAttribute.Namespace" /> value is specified.</exception>
    </member>
    <member name="P:System.Xml.Serialization.XmlArrayItemAttribute.IsNullable">
      <summary>Gets or sets a value that indicates whether the <see cref="T:System.Xml.Serialization.XmlSerializer" /> must serialize a member as an empty XML tag with the <see langword="xsi:nil" /> attribute set to <see langword="true" />.</summary>
      <returns>
        <see langword="true" /> if the <see cref="T:System.Xml.Serialization.XmlSerializer" /> generates the <see langword="xsi:nil" /> attribute; otherwise, <see langword="false" />, and no instance is generated. The default is <see langword="true" />.</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlArrayItemAttribute.Namespace">
      <summary>Gets or sets the namespace of the generated XML element.</summary>
      <returns>The namespace of the generated XML element.</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlArrayItemAttribute.NestingLevel">
      <summary>Gets or sets the level in a hierarchy of XML elements that the <see cref="T:System.Xml.Serialization.XmlArrayItemAttribute" /> affects.</summary>
      <returns>The zero-based index of a set of indexes in an array of arrays.</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlArrayItemAttribute.Type">
      <summary>Gets or sets the type allowed in an array.</summary>
      <returns>A <see cref="T:System.Type" /> that is allowed in the array.</returns>
    </member>
    <member name="T:System.Xml.Serialization.XmlArrayItemAttributes">
      <summary>Represents a collection of <see cref="T:System.Xml.Serialization.XmlArrayItemAttribute" /> objects.</summary>
    </member>
    <member name="M:System.Xml.Serialization.XmlArrayItemAttributes.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Xml.Serialization.XmlArrayItemAttributes" /> class.</summary>
    </member>
    <member name="M:System.Xml.Serialization.XmlArrayItemAttributes.Add(System.Xml.Serialization.XmlArrayItemAttribute)">
      <summary>Adds an <see cref="T:System.Xml.Serialization.XmlArrayItemAttribute" /> to the collection.</summary>
      <param name="attribute">The <see cref="T:System.Xml.Serialization.XmlArrayItemAttribute" /> to add to the collection.</param>
      <returns>The index of the added item.</returns>
    </member>
    <member name="M:System.Xml.Serialization.XmlArrayItemAttributes.Contains(System.Xml.Serialization.XmlArrayItemAttribute)">
      <summary>Determines whether the collection contains the specified <see cref="T:System.Xml.Serialization.XmlArrayItemAttribute" />.</summary>
      <param name="attribute">The <see cref="T:System.Xml.Serialization.XmlArrayItemAttribute" /> to check for.</param>
      <returns>
        <see langword="true" /> if the collection contains the specified <see cref="T:System.Xml.Serialization.XmlArrayItemAttribute" />; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Xml.Serialization.XmlArrayItemAttributes.CopyTo(System.Xml.Serialization.XmlArrayItemAttribute[],System.Int32)">
      <summary>Copies an <see cref="T:System.Xml.Serialization.XmlArrayItemAttribute" /> array to the collection, starting at a specified target index.</summary>
      <param name="array">The array of <see cref="T:System.Xml.Serialization.XmlArrayItemAttribute" /> objects to copy to the collection.</param>
      <param name="index">The index at which the copied attributes begin.</param>
    </member>
    <member name="M:System.Xml.Serialization.XmlArrayItemAttributes.IndexOf(System.Xml.Serialization.XmlArrayItemAttribute)">
      <summary>Returns the zero-based index of the first occurrence of the specified <see cref="T:System.Xml.Serialization.XmlArrayItemAttribute" /> in the collection or -1 if the attribute is not found in the collection.</summary>
      <param name="attribute">The <see cref="T:System.Xml.Serialization.XmlArrayItemAttribute" /> to locate in the collection.</param>
      <returns>The first index of the <see cref="T:System.Xml.Serialization.XmlArrayItemAttribute" /> in the collection or -1 if the attribute is not found in the collection.</returns>
    </member>
    <member name="M:System.Xml.Serialization.XmlArrayItemAttributes.Insert(System.Int32,System.Xml.Serialization.XmlArrayItemAttribute)">
      <summary>Inserts an <see cref="T:System.Xml.Serialization.XmlArrayItemAttribute" /> into the collection at the specified index.</summary>
      <param name="index">The index at which the attribute is inserted.</param>
      <param name="attribute">The <see cref="T:System.Xml.Serialization.XmlArrayItemAttribute" /> to insert.</param>
    </member>
    <member name="P:System.Xml.Serialization.XmlArrayItemAttributes.Item(System.Int32)">
      <summary>Gets or sets the item at the specified index.</summary>
      <param name="index">The zero-based index of the collection member to get or set.</param>
      <returns>The <see cref="T:System.Xml.Serialization.XmlArrayItemAttribute" /> at the specified index.</returns>
    </member>
    <member name="M:System.Xml.Serialization.XmlArrayItemAttributes.Remove(System.Xml.Serialization.XmlArrayItemAttribute)">
      <summary>Removes an <see cref="T:System.Xml.Serialization.XmlArrayItemAttribute" /> from the collection, if it is present.</summary>
      <param name="attribute">The <see cref="T:System.Xml.Serialization.XmlArrayItemAttribute" /> to remove.</param>
    </member>
    <member name="T:System.Xml.Serialization.XmlAttributeEventArgs">
      <summary>Provides data for the <see cref="E:System.Xml.Serialization.XmlSerializer.UnknownAttribute" /> event.</summary>
    </member>
    <member name="P:System.Xml.Serialization.XmlAttributeEventArgs.Attr">
      <summary>Gets an object that represents the unknown XML attribute.</summary>
      <returns>An <see cref="T:System.Xml.XmlAttribute" /> that represents the unknown XML attribute.</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlAttributeEventArgs.ExpectedAttributes">
      <summary>Gets a comma-delimited list of XML attribute names expected to be in an XML document instance.</summary>
      <returns>A comma-delimited list of XML attribute names. Each name is in the following format: <paramref name="namespace" />:<paramref name="name" />.</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlAttributeEventArgs.LineNumber">
      <summary>Gets the line number of the unknown XML attribute.</summary>
      <returns>The line number of the unknown XML attribute.</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlAttributeEventArgs.LinePosition">
      <summary>Gets the position in the line of the unknown XML attribute.</summary>
      <returns>The position number of the unknown XML attribute.</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlAttributeEventArgs.ObjectBeingDeserialized">
      <summary>Gets the object being deserialized.</summary>
      <returns>The object being deserialized.</returns>
    </member>
    <member name="T:System.Xml.Serialization.XmlAttributeEventHandler">
      <summary>Represents the method that handles the <see cref="E:System.Xml.Serialization.XmlSerializer.UnknownAttribute" /></summary>
      <param name="sender">The source of the event.</param>
      <param name="e">An <see cref="T:System.Xml.Serialization.XmlAttributeEventArgs" /> that contains the event data.</param>
    </member>
    <member name="T:System.Xml.Serialization.XmlAttributeOverrides">
      <summary>Allows you to override property, field, and class attributes when you use the <see cref="T:System.Xml.Serialization.XmlSerializer" /> to serialize or deserialize an object.</summary>
    </member>
    <member name="M:System.Xml.Serialization.XmlAttributeOverrides.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Xml.Serialization.XmlAttributeOverrides" /> class.</summary>
    </member>
    <member name="M:System.Xml.Serialization.XmlAttributeOverrides.Add(System.Type,System.String,System.Xml.Serialization.XmlAttributes)">
      <summary>Adds an <see cref="T:System.Xml.Serialization.XmlAttributes" /> object to the collection of <see cref="T:System.Xml.Serialization.XmlAttributes" /> objects. The <paramref name="type" /> parameter specifies an object to be overridden. The <paramref name="member" /> parameter specifies the name of a member that is overridden.</summary>
      <param name="type">The <see cref="T:System.Type" /> of the object to override.</param>
      <param name="member">The name of the member to override.</param>
      <param name="attributes">An <see cref="T:System.Xml.Serialization.XmlAttributes" /> object that represents the overriding attributes.</param>
    </member>
    <member name="M:System.Xml.Serialization.XmlAttributeOverrides.Add(System.Type,System.Xml.Serialization.XmlAttributes)">
      <summary>Adds an <see cref="T:System.Xml.Serialization.XmlAttributes" /> object to the collection of <see cref="T:System.Xml.Serialization.XmlAttributes" /> objects. The <paramref name="type" /> parameter specifies an object to be overridden by the <see cref="T:System.Xml.Serialization.XmlAttributes" /> object.</summary>
      <param name="type">The <see cref="T:System.Type" /> of the object that is overridden.</param>
      <param name="attributes">An <see cref="T:System.Xml.Serialization.XmlAttributes" /> object that represents the overriding attributes.</param>
    </member>
    <member name="P:System.Xml.Serialization.XmlAttributeOverrides.Item(System.Type)">
      <summary>Gets the object associated with the specified, base-class, type.</summary>
      <param name="type">The base class <see cref="T:System.Type" /> that is associated with the collection of attributes you want to retrieve.</param>
      <returns>An <see cref="T:System.Xml.Serialization.XmlAttributes" /> that represents the collection of overriding attributes.</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlAttributeOverrides.Item(System.Type,System.String)">
      <summary>Gets the object associated with the specified (base-class) type. The member parameter specifies the base-class member that is overridden.</summary>
      <param name="type">The base class <see cref="T:System.Type" /> that is associated with the collection of attributes you want.</param>
      <param name="member">The name of the overridden member that specifies the <see cref="T:System.Xml.Serialization.XmlAttributes" /> to return.</param>
      <returns>An <see cref="T:System.Xml.Serialization.XmlAttributes" /> that represents the collection of overriding attributes.</returns>
    </member>
    <member name="T:System.Xml.Serialization.XmlAttributes">
      <summary>Represents a collection of attribute objects that control how the <see cref="T:System.Xml.Serialization.XmlSerializer" /> serializes and deserializes an object.</summary>
    </member>
    <member name="M:System.Xml.Serialization.XmlAttributes.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Xml.Serialization.XmlAttributes" /> class.</summary>
    </member>
    <member name="M:System.Xml.Serialization.XmlAttributes.#ctor(System.Reflection.ICustomAttributeProvider)">
      <summary>Initializes a new instance of the <see cref="T:System.Xml.Serialization.XmlAttributes" /> class and customizes how the <see cref="T:System.Xml.Serialization.XmlSerializer" /> serializes and deserializes an object.</summary>
      <param name="provider">A class that can provide alternative implementations of attributes that control XML serialization.</param>
    </member>
    <member name="P:System.Xml.Serialization.XmlAttributes.XmlAnyAttribute">
      <summary>Gets or sets the <see cref="T:System.Xml.Serialization.XmlAnyAttributeAttribute" /> to override.</summary>
      <returns>The <see cref="T:System.Xml.Serialization.XmlAnyAttributeAttribute" /> to override.</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlAttributes.XmlAnyElements">
      <summary>Gets the collection of <see cref="T:System.Xml.Serialization.XmlAnyElementAttribute" /> objects to override.</summary>
      <returns>An <see cref="T:System.Xml.Serialization.XmlAnyElementAttributes" /> object that represents the collection of <see cref="T:System.Xml.Serialization.XmlAnyElementAttribute" /> objects.</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlAttributes.XmlArray">
      <summary>Gets or sets an object that specifies how the <see cref="T:System.Xml.Serialization.XmlSerializer" /> serializes a public field or read/write property that returns an array.</summary>
      <returns>An <see cref="T:System.Xml.Serialization.XmlArrayAttribute" /> that specifies how the <see cref="T:System.Xml.Serialization.XmlSerializer" /> serializes a public field or read/write property that returns an array.</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlAttributes.XmlArrayItems">
      <summary>Gets or sets a collection of objects that specify how the <see cref="T:System.Xml.Serialization.XmlSerializer" /> serializes items inserted into an array returned by a public field or read/write property.</summary>
      <returns>An <see cref="T:System.Xml.Serialization.XmlArrayItemAttributes" /> object that contains a collection of <see cref="T:System.Xml.Serialization.XmlArrayItemAttribute" /> objects.</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlAttributes.XmlAttribute">
      <summary>Gets or sets an object that specifies how the <see cref="T:System.Xml.Serialization.XmlSerializer" /> serializes a public field or public read/write property as an XML attribute.</summary>
      <returns>An <see cref="T:System.Xml.Serialization.XmlAttributeAttribute" /> that controls the serialization of a public field or read/write property as an XML attribute.</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlAttributes.XmlChoiceIdentifier">
      <summary>Gets or sets an object that allows you to distinguish between a set of choices.</summary>
      <returns>An <see cref="T:System.Xml.Serialization.XmlChoiceIdentifierAttribute" /> that can be applied to a class member that is serialized as an <see langword="xsi:choice" /> element.</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlAttributes.XmlDefaultValue">
      <summary>Gets or sets the default value of an XML element or attribute.</summary>
      <returns>An <see cref="T:System.Object" /> that represents the default value of an XML element or attribute.</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlAttributes.XmlElements">
      <summary>Gets a collection of objects that specify how the <see cref="T:System.Xml.Serialization.XmlSerializer" /> serializes a public field or read/write property as an XML element.</summary>
      <returns>An <see cref="T:System.Xml.Serialization.XmlElementAttributes" /> that contains a collection of <see cref="T:System.Xml.Serialization.XmlElementAttribute" /> objects.</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlAttributes.XmlEnum">
      <summary>Gets or sets an object that specifies how the <see cref="T:System.Xml.Serialization.XmlSerializer" /> serializes an enumeration member.</summary>
      <returns>An <see cref="T:System.Xml.Serialization.XmlEnumAttribute" /> that specifies how the <see cref="T:System.Xml.Serialization.XmlSerializer" /> serializes an enumeration member.</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlAttributes.XmlIgnore">
      <summary>Gets or sets a value that specifies whether or not the <see cref="T:System.Xml.Serialization.XmlSerializer" /> serializes a public field or public read/write property.</summary>
      <returns>
        <see langword="true" /> if the <see cref="T:System.Xml.Serialization.XmlSerializer" /> must not serialize the field or property; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlAttributes.Xmlns">
      <summary>Gets or sets a value that specifies whether to keep all namespace declarations when an object containing a member that returns an <see cref="T:System.Xml.Serialization.XmlSerializerNamespaces" /> object is overridden.</summary>
      <returns>
        <see langword="true" /> if the namespace declarations should be kept; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlAttributes.XmlRoot">
      <summary>Gets or sets an object that specifies how the <see cref="T:System.Xml.Serialization.XmlSerializer" /> serializes a class as an XML root element.</summary>
      <returns>An <see cref="T:System.Xml.Serialization.XmlRootAttribute" /> that overrides a class attributed as an XML root element.</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlAttributes.XmlText">
      <summary>Gets or sets an object that instructs the <see cref="T:System.Xml.Serialization.XmlSerializer" /> to serialize a public field or public read/write property as XML text.</summary>
      <returns>An <see cref="T:System.Xml.Serialization.XmlTextAttribute" /> that overrides the default serialization of a public property or field.</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlAttributes.XmlType">
      <summary>Gets or sets an object that specifies how the <see cref="T:System.Xml.Serialization.XmlSerializer" /> serializes a class to which the <see cref="T:System.Xml.Serialization.XmlTypeAttribute" /> has been applied.</summary>
      <returns>An <see cref="T:System.Xml.Serialization.XmlTypeAttribute" /> that overrides an <see cref="T:System.Xml.Serialization.XmlTypeAttribute" /> applied to a class declaration.</returns>
    </member>
    <member name="T:System.Xml.Serialization.XmlChoiceIdentifierAttribute">
      <summary>Specifies that the member can be further detected by using an enumeration.</summary>
    </member>
    <member name="M:System.Xml.Serialization.XmlChoiceIdentifierAttribute.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Xml.Serialization.XmlChoiceIdentifierAttribute" /> class.</summary>
    </member>
    <member name="M:System.Xml.Serialization.XmlChoiceIdentifierAttribute.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Xml.Serialization.XmlChoiceIdentifierAttribute" /> class.</summary>
      <param name="name">The member name that returns the enumeration used to detect a choice.</param>
    </member>
    <member name="P:System.Xml.Serialization.XmlChoiceIdentifierAttribute.MemberName">
      <summary>Gets or sets the name of the field that returns the enumeration to use when detecting types.</summary>
      <returns>The name of a field that returns an enumeration.</returns>
    </member>
    <member name="T:System.Xml.Serialization.XmlDeserializationEvents">
      <summary>Contains fields that can be used to pass event delegates to a thread-safe <see cref="Overload:System.Xml.Serialization.XmlSerializer.Deserialize" /> method of the <see cref="T:System.Xml.Serialization.XmlSerializer" />.</summary>
    </member>
    <member name="P:System.Xml.Serialization.XmlDeserializationEvents.OnUnknownAttribute_p">
      <summary>Gets or sets an object that represents the method that handles the <see cref="E:System.Xml.Serialization.XmlSerializer.UnknownAttribute" /> event.</summary>
      <returns>An <see cref="T:System.Xml.Serialization.XmlAttributeEventHandler" /> that points to the event handler.</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlDeserializationEvents.OnUnknownElement_p">
      <summary>Gets or sets an object that represents the method that handles the <see cref="E:System.Xml.Serialization.XmlSerializer.UnknownElement" /> event.</summary>
      <returns>An <see cref="T:System.Xml.Serialization.XmlElementEventHandler" /> that points to the event handler.</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlDeserializationEvents.OnUnknownNode_p">
      <summary>Gets or sets an object that represents the method that handles the <see cref="E:System.Xml.Serialization.XmlSerializer.UnknownNode" /> event.</summary>
      <returns>An <see cref="T:System.Xml.Serialization.XmlNodeEventHandler" /> that points to the event handler.</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlDeserializationEvents.OnUnreferencedObject_p">
      <summary>Gets or sets an object that represents the method that handles the <see cref="E:System.Xml.Serialization.XmlSerializer.UnreferencedObject" /> event.</summary>
      <returns>An <see cref="T:System.Xml.Serialization.UnreferencedObjectEventHandler" /> that points to the event handler.</returns>
    </member>
    <member name="T:System.Xml.Serialization.XmlElementAttributes">
      <summary>Represents a collection of <see cref="T:System.Xml.Serialization.XmlElementAttribute" /> objects used by the <see cref="T:System.Xml.Serialization.XmlSerializer" /> to override the default way it serializes a class.</summary>
    </member>
    <member name="M:System.Xml.Serialization.XmlElementAttributes.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Xml.Serialization.XmlElementAttributes" /> class.</summary>
    </member>
    <member name="M:System.Xml.Serialization.XmlElementAttributes.Add(System.Xml.Serialization.XmlElementAttribute)">
      <summary>Adds an <see cref="T:System.Xml.Serialization.XmlElementAttribute" /> to the collection.</summary>
      <param name="attribute">The <see cref="T:System.Xml.Serialization.XmlElementAttribute" /> to add.</param>
      <returns>The zero-based index of the newly added item.</returns>
    </member>
    <member name="M:System.Xml.Serialization.XmlElementAttributes.Contains(System.Xml.Serialization.XmlElementAttribute)">
      <summary>Determines whether the collection contains the specified object.</summary>
      <param name="attribute">The <see cref="T:System.Xml.Serialization.XmlElementAttribute" /> to look for.</param>
      <returns>
        <see langword="true" /> if the object exists in the collection; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Xml.Serialization.XmlElementAttributes.CopyTo(System.Xml.Serialization.XmlElementAttribute[],System.Int32)">
      <summary>Copies the <see cref="T:System.Xml.Serialization.XmlElementAttributes" />, or a portion of it to a one-dimensional array.</summary>
      <param name="array">The <see cref="T:System.Xml.Serialization.XmlElementAttribute" /> array to hold the copied elements.</param>
      <param name="index">The zero-based index in <paramref name="array" /> at which copying begins.</param>
    </member>
    <member name="M:System.Xml.Serialization.XmlElementAttributes.IndexOf(System.Xml.Serialization.XmlElementAttribute)">
      <summary>Gets the index of the specified <see cref="T:System.Xml.Serialization.XmlElementAttribute" />.</summary>
      <param name="attribute">The <see cref="T:System.Xml.Serialization.XmlElementAttribute" /> whose index is being retrieved.</param>
      <returns>The zero-based index of the <see cref="T:System.Xml.Serialization.XmlElementAttribute" />.</returns>
    </member>
    <member name="M:System.Xml.Serialization.XmlElementAttributes.Insert(System.Int32,System.Xml.Serialization.XmlElementAttribute)">
      <summary>Inserts an <see cref="T:System.Xml.Serialization.XmlElementAttribute" /> into the collection.</summary>
      <param name="index">The zero-based index where the member is inserted.</param>
      <param name="attribute">The <see cref="T:System.Xml.Serialization.XmlElementAttribute" /> to insert.</param>
    </member>
    <member name="P:System.Xml.Serialization.XmlElementAttributes.Item(System.Int32)">
      <summary>Gets or sets the element at the specified index.</summary>
      <param name="index">The zero-based index of the element to get or set.</param>
      <returns>The element at the specified index.</returns>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> is not a valid index in the <see cref="T:System.Xml.Serialization.XmlElementAttributes" />.</exception>
      <exception cref="T:System.NotSupportedException">The property is set and the <see cref="T:System.Xml.Serialization.XmlElementAttributes" /> is read-only.</exception>
    </member>
    <member name="M:System.Xml.Serialization.XmlElementAttributes.Remove(System.Xml.Serialization.XmlElementAttribute)">
      <summary>Removes the specified object from the collection.</summary>
      <param name="attribute">The <see cref="T:System.Xml.Serialization.XmlElementAttribute" /> to remove from the collection.</param>
    </member>
    <member name="T:System.Xml.Serialization.XmlElementEventArgs">
      <summary>Provides data for the <see cref="E:System.Xml.Serialization.XmlSerializer.UnknownElement" /> event.</summary>
    </member>
    <member name="P:System.Xml.Serialization.XmlElementEventArgs.Element">
      <summary>Gets the object that represents the unknown XML element.</summary>
      <returns>The object that represents the unknown XML element.</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlElementEventArgs.ExpectedElements">
      <summary>Gets a comma-delimited list of XML element names expected to be in an XML document instance.</summary>
      <returns>A comma-delimited list of XML element names. Each name is in the following format: <paramref name="namespace" />:<paramref name="name" />.</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlElementEventArgs.LineNumber">
      <summary>Gets the line number where the unknown element was encountered if the XML reader is an <see cref="T:System.Xml.XmlTextReader" />.</summary>
      <returns>The line number where the unknown element was encountered if the XML reader is an <see cref="T:System.Xml.XmlTextReader" />; otherwise, -1.</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlElementEventArgs.LinePosition">
      <summary>Gets the place in the line where the unknown element occurs if the XML reader is an <see cref="T:System.Xml.XmlTextReader" />.</summary>
      <returns>The number in the line where the unknown element occurs if the XML reader is an <see cref="T:System.Xml.XmlTextReader" />; otherwise, -1.</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlElementEventArgs.ObjectBeingDeserialized">
      <summary>Gets the object the <see cref="T:System.Xml.Serialization.XmlSerializer" /> is deserializing.</summary>
      <returns>The object that is being deserialized by the <see cref="T:System.Xml.Serialization.XmlSerializer" />.</returns>
    </member>
    <member name="T:System.Xml.Serialization.XmlElementEventHandler">
      <summary>Represents the method that handles the <see cref="E:System.Xml.Serialization.XmlSerializer.UnknownElement" /> event of an <see cref="T:System.Xml.Serialization.XmlSerializer" />.</summary>
      <param name="sender">The source of the event.</param>
      <param name="e">A <see cref="T:System.Xml.Serialization.XmlElementEventArgs" /> that contains the event data.</param>
    </member>
    <member name="T:System.Xml.Serialization.XmlIncludeAttribute">
      <summary>Allows the <see cref="T:System.Xml.Serialization.XmlSerializer" /> to recognize a type when it serializes or deserializes an object.</summary>
    </member>
    <member name="M:System.Xml.Serialization.XmlIncludeAttribute.#ctor(System.Type)">
      <summary>Initializes a new instance of the <see cref="T:System.Xml.Serialization.XmlIncludeAttribute" /> class.</summary>
      <param name="type">The <see cref="T:System.Type" /> of the object to include.</param>
    </member>
    <member name="P:System.Xml.Serialization.XmlIncludeAttribute.Type">
      <summary>Gets or sets the type of the object to include.</summary>
      <returns>The <see cref="T:System.Type" /> of the object to include.</returns>
    </member>
    <member name="T:System.Xml.Serialization.XmlMapping">
      <summary>Supports mappings between .NET Framework types and XML Schema data types.</summary>
    </member>
    <member name="P:System.Xml.Serialization.XmlMapping.ElementName">
      <summary>Gets the name of the mapped element.</summary>
      <returns>The name of the mapped element.</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlMapping.Namespace">
      <summary>Gets the namespace of the mapped element.</summary>
      <returns>The namespace of the mapped element.</returns>
    </member>
    <member name="M:System.Xml.Serialization.XmlMapping.SetKey(System.String)">
      <summary>Sets the key used to look up the mapping.</summary>
      <param name="key">The lookup key.</param>
    </member>
    <member name="P:System.Xml.Serialization.XmlMapping.XsdElementName">
      <summary>Gets the name of the XSD element of the mapping.</summary>
      <returns>The XSD element name.</returns>
    </member>
    <member name="T:System.Xml.Serialization.XmlMappingAccess">
      <summary>Specifies whether a mapping is read, write, or both.</summary>
    </member>
    <member name="F:System.Xml.Serialization.XmlMappingAccess.None">
      <summary>Both read and write methods are generated.</summary>
    </member>
    <member name="F:System.Xml.Serialization.XmlMappingAccess.Read">
      <summary>Read methods are generated.</summary>
    </member>
    <member name="F:System.Xml.Serialization.XmlMappingAccess.Write">
      <summary>Write methods are generated.</summary>
    </member>
    <member name="T:System.Xml.Serialization.XmlMemberMapping">
      <summary>Maps a code entity in a .NET Framework Web service method to an element in a Web Services Description Language (WSDL) message.</summary>
    </member>
    <member name="P:System.Xml.Serialization.XmlMemberMapping.Any">
      <summary>Gets or sets a value that indicates whether the .NET Framework type maps to an XML element or attribute of any type.</summary>
      <returns>
        <see langword="true" />, if the type maps to an XML any element or attribute; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlMemberMapping.CheckSpecified">
      <summary>Gets a value that indicates whether the accompanying field in the .NET Framework type has a value specified.</summary>
      <returns>
        <see langword="true" />, if the accompanying field has a value specified; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlMemberMapping.ElementName">
      <summary>Gets the unqualified name of the XML element declaration that applies to this mapping.</summary>
      <returns>The unqualified name of the XML element declaration that applies to this mapping.</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlMemberMapping.MemberName">
      <summary>Gets the name of the Web service method member that is represented by this mapping.</summary>
      <returns>The name of the Web service method member represented by this mapping.</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlMemberMapping.Namespace">
      <summary>Gets the XML namespace that applies to this mapping.</summary>
      <returns>The XML namespace that applies to this mapping.</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlMemberMapping.TypeFullName">
      <summary>Gets the fully qualified type name of the .NET Framework type for this mapping.</summary>
      <returns>The fully qualified type name of the .NET Framework type for this mapping.</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlMemberMapping.TypeName">
      <summary>Gets the type name of the .NET Framework type for this mapping.</summary>
      <returns>The type name of the .NET Framework type for this mapping.</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlMemberMapping.TypeNamespace">
      <summary>Gets the namespace of the .NET Framework type for this mapping.</summary>
      <returns>The namespace of the .NET Framework type for this mapping.</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlMemberMapping.XsdElementName">
      <summary>Gets the XML element name as it appears in the service description document.</summary>
      <returns>The XML element name.</returns>
    </member>
    <member name="T:System.Xml.Serialization.XmlMembersMapping">
      <summary>Provides mappings between .NET Framework Web service methods and Web Services Description Language (WSDL) messages that are defined for SOAP Web services.</summary>
    </member>
    <member name="P:System.Xml.Serialization.XmlMembersMapping.Count">
      <summary>Gets the number of .NET Framework code entities that belong to a Web service method to which a SOAP message is being mapped.</summary>
      <returns>The number of mappings in the collection.</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlMembersMapping.Item(System.Int32)">
      <summary>Gets an item that contains internal type mapping information for a .NET Framework code entity that belongs to a Web service method being mapped to a SOAP message.</summary>
      <param name="index">The index of the mapping to return.</param>
      <returns>The requested <see cref="T:System.Xml.Serialization.XmlMemberMapping" />.</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlMembersMapping.TypeName">
      <summary>Gets the name of the .NET Framework type being mapped to the data type of an XML Schema element that represents a SOAP message.</summary>
      <returns>The name of the .NET Framework type.</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlMembersMapping.TypeNamespace">
      <summary>Gets the namespace of the .NET Framework type being mapped to the data type of an XML Schema element that represents a SOAP message.</summary>
      <returns>The .NET Framework namespace of the mapping.</returns>
    </member>
    <member name="T:System.Xml.Serialization.XmlNodeEventArgs">
      <summary>Provides data for the <see cref="E:System.Xml.Serialization.XmlSerializer.UnknownNode" /> event.</summary>
    </member>
    <member name="P:System.Xml.Serialization.XmlNodeEventArgs.LineNumber">
      <summary>Gets the line number of the unknown XML node.</summary>
      <returns>The line number of the unknown XML node.</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlNodeEventArgs.LinePosition">
      <summary>Gets the position in the line of the unknown XML node.</summary>
      <returns>The position number of the unknown XML node.</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlNodeEventArgs.LocalName">
      <summary>Gets the XML local name of the XML node being deserialized.</summary>
      <returns>The XML local name of the node being deserialized.</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlNodeEventArgs.Name">
      <summary>Gets the name of the XML node being deserialized.</summary>
      <returns>The name of the node being deserialized.</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlNodeEventArgs.NamespaceURI">
      <summary>Gets the namespace URI that is associated with the XML node being deserialized.</summary>
      <returns>The namespace URI that is associated with the XML node being deserialized.</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlNodeEventArgs.NodeType">
      <summary>Gets the type of the XML node being deserialized.</summary>
      <returns>The <see cref="T:System.Xml.XmlNodeType" /> that represents the XML node being deserialized.</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlNodeEventArgs.ObjectBeingDeserialized">
      <summary>Gets the object being deserialized.</summary>
      <returns>The <see cref="T:System.Object" /> being deserialized.</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlNodeEventArgs.Text">
      <summary>Gets the text of the XML node being deserialized.</summary>
      <returns>The text of the XML node being deserialized.</returns>
    </member>
    <member name="T:System.Xml.Serialization.XmlNodeEventHandler">
      <summary>Represents the method that handles the <see cref="E:System.Xml.Serialization.XmlSerializer.UnknownNode" /> event of an <see cref="T:System.Xml.Serialization.XmlSerializer" />.</summary>
      <param name="sender">The source of the event.</param>
      <param name="e">An <see cref="T:System.Xml.Serialization.XmlNodeEventArgs" /> that contains the event data.</param>
    </member>
    <member name="T:System.Xml.Serialization.XmlReflectionImporter">
      <summary>Generates mappings to XML schema element declarations, including literal XML Schema Definition (XSD) message parts in a Web Services Description Language (WSDL) document, for .NET Framework types or Web service method information.</summary>
    </member>
    <member name="M:System.Xml.Serialization.XmlReflectionImporter.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Xml.Serialization.XmlReflectionImporter" /> class.</summary>
    </member>
    <member name="M:System.Xml.Serialization.XmlReflectionImporter.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Xml.Serialization.XmlReflectionImporter" /> class using the specified default XML namespace.</summary>
      <param name="defaultNamespace">The default XML namespace to use for imported type mappings.</param>
    </member>
    <member name="M:System.Xml.Serialization.XmlReflectionImporter.#ctor(System.Xml.Serialization.XmlAttributeOverrides)">
      <summary>Initializes a new instance of the <see cref="T:System.Xml.Serialization.XmlReflectionImporter" /> class using the specified XML serialization overrides.</summary>
      <param name="attributeOverrides">An object that overrides how the <see cref="T:System.Xml.Serialization.XmlSerializer" /> class serializes mapped types.</param>
    </member>
    <member name="M:System.Xml.Serialization.XmlReflectionImporter.#ctor(System.Xml.Serialization.XmlAttributeOverrides,System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Xml.Serialization.XmlReflectionImporter" /> class using the specified XML serialization overrides and default XML namespace.</summary>
      <param name="attributeOverrides">An object that overrides how the <see cref="T:System.Xml.Serialization.XmlSerializer" /> class serializes mapped types.</param>
      <param name="defaultNamespace">The default XML namespace to use for imported type mappings.</param>
    </member>
    <member name="M:System.Xml.Serialization.XmlReflectionImporter.ImportMembersMapping(System.String,System.String,System.Xml.Serialization.XmlReflectionMember[],System.Boolean)">
      <summary>Generates internal type mappings for information from a Web service method.</summary>
      <param name="elementName">An XML element name produced from the Web service method.</param>
      <param name="ns">An XML element namespace produced from the Web service method.</param>
      <param name="members">An array of <see cref="T:System.Xml.Serialization.XmlReflectionMember" /> objects that contain .NET Framework code entities that belong to a Web service method.</param>
      <param name="hasWrapperElement">
        <see langword="true" /> if elements that correspond to Web Services Description Language (WSDL) message parts should be enclosed in an extra wrapper element in a SOAP message; otherwise, <see langword="false" />.</param>
      <returns>An <see cref="T:System.Xml.Serialization.XmlMembersMapping" /> with mappings to the element parts of a WSDL message definition.</returns>
    </member>
    <member name="M:System.Xml.Serialization.XmlReflectionImporter.ImportMembersMapping(System.String,System.String,System.Xml.Serialization.XmlReflectionMember[],System.Boolean,System.Boolean)">
      <summary>Returns internal type mappings using information from a Web service method, and allows you to specify an XML element name, XML namespace, and other options.</summary>
      <param name="elementName">An XML element name produced from the Web service method.</param>
      <param name="ns">An XML element namespace produced from the Web service method.</param>
      <param name="members">An array of <see cref="T:System.Xml.Serialization.XmlReflectionMember" /> objects that contain .NET Framework code entities that belong to a Web service method.</param>
      <param name="hasWrapperElement">
        <see langword="true" /> if elements that correspond to Web Services Description Language (WSDL) message parts should be enclosed in an extra wrapper element in a SOAP message; otherwise, <see langword="false" />.</param>
      <param name="rpc">
        <see langword="true" /> if the method is a remote procedure call; otherwise, <see langword="false" />.</param>
      <returns>An <see cref="T:System.Xml.Serialization.XmlMembersMapping" /> that contains the mappings.</returns>
    </member>
    <member name="M:System.Xml.Serialization.XmlReflectionImporter.ImportMembersMapping(System.String,System.String,System.Xml.Serialization.XmlReflectionMember[],System.Boolean,System.Boolean,System.Boolean)">
      <summary>Returns internal type mappings using information from a Web service method, and allows you to specify an XML element name, XML namespace, and other options.</summary>
      <param name="elementName">An XML element name produced from the Web service method.</param>
      <param name="ns">An XML element namespace produced from the Web service method.</param>
      <param name="members">An array of <see cref="T:System.Xml.Serialization.XmlReflectionMember" /> objects that contain .NET Framework code entities that belong to a Web service method.</param>
      <param name="hasWrapperElement">
        <see langword="true" /> if elements that correspond to Web Services Description Language (WSDL) message parts should be enclosed in an extra wrapper element in a SOAP message; otherwise, <see langword="false" />.</param>
      <param name="rpc">
        <see langword="true" /> if the method is a remote procedure call; otherwise, <see langword="false" />.</param>
      <param name="openModel">
        <see langword="true" /> to specify that the generated schema type will be marked with the <see langword="&lt;xs:anyAttribute&gt;" /> element; otherwise, <see langword="false" />.</param>
      <returns>An <see cref="T:System.Xml.Serialization.XmlMembersMapping" /> that contains the mappings.</returns>
    </member>
    <member name="M:System.Xml.Serialization.XmlReflectionImporter.ImportMembersMapping(System.String,System.String,System.Xml.Serialization.XmlReflectionMember[],System.Boolean,System.Boolean,System.Boolean,System.Xml.Serialization.XmlMappingAccess)">
      <summary>Generates internal type mappings for information from a Web service method.</summary>
      <param name="elementName">An XML element name produced from the Web service method.</param>
      <param name="ns">An XML element namespace produced from the Web service method.</param>
      <param name="members">An array of <see cref="T:System.Xml.Serialization.XmlReflectionMember" /> objects that contain .NET Framework code entities that belong to a Web service method.</param>
      <param name="hasWrapperElement">
        <see langword="true" /> if elements that correspond to Web Services Description Language (WSDL) message parts should be enclosed in an extra wrapper element in a SOAP message; otherwise, <see langword="false" />.</param>
      <param name="rpc">
        <see langword="true" /> if the method is a remote procedure call; otherwise, <see langword="false" />.</param>
      <param name="openModel">
        <see langword="true" /> to specify that the generated schema type will be marked with the <see langword="&lt;xs:anyAttribute&gt;" /> element; otherwise, <see langword="false" />.</param>
      <param name="access">One of the <see cref="T:System.Xml.Serialization.XmlMappingAccess" /> values. The default is <see langword="None" />.</param>
      <returns>An <see cref="T:System.Xml.Serialization.XmlMembersMapping" /> that contains the mappings.</returns>
    </member>
    <member name="M:System.Xml.Serialization.XmlReflectionImporter.ImportTypeMapping(System.Type)">
      <summary>Generates a mapping to an XML Schema element for a specified .NET Framework type.</summary>
      <param name="type">The .NET Framework type for which to generate a type mapping.</param>
      <returns>Internal .NET Framework mapping of a type to an XML Schema element.</returns>
    </member>
    <member name="M:System.Xml.Serialization.XmlReflectionImporter.ImportTypeMapping(System.Type,System.String)">
      <summary>Generates a mapping to an XML Schema element for a .NET Framework type, using the specified type and namespace.</summary>
      <param name="type">The .NET Framework type for which to generate a type mapping.</param>
      <param name="defaultNamespace">The default XML namespace to use.</param>
      <returns>Internal .NET Framework mapping of a type to an XML Schema element.</returns>
    </member>
    <member name="M:System.Xml.Serialization.XmlReflectionImporter.ImportTypeMapping(System.Type,System.Xml.Serialization.XmlRootAttribute)">
      <summary>Generates a mapping to an XML Schema element for a .NET Framework type, using the specified type and attribute.</summary>
      <param name="type">The .NET Framework type for which to generate a type mapping.</param>
      <param name="root">An <see cref="T:System.Xml.Serialization.XmlRootAttribute" /> attribute that is applied to the type.</param>
      <returns>An <see cref="T:System.Xml.Serialization.XmlTypeMapping" /> that represents a mapping of a .NET Framework type to an XML Schema element.</returns>
    </member>
    <member name="M:System.Xml.Serialization.XmlReflectionImporter.ImportTypeMapping(System.Type,System.Xml.Serialization.XmlRootAttribute,System.String)">
      <summary>Generates a mapping to an XML Schema element for a .NET Framework type, using the specified type, attribute, and namespace.</summary>
      <param name="type">The .NET Framework type for which to generate a type mapping.</param>
      <param name="root">An <see cref="T:System.Xml.Serialization.XmlRootAttribute" /> attribute that is applied to the type.</param>
      <param name="defaultNamespace">The default XML namespace to use.</param>
      <returns>An <see cref="T:System.Xml.Serialization.XmlTypeMapping" /> that contains the internal .NET Framework mapping of a type to an XML Schema element.</returns>
    </member>
    <member name="M:System.Xml.Serialization.XmlReflectionImporter.IncludeType(System.Type)">
      <summary>Includes mappings for a type for later use when import methods are invoked.</summary>
      <param name="type">The .NET Framework type for which to save type mapping information.</param>
    </member>
    <member name="M:System.Xml.Serialization.XmlReflectionImporter.IncludeTypes(System.Reflection.ICustomAttributeProvider)">
      <summary>Includes mappings for derived types for later use when import methods are invoked.</summary>
      <param name="provider">An instance of the <see cref="T:System.Reflection.ICustomAttributeProvider" /> class that contains custom attributes derived from the <see cref="T:System.Xml.Serialization.XmlIncludeAttribute" /> attribute.</param>
    </member>
    <member name="T:System.Xml.Serialization.XmlReflectionMember">
      <summary>Provides mappings between code entities in .NET Framework Web service methods and the content of Web Services Description Language (WSDL) messages that are defined for SOAP Web services.</summary>
    </member>
    <member name="M:System.Xml.Serialization.XmlReflectionMember.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Xml.Serialization.XmlReflectionMember" /> class.</summary>
    </member>
    <member name="P:System.Xml.Serialization.XmlReflectionMember.IsReturnValue">
      <summary>Gets or sets a value that indicates whether the <see cref="T:System.Xml.Serialization.XmlReflectionMember" /> represents a Web service method return value, as opposed to an output parameter.</summary>
      <returns>
        <see langword="true" />, if the member represents a Web service return value; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlReflectionMember.MemberName">
      <summary>Gets or sets the name of the Web service method member for this mapping.</summary>
      <returns>The name of the Web service method.</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlReflectionMember.MemberType">
      <summary>Gets or sets the type of the Web service method member code entity that is represented by this mapping.</summary>
      <returns>The <see cref="T:System.Type" /> of the Web service method member code entity that is represented by this mapping.</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlReflectionMember.OverrideIsNullable">
      <summary>Gets or sets a value that indicates that the value of the corresponding XML element definition's <c>isNullable</c> attribute is <see langword="false" />.</summary>
      <returns>
        <see langword="true" /> to override the <see cref="P:System.Xml.Serialization.XmlElementAttribute.IsNullable" /> property; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlReflectionMember.SoapAttributes">
      <summary>Gets or sets a <see cref="T:System.Xml.Serialization.SoapAttributes" /> with the collection of SOAP-related attributes that have been applied to the member code entity.</summary>
      <returns>A <see cref="T:System.Xml.Serialization.SoapAttributes" /> that contains the objects that represent SOAP attributes applied to the member.</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlReflectionMember.XmlAttributes">
      <summary>Gets or sets an <see cref="T:System.Xml.Serialization.XmlAttributes" /> with the collection of <see cref="T:System.Xml.Serialization.XmlSerializer" />-related attributes that have been applied to the member code entity.</summary>
      <returns>An <see cref="T:System.Xml.Serialization.XmlAttributes" /> that represents XML attributes that have been applied to the member code.</returns>
    </member>
    <member name="T:System.Xml.Serialization.XmlSchemaEnumerator">
      <summary>Enables iteration over a collection of <see cref="T:System.Xml.Schema.XmlSchema" /> objects.</summary>
    </member>
    <member name="M:System.Xml.Serialization.XmlSchemaEnumerator.#ctor(System.Xml.Serialization.XmlSchemas)">
      <summary>Initializes a new instance of the <see cref="T:System.Xml.Serialization.XmlSchemaEnumerator" /> class.</summary>
      <param name="list">The <see cref="T:System.Xml.Serialization.XmlSchemas" /> object you want to iterate over.</param>
    </member>
    <member name="P:System.Xml.Serialization.XmlSchemaEnumerator.Current">
      <summary>Gets the current element in the collection.</summary>
      <returns>The current <see cref="T:System.Xml.Schema.XmlSchema" /> object in the collection.</returns>
    </member>
    <member name="M:System.Xml.Serialization.XmlSchemaEnumerator.Dispose">
      <summary>Releases all resources used by the <see cref="T:System.Xml.Serialization.XmlSchemaEnumerator" />.</summary>
    </member>
    <member name="M:System.Xml.Serialization.XmlSchemaEnumerator.MoveNext">
      <summary>Advances the enumerator to the next item in the collection.</summary>
      <returns>
        <see langword="true" /> if the move is successful; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlSchemaEnumerator.System#Collections#IEnumerator#Current">
      <summary>Gets the current element in the collection of <see cref="T:System.Xml.Schema.XmlSchema" /> objects.</summary>
      <returns>The current element in the collection of <see cref="T:System.Xml.Schema.XmlSchema" /> objects.</returns>
    </member>
    <member name="M:System.Xml.Serialization.XmlSchemaEnumerator.System#Collections#IEnumerator#Reset">
      <summary>Sets the enumerator to its initial position, which is before the first element in the collection of <see cref="T:System.Xml.Schema.XmlSchema" /> objects.</summary>
    </member>
    <member name="T:System.Xml.Serialization.XmlSchemaExporter">
      <summary>Populates <see cref="T:System.Xml.Schema.XmlSchema" /> objects with XML schema element declarations that are found in type mapping objects.</summary>
    </member>
    <member name="M:System.Xml.Serialization.XmlSchemaExporter.#ctor(System.Xml.Serialization.XmlSchemas)">
      <summary>Initializes a new instance of the <see cref="T:System.Xml.Serialization.XmlSchemaExporter" /> class.</summary>
      <param name="schemas">A collection of <see cref="T:System.Xml.Schema.XmlSchema" /> objects to which element declarations obtained from type mappings are added.</param>
    </member>
    <member name="M:System.Xml.Serialization.XmlSchemaExporter.ExportAnyType(System.String)">
      <summary>Exports an &lt;any&gt; element to the <see cref="T:System.Xml.Schema.XmlSchema" /> object that is identified by the specified namespace.</summary>
      <param name="ns">The namespace of the XML schema document to which to add an &lt;any&gt; element.</param>
      <returns>An arbitrary name assigned to the &lt;any&gt; element declaration.</returns>
    </member>
    <member name="M:System.Xml.Serialization.XmlSchemaExporter.ExportAnyType(System.Xml.Serialization.XmlMembersMapping)">
      <summary>Adds an element declaration for an object or type to a SOAP message or to an <see cref="T:System.Xml.Schema.XmlSchema" /> object.</summary>
      <param name="members">An <see cref="T:System.Xml.Serialization.XmlMembersMapping" /> that contains mappings to export.</param>
      <returns>The string "any" with an appended integer.</returns>
    </member>
    <member name="M:System.Xml.Serialization.XmlSchemaExporter.ExportMembersMapping(System.Xml.Serialization.XmlMembersMapping)">
      <summary>Adds an element declaration to the applicable <see cref="T:System.Xml.Schema.XmlSchema" /> for each of the element parts of a literal SOAP message definition.</summary>
      <param name="xmlMembersMapping">The internal .NET Framework type mappings for the element parts of a Web Services Description Language (WSDL) message definition.</param>
    </member>
    <member name="M:System.Xml.Serialization.XmlSchemaExporter.ExportMembersMapping(System.Xml.Serialization.XmlMembersMapping,System.Boolean)">
      <summary>Adds an element declaration to the applicable <see cref="T:System.Xml.Schema.XmlSchema" /> for each of the element parts of a literal SOAP message definition, and specifies whether enclosing elements are included.</summary>
      <param name="xmlMembersMapping">The internal mapping between a .NET Framework type and an XML schema element.</param>
      <param name="exportEnclosingType">
        <see langword="true" /> if the schema elements that enclose the schema are to be included; otherwise, <see langword="false" />.</param>
    </member>
    <member name="M:System.Xml.Serialization.XmlSchemaExporter.ExportTypeMapping(System.Xml.Serialization.XmlMembersMapping)">
      <summary>Adds an element declaration to the applicable <see cref="T:System.Xml.Schema.XmlSchema" /> object for a single element part of a literal SOAP message definition.</summary>
      <param name="xmlMembersMapping">Internal .NET Framework type mappings for the element parts of a Web Services Description Language (WSDL) message definition.</param>
      <returns>An <see cref="T:System.Xml.XmlQualifiedName" /> that represents the qualified XML name of the exported element declaration.</returns>
    </member>
    <member name="M:System.Xml.Serialization.XmlSchemaExporter.ExportTypeMapping(System.Xml.Serialization.XmlTypeMapping)">
      <summary>Adds an element declaration for a .NET Framework type to the applicable <see cref="T:System.Xml.Schema.XmlSchema" /> object.</summary>
      <param name="xmlTypeMapping">The internal mapping between a .NET Framework type and an XML schema element.</param>
    </member>
    <member name="T:System.Xml.Serialization.XmlSchemaImporter">
      <summary>Generates internal mappings to .NET Framework types for XML schema element declarations, including literal XSD message parts in a WSDL document.</summary>
    </member>
    <member name="M:System.Xml.Serialization.XmlSchemaImporter.#ctor(System.Xml.Serialization.XmlSchemas)">
      <summary>Initializes a new instance of the <see cref="T:System.Xml.Serialization.XmlSchemaImporter" /> class, taking a collection of <see cref="T:System.Xml.Schema.XmlSchema" /> objects representing the XML schemas used by SOAP literal messages defined in a WSDL document.</summary>
      <param name="schemas">A collection of <see cref="T:System.Xml.Schema.XmlSchema" /> objects.</param>
    </member>
    <member name="M:System.Xml.Serialization.XmlSchemaImporter.#ctor(System.Xml.Serialization.XmlSchemas,System.Xml.Serialization.CodeIdentifiers)">
      <summary>Initializes a new instance of the <see cref="T:System.Xml.Serialization.XmlSchemaImporter" /> class, taking a collection of <see cref="T:System.Xml.Schema.XmlSchema" /> objects that represents the XML schemas used by SOAP literal messages, plus classes being generated for bindings defined in a Web Services Description Language (WSDL) document.</summary>
      <param name="schemas">An <see cref="T:System.Xml.Serialization.XmlSchemas" /> object.</param>
      <param name="typeIdentifiers">A <see cref="T:System.Xml.Serialization.CodeIdentifiers" /> object that specifies a collection of classes being generated for bindings defined in a WSDL document.</param>
    </member>
    <member name="M:System.Xml.Serialization.XmlSchemaImporter.ImportAnyType(System.Xml.XmlQualifiedName,System.String)">
      <summary>Generates internal type mapping information for a single, (SOAP) literal element part defined in a WSDL document.</summary>
      <param name="typeName">An <see cref="T:System.Xml.XmlQualifiedName" /> that specifies the name of an element's type for which a .NET Framework type is generated.</param>
      <param name="elementName">The name of the part element in the WSDL document.</param>
      <returns>An <see cref="T:System.Xml.Serialization.XmlMembersMapping" /> representing the .NET Framework type mapping for a single element part of a WSDL message definition.</returns>
    </member>
    <member name="M:System.Xml.Serialization.XmlSchemaImporter.ImportDerivedTypeMapping(System.Xml.XmlQualifiedName,System.Type)">
      <summary>Generates internal type mapping information for an element defined in an XML schema document.</summary>
      <param name="name">An <see cref="T:System.Xml.XmlQualifiedName" /> that specifies the name of an element defined in an XML schema document.</param>
      <param name="baseType">A base type for the .NET Framework type that is generated to correspond to an XSD element's type.</param>
      <returns>An <see cref="T:System.Xml.Serialization.XmlTypeMapping" /> representing the.NET Framework type mapping information for an XML schema element.</returns>
    </member>
    <member name="M:System.Xml.Serialization.XmlSchemaImporter.ImportDerivedTypeMapping(System.Xml.XmlQualifiedName,System.Type,System.Boolean)">
      <summary>Generates internal type mapping information for an element defined in an XML schema document or as a part in a WSDL document.</summary>
      <param name="name">An <see cref="T:System.Xml.XmlQualifiedName" /> that specifies the name of an element defined in an XML schema document.</param>
      <param name="baseType">A base type for the .NET Framework type that is generated to correspond to an XSD element's type.</param>
      <param name="baseTypeCanBeIndirect">
        <see langword="true" /> to indicate that the type corresponding to an XSD element can indirectly inherit from the base type; otherwise, <see langword="false" />.</param>
      <returns>The .NET Framework type mapping information for an XML schema element.</returns>
    </member>
    <member name="M:System.Xml.Serialization.XmlSchemaImporter.ImportMembersMapping(System.String,System.String,System.Xml.Serialization.SoapSchemaMember[])">
      <summary>Generates internal type mapping information for the element parts of a literal-use SOAP message defined in a WSDL document.</summary>
      <param name="name">The name of the element for which to generate a mapping.</param>
      <param name="ns">The namespace of the element for which to generate a mapping.</param>
      <param name="members">An array of <see cref="T:System.Xml.Serialization.SoapSchemaMember" /> instances that specifies the members of the element for which to generate a mapping.</param>
      <returns>A <see cref="T:System.Xml.Serialization.XmlMembersMapping" /> that contains type mapping information.</returns>
    </member>
    <member name="M:System.Xml.Serialization.XmlSchemaImporter.ImportMembersMapping(System.Xml.XmlQualifiedName)">
      <summary>Generates internal type mapping information for a single element part of a literal-use SOAP message defined in a WSDL document.</summary>
      <param name="name">An <see cref="T:System.Xml.XmlQualifiedName" /> that specifies the name of the message part.</param>
      <returns>The .NET Framework type mapping for a WSDL message definition containing a single element part.</returns>
    </member>
    <member name="M:System.Xml.Serialization.XmlSchemaImporter.ImportMembersMapping(System.Xml.XmlQualifiedName[])">
      <summary>Generates internal type mapping information for the element parts of a literal-use SOAP message defined in a WSDL document.</summary>
      <param name="names">An array of type <see cref="T:System.Xml.XmlQualifiedName" /> that specifies the names of the message parts.</param>
      <returns>An <see cref="T:System.Xml.Serialization.XmlMembersMapping" /> that represents the .NET Framework type mappings for the element parts of a WSDL message definition.</returns>
    </member>
    <member name="M:System.Xml.Serialization.XmlSchemaImporter.ImportMembersMapping(System.Xml.XmlQualifiedName[],System.Type,System.Boolean)">
      <summary>Generates internal type mapping information for the element parts of a literal-use SOAP message defined in a WSDL document.</summary>
      <param name="names">An array of type <see cref="T:System.Xml.XmlQualifiedName" /> that specifies the names of the message parts.</param>
      <param name="baseType">A base type for all .NET Framework types that are generated to correspond to message parts.</param>
      <param name="baseTypeCanBeIndirect">
        <see langword="true" /> to indicate that the types corresponding to message parts can indirectly inherit from the base type; otherwise, <see langword="false" />.</param>
      <returns>The .NET Framework type mappings for the element parts of a WSDL message definition.</returns>
    </member>
    <member name="M:System.Xml.Serialization.XmlSchemaImporter.ImportSchemaType(System.Xml.XmlQualifiedName)">
      <summary>Generates internal type mapping information for an element defined in an XML schema document.</summary>
      <param name="typeName">A <see cref="T:System.Xml.XmlQualifiedName" /> that specifies an XML element.</param>
      <returns>A <see cref="T:System.Xml.Serialization.XmlTypeMapping" /> object that describes a type mapping.</returns>
    </member>
    <member name="M:System.Xml.Serialization.XmlSchemaImporter.ImportSchemaType(System.Xml.XmlQualifiedName,System.Type)">
      <summary>Generates internal type mapping information for an element defined in an XML schema document.</summary>
      <param name="typeName">A <see cref="T:System.Xml.XmlQualifiedName" /> that specifies an XML element.</param>
      <param name="baseType">A <see cref="T:System.Type" /> object that specifies a base type.</param>
      <returns>A <see cref="T:System.Xml.Serialization.XmlTypeMapping" /> object that describes a type mapping.</returns>
    </member>
    <member name="M:System.Xml.Serialization.XmlSchemaImporter.ImportSchemaType(System.Xml.XmlQualifiedName,System.Type,System.Boolean)">
      <summary>Generates internal type mapping information for an element defined in an XML schema document.</summary>
      <param name="typeName">A <see cref="T:System.Xml.XmlQualifiedName" /> that specifies an XML element.</param>
      <param name="baseType">A <see cref="T:System.Type" /> object that specifies a base type.</param>
      <param name="baseTypeCanBeIndirect">A <see cref="T:System.Boolean" /> value that specifies whether the generated type can indirectly inherit the <paramref name="baseType" />.</param>
      <returns>A <see cref="T:System.Xml.Serialization.XmlTypeMapping" /> object that describes a type mapping.</returns>
    </member>
    <member name="M:System.Xml.Serialization.XmlSchemaImporter.ImportTypeMapping(System.Xml.XmlQualifiedName)">
      <summary>Generates internal type mapping information for an element defined in an XML schema document.</summary>
      <param name="name">An <see cref="T:System.Xml.XmlQualifiedName" /> that specifies the name of an element defined in an XML schema document.</param>
      <returns>The .NET Framework type mapping information for an XML schema element.</returns>
    </member>
    <member name="T:System.Xml.Serialization.XmlSchemas">
      <summary>Represents the collection of XML schemas.</summary>
    </member>
    <member name="M:System.Xml.Serialization.XmlSchemas.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Xml.Serialization.XmlSchemas" /> class.</summary>
    </member>
    <member name="M:System.Xml.Serialization.XmlSchemas.Add(System.Xml.Schema.XmlSchema)">
      <summary>Adds an object to the end of the collection.</summary>
      <param name="schema">The <see cref="T:System.Xml.Schema.XmlSchema" /> object to be added to the collection of objects.</param>
      <returns>The index at which the <see cref="T:System.Xml.Schema.XmlSchema" /> is added.</returns>
    </member>
    <member name="M:System.Xml.Serialization.XmlSchemas.Add(System.Xml.Schema.XmlSchema,System.Uri)">
      <summary>Adds an <see cref="T:System.Xml.Schema.XmlSchema" /> object that represents an assembly reference to the collection.</summary>
      <param name="schema">The <see cref="T:System.Xml.Schema.XmlSchema" /> to add.</param>
      <param name="baseUri">The <see cref="T:System.Uri" /> of the schema object.</param>
      <returns>The index at which the <see cref="T:System.Xml.Schema.XmlSchema" /> is added.</returns>
    </member>
    <member name="M:System.Xml.Serialization.XmlSchemas.Add(System.Xml.Serialization.XmlSchemas)">
      <summary>Adds an instance of the <see cref="T:System.Xml.Serialization.XmlSchemas" /> class to the end of the collection.</summary>
      <param name="schemas">The <see cref="T:System.Xml.Serialization.XmlSchemas" /> object to be added to the end of the collection.</param>
    </member>
    <member name="M:System.Xml.Serialization.XmlSchemas.AddReference(System.Xml.Schema.XmlSchema)">
      <summary>Adds an <see cref="T:System.Xml.Schema.XmlSchema" /> object that represents an assembly reference to the collection.</summary>
      <param name="schema">The <see cref="T:System.Xml.Schema.XmlSchema" /> to add.</param>
    </member>
    <member name="M:System.Xml.Serialization.XmlSchemas.Compile(System.Xml.Schema.ValidationEventHandler,System.Boolean)">
      <summary>Processes the element and attribute names in the XML schemas and, optionally, validates the XML schemas.</summary>
      <param name="handler">A <see cref="T:System.Xml.Schema.ValidationEventHandler" /> that specifies the callback method that handles errors and warnings during XML Schema validation, if the strict parameter is set to <see langword="true" />.</param>
      <param name="fullCompile">
        <see langword="true" /> to validate the XML schemas in the collection using the <see cref="M:System.Xml.Serialization.XmlSchemas.Compile(System.Xml.Schema.ValidationEventHandler,System.Boolean)" /> method of the <see cref="T:System.Xml.Serialization.XmlSchemas" /> class; otherwise, <see langword="false" />.</param>
    </member>
    <member name="M:System.Xml.Serialization.XmlSchemas.Contains(System.String)">
      <summary>Returns a value that indicates whether the collection contains an <see cref="T:System.Xml.Schema.XmlSchema" /> object that belongs to the specified namespace.</summary>
      <param name="targetNamespace">The namespace of the item to check for.</param>
      <returns>
        <see langword="true" /> if the item is found; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Xml.Serialization.XmlSchemas.Contains(System.Xml.Schema.XmlSchema)">
      <summary>Determines whether the <see cref="T:System.Xml.Serialization.XmlSchemas" /> contains a specific schema.</summary>
      <param name="schema">The <see cref="T:System.Xml.Schema.XmlSchema" /> object to locate.</param>
      <returns>
        <see langword="true" />, if the collection contains the specified item; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Xml.Serialization.XmlSchemas.CopyTo(System.Xml.Schema.XmlSchema[],System.Int32)">
      <summary>Copies the entire <see cref="T:System.Xml.Serialization.XmlSchemas" /> to a compatible one-dimensional <see cref="T:System.Array" />, which starts at the specified index of the target array.</summary>
      <param name="array">The one-dimensional <see cref="T:System.Array" /> that is the destination of the schemas copied from <see cref="T:System.Xml.Serialization.XmlSchemas" />. The <see cref="T:System.Array" /> must have zero-based indexing.</param>
      <param name="index">A 32-bit integer that represents the index in the array where copying begins.</param>
    </member>
    <member name="M:System.Xml.Serialization.XmlSchemas.Find(System.Xml.XmlQualifiedName,System.Type)">
      <summary>Locates in one of the XML schemas an <see cref="T:System.Xml.Schema.XmlSchemaObject" /> of the specified name and type.</summary>
      <param name="name">An <see cref="T:System.Xml.XmlQualifiedName" /> that specifies a fully qualified name with a namespace used to locate an <see cref="T:System.Xml.Schema.XmlSchema" /> object in the collection.</param>
      <param name="type">The <see cref="T:System.Type" /> of the object to find. Possible types include: <see cref="T:System.Xml.Schema.XmlSchemaGroup" />, <see cref="T:System.Xml.Schema.XmlSchemaAttributeGroup" />, <see cref="T:System.Xml.Schema.XmlSchemaElement" />, <see cref="T:System.Xml.Schema.XmlSchemaAttribute" />, and <see cref="T:System.Xml.Schema.XmlSchemaNotation" />.</param>
      <returns>An <see cref="T:System.Xml.Schema.XmlSchemaObject" /> instance, such as an <see cref="T:System.Xml.Schema.XmlSchemaElement" /> or <see cref="T:System.Xml.Schema.XmlSchemaAttribute" />.</returns>
    </member>
    <member name="M:System.Xml.Serialization.XmlSchemas.GetSchemas(System.String)">
      <summary>Gets a collection of schemas that belong to the same namespace.</summary>
      <param name="ns">The namespace of the schemas to retrieve.</param>
      <returns>An <see cref="T:System.Collections.IList" /> implementation that contains the schemas.</returns>
    </member>
    <member name="M:System.Xml.Serialization.XmlSchemas.IndexOf(System.Xml.Schema.XmlSchema)">
      <summary>Searches for the specified schema and returns the zero-based index of the first occurrence within the entire <see cref="T:System.Xml.Serialization.XmlSchemas" />.</summary>
      <param name="schema">The <see cref="T:System.Xml.Schema.XmlSchema" /> to locate.</param>
      <returns>The zero-based index of the first occurrence of the value within the entire <see cref="T:System.Xml.Serialization.XmlSchemas" />, if found; otherwise, -1.</returns>
    </member>
    <member name="M:System.Xml.Serialization.XmlSchemas.Insert(System.Int32,System.Xml.Schema.XmlSchema)">
      <summary>Inserts a schema into the <see cref="T:System.Xml.Serialization.XmlSchemas" /> at the specified index.</summary>
      <param name="index">The zero-based index at which <paramref name="schema" /> should be inserted.</param>
      <param name="schema">The <see cref="T:System.Xml.Schema.XmlSchema" /> object to be inserted.</param>
    </member>
    <member name="P:System.Xml.Serialization.XmlSchemas.IsCompiled">
      <summary>Gets a value that indicates whether the schemas have been compiled.</summary>
      <returns>
        <see langword="true" />, if the schemas have been compiled; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Xml.Serialization.XmlSchemas.IsDataSet(System.Xml.Schema.XmlSchema)">
      <summary>Static method that determines whether the specified XML schema contains a custom <see langword="IsDataSet" /> attribute set to <see langword="true" />, or its equivalent.</summary>
      <param name="schema">The XML schema to check for an <see langword="IsDataSet" /> attribute with a <see langword="true" /> value.</param>
      <returns>
        <see langword="true" /> if the specified schema exists; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlSchemas.Item(System.Int32)">
      <summary>Gets or sets the <see cref="T:System.Xml.Schema.XmlSchema" /> object at the specified index.</summary>
      <param name="index">The index of the item to retrieve.</param>
      <returns>The specified <see cref="T:System.Xml.Schema.XmlSchema" />.</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlSchemas.Item(System.String)">
      <summary>Gets a specified <see cref="T:System.Xml.Schema.XmlSchema" /> object that represents the XML schema associated with the specified namespace.</summary>
      <param name="ns">The namespace of the specified object.</param>
      <returns>The specified <see cref="T:System.Xml.Schema.XmlSchema" /> object.</returns>
    </member>
    <member name="M:System.Xml.Serialization.XmlSchemas.OnClear">
      <summary>Performs additional custom processes when clearing the contents of the <see cref="T:System.Xml.Serialization.XmlSchemas" /> instance.</summary>
    </member>
    <member name="M:System.Xml.Serialization.XmlSchemas.OnInsert(System.Int32,System.Object)">
      <summary>Performs additional custom processes before inserting a new element into the <see cref="T:System.Xml.Serialization.XmlSchemas" /> instance.</summary>
      <param name="index">The zero-based index at which to insert <paramref name="value" />.</param>
      <param name="value">The new value of the element at <paramref name="index" />.</param>
    </member>
    <member name="M:System.Xml.Serialization.XmlSchemas.OnRemove(System.Int32,System.Object)">
      <summary>Performs additional custom processes when removing an element from the <see cref="T:System.Xml.Serialization.XmlSchemas" /> instance.</summary>
      <param name="index">The zero-based index at which <paramref name="value" /> can be found.</param>
      <param name="value">The value of the element to remove at <paramref name="index" />.</param>
    </member>
    <member name="M:System.Xml.Serialization.XmlSchemas.OnSet(System.Int32,System.Object,System.Object)">
      <summary>Performs additional custom processes before setting a value in the <see cref="T:System.Xml.Serialization.XmlSchemas" /> instance.</summary>
      <param name="index">The zero-based index at which <paramref name="oldValue" /> can be found.</param>
      <param name="oldValue">The value to replace with <paramref name="newValue" />.</param>
      <param name="newValue">The new value of the element at <paramref name="index" />.</param>
    </member>
    <member name="M:System.Xml.Serialization.XmlSchemas.Remove(System.Xml.Schema.XmlSchema)">
      <summary>Removes the first occurrence of a specific schema from the <see cref="T:System.Xml.Serialization.XmlSchemas" />.</summary>
      <param name="schema">The <see cref="T:System.Xml.Schema.XmlSchema" /> to remove.</param>
    </member>
    <member name="M:System.Xml.Serialization.XmlSchemas.System#Collections#Generic#IEnumerable{System#Xml#Schema#XmlSchema}#GetEnumerator">
      <summary>Returns an enumerator that iterates through the collection of XML schemas.</summary>
      <returns>An <see cref="T:System.Collections.IEnumerator" /> object that can be used to iterate through the collection.</returns>
    </member>
    <member name="T:System.Xml.Serialization.XmlSerializationCollectionFixupCallback">
      <summary>Delegate used by the <see cref="T:System.Xml.Serialization.XmlSerializer" /> class for deserialization of SOAP-encoded XML data types that map to collections or enumerations.</summary>
      <param name="collection">The collection into which the collection items array is copied.</param>
      <param name="collectionItems">An array of items to be copied into the <c>object collection</c>.</param>
    </member>
    <member name="T:System.Xml.Serialization.XmlSerializationFixupCallback">
      <summary>Delegate used by the <see cref="T:System.Xml.Serialization.XmlSerializer" /> class for deserialization of SOAP-encoded XML data.</summary>
      <param name="fixup">An instance of the <see cref="T:System.Xml.Serialization.XmlSerializationReader.Fixup" /> class that contains the object to be fixed and the array of string identifiers for the items to fill in.</param>
    </member>
    <member name="T:System.Xml.Serialization.XmlSerializationGeneratedCode">
      <summary>An abstract class that is the base class for <see cref="T:System.Xml.Serialization.XmlSerializationReader" /> and <see cref="T:System.Xml.Serialization.XmlSerializationWriter" /> and that contains methods common to both of these types.</summary>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializationGeneratedCode.#ctor">
      <summary>Initializes an instance of the <see cref="T:System.Xml.Serialization.XmlSerializationGeneratedCode" /> class.</summary>
    </member>
    <member name="T:System.Xml.Serialization.XmlSerializationReadCallback">
      <summary>Delegate used by the <see cref="T:System.Xml.Serialization.XmlSerializer" /> class for deserialization of types from SOAP-encoded, non-root XML data.</summary>
      <returns>The object returned by the callback.</returns>
    </member>
    <member name="T:System.Xml.Serialization.XmlSerializationReader">
      <summary>Controls deserialization by the <see cref="T:System.Xml.Serialization.XmlSerializer" /> class.</summary>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializationReader.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Xml.Serialization.XmlSerializationReader" /> class.</summary>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializationReader.AddFixup(System.Xml.Serialization.XmlSerializationReader.CollectionFixup)">
      <summary>Stores an object that contains a callback method that will be called, as necessary, to fill in .NET Framework collections or enumerations that map to SOAP-encoded arrays or SOAP-encoded, multi-referenced elements.</summary>
      <param name="fixup">A <see cref="T:System.Xml.Serialization.XmlSerializationCollectionFixupCallback" /> delegate and the callback method's input data.</param>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializationReader.AddFixup(System.Xml.Serialization.XmlSerializationReader.Fixup)">
      <summary>Stores an object that contains a callback method instance that will be called, as necessary, to fill in the objects in a SOAP-encoded array.</summary>
      <param name="fixup">An <see cref="T:System.Xml.Serialization.XmlSerializationFixupCallback" /> delegate and the callback method's input data.</param>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializationReader.AddReadCallback(System.String,System.String,System.Type,System.Xml.Serialization.XmlSerializationReadCallback)">
      <summary>Stores an implementation of the <see cref="T:System.Xml.Serialization.XmlSerializationReadCallback" /> delegate and its input data for a later invocation.</summary>
      <param name="name">The name of the .NET Framework type that is being deserialized.</param>
      <param name="ns">The namespace of the .NET Framework type that is being deserialized.</param>
      <param name="type">The <see cref="T:System.Type" /> to be deserialized.</param>
      <param name="read">An <see cref="T:System.Xml.Serialization.XmlSerializationReadCallback" /> delegate.</param>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializationReader.AddTarget(System.String,System.Object)">
      <summary>Stores an object that is being deserialized from a SOAP-encoded <see langword="multiRef" /> element for later access through the <see cref="M:System.Xml.Serialization.XmlSerializationReader.GetTarget(System.String)" /> method.</summary>
      <param name="id">The value of the <see langword="id" /> attribute of a <see langword="multiRef" /> element that identifies the element.</param>
      <param name="o">The object that is deserialized from the XML element.</param>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializationReader.CheckReaderCount(System.Int32@,System.Int32@)">
      <summary>Checks whether the deserializer has advanced.</summary>
      <param name="whileIterations">The current <see langword="count" /> in a while loop.</param>
      <param name="readerCount">The current <see cref="P:System.Xml.Serialization.XmlSerializationReader.ReaderCount" />.</param>
      <exception cref="T:System.InvalidOperationException">The <see cref="P:System.Xml.Serialization.XmlSerializationReader.ReaderCount" /> has not advanced.</exception>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializationReader.CollapseWhitespace(System.String)">
      <summary>Removes all occurrences of white space characters from the beginning and end of the specified string.</summary>
      <param name="value">The string that will have its white space trimmed.</param>
      <returns>The trimmed string.</returns>
    </member>
    <member name="T:System.Xml.Serialization.XmlSerializationReader.CollectionFixup">
      <summary>Holds an <see cref="T:System.Xml.Serialization.XmlSerializationCollectionFixupCallback" /> delegate instance, plus the method's inputs; also supplies the method's parameters.</summary>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializationReader.CollectionFixup.#ctor(System.Object,System.Xml.Serialization.XmlSerializationCollectionFixupCallback,System.Object)">
      <summary>Initializes a new instance of the <see cref="T:System.Xml.Serialization.XmlSerializationReader.CollectionFixup" /> class with parameters for a callback method.</summary>
      <param name="collection">A collection into which the callback method copies the collection items array.</param>
      <param name="callback">A method that instantiates the <see cref="T:System.Xml.Serialization.XmlSerializationCollectionFixupCallback" /> delegate.</param>
      <param name="collectionItems">An array into which the callback method copies a collection.</param>
    </member>
    <member name="P:System.Xml.Serialization.XmlSerializationReader.CollectionFixup.Callback">
      <summary>Gets the callback method that instantiates the <see cref="T:System.Xml.Serialization.XmlSerializationCollectionFixupCallback" /> delegate.</summary>
      <returns>The <see cref="T:System.Xml.Serialization.XmlSerializationCollectionFixupCallback" /> delegate that points to the callback method.</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlSerializationReader.CollectionFixup.Collection">
      <summary>Gets the <paramref name="object collection" /> for the callback method.</summary>
      <returns>The collection that is used for the fixup.</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlSerializationReader.CollectionFixup.CollectionItems">
      <summary>Gets the array into which the callback method copies a collection.</summary>
      <returns>The array into which the callback method copies a collection.</returns>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializationReader.CreateAbstractTypeException(System.String,System.String)">
      <summary>Creates an <see cref="T:System.InvalidOperationException" /> that indicates that an object being deserialized should be abstract.</summary>
      <param name="name">The name of the abstract type.</param>
      <param name="ns">The .NET Framework namespace of the abstract type.</param>
      <returns>An <see cref="T:System.InvalidOperationException" /> exception.</returns>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializationReader.CreateBadDerivationException(System.String,System.String,System.String,System.String,System.String,System.String)">
      <summary>Populates an object from its XML representation at the current location of the <see cref="T:System.Xml.XmlReader" />, with an option to read the inner element.</summary>
      <param name="xsdDerived">The local name of the derived XML Schema data type.</param>
      <param name="nsDerived">The namespace of the derived XML Schema data type.</param>
      <param name="xsdBase">The local name of the base XML Schema data type.</param>
      <param name="nsBase">The namespace of the base XML Schema data type.</param>
      <param name="clrDerived">The namespace of the derived .NET Framework type.</param>
      <param name="clrBase">The name of the base .NET Framework type.</param>
      <returns>An <see cref="T:System.InvalidOperationException" /> exception.</returns>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializationReader.CreateCtorHasSecurityException(System.String)">
      <summary>Creates an <see cref="T:System.InvalidOperationException" /> that indicates that an object being deserialized cannot be instantiated because the constructor throws a security exception.</summary>
      <param name="typeName">The name of the type.</param>
      <returns>An <see cref="T:System.InvalidOperationException" /> exception.</returns>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializationReader.CreateInaccessibleConstructorException(System.String)">
      <summary>Creates an <see cref="T:System.InvalidOperationException" /> that indicates that an object being deserialized cannot be instantiated because there is no constructor available.</summary>
      <param name="typeName">The name of the type.</param>
      <returns>An <see cref="T:System.InvalidOperationException" /> exception.</returns>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializationReader.CreateInvalidCastException(System.Type,System.Object)">
      <summary>Creates an <see cref="T:System.InvalidCastException" /> that indicates that an explicit reference conversion failed.</summary>
      <param name="type">The <see cref="T:System.Type" /> that an object cannot be cast to. This type is incorporated into the exception message.</param>
      <param name="value">The object that cannot be cast. This object is incorporated into the exception message.</param>
      <returns>An <see cref="T:System.InvalidCastException" /> exception.</returns>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializationReader.CreateInvalidCastException(System.Type,System.Object,System.String)">
      <summary>Creates an <see cref="T:System.InvalidCastException" /> that indicates that an explicit reference conversion failed.</summary>
      <param name="type">The <see cref="T:System.Type" /> that an object cannot be cast to. This type is incorporated into the exception message.</param>
      <param name="value">The object that cannot be cast. This object is incorporated into the exception message.</param>
      <param name="id">A string identifier.</param>
      <returns>An <see cref="T:System.InvalidCastException" /> exception.</returns>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializationReader.CreateMissingIXmlSerializableType(System.String,System.String,System.String)">
      <summary>Creates an <see cref="T:System.InvalidOperationException" /> that indicates that a derived type that is mapped to an XML Schema data type cannot be located.</summary>
      <param name="name">The local name of the XML Schema data type that is mapped to the unavailable derived type.</param>
      <param name="ns">The namespace of the XML Schema data type that is mapped to the unavailable derived type.</param>
      <param name="clrType">The full name of the .NET Framework base type for which a derived type cannot be located.</param>
      <returns>An <see cref="T:System.InvalidOperationException" /> exception.</returns>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializationReader.CreateReadOnlyCollectionException(System.String)">
      <summary>Creates an <see cref="T:System.InvalidOperationException" /> that indicates that a SOAP-encoded collection type cannot be modified and its values cannot be filled in.</summary>
      <param name="name">The fully qualified name of the .NET Framework type for which there is a mapping.</param>
      <returns>An <see cref="T:System.InvalidOperationException" /> exception.</returns>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializationReader.CreateUnknownConstantException(System.String,System.Type)">
      <summary>Creates an <see cref="T:System.InvalidOperationException" /> that indicates that an enumeration value is not valid.</summary>
      <param name="value">The enumeration value that is not valid.</param>
      <param name="enumType">The enumeration type.</param>
      <returns>An <see cref="T:System.InvalidOperationException" /> exception.</returns>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializationReader.CreateUnknownNodeException">
      <summary>Creates an <see cref="T:System.InvalidOperationException" /> that indicates that the current position of <see cref="T:System.Xml.XmlReader" /> represents an unknown XML node.</summary>
      <returns>An <see cref="T:System.InvalidOperationException" /> exception.</returns>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializationReader.CreateUnknownTypeException(System.Xml.XmlQualifiedName)">
      <summary>Creates an <see cref="T:System.InvalidOperationException" /> that indicates that a type is unknown.</summary>
      <param name="type">An <see cref="T:System.Xml.XmlQualifiedName" /> that represents the name of the unknown type.</param>
      <returns>An <see cref="T:System.InvalidOperationException" /> exception.</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlSerializationReader.DecodeName">
      <summary>Gets or sets a value that determines whether XML strings are translated into valid .NET Framework type names.</summary>
      <returns>
        <see langword="true" /> if XML strings are decoded into valid .NET Framework type names; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlSerializationReader.Document">
      <summary>Gets the XML document object into which the XML document is being deserialized.</summary>
      <returns>An <see cref="T:System.Xml.XmlDocument" /> that represents the deserialized <see cref="T:System.Xml.XmlDocument" /> data.</returns>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializationReader.EnsureArrayIndex(System.Array,System.Int32,System.Type)">
      <summary>Ensures that a given array, or a copy, is large enough to contain a specified index.</summary>
      <param name="a">The <see cref="T:System.Array" /> that is being checked.</param>
      <param name="index">The required index.</param>
      <param name="elementType">The <see cref="T:System.Type" /> of the array's elements.</param>
      <returns>The existing <see cref="T:System.Array" />, if it is already large enough; otherwise, a new, larger array that contains the original array's elements.</returns>
    </member>
    <member name="T:System.Xml.Serialization.XmlSerializationReader.Fixup">
      <summary>Holds an <see cref="T:System.Xml.Serialization.XmlSerializationFixupCallback" /> delegate instance, plus the method's inputs; also serves as the parameter for the method.</summary>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializationReader.Fixup.#ctor(System.Object,System.Xml.Serialization.XmlSerializationFixupCallback,System.Int32)">
      <summary>Initializes a new instance of the <see cref="T:System.Xml.Serialization.XmlSerializationReader.Fixup" /> class.</summary>
      <param name="o">The object that contains other objects whose values get filled in by the callback implementation.</param>
      <param name="callback">A method that instantiates the <see cref="T:System.Xml.Serialization.XmlSerializationFixupCallback" /> delegate.</param>
      <param name="count">The size of the string array obtained through the <see cref="P:System.Xml.Serialization.XmlSerializationReader.Fixup.Ids" /> property.</param>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializationReader.Fixup.#ctor(System.Object,System.Xml.Serialization.XmlSerializationFixupCallback,System.String[])">
      <summary>Initializes a new instance of the <see cref="T:System.Xml.Serialization.XmlSerializationReader.Fixup" /> class.</summary>
      <param name="o">The object that contains other objects whose values get filled in by the callback implementation.</param>
      <param name="callback">A method that instantiates the <see cref="T:System.Xml.Serialization.XmlSerializationFixupCallback" /> delegate.</param>
      <param name="ids">The string array obtained through the <see cref="P:System.Xml.Serialization.XmlSerializationReader.Fixup.Ids" /> property.</param>
    </member>
    <member name="P:System.Xml.Serialization.XmlSerializationReader.Fixup.Callback">
      <summary>Gets the callback method that creates an instance of the <see cref="T:System.Xml.Serialization.XmlSerializationFixupCallback" /> delegate.</summary>
      <returns>The callback method that creates an instance of the <see cref="T:System.Xml.Serialization.XmlSerializationFixupCallback" /> delegate.</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlSerializationReader.Fixup.Ids">
      <summary>Gets or sets an array of keys for the objects that belong to the <see cref="P:System.Xml.Serialization.XmlSerializationReader.Fixup.Source" /> property whose values get filled in by the callback implementation.</summary>
      <returns>The array of keys.</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlSerializationReader.Fixup.Source">
      <summary>Gets or sets the object that contains other objects whose values get filled in by the callback implementation.</summary>
      <returns>The source containing objects with values to fill.</returns>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializationReader.FixupArrayRefs(System.Object)">
      <summary>Fills in the values of a SOAP-encoded array whose data type maps to a .NET Framework reference type.</summary>
      <param name="fixup">An object that contains the array whose values are filled in.</param>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializationReader.GetArrayLength(System.String,System.String)">
      <summary>Gets the length of the SOAP-encoded array where the <see cref="T:System.Xml.XmlReader" /> is currently positioned.</summary>
      <param name="name">The local name that the array should have.</param>
      <param name="ns">The namespace that the array should have.</param>
      <returns>The length of the SOAP array.</returns>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializationReader.GetNullAttr">
      <summary>Determines whether the XML element where the <see cref="T:System.Xml.XmlReader" /> is currently positioned has a null attribute set to the value <see langword="true" />.</summary>
      <returns>
        <see langword="true" /> if <see cref="T:System.Xml.XmlReader" /> is currently positioned over a null attribute with the value <see langword="true" />; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializationReader.GetTarget(System.String)">
      <summary>Gets an object that is being deserialized from a SOAP-encoded <see langword="multiRef" /> element and that was stored earlier by <see cref="M:System.Xml.Serialization.XmlSerializationReader.AddTarget(System.String,System.Object)" />.</summary>
      <param name="id">The value of the <see langword="id" /> attribute of a <see langword="multiRef" /> element that identifies the element.</param>
      <returns>An object to be deserialized from a SOAP-encoded <see langword="multiRef" /> element.</returns>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializationReader.GetXsiType">
      <summary>Gets the value of the <see langword="xsi:type" /> attribute for the XML element at the current location of the <see cref="T:System.Xml.XmlReader" />.</summary>
      <returns>An XML qualified name that indicates the data type of an XML element.</returns>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializationReader.InitCallbacks">
      <summary>Initializes callback methods that populate objects that map to SOAP-encoded XML data.</summary>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializationReader.InitIDs">
      <summary>Stores element and attribute names in a <see cref="T:System.Xml.NameTable" /> object.</summary>
    </member>
    <member name="P:System.Xml.Serialization.XmlSerializationReader.IsReturnValue">
      <summary>Gets or sets a value that should be <see langword="true" /> for a SOAP 1.1 return value.</summary>
      <returns>
        <see langword="true" />, if the value is a return value.</returns>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializationReader.IsXmlnsAttribute(System.String)">
      <summary>Determines whether an XML attribute name indicates an XML namespace.</summary>
      <param name="name">The name of an XML attribute.</param>
      <returns>
        <see langword="true" /> if the XML attribute name indicates an XML namespace; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializationReader.ParseWsdlArrayType(System.Xml.XmlAttribute)">
      <summary>Sets the value of the XML attribute if it is of type <see langword="arrayType" /> from the Web Services Description Language (WSDL) namespace.</summary>
      <param name="attr">An <see cref="T:System.Xml.XmlAttribute" /> that may have the type <see langword="wsdl:array" />.</param>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializationReader.ReadElementQualifiedName">
      <summary>Makes the <see cref="T:System.Xml.XmlReader" /> read the fully qualified name of the element where it is currently positioned.</summary>
      <returns>The fully qualified name of the current XML element.</returns>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializationReader.ReadEndElement">
      <summary>Makes the <see cref="T:System.Xml.XmlReader" /> read an XML end tag.</summary>
    </member>
    <member name="P:System.Xml.Serialization.XmlSerializationReader.Reader">
      <summary>Gets the <see cref="T:System.Xml.XmlReader" /> object that is being used by <see cref="T:System.Xml.Serialization.XmlSerializationReader" />.</summary>
      <returns>The <see cref="T:System.Xml.XmlReader" /> that is being used by the <see cref="T:System.Xml.Serialization.XmlSerializationReader" />.</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlSerializationReader.ReaderCount">
      <summary>Gets the current count of the <see cref="T:System.Xml.XmlReader" />.</summary>
      <returns>The current count of an <see cref="T:System.Xml.XmlReader" />.</returns>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializationReader.ReadNull">
      <summary>Instructs the <see cref="T:System.Xml.XmlReader" /> to read the current XML element if the element has a null attribute with the value true.</summary>
      <returns>
        <see langword="true" /> if the element has a null="true" attribute value and has been read; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializationReader.ReadNullableQualifiedName">
      <summary>Instructs the <see cref="T:System.Xml.XmlReader" /> to read the fully qualified name of the element where it is currently positioned.</summary>
      <returns>A <see cref="T:System.Xml.XmlQualifiedName" /> that represents the fully qualified name of the current XML element; otherwise, <see langword="null" /> if a null="true" attribute value is present.</returns>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializationReader.ReadNullableString">
      <summary>Instructs the <see cref="T:System.Xml.XmlReader" /> to read a simple, text-only XML element that could be <see langword="null" />.</summary>
      <returns>The string value; otherwise, <see langword="null" />.</returns>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializationReader.ReadReference(System.String@)">
      <summary>Reads the value of the <see langword="href" /> attribute (<see langword="ref" /> attribute for SOAP 1.2) that is used to refer to an XML element in SOAP encoding.</summary>
      <param name="fixupReference">An output string into which the <see langword="href" /> attribute value is read.</param>
      <returns>
        <see langword="true" /> if the value was read; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializationReader.ReadReferencedElement">
      <summary>Deserializes an object from a SOAP-encoded <see langword="multiRef" /> XML element.</summary>
      <returns>The value of the referenced element in the document.</returns>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializationReader.ReadReferencedElement(System.String,System.String)">
      <summary>Deserializes an object from a SOAP-encoded <see langword="multiRef" /> XML element.</summary>
      <param name="name">The local name of the element's XML Schema data type.</param>
      <param name="ns">The namespace of the element's XML Schema data type.</param>
      <returns>The value of the referenced element in the document.</returns>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializationReader.ReadReferencedElements">
      <summary>Deserializes objects from the SOAP-encoded <see langword="multiRef" /> elements in a SOAP message.</summary>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializationReader.ReadReferencingElement(System.String,System.String,System.Boolean,System.String@)">
      <summary>Deserializes an object from an XML element in a SOAP message that contains a reference to a <see langword="multiRef" /> element.</summary>
      <param name="name">The local name of the element's XML Schema data type.</param>
      <param name="ns">The namespace of the element's XML Schema data type.</param>
      <param name="elementCanBeType">
        <see langword="true" /> if the element name is also the XML Schema data type name; otherwise, <see langword="false" />.</param>
      <param name="fixupReference">An output string into which the value of the <see langword="href" /> attribute is read.</param>
      <returns>The deserialized object.</returns>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializationReader.ReadReferencingElement(System.String,System.String,System.String@)">
      <summary>Deserializes an object from an XML element in a SOAP message that contains a reference to a <see langword="multiRef" /> element.</summary>
      <param name="name">The local name of the element's XML Schema data type.</param>
      <param name="ns">The namespace of the element's XML Schema data type.</param>
      <param name="fixupReference">An output string into which the <see langword="href" /> attribute value is read.</param>
      <returns>The deserialized object.</returns>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializationReader.ReadReferencingElement(System.String@)">
      <summary>Deserializes an object from an XML element in a SOAP message that contains a reference to a <see langword="multiRef" /> element.</summary>
      <param name="fixupReference">An output string into which the <see langword="href" /> attribute value is read.</param>
      <returns>The deserialized object.</returns>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializationReader.ReadSerializable(System.Xml.Serialization.IXmlSerializable)">
      <summary>Populates an object from its XML representation at the current location of the <see cref="T:System.Xml.XmlReader" />.</summary>
      <param name="serializable">An <see cref="T:System.Xml.Serialization.IXmlSerializable" /> that corresponds to the current position of the <see cref="T:System.Xml.XmlReader" />.</param>
      <returns>An object that implements the <see cref="T:System.Xml.Serialization.IXmlSerializable" /> interface with its members populated from the location of the <see cref="T:System.Xml.XmlReader" />.</returns>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializationReader.ReadSerializable(System.Xml.Serialization.IXmlSerializable,System.Boolean)">
      <summary>This method supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
      <param name="serializable">An IXmlSerializable object that corresponds to the current position of the XMLReader.</param>
      <param name="wrappedAny">Specifies whether the serializable object is wrapped.</param>
      <returns>An object that implements the IXmlSerializable interface with its members populated from the location of the XmlReader.</returns>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializationReader.ReadString(System.String)">
      <summary>Produces the result of a call to the <see cref="M:System.Xml.XmlReader.ReadString" /> method appended to the input value.</summary>
      <param name="value">A string to prefix to the result of a call to the <see cref="M:System.Xml.XmlReader.ReadString" /> method.</param>
      <returns>The result of call to the <see cref="M:System.Xml.XmlReader.ReadString" /> method appended to the input value.</returns>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializationReader.ReadString(System.String,System.Boolean)">
      <summary>Returns the result of a call to the <see cref="M:System.Xml.XmlReader.ReadString" /> method of the <see cref="T:System.Xml.XmlReader" /> class, trimmed of white space if needed, and appended to the input value.</summary>
      <param name="value">A string that will be appended to.</param>
      <param name="trim">
        <see langword="true" /> if the result of the read operation should be trimmed; otherwise, <see langword="false" />.</param>
      <returns>The result of the read operation appended to the input value.</returns>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializationReader.ReadTypedNull(System.Xml.XmlQualifiedName)">
      <summary>Reads an XML element that allows null values (<see langword="xsi:nil = 'true'" />) and returns a generic <see cref="T:System.Nullable`1" /> value.</summary>
      <param name="type">The <see cref="T:System.Xml.XmlQualifiedName" /> that represents the simple data type for the current location of the <see cref="T:System.Xml.XmlReader" />.</param>
      <returns>A generic <see cref="T:System.Nullable`1" /> that represents a null XML value.</returns>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializationReader.ReadTypedPrimitive(System.Xml.XmlQualifiedName)">
      <summary>Gets the value of the XML node at which the <see cref="T:System.Xml.XmlReader" /> is currently positioned.</summary>
      <param name="type">The <see cref="T:System.Xml.XmlQualifiedName" /> that represents the simple data type for the current location of the <see cref="T:System.Xml.XmlReader" />.</param>
      <returns>The value of the node as a .NET Framework value type, if the value is a simple XML Schema data type.</returns>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializationReader.ReadXmlDocument(System.Boolean)">
      <summary>Instructs the <see cref="T:System.Xml.XmlReader" /> to read an XML document root element at its current position.</summary>
      <param name="wrapped">
        <see langword="true" /> if the method should read content only after reading the element's start element; otherwise, <see langword="false" />.</param>
      <returns>An <see cref="T:System.Xml.XmlDocument" /> that contains the root element that has been read.</returns>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializationReader.ReadXmlNode(System.Boolean)">
      <summary>Instructs the <see cref="T:System.Xml.XmlReader" /> to read the XML node at its current position.</summary>
      <param name="wrapped">
        <see langword="true" /> to read content only after reading the element's start element; otherwise, <see langword="false" />.</param>
      <returns>An <see cref="T:System.Xml.XmlNode" /> that represents the XML node that has been read.</returns>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializationReader.Referenced(System.Object)">
      <summary>Stores an object to be deserialized from a SOAP-encoded <see langword="multiRef" /> element.</summary>
      <param name="o">The object to be deserialized.</param>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializationReader.ResolveDynamicAssembly(System.String)">
      <summary>Gets a dynamically generated assembly by name.</summary>
      <param name="assemblyFullName">The full name of the assembly.</param>
      <returns>A dynamically generated <see cref="T:System.Reflection.Assembly" />.</returns>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializationReader.ShrinkArray(System.Array,System.Int32,System.Type,System.Boolean)">
      <summary>Ensures that a given array, or a copy, is no larger than a specified length.</summary>
      <param name="a">The array that is being checked.</param>
      <param name="length">The maximum length of the array.</param>
      <param name="elementType">The <see cref="T:System.Type" /> of the array's elements.</param>
      <param name="isNullable">
        <see langword="true" /> if <see langword="null" /> for the array, if present for the input array, can be returned; otherwise, a new, smaller array.</param>
      <returns>The existing <see cref="T:System.Array" />, if it is already small enough; otherwise, a new, smaller array that contains the original array's elements up to the size of <paramref name="length" />.</returns>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializationReader.ToByteArrayBase64(System.Boolean)">
      <summary>Instructs the <see cref="T:System.Xml.XmlReader" /> to read the string value at its current position and return it as a base-64 byte array.</summary>
      <param name="isNull">
        <see langword="true" /> to return <see langword="null" />; <see langword="false" /> to return a base-64 byte array.</param>
      <returns>A base-64 byte array; otherwise, <see langword="null" /> if the value of the <paramref name="isNull" /> parameter is <see langword="true" />.</returns>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializationReader.ToByteArrayBase64(System.String)">
      <summary>Produces a base-64 byte array from an input string.</summary>
      <param name="value">A string to translate into a base-64 byte array.</param>
      <returns>A base-64 byte array.</returns>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializationReader.ToByteArrayHex(System.Boolean)">
      <summary>Instructs the <see cref="T:System.Xml.XmlReader" /> to read the string value at its current position and return it as a hexadecimal byte array.</summary>
      <param name="isNull">
        <see langword="true" /> to return <see langword="null" />; <see langword="false" /> to return a hexadecimal byte array.</param>
      <returns>A hexadecimal byte array; otherwise, <see langword="null" /> if the value of the <paramref name="isNull" /> parameter is <see langword="true" />.</returns>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializationReader.ToByteArrayHex(System.String)">
      <summary>Produces a hexadecimal byte array from an input string.</summary>
      <param name="value">A string to translate into a hexadecimal byte array.</param>
      <returns>A hexadecimal byte array.</returns>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializationReader.ToChar(System.String)">
      <summary>Produces a <see cref="T:System.Char" /> object from an input string.</summary>
      <param name="value">A string to translate into a <see cref="T:System.Char" /> object.</param>
      <returns>A <see cref="T:System.Char" /> object.</returns>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializationReader.ToDate(System.String)">
      <summary>Produces a <see cref="T:System.DateTime" /> object from an input string.</summary>
      <param name="value">A string to translate into a <see cref="T:System.DateTime" /> class object.</param>
      <returns>A <see cref="T:System.DateTime" /> object.</returns>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializationReader.ToDateTime(System.String)">
      <summary>Produces a <see cref="T:System.DateTime" /> object from an input string.</summary>
      <param name="value">A string to translate into a <see cref="T:System.DateTime" /> object.</param>
      <returns>A <see cref="T:System.DateTime" /> object.</returns>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializationReader.ToEnum(System.String,System.Collections.Hashtable,System.String)">
      <summary>Produces a numeric enumeration value from a string that consists of delimited identifiers that represent constants from the enumerator list.</summary>
      <param name="value">A string that consists of delimited identifiers where each identifier represents a constant from the set enumerator list.</param>
      <param name="h">A <see cref="T:System.Collections.Hashtable" /> that consists of the identifiers as keys and the constants as integral numbers.</param>
      <param name="typeName">The name of the enumeration type.</param>
      <returns>A long value that consists of the enumeration value as a series of bitwise <see langword="OR" /> operations.</returns>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializationReader.ToTime(System.String)">
      <summary>Produces a <see cref="T:System.DateTime" /> from a string that represents the time.</summary>
      <param name="value">A string to translate into a <see cref="T:System.DateTime" /> object.</param>
      <returns>A <see cref="T:System.DateTime" /> object.</returns>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializationReader.ToXmlName(System.String)">
      <summary>Decodes an XML name.</summary>
      <param name="value">An XML name to be decoded.</param>
      <returns>A decoded string.</returns>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializationReader.ToXmlNCName(System.String)">
      <summary>Decodes an XML name.</summary>
      <param name="value">An XML name to be decoded.</param>
      <returns>A decoded string.</returns>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializationReader.ToXmlNmToken(System.String)">
      <summary>Decodes an XML name.</summary>
      <param name="value">An XML name to be decoded.</param>
      <returns>A decoded string.</returns>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializationReader.ToXmlNmTokens(System.String)">
      <summary>Decodes an XML name.</summary>
      <param name="value">An XML name to be decoded.</param>
      <returns>A decoded string.</returns>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializationReader.ToXmlQualifiedName(System.String)">
      <summary>Obtains an <see cref="T:System.Xml.XmlQualifiedName" /> from a name that may contain a prefix.</summary>
      <param name="value">A name that may contain a prefix.</param>
      <returns>An <see cref="T:System.Xml.XmlQualifiedName" /> that represents a namespace-qualified XML name.</returns>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializationReader.UnknownAttribute(System.Object,System.Xml.XmlAttribute)">
      <summary>Raises an <see cref="E:System.Xml.Serialization.XmlSerializer.UnknownAttribute" /> event for the current position of the <see cref="T:System.Xml.XmlReader" />.</summary>
      <param name="o">An object that the <see cref="T:System.Xml.Serialization.XmlSerializer" /> is attempting to deserialize, subsequently accessible through the <see cref="P:System.Xml.Serialization.XmlAttributeEventArgs.ObjectBeingDeserialized" /> property.</param>
      <param name="attr">An <see cref="T:System.Xml.XmlAttribute" /> that represents the attribute in question.</param>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializationReader.UnknownAttribute(System.Object,System.Xml.XmlAttribute,System.String)">
      <summary>Raises an <see cref="E:System.Xml.Serialization.XmlSerializer.UnknownAttribute" /> event for the current position of the <see cref="T:System.Xml.XmlReader" />.</summary>
      <param name="o">An object that the <see cref="T:System.Xml.Serialization.XmlSerializer" /> is attempting to deserialize, subsequently accessible through the <see cref="P:System.Xml.Serialization.XmlAttributeEventArgs.ObjectBeingDeserialized" /> property.</param>
      <param name="attr">A <see cref="T:System.Xml.XmlAttribute" /> that represents the attribute in question.</param>
      <param name="qnames">A comma-delimited list of XML qualified names.</param>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializationReader.UnknownElement(System.Object,System.Xml.XmlElement)">
      <summary>Raises an <see cref="E:System.Xml.Serialization.XmlSerializer.UnknownElement" /> event for the current position of the <see cref="T:System.Xml.XmlReader" />.</summary>
      <param name="o">The <see cref="T:System.Object" /> that is being deserialized.</param>
      <param name="elem">The <see cref="T:System.Xml.XmlElement" /> for which an event is raised.</param>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializationReader.UnknownElement(System.Object,System.Xml.XmlElement,System.String)">
      <summary>Raises an <see cref="E:System.Xml.Serialization.XmlSerializer.UnknownElement" /> event for the current position of the <see cref="T:System.Xml.XmlReader" />.</summary>
      <param name="o">An object that the <see cref="T:System.Xml.Serialization.XmlSerializer" /> is attempting to deserialize, subsequently accessible through the <see cref="P:System.Xml.Serialization.XmlAttributeEventArgs.ObjectBeingDeserialized" /> property.</param>
      <param name="elem">The <see cref="T:System.Xml.XmlElement" /> for which an event is raised.</param>
      <param name="qnames">A comma-delimited list of XML qualified names.</param>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializationReader.UnknownNode(System.Object)">
      <summary>Raises an <see cref="E:System.Xml.Serialization.XmlSerializer.UnknownNode" /> event for the current position of the <see cref="T:System.Xml.XmlReader" />.</summary>
      <param name="o">The object that is being deserialized.</param>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializationReader.UnknownNode(System.Object,System.String)">
      <summary>Raises an <see cref="E:System.Xml.Serialization.XmlSerializer.UnknownNode" /> event for the current position of the <see cref="T:System.Xml.XmlReader" />.</summary>
      <param name="o">The object being deserialized.</param>
      <param name="qnames">A comma-delimited list of XML qualified names.</param>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializationReader.UnreferencedObject(System.String,System.Object)">
      <summary>Raises an <see cref="E:System.Xml.Serialization.XmlSerializer.UnreferencedObject" /> event for the current position of the <see cref="T:System.Xml.XmlReader" />.</summary>
      <param name="id">A unique string that is used to identify the unreferenced object, subsequently accessible through the <see cref="P:System.Xml.Serialization.UnreferencedObjectEventArgs.UnreferencedId" /> property.</param>
      <param name="o">An object that the <see cref="T:System.Xml.Serialization.XmlSerializer" /> is attempting to deserialize, subsequently accessible through the <see cref="P:System.Xml.Serialization.UnreferencedObjectEventArgs.UnreferencedObject" /> property.</param>
    </member>
    <member name="T:System.Xml.Serialization.XmlSerializationWriteCallback">
      <summary>Delegate that is used by the <see cref="T:System.Xml.Serialization.XmlSerializer" /> class for serialization of types from SOAP-encoded, non-root XML data.</summary>
      <param name="o">The object being serialized.</param>
    </member>
    <member name="T:System.Xml.Serialization.XmlSerializationWriter">
      <summary>Represents an abstract class used for controlling serialization by the <see cref="T:System.Xml.Serialization.XmlSerializer" /> class.</summary>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializationWriter.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Xml.Serialization.XmlSerializationWriter" /> class.</summary>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializationWriter.AddWriteCallback(System.Type,System.String,System.String,System.Xml.Serialization.XmlSerializationWriteCallback)">
      <summary>Stores an implementation of the <see cref="T:System.Xml.Serialization.XmlSerializationWriteCallback" /> delegate and the type it applies to, for a later invocation.</summary>
      <param name="type">The <see cref="T:System.Type" /> of objects that are serialized.</param>
      <param name="typeName">The name of the type of objects that are serialized.</param>
      <param name="typeNs">The namespace of the type of objects that are serialized.</param>
      <param name="callback">An instance of the <see cref="T:System.Xml.Serialization.XmlSerializationWriteCallback" /> delegate.</param>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializationWriter.CreateChoiceIdentifierValueException(System.String,System.String,System.String,System.String)">
      <summary>Creates an <see cref="T:System.InvalidOperationException" /> that indicates an unexpected name for an element that adheres to an XML Schema <see langword="choice" /> element declaration.</summary>
      <param name="value">The name that is not valid.</param>
      <param name="identifier">The <see langword="choice" /> element declaration that the name belongs to.</param>
      <param name="name">The expected local name of an element.</param>
      <param name="ns">The expected namespace of an element.</param>
      <returns>The newly created exception.</returns>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializationWriter.CreateInvalidAnyTypeException(System.Object)">
      <summary>Creates an <see cref="T:System.InvalidOperationException" /> that indicates the <see cref="T:System.Xml.Serialization.XmlAnyElementAttribute" /> which has been invalidly applied to a member; only members that are of type <see cref="T:System.Xml.XmlNode" />, or derived from <see cref="T:System.Xml.XmlNode" />, are valid.</summary>
      <param name="o">The object that represents the invalid member.</param>
      <returns>The newly created exception.</returns>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializationWriter.CreateInvalidAnyTypeException(System.Type)">
      <summary>Creates an <see cref="T:System.InvalidOperationException" /> that indicates the <see cref="T:System.Xml.Serialization.XmlAnyElementAttribute" /> which has been invalidly applied to a member; only members that are of type <see cref="T:System.Xml.XmlNode" />, or derived from <see cref="T:System.Xml.XmlNode" />, are valid.</summary>
      <param name="type">The <see cref="T:System.Type" /> that is invalid.</param>
      <returns>The newly created exception.</returns>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializationWriter.CreateInvalidChoiceIdentifierValueException(System.String,System.String)">
      <summary>Creates an <see cref="T:System.InvalidOperationException" /> that indicates a failure while writing an array where an XML Schema <see langword="choice" /> element declaration is applied.</summary>
      <param name="type">The type being serialized.</param>
      <param name="identifier">A name for the <see langword="choice" /> element declaration.</param>
      <returns>The newly created exception.</returns>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializationWriter.CreateInvalidEnumValueException(System.Object,System.String)">
      <summary>Creates an <see cref="T:System.InvalidOperationException" /> for an invalid enumeration value.</summary>
      <param name="value">An object that represents the invalid enumeration.</param>
      <param name="typeName">The XML type name.</param>
      <returns>The newly created exception.</returns>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializationWriter.CreateMismatchChoiceException(System.String,System.String,System.String)">
      <summary>Creates an <see cref="T:System.InvalidOperationException" /> that indicates that a value for an XML element does not match an enumeration type.</summary>
      <param name="value">The value that is not valid.</param>
      <param name="elementName">The name of the XML element with an invalid value.</param>
      <param name="enumValue">The valid value.</param>
      <returns>The newly created exception.</returns>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializationWriter.CreateUnknownAnyElementException(System.String,System.String)">
      <summary>Creates an <see cref="T:System.InvalidOperationException" /> that indicates that an XML element that should adhere to the XML Schema <see langword="any" /> element declaration cannot be processed.</summary>
      <param name="name">The XML element that cannot be processed.</param>
      <param name="ns">The namespace of the XML element.</param>
      <returns>The newly created exception.</returns>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializationWriter.CreateUnknownTypeException(System.Object)">
      <summary>Creates an <see cref="T:System.InvalidOperationException" /> that indicates that a type being serialized is not being used in a valid manner or is unexpectedly encountered.</summary>
      <param name="o">The object whose type cannot be serialized.</param>
      <returns>The newly created exception.</returns>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializationWriter.CreateUnknownTypeException(System.Type)">
      <summary>Creates an <see cref="T:System.InvalidOperationException" /> that indicates that a type being serialized is not being used in a valid manner or is unexpectedly encountered.</summary>
      <param name="type">The type that cannot be serialized.</param>
      <returns>The newly created exception.</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlSerializationWriter.EscapeName">
      <summary>Gets or sets a value that indicates whether the <see cref="M:System.Xml.XmlConvert.EncodeName(System.String)" /> method is used to write valid XML.</summary>
      <returns>
        <see langword="true" /> if the <see cref="M:System.Xml.Serialization.XmlSerializationWriter.FromXmlQualifiedName(System.Xml.XmlQualifiedName)" /> method returns an encoded name; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializationWriter.FromByteArrayBase64(System.Byte[])">
      <summary>Processes a base-64 byte array.</summary>
      <param name="value">A base-64 <see cref="T:System.Byte" /> array.</param>
      <returns>The same byte array that was passed in as an argument.</returns>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializationWriter.FromByteArrayHex(System.Byte[])">
      <summary>Produces a string from an input hexadecimal byte array.</summary>
      <param name="value">A hexadecimal byte array to translate to a string.</param>
      <returns>The byte array value converted to a string.</returns>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializationWriter.FromChar(System.Char)">
      <summary>Produces a string from an input <see cref="T:System.Char" />.</summary>
      <param name="value">A <see cref="T:System.Char" /> to translate to a string.</param>
      <returns>The <see cref="T:System.Char" /> value converted to a string.</returns>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializationWriter.FromDate(System.DateTime)">
      <summary>Produces a string from a <see cref="T:System.DateTime" /> object.</summary>
      <param name="value">A <see cref="T:System.DateTime" /> to translate to a string.</param>
      <returns>A string representation of the <see cref="T:System.DateTime" /> that shows the date but no time.</returns>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializationWriter.FromDateTime(System.DateTime)">
      <summary>Produces a string from an input <see cref="T:System.DateTime" />.</summary>
      <param name="value">A <see cref="T:System.DateTime" /> to translate to a string.</param>
      <returns>A string representation of the <see cref="T:System.DateTime" /> that shows the date and time.</returns>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializationWriter.FromEnum(System.Int64,System.String[],System.Int64[])">
      <summary>Produces a string that consists of delimited identifiers that represent the enumeration members that have been set.</summary>
      <param name="value">The enumeration value as a series of bitwise <see langword="OR" /> operations.</param>
      <param name="values">The enumeration's name values.</param>
      <param name="ids">The enumeration's constant values.</param>
      <returns>A string that consists of delimited identifiers, where each represents a member from the set enumerator list.</returns>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializationWriter.FromEnum(System.Int64,System.String[],System.Int64[],System.String)">
      <summary>Takes a numeric enumeration value and the names and constants from the enumerator list for the enumeration and returns a string that consists of delimited identifiers that represent the enumeration members that have been set.</summary>
      <param name="value">The enumeration value as a series of bitwise <see langword="OR" /> operations.</param>
      <param name="values">The values of the enumeration.</param>
      <param name="ids">The constants of the enumeration.</param>
      <param name="typeName">The name of the type</param>
      <returns>A string that consists of delimited identifiers, where each item is one of the values set by the bitwise operation.</returns>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializationWriter.FromTime(System.DateTime)">
      <summary>Produces a string from a <see cref="T:System.DateTime" /> object.</summary>
      <param name="value">A <see cref="T:System.DateTime" /> that is translated to a string.</param>
      <returns>A string representation of the <see cref="T:System.DateTime" /> object that shows the time but no date.</returns>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializationWriter.FromXmlName(System.String)">
      <summary>Encodes a valid XML name by replacing characters that are not valid with escape sequences.</summary>
      <param name="name">A string to be used as an XML name.</param>
      <returns>An encoded string.</returns>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializationWriter.FromXmlNCName(System.String)">
      <summary>Encodes a valid XML local name by replacing characters that are not valid with escape sequences.</summary>
      <param name="ncName">A string to be used as a local (unqualified) XML name.</param>
      <returns>An encoded string.</returns>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializationWriter.FromXmlNmToken(System.String)">
      <summary>Encodes an XML name.</summary>
      <param name="nmToken">An XML name to be encoded.</param>
      <returns>An encoded string.</returns>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializationWriter.FromXmlNmTokens(System.String)">
      <summary>Encodes a space-delimited sequence of XML names into a single XML name.</summary>
      <param name="nmTokens">A space-delimited sequence of XML names to be encoded.</param>
      <returns>An encoded string.</returns>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializationWriter.FromXmlQualifiedName(System.Xml.XmlQualifiedName)">
      <summary>Returns an XML qualified name, with invalid characters replaced by escape sequences.</summary>
      <param name="xmlQualifiedName">An <see cref="T:System.Xml.XmlQualifiedName" /> that represents the XML to be written.</param>
      <returns>An XML qualified name, with invalid characters replaced by escape sequences.</returns>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializationWriter.FromXmlQualifiedName(System.Xml.XmlQualifiedName,System.Boolean)">
      <summary>Produces a string that can be written as an XML qualified name, with invalid characters replaced by escape sequences.</summary>
      <param name="xmlQualifiedName">An <see cref="T:System.Xml.XmlQualifiedName" /> that represents the XML to be written.</param>
      <param name="ignoreEmpty">
        <see langword="true" /> to ignore empty spaces in the string; otherwise, <see langword="false" />.</param>
      <returns>An XML qualified name, with invalid characters replaced by escape sequences.</returns>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializationWriter.InitCallbacks">
      <summary>Initializes an instances of the <see cref="T:System.Xml.Serialization.XmlSerializationWriteCallback" /> delegate to serialize SOAP-encoded XML data.</summary>
    </member>
    <member name="P:System.Xml.Serialization.XmlSerializationWriter.Namespaces">
      <summary>Gets or sets a list of XML qualified name objects that contain the namespaces and prefixes used to produce qualified names in XML documents.</summary>
      <returns>An <see cref="T:System.Collections.ArrayList" /> that contains the namespaces and prefix pairs.</returns>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializationWriter.ResolveDynamicAssembly(System.String)">
      <summary>Gets a dynamically generated assembly by name.</summary>
      <param name="assemblyFullName">The full name of the assembly.</param>
      <returns>A dynamically generated assembly.</returns>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializationWriter.TopLevelElement">
      <summary>Initializes object references only while serializing a SOAP-encoded SOAP message.</summary>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializationWriter.WriteAttribute(System.String,System.Byte[])">
      <summary>Instructs an <see cref="T:System.Xml.XmlWriter" /> object to write an XML attribute that has no namespace specified for its name.</summary>
      <param name="localName">The local name of the XML attribute.</param>
      <param name="value">The value of the XML attribute as a byte array.</param>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializationWriter.WriteAttribute(System.String,System.String)">
      <summary>Instructs the <see cref="T:System.Xml.XmlWriter" /> to write an XML attribute that has no namespace specified for its name.</summary>
      <param name="localName">The local name of the XML attribute.</param>
      <param name="value">The value of the XML attribute as a string.</param>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializationWriter.WriteAttribute(System.String,System.String,System.Byte[])">
      <summary>Instructs an <see cref="T:System.Xml.XmlWriter" /> object to write an XML attribute.</summary>
      <param name="localName">The local name of the XML attribute.</param>
      <param name="ns">The namespace of the XML attribute.</param>
      <param name="value">The value of the XML attribute as a byte array.</param>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializationWriter.WriteAttribute(System.String,System.String,System.String)">
      <summary>Writes an XML attribute.</summary>
      <param name="localName">The local name of the XML attribute.</param>
      <param name="ns">The namespace of the XML attribute.</param>
      <param name="value">The value of the XML attribute as a string.</param>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializationWriter.WriteAttribute(System.String,System.String,System.String,System.String)">
      <summary>Writes an XML attribute where the namespace prefix is provided manually.</summary>
      <param name="prefix">The namespace prefix to write.</param>
      <param name="localName">The local name of the XML attribute.</param>
      <param name="ns">The namespace represented by the prefix.</param>
      <param name="value">The value of the XML attribute as a string.</param>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializationWriter.WriteElementEncoded(System.Xml.XmlNode,System.String,System.String,System.Boolean,System.Boolean)">
      <summary>Writes an XML node object within the body of a named XML element.</summary>
      <param name="node">The XML node to write, possibly a child XML element.</param>
      <param name="name">The local name of the parent XML element to write.</param>
      <param name="ns">The namespace of the parent XML element to write.</param>
      <param name="isNullable">
        <see langword="true" /> to write an <see langword="xsi:nil='true'" /> attribute if the object to serialize is <see langword="null" />; otherwise, <see langword="false" />.</param>
      <param name="any">
        <see langword="true" /> to indicate that the node, if an XML element, adheres to an XML Schema <see langword="any" /> element declaration; otherwise, <see langword="false" />.</param>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializationWriter.WriteElementLiteral(System.Xml.XmlNode,System.String,System.String,System.Boolean,System.Boolean)">
      <summary>Instructs an <see cref="T:System.Xml.XmlWriter" /> object to write an <see cref="T:System.Xml.XmlNode" /> object within the body of a named XML element.</summary>
      <param name="node">The XML node to write, possibly a child XML element.</param>
      <param name="name">The local name of the parent XML element to write.</param>
      <param name="ns">The namespace of the parent XML element to write.</param>
      <param name="isNullable">
        <see langword="true" /> to write an <see langword="xsi:nil='true'" /> attribute if the object to serialize is <see langword="null" />; otherwise, <see langword="false" />.</param>
      <param name="any">
        <see langword="true" /> to indicate that the node, if an XML element, adheres to an XML Schema <see langword="any" /> element declaration; otherwise, <see langword="false" />.</param>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializationWriter.WriteElementQualifiedName(System.String,System.String,System.Xml.XmlQualifiedName)">
      <summary>Writes an XML element with a specified qualified name in its body.</summary>
      <param name="localName">The local name of the XML element.</param>
      <param name="ns">The namespace of the XML element.</param>
      <param name="value">The name to write, using its prefix if namespace-qualified, in the element text.</param>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializationWriter.WriteElementQualifiedName(System.String,System.String,System.Xml.XmlQualifiedName,System.Xml.XmlQualifiedName)">
      <summary>Writes an XML element with a specified qualified name in its body.</summary>
      <param name="localName">The local name of the XML element.</param>
      <param name="ns">The namespace of the XML element.</param>
      <param name="value">The name to write, using its prefix if namespace-qualified, in the element text.</param>
      <param name="xsiType">The name of the XML Schema data type to be written to the <see langword="xsi:type" /> attribute.</param>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializationWriter.WriteElementQualifiedName(System.String,System.Xml.XmlQualifiedName)">
      <summary>Writes an XML element with a specified qualified name in its body.</summary>
      <param name="localName">The local name of the XML element.</param>
      <param name="value">The name to write, using its prefix if namespace-qualified, in the element text.</param>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializationWriter.WriteElementQualifiedName(System.String,System.Xml.XmlQualifiedName,System.Xml.XmlQualifiedName)">
      <summary>Writes an XML element with a specified qualified name in its body.</summary>
      <param name="localName">The local name of the XML element.</param>
      <param name="value">The name to write, using its prefix if namespace-qualified, in the element text.</param>
      <param name="xsiType">The name of the XML Schema data type to be written to the <see langword="xsi:type" /> attribute.</param>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializationWriter.WriteElementString(System.String,System.String)">
      <summary>Writes an XML element with a specified value in its body.</summary>
      <param name="localName">The local name of the XML element to be written without namespace qualification.</param>
      <param name="value">The text value of the XML element.</param>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializationWriter.WriteElementString(System.String,System.String,System.String)">
      <summary>Writes an XML element with a specified value in its body.</summary>
      <param name="localName">The local name of the XML element.</param>
      <param name="ns">The namespace of the XML element.</param>
      <param name="value">The text value of the XML element.</param>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializationWriter.WriteElementString(System.String,System.String,System.String,System.Xml.XmlQualifiedName)">
      <summary>Writes an XML element with a specified value in its body.</summary>
      <param name="localName">The local name of the XML element.</param>
      <param name="ns">The namespace of the XML element.</param>
      <param name="value">The text value of the XML element.</param>
      <param name="xsiType">The name of the XML Schema data type to be written to the <see langword="xsi:type" /> attribute.</param>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializationWriter.WriteElementString(System.String,System.String,System.Xml.XmlQualifiedName)">
      <summary>Writes an XML element with a specified value in its body.</summary>
      <param name="localName">The local name of the XML element.</param>
      <param name="value">The text value of the XML element.</param>
      <param name="xsiType">The name of the XML Schema data type to be written to the <see langword="xsi:type" /> attribute.</param>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializationWriter.WriteElementStringRaw(System.String,System.Byte[])">
      <summary>Writes an XML element with a specified value in its body.</summary>
      <param name="localName">The local name of the XML element.</param>
      <param name="value">The text value of the XML element.</param>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializationWriter.WriteElementStringRaw(System.String,System.Byte[],System.Xml.XmlQualifiedName)">
      <summary>Writes an XML element with a specified value in its body.</summary>
      <param name="localName">The local name of the XML element.</param>
      <param name="value">The text value of the XML element.</param>
      <param name="xsiType">The name of the XML Schema data type to be written to the <see langword="xsi:type" /> attribute.</param>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializationWriter.WriteElementStringRaw(System.String,System.String)">
      <summary>Writes an XML element with a specified value in its body.</summary>
      <param name="localName">The local name of the XML element.</param>
      <param name="value">The text value of the XML element.</param>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializationWriter.WriteElementStringRaw(System.String,System.String,System.Byte[])">
      <summary>Writes an XML element with a specified value in its body.</summary>
      <param name="localName">The local name of the XML element.</param>
      <param name="ns">The namespace of the XML element.</param>
      <param name="value">The text value of the XML element.</param>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializationWriter.WriteElementStringRaw(System.String,System.String,System.Byte[],System.Xml.XmlQualifiedName)">
      <summary>Writes an XML element with a specified value in its body.</summary>
      <param name="localName">The local name of the XML element.</param>
      <param name="ns">The namespace of the XML element.</param>
      <param name="value">The text value of the XML element.</param>
      <param name="xsiType">The name of the XML Schema data type to be written to the <see langword="xsi:type" /> attribute.</param>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializationWriter.WriteElementStringRaw(System.String,System.String,System.String)">
      <summary>Writes an XML element with a specified value in its body.</summary>
      <param name="localName">The local name of the XML element.</param>
      <param name="ns">The namespace of the XML element.</param>
      <param name="value">The text value of the XML element.</param>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializationWriter.WriteElementStringRaw(System.String,System.String,System.String,System.Xml.XmlQualifiedName)">
      <summary>Writes an XML element with a specified value in its body.</summary>
      <param name="localName">The local name of the XML element.</param>
      <param name="ns">The namespace of the XML element.</param>
      <param name="value">The text value of the XML element.</param>
      <param name="xsiType">The name of the XML Schema data type to be written to the <see langword="xsi:type" /> attribute.</param>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializationWriter.WriteElementStringRaw(System.String,System.String,System.Xml.XmlQualifiedName)">
      <summary>Writes an XML element with a specified value in its body.</summary>
      <param name="localName">The local name of the XML element.</param>
      <param name="value">The text value of the XML element.</param>
      <param name="xsiType">The name of the XML Schema data type to be written to the <see langword="xsi:type" /> attribute.</param>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializationWriter.WriteEmptyTag(System.String)">
      <summary>Writes an XML element whose body is empty.</summary>
      <param name="name">The local name of the XML element to write.</param>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializationWriter.WriteEmptyTag(System.String,System.String)">
      <summary>Writes an XML element whose body is empty.</summary>
      <param name="name">The local name of the XML element to write.</param>
      <param name="ns">The namespace of the XML element to write.</param>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializationWriter.WriteEndElement">
      <summary>Writes a <see langword="&lt;closing&gt;" /> element tag.</summary>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializationWriter.WriteEndElement(System.Object)">
      <summary>Writes a <see langword="&lt;closing&gt;" /> element tag.</summary>
      <param name="o">The object being serialized.</param>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializationWriter.WriteId(System.Object)">
      <summary>Writes an <see langword="id" /> attribute that appears in a SOAP-encoded <see langword="multiRef" /> element.</summary>
      <param name="o">The object being serialized.</param>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializationWriter.WriteNamespaceDeclarations(System.Xml.Serialization.XmlSerializerNamespaces)">
      <summary>Writes the namespace declaration attributes.</summary>
      <param name="xmlns">The XML namespaces to declare.</param>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializationWriter.WriteNullableQualifiedNameEncoded(System.String,System.String,System.Xml.XmlQualifiedName,System.Xml.XmlQualifiedName)">
      <summary>Writes an XML element whose body contains a valid XML qualified name. <see cref="T:System.Xml.XmlWriter" /> inserts an <see langword="xsi:nil='true'" /> attribute if the string's value is <see langword="null" />.</summary>
      <param name="name">The local name of the XML element to write.</param>
      <param name="ns">The namespace of the XML element to write.</param>
      <param name="value">The XML qualified name to write in the body of the XML element.</param>
      <param name="xsiType">The name of the XML Schema data type to be written to the <see langword="xsi:type" /> attribute.</param>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializationWriter.WriteNullableQualifiedNameLiteral(System.String,System.String,System.Xml.XmlQualifiedName)">
      <summary>Writes an XML element whose body contains a valid XML qualified name. <see cref="T:System.Xml.XmlWriter" /> inserts an <see langword="xsi:nil='true'" /> attribute if the string's value is <see langword="null" />.</summary>
      <param name="name">The local name of the XML element to write.</param>
      <param name="ns">The namespace of the XML element to write.</param>
      <param name="value">The XML qualified name to write in the body of the XML element.</param>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializationWriter.WriteNullableStringEncoded(System.String,System.String,System.String,System.Xml.XmlQualifiedName)">
      <summary>Writes an XML element that contains a string as the body. <see cref="T:System.Xml.XmlWriter" /> inserts an <see langword="xsi:nil='true'" /> attribute if the string's value is <see langword="null" />.</summary>
      <param name="name">The local name of the XML element to write.</param>
      <param name="ns">The namespace of the XML element to write.</param>
      <param name="value">The string to write in the body of the XML element.</param>
      <param name="xsiType">The name of the XML Schema data type to be written to the <see langword="xsi:type" /> attribute.</param>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializationWriter.WriteNullableStringEncodedRaw(System.String,System.String,System.Byte[],System.Xml.XmlQualifiedName)">
      <summary>Writes a byte array as the body of an XML element. <see cref="T:System.Xml.XmlWriter" /> inserts an <see langword="xsi:nil='true'" /> attribute if the string's value is <see langword="null" />.</summary>
      <param name="name">The local name of the XML element to write.</param>
      <param name="ns">The namespace of the XML element to write.</param>
      <param name="value">The byte array to write in the body of the XML element.</param>
      <param name="xsiType">The name of the XML Schema data type to be written to the <see langword="xsi:type" /> attribute.</param>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializationWriter.WriteNullableStringEncodedRaw(System.String,System.String,System.String,System.Xml.XmlQualifiedName)">
      <summary>Writes an XML element that contains a string as the body. <see cref="T:System.Xml.XmlWriter" /> inserts an <see langword="xsi:nil='true'" /> attribute if the string's value is <see langword="null" />.</summary>
      <param name="name">The local name of the XML element to write.</param>
      <param name="ns">The namespace of the XML element to write.</param>
      <param name="value">The string to write in the body of the XML element.</param>
      <param name="xsiType">The name of the XML Schema data type to be written to the <see langword="xsi:type" /> attribute.</param>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializationWriter.WriteNullableStringLiteral(System.String,System.String,System.String)">
      <summary>Writes an XML element that contains a string as the body. <see cref="T:System.Xml.XmlWriter" /> inserts an <see langword="xsi:nil='true'" /> attribute if the string's value is <see langword="null" />.</summary>
      <param name="name">The local name of the XML element to write.</param>
      <param name="ns">The namespace of the XML element to write.</param>
      <param name="value">The string to write in the body of the XML element.</param>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializationWriter.WriteNullableStringLiteralRaw(System.String,System.String,System.Byte[])">
      <summary>Writes a byte array as the body of an XML element. <see cref="T:System.Xml.XmlWriter" /> inserts an <see langword="xsi:nil='true'" /> attribute if the string's value is <see langword="null" />.</summary>
      <param name="name">The local name of the XML element to write.</param>
      <param name="ns">The namespace of the XML element to write.</param>
      <param name="value">The byte array to write in the body of the XML element.</param>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializationWriter.WriteNullableStringLiteralRaw(System.String,System.String,System.String)">
      <summary>Writes an XML element that contains a string as the body. <see cref="T:System.Xml.XmlWriter" /> inserts a <see langword="xsi:nil='true'" /> attribute if the string's value is <see langword="null" />.</summary>
      <param name="name">The local name of the XML element to write.</param>
      <param name="ns">The namespace of the XML element to write.</param>
      <param name="value">The string to write in the body of the XML element.</param>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializationWriter.WriteNullTagEncoded(System.String)">
      <summary>Writes an XML element with an <see langword="xsi:nil='true'" /> attribute.</summary>
      <param name="name">The local name of the XML element to write.</param>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializationWriter.WriteNullTagEncoded(System.String,System.String)">
      <summary>Writes an XML element with an <see langword="xsi:nil='true'" /> attribute.</summary>
      <param name="name">The local name of the XML element to write.</param>
      <param name="ns">The namespace of the XML element to write.</param>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializationWriter.WriteNullTagLiteral(System.String)">
      <summary>Writes an XML element with an <see langword="xsi:nil='true'" /> attribute.</summary>
      <param name="name">The local name of the XML element to write.</param>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializationWriter.WriteNullTagLiteral(System.String,System.String)">
      <summary>Writes an XML element with an <see langword="xsi:nil='true'" /> attribute.</summary>
      <param name="name">The local name of the XML element to write.</param>
      <param name="ns">The namespace of the XML element to write.</param>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializationWriter.WritePotentiallyReferencingElement(System.String,System.String,System.Object)">
      <summary>Writes a SOAP message XML element that can contain a reference to a <see langword="&lt;multiRef&gt;" /> XML element for a given object.</summary>
      <param name="n">The local name of the XML element to write.</param>
      <param name="ns">The namespace of the XML element to write.</param>
      <param name="o">The object being serialized either in the current XML element or a <see langword="multiRef" /> element that is referenced by the current element.</param>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializationWriter.WritePotentiallyReferencingElement(System.String,System.String,System.Object,System.Type)">
      <summary>Writes a SOAP message XML element that can contain a reference to a <see langword="&lt;multiRef&gt;" /> XML element for a given object.</summary>
      <param name="n">The local name of the XML element to write.</param>
      <param name="ns">The namespace of the XML element to write.</param>
      <param name="o">The object being serialized either in the current XML element or a <see langword="multiRef" /> element that referenced by the current element.</param>
      <param name="ambientType">The type stored in the object's type mapping (as opposed to the object's type found directly through the <see langword="typeof" /> operation).</param>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializationWriter.WritePotentiallyReferencingElement(System.String,System.String,System.Object,System.Type,System.Boolean)">
      <summary>Writes a SOAP message XML element that can contain a reference to a <see langword="&lt;multiRef&gt;" /> XML element for a given object.</summary>
      <param name="n">The local name of the XML element to write.</param>
      <param name="ns">The namespace of the XML element to write.</param>
      <param name="o">The object being serialized either in the current XML element or a <see langword="multiRef" /> element that is referenced by the current element.</param>
      <param name="ambientType">The type stored in the object's type mapping (as opposed to the object's type found directly through the <see langword="typeof" /> operation).</param>
      <param name="suppressReference">
        <see langword="true" /> to serialize the object directly into the XML element rather than make the element reference another element that contains the data; otherwise, <see langword="false" />.</param>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializationWriter.WritePotentiallyReferencingElement(System.String,System.String,System.Object,System.Type,System.Boolean,System.Boolean)">
      <summary>Writes a SOAP message XML element that can contain a reference to a <see langword="multiRef" /> XML element for a given object.</summary>
      <param name="n">The local name of the XML element to write.</param>
      <param name="ns">The namespace of the XML element to write.</param>
      <param name="o">The object being serialized either in the current XML element or a <see langword="multiRef" /> element that referenced by the current element.</param>
      <param name="ambientType">The type stored in the object's type mapping (as opposed to the object's type found directly through the <see langword="typeof" /> operation).</param>
      <param name="suppressReference">
        <see langword="true" /> to serialize the object directly into the XML element rather than make the element reference another element that contains the data; otherwise, <see langword="false" />.</param>
      <param name="isNullable">
        <see langword="true" /> to write an <see langword="xsi:nil='true'" /> attribute if the object to serialize is <see langword="null" />; otherwise, <see langword="false" />.</param>
    </member>
    <member name="P:System.Xml.Serialization.XmlSerializationWriter.Writer">
      <summary>Gets or sets the <see cref="T:System.Xml.XmlWriter" /> that is being used by the <see cref="T:System.Xml.Serialization.XmlSerializationWriter" />.</summary>
      <returns>The <see cref="T:System.Xml.XmlWriter" /> used by the class instance.</returns>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializationWriter.WriteReferencedElements">
      <summary>Serializes objects into SOAP-encoded <see langword="multiRef" /> XML elements in a SOAP message.</summary>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializationWriter.WriteReferencingElement(System.String,System.String,System.Object)">
      <summary>Writes a SOAP message XML element that contains a reference to a <see langword="multiRef" /> element for a given object.</summary>
      <param name="n">The local name of the referencing element being written.</param>
      <param name="ns">The namespace of the referencing element being written.</param>
      <param name="o">The object being serialized.</param>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializationWriter.WriteReferencingElement(System.String,System.String,System.Object,System.Boolean)">
      <summary>Writes a SOAP message XML element that contains a reference to a <see langword="multiRef" /> element for a given object.</summary>
      <param name="n">The local name of the referencing element being written.</param>
      <param name="ns">The namespace of the referencing element being written.</param>
      <param name="o">The object being serialized.</param>
      <param name="isNullable">
        <see langword="true" /> to write an <see langword="xsi:nil='true'" /> attribute if the object to serialize is <see langword="null" />; otherwise, <see langword="false" />.</param>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializationWriter.WriteRpcResult(System.String,System.String)">
      <summary>Writes a SOAP 1.2 RPC result element with a specified qualified name in its body.</summary>
      <param name="name">The local name of the result body.</param>
      <param name="ns">The namespace of the result body.</param>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializationWriter.WriteSerializable(System.Xml.Serialization.IXmlSerializable,System.String,System.String,System.Boolean)">
      <summary>Writes an object that uses custom XML formatting as an XML element.</summary>
      <param name="serializable">An object that implements the <see cref="T:System.Xml.Serialization.IXmlSerializable" /> interface that uses custom XML formatting.</param>
      <param name="name">The local name of the XML element to write.</param>
      <param name="ns">The namespace of the XML element to write.</param>
      <param name="isNullable">
        <see langword="true" /> to write an <see langword="xsi:nil='true'" /> attribute if the <see cref="T:System.Xml.Serialization.IXmlSerializable" /> class object is <see langword="null" />; otherwise, <see langword="false" />.</param>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializationWriter.WriteSerializable(System.Xml.Serialization.IXmlSerializable,System.String,System.String,System.Boolean,System.Boolean)">
      <summary>Instructs <see cref="T:System.Xml.XmlNode" /> to write an object that uses custom XML formatting as an XML element.</summary>
      <param name="serializable">An object that implements the <see cref="T:System.Xml.Serialization.IXmlSerializable" /> interface that uses custom XML formatting.</param>
      <param name="name">The local name of the XML element to write.</param>
      <param name="ns">The namespace of the XML element to write.</param>
      <param name="isNullable">
        <see langword="true" /> to write an <see langword="xsi:nil='true'" /> attribute if the <see cref="T:System.Xml.Serialization.IXmlSerializable" /> object is <see langword="null" />; otherwise, <see langword="false" />.</param>
      <param name="wrapped">
        <see langword="true" /> to ignore writing the opening element tag; otherwise, <see langword="false" /> to write the opening element tag.</param>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializationWriter.WriteStartDocument">
      <summary>Writes the XML declaration if the writer is positioned at the start of an XML document.</summary>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializationWriter.WriteStartElement(System.String)">
      <summary>Writes an opening element tag, including any attributes.</summary>
      <param name="name">The local name of the XML element to write.</param>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializationWriter.WriteStartElement(System.String,System.String)">
      <summary>Writes an opening element tag, including any attributes.</summary>
      <param name="name">The local name of the XML element to write.</param>
      <param name="ns">The namespace of the XML element to write.</param>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializationWriter.WriteStartElement(System.String,System.String,System.Boolean)">
      <summary>Writes an opening element tag, including any attributes.</summary>
      <param name="name">The local name of the XML element to write.</param>
      <param name="ns">The namespace of the XML element to write.</param>
      <param name="writePrefixed">
        <see langword="true" /> to write the element name with a prefix if none is available for the specified namespace; otherwise, <see langword="false" />.</param>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializationWriter.WriteStartElement(System.String,System.String,System.Object)">
      <summary>Writes an opening element tag, including any attributes.</summary>
      <param name="name">The local name of the XML element to write.</param>
      <param name="ns">The namespace of the XML element to write.</param>
      <param name="o">The object being serialized as an XML element.</param>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializationWriter.WriteStartElement(System.String,System.String,System.Object,System.Boolean)">
      <summary>Writes an opening element tag, including any attributes.</summary>
      <param name="name">The local name of the XML element to write.</param>
      <param name="ns">The namespace of the XML element to write.</param>
      <param name="o">The object being serialized as an XML element.</param>
      <param name="writePrefixed">
        <see langword="true" /> to write the element name with a prefix if none is available for the specified namespace; otherwise, <see langword="false" />.</param>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializationWriter.WriteStartElement(System.String,System.String,System.Object,System.Boolean,System.Xml.Serialization.XmlSerializerNamespaces)">
      <summary>Writes an opening element tag, including any attributes.</summary>
      <param name="name">The local name of the XML element to write.</param>
      <param name="ns">The namespace of the XML element to write.</param>
      <param name="o">The object being serialized as an XML element.</param>
      <param name="writePrefixed">
        <see langword="true" /> to write the element name with a prefix if none is available for the specified namespace; otherwise, <see langword="false" />.</param>
      <param name="xmlns">An instance of the <see cref="T:System.Xml.Serialization.XmlSerializerNamespaces" /> class that contains prefix and namespace pairs to be used in the generated XML.</param>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializationWriter.WriteTypedPrimitive(System.String,System.String,System.Object,System.Boolean)">
      <summary>Writes an XML element whose text body is a value of a simple XML Schema data type.</summary>
      <param name="name">The local name of the element to write.</param>
      <param name="ns">The namespace of the element to write.</param>
      <param name="o">The object to be serialized in the element body.</param>
      <param name="xsiType">
        <see langword="true" /> if the XML element explicitly specifies the text value's type using the <see langword="xsi:type" /> attribute; otherwise, <see langword="false" />.</param>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializationWriter.WriteValue(System.Byte[])">
      <summary>Writes a base-64 byte array.</summary>
      <param name="value">The byte array to write.</param>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializationWriter.WriteValue(System.String)">
      <summary>Writes a specified string value.</summary>
      <param name="value">The value of the string to write.</param>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializationWriter.WriteXmlAttribute(System.Xml.XmlNode)">
      <summary>Writes the specified <see cref="T:System.Xml.XmlNode" /> as an XML attribute.</summary>
      <param name="node">The XML node to write.</param>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializationWriter.WriteXmlAttribute(System.Xml.XmlNode,System.Object)">
      <summary>Writes the specified <see cref="T:System.Xml.XmlNode" /> object as an XML attribute.</summary>
      <param name="node">The XML node to write.</param>
      <param name="container">An <see cref="T:System.Xml.Schema.XmlSchemaObject" /> object (or <see langword="null" />) used to generate a qualified name value for an <see langword="arrayType" /> attribute from the Web Services Description Language (WSDL) namespace ("http://schemas.xmlsoap.org/wsdl/").</param>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializationWriter.WriteXsiType(System.String,System.String)">
      <summary>Writes an <see langword="xsi:type" /> attribute for an XML element that is being serialized into a document.</summary>
      <param name="name">The local name of an XML Schema data type.</param>
      <param name="ns">The namespace of an XML Schema data type.</param>
    </member>
    <member name="T:System.Xml.Serialization.XmlSerializer">
      <summary>Serializes and deserializes objects into and from XML documents. The <see cref="T:System.Xml.Serialization.XmlSerializer" /> enables you to control how objects are encoded into XML.</summary>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializer.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Xml.Serialization.XmlSerializer" /> class.</summary>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializer.#ctor(System.Type)">
      <summary>Initializes a new instance of the <see cref="T:System.Xml.Serialization.XmlSerializer" /> class that can serialize objects of the specified type into XML documents, and deserialize XML documents into objects of the specified type.</summary>
      <param name="type">The type of the object that this <see cref="T:System.Xml.Serialization.XmlSerializer" /> can serialize.</param>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializer.#ctor(System.Type,System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Xml.Serialization.XmlSerializer" /> class that can serialize objects of the specified type into XML documents, and deserialize XML documents into objects of the specified type. Specifies the default namespace for all the XML elements.</summary>
      <param name="type">The type of the object that this <see cref="T:System.Xml.Serialization.XmlSerializer" /> can serialize.</param>
      <param name="defaultNamespace">The default namespace to use for all the XML elements.</param>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializer.#ctor(System.Type,System.Type[])">
      <summary>Initializes a new instance of the <see cref="T:System.Xml.Serialization.XmlSerializer" /> class that can serialize objects of the specified type into XML documents, and deserialize XML documents into object of a specified type. If a property or field returns an array, the <paramref name="extraTypes" /> parameter specifies objects that can be inserted into the array.</summary>
      <param name="type">The type of the object that this <see cref="T:System.Xml.Serialization.XmlSerializer" /> can serialize.</param>
      <param name="extraTypes">A <see cref="T:System.Type" /> array of additional object types to serialize.</param>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializer.#ctor(System.Type,System.Xml.Serialization.XmlAttributeOverrides)">
      <summary>Initializes a new instance of the <see cref="T:System.Xml.Serialization.XmlSerializer" /> class that can serialize objects of the specified type into XML documents, and deserialize XML documents into objects of the specified type. Each object to be serialized can itself contain instances of classes, which this overload can override with other classes.</summary>
      <param name="type">The type of the object to serialize.</param>
      <param name="overrides">An <see cref="T:System.Xml.Serialization.XmlAttributeOverrides" />.</param>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializer.#ctor(System.Type,System.Xml.Serialization.XmlAttributeOverrides,System.Type[],System.Xml.Serialization.XmlRootAttribute,System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Xml.Serialization.XmlSerializer" /> class that can serialize objects of type <see cref="T:System.Object" /> into XML document instances, and deserialize XML document instances into objects of type <see cref="T:System.Object" />. Each object to be serialized can itself contain instances of classes, which this overload overrides with other classes. This overload also specifies the default namespace for all the XML elements and the class to use as the XML root element.</summary>
      <param name="type">The type of the object that this <see cref="T:System.Xml.Serialization.XmlSerializer" /> can serialize.</param>
      <param name="overrides">An <see cref="T:System.Xml.Serialization.XmlAttributeOverrides" /> that extends or overrides the behavior of the class specified in the <paramref name="type" /> parameter.</param>
      <param name="extraTypes">A <see cref="T:System.Type" /> array of additional object types to serialize.</param>
      <param name="root">An <see cref="T:System.Xml.Serialization.XmlRootAttribute" /> that defines the XML root element properties.</param>
      <param name="defaultNamespace">The default namespace of all XML elements in the XML document.</param>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializer.#ctor(System.Type,System.Xml.Serialization.XmlAttributeOverrides,System.Type[],System.Xml.Serialization.XmlRootAttribute,System.String,System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Xml.Serialization.XmlSerializer" /> class that can serialize objects of type <see cref="T:System.Object" /> into XML document instances, and deserialize XML document instances into objects of type <see cref="T:System.Object" />. Each object to be serialized can itself contain instances of classes, which this overload overrides with other classes. This overload also specifies the default namespace for all the XML elements and the class to use as the XML root element.</summary>
      <param name="type">The type of the object that this <see cref="T:System.Xml.Serialization.XmlSerializer" /> can serialize.</param>
      <param name="overrides">An <see cref="T:System.Xml.Serialization.XmlAttributeOverrides" /> that extends or overrides the behavior of the class specified in the <paramref name="type" /> parameter.</param>
      <param name="extraTypes">A <see cref="T:System.Type" /> array of additional object types to serialize.</param>
      <param name="root">An <see cref="T:System.Xml.Serialization.XmlRootAttribute" /> that defines the XML root element properties.</param>
      <param name="defaultNamespace">The default namespace of all XML elements in the XML document.</param>
      <param name="location">The location of the types.</param>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializer.#ctor(System.Type,System.Xml.Serialization.XmlRootAttribute)">
      <summary>Initializes a new instance of the <see cref="T:System.Xml.Serialization.XmlSerializer" /> class that can serialize objects of the specified type into XML documents, and deserialize an XML document into object of the specified type. It also specifies the class to use as the XML root element.</summary>
      <param name="type">The type of the object that this <see cref="T:System.Xml.Serialization.XmlSerializer" /> can serialize.</param>
      <param name="root">An <see cref="T:System.Xml.Serialization.XmlRootAttribute" /> that represents the XML root element.</param>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializer.#ctor(System.Xml.Serialization.XmlTypeMapping)">
      <summary>Initializes an instance of the <see cref="T:System.Xml.Serialization.XmlSerializer" /> class using an object that maps one type to another.</summary>
      <param name="xmlTypeMapping">An <see cref="T:System.Xml.Serialization.XmlTypeMapping" /> that maps one type to another.</param>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializer.CanDeserialize(System.Xml.XmlReader)">
      <summary>Gets a value that indicates whether this <see cref="T:System.Xml.Serialization.XmlSerializer" /> can deserialize a specified XML document.</summary>
      <param name="xmlReader">An <see cref="T:System.Xml.XmlReader" /> that points to the document to deserialize.</param>
      <returns>
        <see langword="true" /> if this <see cref="T:System.Xml.Serialization.XmlSerializer" /> can deserialize the object that the <see cref="T:System.Xml.XmlReader" /> points to; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializer.CreateReader">
      <summary>Returns an object used to read the XML document to be serialized.</summary>
      <returns>An <see cref="T:System.Xml.Serialization.XmlSerializationReader" /> used to read the XML document.</returns>
      <exception cref="T:System.NotImplementedException">Any attempt is made to access the method when the method is not overridden in a descendant class.</exception>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializer.CreateWriter">
      <summary>When overridden in a derived class, returns a writer used to serialize the object.</summary>
      <returns>An instance that implements the <see cref="T:System.Xml.Serialization.XmlSerializationWriter" /> class.</returns>
      <exception cref="T:System.NotImplementedException">Any attempt is made to access the method when the method is not overridden in a descendant class.</exception>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializer.Deserialize(System.IO.Stream)">
      <summary>Deserializes the XML document contained by the specified <see cref="T:System.IO.Stream" />.</summary>
      <param name="stream">The <see cref="T:System.IO.Stream" /> that contains the XML document to deserialize.</param>
      <returns>The <see cref="T:System.Object" /> being deserialized.</returns>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializer.Deserialize(System.IO.TextReader)">
      <summary>Deserializes the XML document contained by the specified <see cref="T:System.IO.TextReader" />.</summary>
      <param name="textReader">The <see cref="T:System.IO.TextReader" /> that contains the XML document to deserialize.</param>
      <returns>The <see cref="T:System.Object" /> being deserialized.</returns>
      <exception cref="T:System.InvalidOperationException">An error occurred during deserialization. The original exception is available using the <see cref="P:System.Exception.InnerException" /> property.</exception>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializer.Deserialize(System.Xml.Serialization.XmlSerializationReader)">
      <summary>Deserializes the XML document contained by the specified <see cref="T:System.Xml.Serialization.XmlSerializationReader" />.</summary>
      <param name="reader">The <see cref="T:System.Xml.Serialization.XmlSerializationReader" /> that contains the XML document to deserialize.</param>
      <returns>The deserialized object.</returns>
      <exception cref="T:System.NotImplementedException">Any attempt is made to access the method when the method is not overridden in a descendant class.</exception>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializer.Deserialize(System.Xml.XmlReader)">
      <summary>Deserializes the XML document contained by the specified <see cref="T:System.Xml.XmlReader" />.</summary>
      <param name="xmlReader">The <see cref="T:System.Xml.XmlReader" /> that contains the XML document to deserialize.</param>
      <returns>The <see cref="T:System.Object" /> being deserialized.</returns>
      <exception cref="T:System.InvalidOperationException">An error occurred during deserialization. The original exception is available using the <see cref="P:System.Exception.InnerException" /> property.</exception>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializer.Deserialize(System.Xml.XmlReader,System.String)">
      <summary>Deserializes the XML document contained by the specified <see cref="T:System.Xml.XmlReader" /> and encoding style.</summary>
      <param name="xmlReader">The <see cref="T:System.Xml.XmlReader" /> that contains the XML document to deserialize.</param>
      <param name="encodingStyle">The encoding style of the serialized XML.</param>
      <returns>The deserialized object.</returns>
      <exception cref="T:System.InvalidOperationException">An error occurred during deserialization. The original exception is available using the <see cref="P:System.Exception.InnerException" /> property.</exception>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializer.Deserialize(System.Xml.XmlReader,System.String,System.Xml.Serialization.XmlDeserializationEvents)">
      <summary>Deserializes the object using the data contained by the specified <see cref="T:System.Xml.XmlReader" />.</summary>
      <param name="xmlReader">An instance of the <see cref="T:System.Xml.XmlReader" /> class used to read the document.</param>
      <param name="encodingStyle">The encoding used.</param>
      <param name="events">An instance of the <see cref="T:System.Xml.Serialization.XmlDeserializationEvents" /> class.</param>
      <returns>The object being deserialized.</returns>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializer.Deserialize(System.Xml.XmlReader,System.Xml.Serialization.XmlDeserializationEvents)">
      <summary>Deserializes an XML document contained by the specified <see cref="T:System.Xml.XmlReader" /> and allows the overriding of events that occur during deserialization.</summary>
      <param name="xmlReader">The <see cref="T:System.Xml.XmlReader" /> that contains the document to deserialize.</param>
      <param name="events">An instance of the <see cref="T:System.Xml.Serialization.XmlDeserializationEvents" /> class.</param>
      <returns>The <see cref="T:System.Object" /> being deserialized.</returns>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializer.FromMappings(System.Xml.Serialization.XmlMapping[])">
      <summary>Returns an array of <see cref="T:System.Xml.Serialization.XmlSerializer" /> objects created from an array of <see cref="T:System.Xml.Serialization.XmlTypeMapping" /> objects.</summary>
      <param name="mappings">An array of <see cref="T:System.Xml.Serialization.XmlTypeMapping" /> that maps one type to another.</param>
      <returns>An array of <see cref="T:System.Xml.Serialization.XmlSerializer" /> objects.</returns>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializer.FromMappings(System.Xml.Serialization.XmlMapping[],System.Type)">
      <summary>Returns an instance of the <see cref="T:System.Xml.Serialization.XmlSerializer" /> class from the specified mappings.</summary>
      <param name="mappings">An array of <see cref="T:System.Xml.Serialization.XmlMapping" /> objects.</param>
      <param name="type">The <see cref="T:System.Type" /> of the deserialized object.</param>
      <returns>An instance of the <see cref="T:System.Xml.Serialization.XmlSerializer" /> class.</returns>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializer.FromTypes(System.Type[])">
      <summary>Returns an array of <see cref="T:System.Xml.Serialization.XmlSerializer" /> objects created from an array of types.</summary>
      <param name="types">An array of <see cref="T:System.Type" /> objects.</param>
      <returns>An array of <see cref="T:System.Xml.Serialization.XmlSerializer" /> objects.</returns>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializer.GetXmlSerializerAssemblyName(System.Type)">
      <summary>Returns the name of the assembly that contains one or more versions of the <see cref="T:System.Xml.Serialization.XmlSerializer" /> especially created to serialize or deserialize the specified type.</summary>
      <param name="type">The <see cref="T:System.Type" /> you are deserializing.</param>
      <returns>The name of the assembly that contains an <see cref="T:System.Xml.Serialization.XmlSerializer" /> for the type.</returns>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializer.GetXmlSerializerAssemblyName(System.Type,System.String)">
      <summary>Returns the name of the assembly that contains the serializer for the specified type in the specified namespace.</summary>
      <param name="type">The <see cref="T:System.Type" /> you are interested in.</param>
      <param name="defaultNamespace">The namespace of the type.</param>
      <returns>The name of the assembly that contains specially built serializers.</returns>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializer.Serialize(System.IO.Stream,System.Object)">
      <summary>Serializes the specified <see cref="T:System.Object" /> and writes the XML document to a file using the specified <see cref="T:System.IO.Stream" />.</summary>
      <param name="stream">The <see cref="T:System.IO.Stream" /> used to write the XML document.</param>
      <param name="o">The <see cref="T:System.Object" /> to serialize.</param>
      <exception cref="T:System.InvalidOperationException">An error occurred during serialization. The original exception is available using the <see cref="P:System.Exception.InnerException" /> property.</exception>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializer.Serialize(System.IO.Stream,System.Object,System.Xml.Serialization.XmlSerializerNamespaces)">
      <summary>Serializes the specified <see cref="T:System.Object" /> and writes the XML document to a file using the specified <see cref="T:System.IO.Stream" /> that references the specified namespaces.</summary>
      <param name="stream">The <see cref="T:System.IO.Stream" /> used to write the XML document.</param>
      <param name="o">The <see cref="T:System.Object" /> to serialize.</param>
      <param name="namespaces">The <see cref="T:System.Xml.Serialization.XmlSerializerNamespaces" /> referenced by the object.</param>
      <exception cref="T:System.InvalidOperationException">An error occurred during serialization. The original exception is available using the <see cref="P:System.Exception.InnerException" /> property.</exception>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializer.Serialize(System.IO.TextWriter,System.Object)">
      <summary>Serializes the specified <see cref="T:System.Object" /> and writes the XML document to a file using the specified <see cref="T:System.IO.TextWriter" />.</summary>
      <param name="textWriter">The <see cref="T:System.IO.TextWriter" /> used to write the XML document.</param>
      <param name="o">The <see cref="T:System.Object" /> to serialize.</param>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializer.Serialize(System.IO.TextWriter,System.Object,System.Xml.Serialization.XmlSerializerNamespaces)">
      <summary>Serializes the specified <see cref="T:System.Object" /> and writes the XML document to a file using the specified <see cref="T:System.IO.TextWriter" /> and references the specified namespaces.</summary>
      <param name="textWriter">The <see cref="T:System.IO.TextWriter" /> used to write the XML document.</param>
      <param name="o">The <see cref="T:System.Object" /> to serialize.</param>
      <param name="namespaces">The <see cref="T:System.Xml.Serialization.XmlSerializerNamespaces" /> that contains namespaces for the generated XML document.</param>
      <exception cref="T:System.InvalidOperationException">An error occurred during serialization. The original exception is available using the <see cref="P:System.Exception.InnerException" /> property.</exception>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializer.Serialize(System.Object,System.Xml.Serialization.XmlSerializationWriter)">
      <summary>Serializes the specified <see cref="T:System.Object" /> and writes the XML document to a file using the specified <see cref="T:System.Xml.Serialization.XmlSerializationWriter" />.</summary>
      <param name="o">The <see cref="T:System.Object" /> to serialize.</param>
      <param name="writer">The <see cref="T:System.Xml.Serialization.XmlSerializationWriter" /> used to write the XML document.</param>
      <exception cref="T:System.NotImplementedException">Any attempt is made to access the method when the method is not overridden in a descendant class.</exception>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializer.Serialize(System.Xml.XmlWriter,System.Object)">
      <summary>Serializes the specified <see cref="T:System.Object" /> and writes the XML document to a file using the specified <see cref="T:System.Xml.XmlWriter" />.</summary>
      <param name="xmlWriter">The <see cref="T:System.Xml.XmlWriter" /> used to write the XML document.</param>
      <param name="o">The <see cref="T:System.Object" /> to serialize.</param>
      <exception cref="T:System.InvalidOperationException">An error occurred during serialization. The original exception is available using the <see cref="P:System.Exception.InnerException" /> property.</exception>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializer.Serialize(System.Xml.XmlWriter,System.Object,System.Xml.Serialization.XmlSerializerNamespaces)">
      <summary>Serializes the specified <see cref="T:System.Object" /> and writes the XML document to a file using the specified <see cref="T:System.Xml.XmlWriter" /> and references the specified namespaces.</summary>
      <param name="xmlWriter">The <see cref="T:System.Xml.XmlWriter" /> used to write the XML document.</param>
      <param name="o">The <see cref="T:System.Object" /> to serialize.</param>
      <param name="namespaces">The <see cref="T:System.Xml.Serialization.XmlSerializerNamespaces" /> referenced by the object.</param>
      <exception cref="T:System.InvalidOperationException">An error occurred during serialization. The original exception is available using the <see cref="P:System.Exception.InnerException" /> property.</exception>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializer.Serialize(System.Xml.XmlWriter,System.Object,System.Xml.Serialization.XmlSerializerNamespaces,System.String)">
      <summary>Serializes the specified object and writes the XML document to a file using the specified <see cref="T:System.Xml.XmlWriter" /> and references the specified namespaces and encoding style.</summary>
      <param name="xmlWriter">The <see cref="T:System.Xml.XmlWriter" /> used to write the XML document.</param>
      <param name="o">The object to serialize.</param>
      <param name="namespaces">The <see cref="T:System.Xml.Serialization.XmlSerializerNamespaces" /> referenced by the object.</param>
      <param name="encodingStyle">The encoding style of the serialized XML.</param>
      <exception cref="T:System.InvalidOperationException">An error occurred during serialization. The original exception is available using the <see cref="P:System.Exception.InnerException" /> property.</exception>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializer.Serialize(System.Xml.XmlWriter,System.Object,System.Xml.Serialization.XmlSerializerNamespaces,System.String,System.String)">
      <summary>Serializes the specified <see cref="T:System.Object" /> and writes the XML document to a file using the specified <see cref="T:System.Xml.XmlWriter" />, XML namespaces, and encoding.</summary>
      <param name="xmlWriter">The <see cref="T:System.Xml.XmlWriter" /> used to write the XML document.</param>
      <param name="o">The object to serialize.</param>
      <param name="namespaces">An instance of the <see langword="XmlSerializerNamespaces" /> that contains namespaces and prefixes to use.</param>
      <param name="encodingStyle">The encoding used in the document.</param>
      <param name="id">For SOAP encoded messages, the base used to generate id attributes.</param>
    </member>
    <member name="E:System.Xml.Serialization.XmlSerializer.UnknownAttribute">
      <summary>Occurs when the <see cref="T:System.Xml.Serialization.XmlSerializer" /> encounters an XML attribute of unknown type during deserialization.</summary>
    </member>
    <member name="E:System.Xml.Serialization.XmlSerializer.UnknownElement">
      <summary>Occurs when the <see cref="T:System.Xml.Serialization.XmlSerializer" /> encounters an XML element of unknown type during deserialization.</summary>
    </member>
    <member name="E:System.Xml.Serialization.XmlSerializer.UnknownNode">
      <summary>Occurs when the <see cref="T:System.Xml.Serialization.XmlSerializer" /> encounters an XML node of unknown type during deserialization.</summary>
    </member>
    <member name="E:System.Xml.Serialization.XmlSerializer.UnreferencedObject">
      <summary>Occurs during deserialization of a SOAP-encoded XML stream, when the <see cref="T:System.Xml.Serialization.XmlSerializer" /> encounters a recognized type that is not used or is unreferenced.</summary>
    </member>
    <member name="T:System.Xml.Serialization.XmlSerializerAssemblyAttribute">
      <summary>Applied to a Web service client proxy, enables you to specify an assembly that contains custom-made serializers.</summary>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializerAssemblyAttribute.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Xml.Serialization.XmlSerializerAssemblyAttribute" /> class.</summary>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializerAssemblyAttribute.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Xml.Serialization.XmlSerializerAssemblyAttribute" /> class with the specified assembly name.</summary>
      <param name="assemblyName">The simple, unencrypted name of the assembly.</param>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializerAssemblyAttribute.#ctor(System.String,System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Xml.Serialization.XmlSerializerAssemblyAttribute" /> class with the specified assembly name and location of the assembly.</summary>
      <param name="assemblyName">The simple, unencrypted name of the assembly.</param>
      <param name="codeBase">A string that is the URL location of the assembly.</param>
    </member>
    <member name="P:System.Xml.Serialization.XmlSerializerAssemblyAttribute.AssemblyName">
      <summary>Gets or sets the name of the assembly that contains serializers for a specific set of types.</summary>
      <returns>The simple, unencrypted name of the assembly.</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlSerializerAssemblyAttribute.CodeBase">
      <summary>Gets or sets the location of the assembly that contains the serializers.</summary>
      <returns>A location, such as a path or URI, that points to the assembly.</returns>
    </member>
    <member name="T:System.Xml.Serialization.XmlSerializerFactory">
      <summary>Creates typed versions of the <see cref="T:System.Xml.Serialization.XmlSerializer" /> for more efficient serialization.</summary>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializerFactory.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Xml.Serialization.XmlSerializerFactory" /> class.</summary>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializerFactory.CreateSerializer(System.Type)">
      <summary>Returns a derivation of the <see cref="T:System.Xml.Serialization.XmlSerializer" /> class that is used to serialize the specified type.</summary>
      <param name="type">The <see cref="T:System.Type" /> to serialize.</param>
      <returns>A derivation of the <see cref="T:System.Xml.Serialization.XmlSerializer" /> class that is specifically created to serialize the specified type.</returns>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializerFactory.CreateSerializer(System.Type,System.String)">
      <summary>Returns a derivation of the <see cref="T:System.Xml.Serialization.XmlSerializer" /> class that is used to serialize the specified type and namespace.</summary>
      <param name="type">The <see cref="T:System.Type" /> to serialize.</param>
      <param name="defaultNamespace">The default namespace to use for all the XML elements.</param>
      <returns>A derivation of the <see cref="T:System.Xml.Serialization.XmlSerializer" /> class that is specifically created to serialize the specified type.</returns>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializerFactory.CreateSerializer(System.Type,System.Type[])">
      <summary>Returns a derivation of the <see cref="T:System.Xml.Serialization.XmlSerializer" /> class that is used to serialize the specified type. If a property or field returns an array, the <paramref name="extraTypes" /> parameter specifies objects that can be inserted into the array.</summary>
      <param name="type">The <see cref="T:System.Type" /> to serialize.</param>
      <param name="extraTypes">A <see cref="T:System.Type" /> array of additional object types to serialize.</param>
      <returns>A derivation of the <see cref="T:System.Xml.Serialization.XmlSerializer" />.</returns>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializerFactory.CreateSerializer(System.Type,System.Xml.Serialization.XmlAttributeOverrides)">
      <summary>Returns a derivation of the <see cref="T:System.Xml.Serialization.XmlSerializer" /> class that can serialize objects of the specified type into XML documents, and vice versa. Each object to be serialized can itself contain instances of classes, which this overload can override with other classes.</summary>
      <param name="type">The <see cref="T:System.Type" /> to serialize.</param>
      <param name="overrides">An <see cref="T:System.Xml.Serialization.XmlAttributeOverrides" /> that contains fields that override the default serialization behavior.</param>
      <returns>A derivation of the <see cref="T:System.Xml.Serialization.XmlSerializer" />.</returns>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializerFactory.CreateSerializer(System.Type,System.Xml.Serialization.XmlAttributeOverrides,System.Type[],System.Xml.Serialization.XmlRootAttribute,System.String)">
      <summary>Returns a derivation of the <see cref="T:System.Xml.Serialization.XmlSerializer" /> class that can serialize objects of the specified type into XML document instances, and vice versa. Each object to be serialized can itself contain instances of classes, which this overload can override with other classes. This overload also specifies the default namespace for all the XML elements, and the class to use as the XML root element.</summary>
      <param name="type">The <see cref="T:System.Type" /> to serialize.</param>
      <param name="overrides">An <see cref="T:System.Xml.Serialization.XmlAttributeOverrides" /> that contains fields that override the default serialization behavior.</param>
      <param name="extraTypes">A <see cref="T:System.Type" /> array of additional object types to serialize.</param>
      <param name="root">An <see cref="T:System.Xml.Serialization.XmlRootAttribute" /> that represents the XML root element.</param>
      <param name="defaultNamespace">The default namespace of all XML elements in the XML document.</param>
      <returns>A derivation of the <see cref="T:System.Xml.Serialization.XmlSerializer" />.</returns>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializerFactory.CreateSerializer(System.Type,System.Xml.Serialization.XmlAttributeOverrides,System.Type[],System.Xml.Serialization.XmlRootAttribute,System.String,System.String)">
      <summary>Returns a derivation of the <see cref="T:System.Xml.Serialization.XmlSerializer" /> class that can serialize objects of the specified type into XML document instances, and vice versa. Each object to be serialized can itself contain instances of classes, which this overload can override with other classes. This overload also specifies the default namespace for all the XML elements, and the class to use as the XML root element.</summary>
      <param name="type">The <see cref="T:System.Type" /> of the object that this <see cref="T:System.Xml.Serialization.XmlSerializer" /> can serialize.</param>
      <param name="overrides">An <see cref="T:System.Xml.Serialization.XmlAttributeOverrides" /> that extends or overrides the behavior of the class specified in the type parameter.</param>
      <param name="extraTypes">A <see cref="T:System.Type" /> array of additional object types to serialize.</param>
      <param name="root">An <see cref="T:System.Xml.Serialization.XmlRootAttribute" /> that defines the XML root element properties.</param>
      <param name="defaultNamespace">The default namespace of all XML elements in the XML document.</param>
      <param name="location">The path that specifies the location of the types.</param>
      <returns>A derivation of the <see cref="T:System.Xml.Serialization.XmlSerializer" />.</returns>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializerFactory.CreateSerializer(System.Type,System.Xml.Serialization.XmlRootAttribute)">
      <summary>Returns a derivation of the <see cref="T:System.Xml.Serialization.XmlSerializer" /> class that can serialize objects of the specified type into XML documents, and vice versa. Specifies the object that represents the XML root element.</summary>
      <param name="type">The <see cref="T:System.Type" /> to serialize.</param>
      <param name="root">An <see cref="T:System.Xml.Serialization.XmlRootAttribute" /> that represents the XML root element.</param>
      <returns>A derivation of the <see cref="T:System.Xml.Serialization.XmlSerializer" />.</returns>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializerFactory.CreateSerializer(System.Xml.Serialization.XmlTypeMapping)">
      <summary>Returns a derivation of the <see cref="T:System.Xml.Serialization.XmlSerializer" /> class using an object that maps one type to another.</summary>
      <param name="xmlTypeMapping">An <see cref="T:System.Xml.Serialization.XmlTypeMapping" /> that maps one type to another.</param>
      <returns>A derivation of the <see cref="T:System.Xml.Serialization.XmlSerializer" /> class that is specifically created to serialize the mapped type.</returns>
    </member>
    <member name="T:System.Xml.Serialization.XmlSerializerImplementation">
      <summary>Defines the reader, writer, and methods for pre-generated, typed serializers.</summary>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializerImplementation.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Xml.Serialization.XmlSerializerImplementation" /> class.</summary>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializerImplementation.CanSerialize(System.Type)">
      <summary>Gets a value that determines whether a type can be serialized.</summary>
      <param name="type">The type to be serialized.</param>
      <returns>
        <see langword="true" /> if the type can be serialized; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializerImplementation.GetSerializer(System.Type)">
      <summary>Returns a serializer for the specified type.</summary>
      <param name="type">The type to be serialized.</param>
      <returns>An instance of a type derived from the <see cref="T:System.Xml.Serialization.XmlSerializer" /> class.</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlSerializerImplementation.Reader">
      <summary>Gets the XML reader object that is used by the serializer.</summary>
      <returns>An <see cref="T:System.Xml.Serialization.XmlSerializationReader" /> that is used to read an XML document or data stream.</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlSerializerImplementation.ReadMethods">
      <summary>Gets the collection of methods that is used to read a data stream.</summary>
      <returns>A <see cref="T:System.Collections.Hashtable" /> that contains the methods.</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlSerializerImplementation.TypedSerializers">
      <summary>Gets the collection of typed serializers that is found in the assembly.</summary>
      <returns>A <see cref="T:System.Collections.Hashtable" /> that contains the typed serializers.</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlSerializerImplementation.WriteMethods">
      <summary>Gets the collection of methods that is used to write to a data stream.</summary>
      <returns>A <see cref="T:System.Collections.Hashtable" /> that contains the methods.</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlSerializerImplementation.Writer">
      <summary>Gets the XML writer object for the serializer.</summary>
      <returns>An <see cref="T:System.Xml.Serialization.XmlSerializationWriter" /> that is used to write to an XML data stream or document.</returns>
    </member>
    <member name="T:System.Xml.Serialization.XmlSerializerVersionAttribute">
      <summary>Signifies that the code was generated by the serialization infrastructure and can be reused for increased performance, when this attribute is applied to an assembly.</summary>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializerVersionAttribute.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Xml.Serialization.XmlSerializerVersionAttribute" /> class.</summary>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializerVersionAttribute.#ctor(System.Type)">
      <summary>Initializes a new instance of the <see cref="T:System.Xml.Serialization.XmlSerializerVersionAttribute" /> class for the specified type.</summary>
      <param name="type">The type that is being serialized.</param>
    </member>
    <member name="P:System.Xml.Serialization.XmlSerializerVersionAttribute.Namespace">
      <summary>Gets or sets the common language runtime (CLR) namespace of the assembly.</summary>
      <returns>A <see cref="T:System.String" /> containing the common language runtime (CLR) namespace of the assembly.</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlSerializerVersionAttribute.ParentAssemblyId">
      <summary>Gets or sets the identity of the parent assembly.</summary>
      <returns>The version of the parent assembly.</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlSerializerVersionAttribute.Type">
      <summary>Gets or sets the type that the serializer operates on.</summary>
      <returns>The <see cref="T:System.Type" /> to be serialized.</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlSerializerVersionAttribute.Version">
      <summary>Gets or sets the assembly's version number.</summary>
      <returns>The version of the assembly.</returns>
    </member>
    <member name="T:System.Xml.Serialization.XmlTypeAttribute">
      <summary>Controls the XML schema that is generated when the attribute target is serialized by the <see cref="T:System.Xml.Serialization.XmlSerializer" />.</summary>
    </member>
    <member name="M:System.Xml.Serialization.XmlTypeAttribute.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Xml.Serialization.XmlTypeAttribute" /> class.</summary>
    </member>
    <member name="M:System.Xml.Serialization.XmlTypeAttribute.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Xml.Serialization.XmlTypeAttribute" /> class and specifies the name of the XML type.</summary>
      <param name="typeName">The name of the XML type that the <see cref="T:System.Xml.Serialization.XmlSerializer" /> generates when it serializes the class instance (and recognizes when it deserializes the class instance).</param>
    </member>
    <member name="P:System.Xml.Serialization.XmlTypeAttribute.AnonymousType">
      <summary>Gets or sets a value that determines whether the resulting schema type is an XSD anonymous type.</summary>
      <returns>
        <see langword="true" />, if the resulting schema type is an XSD anonymous type; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlTypeAttribute.IncludeInSchema">
      <summary>Gets or sets a value that indicates whether to include the type in XML schema documents.</summary>
      <returns>
        <see langword="true" /> to include the type in XML schema documents; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlTypeAttribute.Namespace">
      <summary>Gets or sets the namespace of the XML type.</summary>
      <returns>The namespace of the XML type.</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlTypeAttribute.TypeName">
      <summary>Gets or sets the name of the XML type.</summary>
      <returns>The name of the XML type.</returns>
    </member>
    <member name="T:System.Xml.Serialization.XmlTypeMapping">
      <summary>Contains a mapping of one type to another.</summary>
    </member>
    <member name="P:System.Xml.Serialization.XmlTypeMapping.TypeFullName">
      <summary>The fully qualified type name that includes the namespace (or namespaces) and type.</summary>
      <returns>The fully qualified type name.</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlTypeMapping.TypeName">
      <summary>Gets the type name of the mapped object.</summary>
      <returns>The type name of the mapped object.</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlTypeMapping.XsdTypeName">
      <summary>Gets the XML element name of the mapped object.</summary>
      <returns>The XML element name of the mapped object. The default is the class name of the object.</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlTypeMapping.XsdTypeNamespace">
      <summary>Gets the XML namespace of the mapped object.</summary>
      <returns>The XML namespace of the mapped object. The default is an empty string ("").</returns>
    </member>
  </members>
</doc>