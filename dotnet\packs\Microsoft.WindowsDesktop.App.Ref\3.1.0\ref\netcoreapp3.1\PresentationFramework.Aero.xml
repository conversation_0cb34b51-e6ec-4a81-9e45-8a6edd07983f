﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>PresentationFramework.Aero</name>
  </assembly>
  <members>
    <member name="T:Microsoft.Windows.Themes.BulletChrome">
      <summary>Creates the theme-specific look for Windows Presentation Foundation (WPF) <see cref="P:System.Windows.Controls.Primitives.BulletDecorator.Bullet" /> elements. A <see cref="P:System.Windows.Controls.Primitives.BulletDecorator.Bullet" /> defines the appearance of <see cref="T:System.Windows.Controls.CheckBox" /> and <see cref="T:System.Windows.Controls.RadioButton" /> elements.</summary>
    </member>
    <member name="M:Microsoft.Windows.Themes.BulletChrome.#ctor">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.Windows.Themes.BulletChrome" /> class.</summary>
    </member>
    <member name="P:Microsoft.Windows.Themes.BulletChrome.Background">
      <summary>Gets or sets the brush used to fill the background of the <see cref="P:System.Windows.Controls.Primitives.BulletDecorator.Bullet" />.</summary>
      <returns>The brush used to fill the background of the <see cref="P:System.Windows.Controls.Primitives.BulletDecorator.Bullet" />.</returns>
    </member>
    <member name="F:Microsoft.Windows.Themes.BulletChrome.BackgroundProperty">
      <summary>Identifies the <see cref="P:Microsoft.Windows.Themes.BulletChrome.Background" /> dependency property.</summary>
    </member>
    <member name="P:Microsoft.Windows.Themes.BulletChrome.BorderBrush">
      <summary>Gets or sets the brush used to draw the outer border of the <see cref="P:System.Windows.Controls.Primitives.BulletDecorator.Bullet" />.</summary>
      <returns>The brush used to draw the outer border of the <see cref="P:System.Windows.Controls.Primitives.BulletDecorator.Bullet" />.</returns>
    </member>
    <member name="F:Microsoft.Windows.Themes.BulletChrome.BorderBrushProperty">
      <summary>Identifies the <see cref="P:Microsoft.Windows.Themes.BulletChrome.BorderBrush" /> dependency property.</summary>
    </member>
    <member name="P:Microsoft.Windows.Themes.BulletChrome.IsChecked">
      <summary>Gets or sets a value indicating whether the <see cref="P:System.Windows.Controls.Primitives.BulletDecorator.Bullet" /> is checked.</summary>
      <returns>
        <see langword="true" /> if the <see cref="P:System.Windows.Controls.Primitives.BulletDecorator.Bullet" /> is checked; <see langword="false" /> if the <see cref="P:System.Windows.Controls.Primitives.BulletDecorator.Bullet" /> is not checked; otherwise, <see langword="null" />.</returns>
    </member>
    <member name="F:Microsoft.Windows.Themes.BulletChrome.IsCheckedProperty">
      <summary>Identifies the <see cref="P:Microsoft.Windows.Themes.BulletChrome.IsChecked" /> dependency property.</summary>
    </member>
    <member name="P:Microsoft.Windows.Themes.BulletChrome.IsRound">
      <summary>Gets or sets a value indicating whether the <see cref="P:System.Windows.Controls.Primitives.BulletDecorator.Bullet" /> has round corners.</summary>
      <returns>
        <see langword="true" /> if the <see cref="P:System.Windows.Controls.Primitives.BulletDecorator.Bullet" /> has round corners; otherwise <see langword="false" />.</returns>
    </member>
    <member name="F:Microsoft.Windows.Themes.BulletChrome.IsRoundProperty">
      <summary>Identifies the <see cref="P:Microsoft.Windows.Themes.BulletChrome.IsRound" /> dependency property.</summary>
    </member>
    <member name="P:Microsoft.Windows.Themes.BulletChrome.RenderMouseOver">
      <summary>Gets or sets a value indicating whether the <see cref="P:System.Windows.Controls.Primitives.BulletDecorator.Bullet" /> appears as if the mouse is over it.</summary>
      <returns>
        <see langword="true" /> if the <see cref="P:System.Windows.Controls.Primitives.BulletDecorator.Bullet" /> appears as if the mouse is over it; otherwise <see langword="false" />.</returns>
    </member>
    <member name="F:Microsoft.Windows.Themes.BulletChrome.RenderMouseOverProperty">
      <summary>Identifies the <see cref="P:Microsoft.Windows.Themes.BulletChrome.RenderMouseOver" /> dependency property.</summary>
    </member>
    <member name="P:Microsoft.Windows.Themes.BulletChrome.RenderPressed">
      <summary>Gets or sets a value indicating whether the <see cref="P:System.Windows.Controls.Primitives.BulletDecorator.Bullet" /> appears pressed.</summary>
      <returns>
        <see langword="true" /> if the <see cref="P:System.Windows.Controls.Primitives.BulletDecorator.Bullet" /> appears pressed; otherwise <see langword="false" />.</returns>
    </member>
    <member name="F:Microsoft.Windows.Themes.BulletChrome.RenderPressedProperty">
      <summary>Identifies the <see cref="P:Microsoft.Windows.Themes.BulletChrome.RenderPressed" /> dependency property.</summary>
    </member>
    <member name="T:Microsoft.Windows.Themes.ButtonChrome">
      <summary>Creates the theme-specific look for .NET Framework <see cref="T:System.Windows.Controls.Button" /> elements.</summary>
    </member>
    <member name="M:Microsoft.Windows.Themes.ButtonChrome.#ctor">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.Windows.Themes.ButtonChrome" /> class.</summary>
    </member>
    <member name="P:Microsoft.Windows.Themes.ButtonChrome.Background">
      <summary>Gets or sets the brush used to fill the background of the <see cref="T:System.Windows.Controls.Button" />.</summary>
      <returns>The brush used to fill the background of the <see cref="T:System.Windows.Controls.Button" />.</returns>
    </member>
    <member name="F:Microsoft.Windows.Themes.ButtonChrome.BackgroundProperty">
      <summary>Identifies the <see cref="P:Microsoft.Windows.Themes.ButtonChrome.Background" /> dependency property.</summary>
    </member>
    <member name="P:Microsoft.Windows.Themes.ButtonChrome.BorderBrush">
      <summary>Gets or sets the brush used to draw the outer border of the <see cref="T:System.Windows.Controls.Button" />.</summary>
      <returns>The brush used to draw the outer border of the <see cref="T:System.Windows.Controls.Button" />.</returns>
    </member>
    <member name="F:Microsoft.Windows.Themes.ButtonChrome.BorderBrushProperty">
      <summary>Identifies the <see cref="P:Microsoft.Windows.Themes.ButtonChrome.BorderBrush" /> dependency property.</summary>
    </member>
    <member name="P:Microsoft.Windows.Themes.ButtonChrome.RenderDefaulted">
      <summary>Gets or sets a value indicating whether the <see cref="T:System.Windows.Controls.Button" /> has the appearance of the default button on the form.</summary>
      <returns>
        <see langword="true" /> if the <see cref="T:System.Windows.Controls.Button" /> has the appearance of the default button; otherwise <see langword="false" />.</returns>
    </member>
    <member name="F:Microsoft.Windows.Themes.ButtonChrome.RenderDefaultedProperty">
      <summary>Identifies the <see cref="P:Microsoft.Windows.Themes.ButtonChrome.RenderDefaulted" /> dependency property.</summary>
    </member>
    <member name="P:Microsoft.Windows.Themes.ButtonChrome.RenderMouseOver">
      <summary>Gets or sets a value indicating whether the <see cref="T:System.Windows.Controls.Button" /> appears as if the mouse is over it.</summary>
      <returns>
        <see langword="true" /> if the <see cref="T:System.Windows.Controls.Button" /> appears as if the mouse is over it; otherwise <see langword="false" />.</returns>
    </member>
    <member name="F:Microsoft.Windows.Themes.ButtonChrome.RenderMouseOverProperty">
      <summary>Identifies the <see cref="P:Microsoft.Windows.Themes.ButtonChrome.RenderMouseOver" /> dependency property.</summary>
    </member>
    <member name="P:Microsoft.Windows.Themes.ButtonChrome.RenderPressed">
      <summary>Gets or sets a value indicating whether the <see cref="T:System.Windows.Controls.Button" /> appears pressed.</summary>
      <returns>
        <see langword="true" /> if the <see cref="T:System.Windows.Controls.Button" /> appears pressed; otherwise <see langword="false" />.</returns>
    </member>
    <member name="F:Microsoft.Windows.Themes.ButtonChrome.RenderPressedProperty">
      <summary>Identifies the <see cref="P:Microsoft.Windows.Themes.ButtonChrome.RenderPressed" /> dependency property.</summary>
    </member>
    <member name="P:Microsoft.Windows.Themes.ButtonChrome.RoundCorners">
      <summary>Gets or sets a value indicating whether the <see cref="T:System.Windows.Controls.Button" /> has round corners.</summary>
      <returns>
        <see langword="true" /> if the <see cref="T:System.Windows.Controls.Button" /> has round corners; otherwise <see langword="false" />.</returns>
    </member>
    <member name="F:Microsoft.Windows.Themes.ButtonChrome.RoundCornersProperty">
      <summary>Identifies the <see cref="P:Microsoft.Windows.Themes.ButtonChrome.RoundCorners" /> dependency property.</summary>
    </member>
    <member name="T:Microsoft.Windows.Themes.DataGridHeaderBorder">
      <summary>Creates the theme specific-look for headers in .NET Framework 4.5 <see cref="T:System.Windows.Controls.DataGrid" />.</summary>
    </member>
    <member name="M:Microsoft.Windows.Themes.DataGridHeaderBorder.#ctor">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.Windows.Themes.DataGridHeaderBorder" /> class.</summary>
    </member>
    <member name="P:Microsoft.Windows.Themes.DataGridHeaderBorder.IsClickable">
      <summary>Gets or sets a value that indicates whether the header is clickable.</summary>
      <returns>
        <see langword="true" /> if the header clickable; otherwise, <see langword="false" />. The registered default is <see langword="true" />. For more information about what can influence the value, see <see cref="T:System.Windows.DependencyProperty" />.</returns>
    </member>
    <member name="F:Microsoft.Windows.Themes.DataGridHeaderBorder.IsClickableProperty">
      <summary>Identifies the <see cref="P:Microsoft.Windows.Themes.DataGridHeaderBorder.IsClickable" /> dependency property.</summary>
    </member>
    <member name="P:Microsoft.Windows.Themes.DataGridHeaderBorder.IsHovered">
      <summary>Gets or sets a value that indicates whether the header appears as if the mouse pointer is moved over it.</summary>
      <returns>
        <see langword="true" /> if the header appears as if the mouse pointer is moved over it; otherwise, <see langword="false" />. The registered default is <see langword="false" />. For more information about what can influence the value, see <see cref="T:System.Windows.DependencyProperty" />.</returns>
    </member>
    <member name="F:Microsoft.Windows.Themes.DataGridHeaderBorder.IsHoveredProperty">
      <summary>Identifies the <see cref="P:Microsoft.Windows.Themes.DataGridHeaderBorder.IsHovered" /> dependency property.</summary>
    </member>
    <member name="P:Microsoft.Windows.Themes.DataGridHeaderBorder.IsPressed">
      <summary>Gets or sets a value that indicates whether the header appears pressed.</summary>
      <returns>
        <see langword="true" /> if the header appears pressed; otherwise, <see langword="false" />. The registered default is <see langword="false" />. For more information about what can influence the value, see <see cref="T:System.Windows.DependencyProperty" />.</returns>
    </member>
    <member name="F:Microsoft.Windows.Themes.DataGridHeaderBorder.IsPressedProperty">
      <summary>Identifies the <see cref="P:Microsoft.Windows.Themes.DataGridHeaderBorder.IsPressed" /> dependency property.</summary>
    </member>
    <member name="P:Microsoft.Windows.Themes.DataGridHeaderBorder.IsSelected">
      <summary>Gets or sets a value that indicates whether the header appears selected.</summary>
      <returns>
        <see langword="true" /> if the header appears selected; otherwise, <see langword="false" />. The registered default is <see langword="false" />. For more information about what can influence the value, see <see cref="T:System.Windows.DependencyProperty" />.</returns>
    </member>
    <member name="F:Microsoft.Windows.Themes.DataGridHeaderBorder.IsSelectedProperty">
      <summary>Identifies the <see cref="P:Microsoft.Windows.Themes.DataGridHeaderBorder.IsSelected" /> dependency property.</summary>
    </member>
    <member name="P:Microsoft.Windows.Themes.DataGridHeaderBorder.Orientation">
      <summary>Gets or sets whether the header renders in the vertical direction, as a column header, or horizontal direction, as a row header.</summary>
      <returns>One of the enumeration values that indicates which direction the header renders. The registered default is <see cref="F:System.Windows.Controls.Orientation.Vertical" />. For more information about what can influence the value, see <see cref="T:System.Windows.DependencyProperty" />.</returns>
    </member>
    <member name="F:Microsoft.Windows.Themes.DataGridHeaderBorder.OrientationProperty">
      <summary>Identifies the <see cref="P:Microsoft.Windows.Themes.DataGridHeaderBorder.Orientation" /> dependency property.</summary>
    </member>
    <member name="P:Microsoft.Windows.Themes.DataGridHeaderBorder.SeparatorBrush">
      <summary>Gets or sets the brush that draws the separation between headers.</summary>
      <returns>The brush that draws the separation between headers. The registered default is <see langword="null" />. For more information about what can influence the value, see <see cref="T:System.Windows.DependencyProperty" />.</returns>
    </member>
    <member name="F:Microsoft.Windows.Themes.DataGridHeaderBorder.SeparatorBrushProperty">
      <summary>Identifies the <see cref="P:Microsoft.Windows.Themes.DataGridHeaderBorder.SeparatorBrush" /> dependency property.</summary>
    </member>
    <member name="P:Microsoft.Windows.Themes.DataGridHeaderBorder.SeparatorVisibility">
      <summary>Gets or sets the value that indicates whether the separation between headers is visible.</summary>
      <returns>One of the enumeration values that indicates whether the <see cref="P:Microsoft.Windows.Themes.DataGridHeaderBorder.SeparatorBrush" /> is visible. The registered default is <see cref="F:System.Windows.Visibility.Visible" />. For more information about what can influence the value, see <see cref="T:System.Windows.DependencyProperty" />.</returns>
    </member>
    <member name="F:Microsoft.Windows.Themes.DataGridHeaderBorder.SeparatorVisibilityProperty">
      <summary>Identifies the <see cref="P:Microsoft.Windows.Themes.DataGridHeaderBorder.SeparatorVisibility" /> dependency property.</summary>
    </member>
    <member name="P:Microsoft.Windows.Themes.DataGridHeaderBorder.SortDirection">
      <summary>Gets or sets the header sort direction.</summary>
      <returns>One of the enumeration values that indicates which direction the column is sorted. The registered default is <see langword="null" />. For more information about what can influence the value, see <see cref="T:System.Windows.DependencyProperty" />.</returns>
    </member>
    <member name="F:Microsoft.Windows.Themes.DataGridHeaderBorder.SortDirectionProperty">
      <summary>Identifies the <see cref="P:Microsoft.Windows.Themes.DataGridHeaderBorder.SortDirection" /> dependency property.</summary>
    </member>
    <member name="T:Microsoft.Windows.Themes.ListBoxChrome">
      <summary>Creates the theme-specific look for .NET Framework <see cref="T:System.Windows.Controls.ListBox" /> elements.</summary>
    </member>
    <member name="M:Microsoft.Windows.Themes.ListBoxChrome.#ctor">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.Windows.Themes.ListBoxChrome" /> class.</summary>
    </member>
    <member name="P:Microsoft.Windows.Themes.ListBoxChrome.Background">
      <summary>Gets or sets the brush used to fill the background of the <see cref="T:System.Windows.Controls.ListBox" />.</summary>
      <returns>The brush used to fill the background of the <see cref="T:System.Windows.Controls.ListBox" />.</returns>
    </member>
    <member name="F:Microsoft.Windows.Themes.ListBoxChrome.BackgroundProperty">
      <summary>Identifies the <see cref="P:Microsoft.Windows.Themes.ListBoxChrome.Background" /> dependency property.</summary>
    </member>
    <member name="P:Microsoft.Windows.Themes.ListBoxChrome.BorderBrush">
      <summary>Gets or sets the brush used to draw the outer border of the <see cref="T:System.Windows.Controls.ListBox" />.</summary>
      <returns>The brush used to draw the outer border of the <see cref="T:System.Windows.Controls.ListBox" />.</returns>
    </member>
    <member name="F:Microsoft.Windows.Themes.ListBoxChrome.BorderBrushProperty">
      <summary>Identifies the <see cref="P:Microsoft.Windows.Themes.ListBoxChrome.BorderBrush" /> dependency property.</summary>
    </member>
    <member name="P:Microsoft.Windows.Themes.ListBoxChrome.BorderThickness">
      <summary>Gets or sets the thickness of the border of the <see cref="T:System.Windows.Controls.ListBox" />.</summary>
      <returns>The thickness of the border.</returns>
    </member>
    <member name="F:Microsoft.Windows.Themes.ListBoxChrome.BorderThicknessProperty">
      <summary>Identifies the <see cref="P:Microsoft.Windows.Themes.ListBoxChrome.BorderThickness" /> dependency property.</summary>
    </member>
    <member name="P:Microsoft.Windows.Themes.ListBoxChrome.RenderFocused">
      <summary>Gets or sets a value indicating whether the <see cref="T:System.Windows.Controls.ListBox" /> appears as if it has keyboard focus.</summary>
      <returns>
        <see langword="true" /> if the <see cref="T:System.Windows.Controls.ListBox" /> appears as if it has keyboard focus; otherwise <see langword="false" />.</returns>
    </member>
    <member name="F:Microsoft.Windows.Themes.ListBoxChrome.RenderFocusedProperty">
      <summary>Identifies the <see cref="P:Microsoft.Windows.Themes.ListBoxChrome.RenderFocused" /> dependency property.</summary>
    </member>
    <member name="P:Microsoft.Windows.Themes.ListBoxChrome.RenderMouseOver">
      <summary>Gets or sets a value indicating whether the <see cref="T:System.Windows.Controls.ListBox" /> appears as if the mouse is over it.</summary>
      <returns>
        <see langword="true" /> if the <see cref="T:System.Windows.Controls.ListBox" /> appears as if the mouse is over it; otherwise <see langword="false" />.</returns>
    </member>
    <member name="F:Microsoft.Windows.Themes.ListBoxChrome.RenderMouseOverProperty">
      <summary>Identifies the <see cref="P:Microsoft.Windows.Themes.ListBoxChrome.RenderMouseOver" /> dependency property.</summary>
    </member>
    <member name="T:Microsoft.Windows.Themes.PlatformCulture">
      <summary>Provides culture-specific information used by the .NET Framework system themes.</summary>
    </member>
    <member name="P:Microsoft.Windows.Themes.PlatformCulture.FlowDirection">
      <summary>Gets a value that specifies whether the primary text advance direction shall be left-to-right, right-to-left, or top-to-bottom.</summary>
      <returns>One of the enumeration values.</returns>
    </member>
    <member name="T:Microsoft.Windows.Themes.ProgressBarHighlightConverter">
      <summary>Creates a <see cref="T:System.Windows.Media.DrawingBrush" /> used to draw the highlighting for the <see cref="T:System.Windows.Controls.ProgressBar" />.</summary>
    </member>
    <member name="M:Microsoft.Windows.Themes.ProgressBarHighlightConverter.#ctor">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.Windows.Themes.ProgressBarHighlightConverter" /> class.</summary>
    </member>
    <member name="M:Microsoft.Windows.Themes.ProgressBarHighlightConverter.Convert(System.Object[],System.Type,System.Object,System.Globalization.CultureInfo)">
      <summary>Creates the <see cref="T:System.Windows.Media.DrawingBrush" />.</summary>
      <param name="values">The <see cref="T:System.Windows.Media.Brush" /> used for the <see cref="P:System.Windows.Controls.Control.Foreground" /> of the <see cref="T:System.Windows.Controls.ProgressBar" />, the <see cref="T:System.Double" /> defining the <see cref="P:System.Windows.FrameworkElement.Width" />, and the <see cref="T:System.Double" /> defining the <see cref="P:System.Windows.FrameworkElement.Height" />.</param>
      <param name="targetType">This parameter is not used.</param>
      <param name="parameter">This parameter is not used.</param>
      <param name="culture">This parameter is not used.</param>
      <returns>The created <see cref="T:System.Windows.Media.DrawingBrush" />.</returns>
    </member>
    <member name="M:Microsoft.Windows.Themes.ProgressBarHighlightConverter.ConvertBack(System.Object,System.Type[],System.Object,System.Globalization.CultureInfo)">
      <summary>Not implemented.</summary>
      <param name="value">This parameter is not used.</param>
      <param name="targetTypes">This parameter is not used.</param>
      <param name="parameter">This parameter is not used.</param>
      <param name="culture">This parameter is not used.</param>
      <returns>
        <see langword="null" />.</returns>
    </member>
    <member name="T:Microsoft.Windows.Themes.ScrollChrome">
      <summary>Creates the theme-specific look for .NET Framework <see cref="T:System.Windows.Controls.Primitives.ScrollBar" /> elements.</summary>
    </member>
    <member name="M:Microsoft.Windows.Themes.ScrollChrome.#ctor">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.Windows.Themes.ScrollChrome" /> class.</summary>
    </member>
    <member name="M:Microsoft.Windows.Themes.ScrollChrome.GetScrollGlyph(System.Windows.DependencyObject)">
      <summary>Gets the <see cref="T:Microsoft.Windows.Themes.ScrollGlyph" /> for the specified object.</summary>
      <param name="element">The dependency object to which the <see cref="T:Microsoft.Windows.Themes.ScrollGlyph" /> is attached.</param>
      <returns>One of the enumeration values.</returns>
    </member>
    <member name="P:Microsoft.Windows.Themes.ScrollChrome.RenderMouseOver">
      <summary>Gets or sets a value indicating whether the <see cref="T:System.Windows.Controls.Primitives.ScrollBar" /> appears as if the mouse is over it.</summary>
      <returns>
        <see langword="true" /> if the <see cref="T:System.Windows.Controls.Primitives.ScrollBar" /> appears as if the mouse is over it; otherwise <see langword="false" />.</returns>
    </member>
    <member name="F:Microsoft.Windows.Themes.ScrollChrome.RenderMouseOverProperty">
      <summary>Identifies the <see cref="P:Microsoft.Windows.Themes.ScrollChrome.RenderMouseOver" /> dependency property.</summary>
    </member>
    <member name="P:Microsoft.Windows.Themes.ScrollChrome.RenderPressed">
      <summary>Gets or sets a value indicating whether the <see cref="T:System.Windows.Controls.Primitives.ScrollBar" /> appears pressed.</summary>
      <returns>
        <see langword="true" /> if the <see cref="T:System.Windows.Controls.Primitives.ScrollBar" /> appears pressed; otherwise <see langword="false" />.</returns>
    </member>
    <member name="F:Microsoft.Windows.Themes.ScrollChrome.RenderPressedProperty">
      <summary>Identifies the <see cref="P:Microsoft.Windows.Themes.ScrollChrome.RenderPressed" /> dependency property.</summary>
    </member>
    <member name="P:Microsoft.Windows.Themes.ScrollChrome.ScrollGlyph">
      <summary>Gets or sets the color of the <see cref="T:Microsoft.Windows.Themes.ScrollGlyph" /> for the <see cref="T:Microsoft.Windows.Themes.ScrollChrome" />.</summary>
    </member>
    <member name="F:Microsoft.Windows.Themes.ScrollChrome.ScrollGlyphProperty">
      <summary>Identifies the <see cref="T:Microsoft.Windows.Themes.ScrollGlyph" /> attached property.</summary>
    </member>
    <member name="M:Microsoft.Windows.Themes.ScrollChrome.SetScrollGlyph(System.Windows.DependencyObject,Microsoft.Windows.Themes.ScrollGlyph)">
      <summary>Sets the <see cref="T:Microsoft.Windows.Themes.ScrollGlyph" /> on the specified object.</summary>
      <param name="element">The dependency object to which the <see cref="T:Microsoft.Windows.Themes.ScrollGlyph" /> is attached.</param>
      <param name="value">One of the enumeration values.</param>
    </member>
    <member name="T:Microsoft.Windows.Themes.ScrollGlyph">
      <summary>Describes the glyphs used to represent the <see cref="T:System.Windows.Controls.Primitives.Thumb" />.</summary>
    </member>
    <member name="F:Microsoft.Windows.Themes.ScrollGlyph.DownArrow">
      <summary>An arrow glyph pointing down.</summary>
    </member>
    <member name="F:Microsoft.Windows.Themes.ScrollGlyph.HorizontalGripper">
      <summary>horizontal gripper glyph.</summary>
    </member>
    <member name="F:Microsoft.Windows.Themes.ScrollGlyph.LeftArrow">
      <summary>An arrow glyph pointing to the left.</summary>
    </member>
    <member name="F:Microsoft.Windows.Themes.ScrollGlyph.None">
      <summary>No glyph is used.</summary>
    </member>
    <member name="F:Microsoft.Windows.Themes.ScrollGlyph.RightArrow">
      <summary>An arrow glyph pointing to the right.</summary>
    </member>
    <member name="F:Microsoft.Windows.Themes.ScrollGlyph.UpArrow">
      <summary>An arrow glyph pointing up.</summary>
    </member>
    <member name="F:Microsoft.Windows.Themes.ScrollGlyph.VerticalGripper">
      <summary>A vertical gripper glyph.</summary>
    </member>
    <member name="T:Microsoft.Windows.Themes.SystemDropShadowChrome">
      <summary>Creates a theme specific look for drop shadow effects.</summary>
    </member>
    <member name="M:Microsoft.Windows.Themes.SystemDropShadowChrome.#ctor">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.Windows.Themes.SystemDropShadowChrome" /> class.</summary>
    </member>
    <member name="P:Microsoft.Windows.Themes.SystemDropShadowChrome.Color">
      <summary>Gets or sets the color used by the drop shadow.</summary>
      <returns>The color.</returns>
    </member>
    <member name="F:Microsoft.Windows.Themes.SystemDropShadowChrome.ColorProperty">
      <summary>Identifies the <see cref="P:Microsoft.Windows.Themes.SystemDropShadowChrome.Color" /> dependency property.</summary>
    </member>
    <member name="P:Microsoft.Windows.Themes.SystemDropShadowChrome.CornerRadius">
      <summary>Gets or sets the radii of a rectangle's corners.</summary>
      <returns>The radii of a rectangle's corners.</returns>
    </member>
    <member name="F:Microsoft.Windows.Themes.SystemDropShadowChrome.CornerRadiusProperty">
      <summary>Identifies the <see cref="P:Microsoft.Windows.Themes.SystemDropShadowChrome.CornerRadius" /> dependency property.</summary>
    </member>
  </members>
</doc>