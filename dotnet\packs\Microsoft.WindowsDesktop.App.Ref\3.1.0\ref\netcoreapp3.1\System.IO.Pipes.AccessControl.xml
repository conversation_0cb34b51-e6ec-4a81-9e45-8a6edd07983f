﻿<?xml version="1.0" encoding="utf-8"?><doc>
  <assembly>
    <name>System.IO.Pipes.AccessControl</name>
  </assembly>
  <members>
    <member name="T:System.IO.Pipes.PipeAccessRights">
      <summary>Defines the access rights to use when you create access and audit rules.</summary>
    </member>
    <member name="F:System.IO.Pipes.PipeAccessRights.AccessSystemSecurity">
      <summary>Specifies the right to make changes to the system access control list (SACL).</summary>
      <returns></returns>
    </member>
    <member name="F:System.IO.Pipes.PipeAccessRights.ChangePermissions">
      <summary>Specifies the right to change the security and audit rules that are associated with a pipe.</summary>
      <returns></returns>
    </member>
    <member name="F:System.IO.Pipes.PipeAccessRights.CreateNewInstance">
      <summary>Specifies the right to create a new pipe. Setting this right also sets the <see cref="F:System.IO.Pipes.PipeAccessRights.Synchronize"></see> right.</summary>
      <returns></returns>
    </member>
    <member name="F:System.IO.Pipes.PipeAccessRights.Delete">
      <summary>Specifies the right to delete a pipe.</summary>
      <returns></returns>
    </member>
    <member name="F:System.IO.Pipes.PipeAccessRights.FullControl">
      <summary>Specifies the right to exert full control over a pipe, and to modify access control and audit rules. This value represents the combination of all rights in this enumeration.</summary>
      <returns></returns>
    </member>
    <member name="F:System.IO.Pipes.PipeAccessRights.Read">
      <summary>Specifies the right to read from the pipe. This right includes the <see cref="F:System.IO.Pipes.PipeAccessRights.ReadAttributes"></see>, <see cref="F:System.IO.Pipes.PipeAccessRights.ReadData"></see>, <see cref="F:System.IO.Pipes.PipeAccessRights.ReadExtendedAttributes"></see>, and <see cref="F:System.IO.Pipes.PipeAccessRights.ReadPermissions"></see> rights.</summary>
      <returns></returns>
    </member>
    <member name="F:System.IO.Pipes.PipeAccessRights.ReadAttributes">
      <summary>Specifies the right to read file system attributes from a pipe. This does not include the right to read data, extended file system attributes, or access and audit rules.</summary>
      <returns></returns>
    </member>
    <member name="F:System.IO.Pipes.PipeAccessRights.ReadData">
      <summary>Specifies the right to read data from the pipe. This does not include the right to read file system attributes, extended file system attributes, or access and audit rules.</summary>
      <returns></returns>
    </member>
    <member name="F:System.IO.Pipes.PipeAccessRights.ReadExtendedAttributes">
      <summary>Specifies the right to read extended file system attributes from a pipe. This does not include the right to read data, file system attributes, or access and audit rules.</summary>
      <returns></returns>
    </member>
    <member name="F:System.IO.Pipes.PipeAccessRights.ReadPermissions">
      <summary>Specifies the right to read access and audit rules from the pipe. This does not include the right to read data, file system attributes, or extended file system attributes.</summary>
      <returns></returns>
    </member>
    <member name="F:System.IO.Pipes.PipeAccessRights.ReadWrite">
      <summary>Specifies the right to read and write from the pipe. This right includes the <see cref="F:System.IO.Pipes.PipeAccessRights.ReadAttributes"></see>, <see cref="F:System.IO.Pipes.PipeAccessRights.ReadData"></see>, <see cref="F:System.IO.Pipes.PipeAccessRights.ReadExtendedAttributes"></see>, <see cref="F:System.IO.Pipes.PipeAccessRights.ReadPermissions"></see>, <see cref="F:System.IO.Pipes.PipeAccessRights.WriteAttributes"></see>, <see cref="F:System.IO.Pipes.PipeAccessRights.WriteData"></see>, and <see cref="F:System.IO.Pipes.PipeAccessRights.WriteExtendedAttributes"></see> rights.</summary>
      <returns></returns>
    </member>
    <member name="F:System.IO.Pipes.PipeAccessRights.Synchronize">
      <summary>Specifies whether the application can wait for a pipe handle to synchronize with the completion of an I/O operation.</summary>
      <returns></returns>
    </member>
    <member name="F:System.IO.Pipes.PipeAccessRights.TakeOwnership">
      <summary>Specifies the right to change the owner of a pipe. Note that owners of a pipe have full access to that resource.</summary>
      <returns></returns>
    </member>
    <member name="F:System.IO.Pipes.PipeAccessRights.Write">
      <summary>Specifies the right to write to the pipe. This right includes the <see cref="F:System.IO.Pipes.PipeAccessRights.WriteAttributes"></see>, <see cref="F:System.IO.Pipes.PipeAccessRights.WriteData"></see>, and <see cref="F:System.IO.Pipes.PipeAccessRights.WriteExtendedAttributes"></see> rights.</summary>
      <returns></returns>
    </member>
    <member name="F:System.IO.Pipes.PipeAccessRights.WriteAttributes">
      <summary>Specifies the right to write file system attributes to a pipe. This does not include the right to write data or extended file system attributes.</summary>
      <returns></returns>
    </member>
    <member name="F:System.IO.Pipes.PipeAccessRights.WriteData">
      <summary>Specifies the right to write data to a pipe. This does not include the right to write file system attributes or extended file system attributes.</summary>
      <returns></returns>
    </member>
    <member name="F:System.IO.Pipes.PipeAccessRights.WriteExtendedAttributes">
      <summary>Specifies the right to write extended file system attributes to a pipe. This does not include the right to write file attributes or data.</summary>
      <returns></returns>
    </member>
    <member name="T:System.IO.Pipes.PipeAccessRule">
      <summary>Represents an abstraction of an access control entry (ACE) that defines an access rule for a pipe.</summary>
    </member>
    <member name="M:System.IO.Pipes.PipeAccessRule.#ctor(System.Security.Principal.IdentityReference,System.IO.Pipes.PipeAccessRights,System.Security.AccessControl.AccessControlType)">
      <summary>Initializes a new instance of the <see cref="T:System.IO.Pipes.PipeAccessRule"></see> class with the specified identity, pipe access rights, and access control type.</summary>
      <param name="identity">An <see cref="T:System.Security.Principal.IdentityReference"></see> object that encapsulates a reference to a user account.</param>
      <param name="rights">One of the <see cref="T:System.IO.Pipes.PipeAccessRights"></see> values that specifies the type of operation associated with the access rule.</param>
      <param name="type">One of the <see cref="T:System.Security.AccessControl.AccessControlType"></see> values that specifies whether to allow or deny the operation.</param>
    </member>
    <member name="M:System.IO.Pipes.PipeAccessRule.#ctor(System.String,System.IO.Pipes.PipeAccessRights,System.Security.AccessControl.AccessControlType)">
      <summary>Initializes a new instance of the <see cref="T:System.IO.Pipes.PipeAccessRule"></see> class with the specified identity, pipe access rights, and access control type.</summary>
      <param name="identity">The name of the user account.</param>
      <param name="rights">One of the <see cref="T:System.IO.Pipes.PipeAccessRights"></see> values that specifies the type of operation associated with the access rule.</param>
      <param name="type">One of the <see cref="T:System.Security.AccessControl.AccessControlType"></see> values that specifies whether to allow or deny the operation.</param>
    </member>
    <member name="P:System.IO.Pipes.PipeAccessRule.PipeAccessRights">
      <summary>Gets the <see cref="T:System.IO.Pipes.PipeAccessRights"></see> flags that are associated with the current <see cref="T:System.IO.Pipes.PipeAccessRule"></see> object.</summary>
      <returns>A bitwise combination of the <see cref="System.IO.Pipes.PipeAccessRights"></see> values.</returns>
    </member>
    <member name="T:System.IO.Pipes.PipeAuditRule">
      <summary>Represents an abstraction of an access control entry (ACE) that defines an audit rule for a pipe.</summary>
    </member>
    <member name="M:System.IO.Pipes.PipeAuditRule.#ctor(System.Security.Principal.IdentityReference,System.IO.Pipes.PipeAccessRights,System.Security.AccessControl.AuditFlags)">
      <summary>Initializes a new instance of the <see cref="T:System.IO.Pipes.PipeAuditRule"></see> class for a user account specified in a <see cref="T:System.Security.Principal.IdentityReference"></see> object.</summary>
      <param name="identity">An <see cref="T:System.Security.Principal.IdentityReference"></see> object that encapsulates a reference to a user account.</param>
      <param name="rights">One of the <see cref="T:System.IO.Pipes.PipeAccessRights"></see> values that specifies the type of operation associated with the access rule.</param>
      <param name="flags">One of the <see cref="T:System.Security.AccessControl.AuditFlags"></see> values that specifies when to perform auditing.</param>
    </member>
    <member name="M:System.IO.Pipes.PipeAuditRule.#ctor(System.String,System.IO.Pipes.PipeAccessRights,System.Security.AccessControl.AuditFlags)">
      <summary>Initializes a new instance of the <see cref="T:System.IO.Pipes.PipeAuditRule"></see> class for a named user account.</summary>
      <param name="identity">The name of the user account.</param>
      <param name="rights">One of the <see cref="T:System.IO.Pipes.PipeAccessRights"></see> values that specifies the type of operation associated with the access rule.</param>
      <param name="flags">One of the <see cref="T:System.Security.AccessControl.AuditFlags"></see> values that specifies when to perform auditing.</param>
    </member>
    <member name="P:System.IO.Pipes.PipeAuditRule.PipeAccessRights">
      <summary>Gets the <see cref="T:System.IO.Pipes.PipeAccessRights"></see> flags that are associated with the current <see cref="T:System.IO.Pipes.PipeAuditRule"></see> object.</summary>
      <returns>A bitwise combination of the <see cref="System.IO.Pipes.PipeAccessRights"></see> values.</returns>
    </member>
    <member name="T:System.IO.Pipes.PipesAclExtensions">
      
    </member>
    <member name="M:System.IO.Pipes.PipesAclExtensions.GetAccessControl(System.IO.Pipes.PipeStream)">
      <param name="stream"></param>
      <returns></returns>
    </member>
    <member name="M:System.IO.Pipes.PipesAclExtensions.SetAccessControl(System.IO.Pipes.PipeStream,System.IO.Pipes.PipeSecurity)">
      <param name="stream"></param>
      <param name="pipeSecurity"></param>
    </member>
    <member name="T:System.IO.Pipes.PipeSecurity">
      <summary>Represents the access control and audit security for a pipe.</summary>
    </member>
    <member name="M:System.IO.Pipes.PipeSecurity.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.IO.Pipes.PipeSecurity"></see> class.</summary>
    </member>
    <member name="P:System.IO.Pipes.PipeSecurity.AccessRightType">
      <summary>Gets the <see cref="T:System.Type"></see> of the securable object that is associated with the current <see cref="T:System.IO.Pipes.PipeSecurity"></see> object.</summary>
      <returns>The type of the securable object that is associated with the current <see cref="System.IO.Pipes.PipeSecurity"></see> object.</returns>
    </member>
    <member name="M:System.IO.Pipes.PipeSecurity.AccessRuleFactory(System.Security.Principal.IdentityReference,System.Int32,System.Boolean,System.Security.AccessControl.InheritanceFlags,System.Security.AccessControl.PropagationFlags,System.Security.AccessControl.AccessControlType)">
      <summary>Initializes a new instance of the <see cref="T:System.Security.AccessControl.AccessRule"></see> class with the specified values.</summary>
      <param name="identityReference">The identity that the access rule applies to. It must be an object that can be cast as a <see cref="T:System.Security.Principal.SecurityIdentifier"></see> object.</param>
      <param name="accessMask">The access mask of this rule. The access mask is a 32-bit collection of anonymous bits, the meaning of which is defined by the individual integrators</param>
      <param name="isInherited">true if this rule is inherited from a parent container; otherwise false.</param>
      <param name="inheritanceFlags">One of the <see cref="T:System.Security.AccessControl.InheritanceFlags"></see> values that specifies the inheritance properties of the access rule.</param>
      <param name="propagationFlags">One of the <see cref="T:System.Security.AccessControl.PropagationFlags"></see> values that specifies whether inherited access rules are automatically propagated. The propagation flags are ignored if inheritanceFlags is set to <see cref="F:System.Security.AccessControl.InheritanceFlags.None"></see>.</param>
      <param name="type">Specifies the valid access control type.</param>
      <returns>The <see cref="System.Security.AccessControl.AccessRule"></see> object that this method creates.</returns>
      <exception cref="T:System.ArgumentOutOfRangeException"><paramref name="accessMask">accessMask</paramref>, <paramref name="inheritanceFlags">inheritanceFlags</paramref>, <paramref name="propagationFlags">propagationFlags</paramref>, or <paramref name="type">type</paramref> specifies an invalid value.</exception>
      <exception cref="T:System.ArgumentNullException"><paramref name="identityReference">identityReference</paramref> is null.  
 -or-  
 <paramref name="accessMask">accessMask</paramref> is zero.</exception>
      <exception cref="T:System.ArgumentException"><paramref name="identityReference">identityReference</paramref> is neither of type <see cref="System.Security.Principal.SecurityIdentifier"></see> nor of a type, such as <see cref="System.Security.Principal.NTAccount"></see>, that can be converted to type <see cref="System.Security.Principal.SecurityIdentifier"></see>.</exception>
    </member>
    <member name="P:System.IO.Pipes.PipeSecurity.AccessRuleType">
      <summary>Gets the <see cref="T:System.Type"></see> of the object that is associated with the access rules of the current <see cref="T:System.IO.Pipes.PipeSecurity"></see> object.</summary>
      <returns>The type of the object that is associated with the access rules of the current <see cref="System.IO.Pipes.PipeSecurity"></see> object.</returns>
    </member>
    <member name="M:System.IO.Pipes.PipeSecurity.AddAccessRule(System.IO.Pipes.PipeAccessRule)">
      <summary>Adds an access rule to the Discretionary Access Control List (DACL) that is associated with the current <see cref="T:System.IO.Pipes.PipeSecurity"></see> object.</summary>
      <param name="rule">The access rule to add.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="rule">rule</paramref> parameter is null.</exception>
    </member>
    <member name="M:System.IO.Pipes.PipeSecurity.AddAuditRule(System.IO.Pipes.PipeAuditRule)">
      <summary>Adds an audit rule to the System Access Control List (SACL)that is associated with the current <see cref="T:System.IO.Pipes.PipeSecurity"></see> object.</summary>
      <param name="rule">The audit rule to add.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="rule">rule</paramref> parameter is null.</exception>
    </member>
    <member name="M:System.IO.Pipes.PipeSecurity.AuditRuleFactory(System.Security.Principal.IdentityReference,System.Int32,System.Boolean,System.Security.AccessControl.InheritanceFlags,System.Security.AccessControl.PropagationFlags,System.Security.AccessControl.AuditFlags)">
      <summary>Initializes a new instance of the <see cref="T:System.Security.AccessControl.AuditRule"></see> class with the specified values.</summary>
      <param name="identityReference">The identity that the access rule applies to. It must be an object that can be cast as a <see cref="T:System.Security.Principal.SecurityIdentifier"></see> object.</param>
      <param name="accessMask">The access mask of this rule. The access mask is a 32-bit collection of anonymous bits, the meaning of which is defined by the individual integrators</param>
      <param name="isInherited">true if this rule is inherited from a parent container; otherwise, false..</param>
      <param name="inheritanceFlags">One of the <see cref="T:System.Security.AccessControl.InheritanceFlags"></see> values that specifies the inheritance properties of the access rule.</param>
      <param name="propagationFlags">One of the <see cref="T:System.Security.AccessControl.PropagationFlags"></see> values that specifies whether inherited access rules are automatically propagated. The propagation flags are ignored if inheritanceFlags is set to <see cref="F:System.Security.AccessControl.InheritanceFlags.None"></see>.</param>
      <param name="flags">One of the <see cref="T:System.Security.AccessControl.AuditFlags"></see> values that specifies the valid access control type.</param>
      <returns>The <see cref="System.Security.AccessControl.AuditRule"></see> object that this method creates.</returns>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="accessMask">accessMask</paramref>, <paramref name="inheritanceFlags">inheritanceFlags</paramref>, <paramref name="propagationFlags">propagationFlags</paramref>, or <paramref name="flags">flags</paramref> properties specify an invalid value.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="identityReference">identityReference</paramref> property is null.  
 -or-  
 The <paramref name="accessMask">accessMask</paramref> property is zero.</exception>
      <exception cref="T:System.ArgumentException">The <paramref name="identityReference">identityReference</paramref> property is neither of type <see cref="System.Security.Principal.SecurityIdentifier"></see> nor of a type, such as <see cref="System.Security.Principal.NTAccount"></see>, that can be converted to type <see cref="System.Security.Principal.SecurityIdentifier"></see>.</exception>
    </member>
    <member name="P:System.IO.Pipes.PipeSecurity.AuditRuleType">
      <summary>Gets the <see cref="T:System.Type"></see> object associated with the audit rules of the current <see cref="T:System.IO.Pipes.PipeSecurity"></see> object.</summary>
      <returns>The type of the object that is associated with the audit rules of the current <see cref="System.IO.Pipes.PipeSecurity"></see> object.</returns>
    </member>
    <member name="M:System.IO.Pipes.PipeSecurity.Persist(System.String)">
      <summary>Saves the specified sections of the security descriptor that is associated with the current <see cref="T:System.IO.Pipes.PipeSecurity"></see> object to permanent storage.</summary>
      <param name="name">The name of the securable object that the current <see cref="T:System.IO.Pipes.PipeSecurity"></see> object is associated with.</param>
    </member>
    <member name="M:System.IO.Pipes.PipeSecurity.Persist(System.Runtime.InteropServices.SafeHandle)">
      <summary>Saves the specified sections of the security descriptor that is associated with the current <see cref="T:System.IO.Pipes.PipeSecurity"></see> object to permanent storage.</summary>
      <param name="handle">The handle of the securable object that the current <see cref="T:System.IO.Pipes.PipeSecurity"></see> object is associated with.</param>
    </member>
    <member name="M:System.IO.Pipes.PipeSecurity.RemoveAccessRule(System.IO.Pipes.PipeAccessRule)">
      <summary>Removes an access rule from the Discretionary Access Control List (DACL) that is associated with the current <see cref="T:System.IO.Pipes.PipeSecurity"></see> object.</summary>
      <param name="rule">The access rule to remove.</param>
      <returns>true if the operation is successful; otherwise, false.</returns>
      <exception cref="T:System.ArgumentNullException">The <paramref name="rule">rule</paramref> parameter is null.</exception>
    </member>
    <member name="M:System.IO.Pipes.PipeSecurity.RemoveAccessRuleSpecific(System.IO.Pipes.PipeAccessRule)">
      <summary>Removes the specified access rule from the Discretionary Access Control List (DACL) that is associated with the current <see cref="T:System.IO.Pipes.PipeSecurity"></see> object.</summary>
      <param name="rule">The access rule to remove.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="rule">rule</paramref> parameter is null.</exception>
    </member>
    <member name="M:System.IO.Pipes.PipeSecurity.RemoveAuditRule(System.IO.Pipes.PipeAuditRule)">
      <summary>Removes an audit rule from the System Access Control List (SACL) that is associated with the current <see cref="T:System.IO.Pipes.PipeSecurity"></see> object.</summary>
      <param name="rule">The audit rule to remove.</param>
      <returns>true if the audit rule was removed; otherwise, false</returns>
      <exception cref="T:System.ArgumentNullException">The <paramref name="rule">rule</paramref> parameter is null.</exception>
    </member>
    <member name="M:System.IO.Pipes.PipeSecurity.RemoveAuditRuleAll(System.IO.Pipes.PipeAuditRule)">
      <summary>Removes all audit rules that have the same security identifier as the specified audit rule from the System Access Control List (SACL) that is associated with the current <see cref="T:System.IO.Pipes.PipeSecurity"></see> object.</summary>
      <param name="rule">The audit rule to remove.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="rule">rule</paramref> parameter is null.</exception>
    </member>
    <member name="M:System.IO.Pipes.PipeSecurity.RemoveAuditRuleSpecific(System.IO.Pipes.PipeAuditRule)">
      <summary>Removes the specified audit rule from the System Access Control List (SACL) that is associated with the current <see cref="T:System.IO.Pipes.PipeSecurity"></see> object.</summary>
      <param name="rule">The audit rule to remove.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="rule">rule</paramref> parameter is null.</exception>
    </member>
    <member name="M:System.IO.Pipes.PipeSecurity.ResetAccessRule(System.IO.Pipes.PipeAccessRule)">
      <summary>Removes all access rules in the Discretionary Access Control List (DACL) that is associated with the current <see cref="T:System.IO.Pipes.PipeSecurity"></see> object and then adds the specified access rule.</summary>
      <param name="rule">The access rule to add.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="rule">rule</paramref> parameter is null.</exception>
    </member>
    <member name="M:System.IO.Pipes.PipeSecurity.SetAccessRule(System.IO.Pipes.PipeAccessRule)">
      <summary>Sets an access rule in the Discretionary Access Control List (DACL) that is associated with the current <see cref="T:System.IO.Pipes.PipeSecurity"></see> object.</summary>
      <param name="rule">The rule to set.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="rule">rule</paramref> parameter is null.</exception>
    </member>
    <member name="M:System.IO.Pipes.PipeSecurity.SetAuditRule(System.IO.Pipes.PipeAuditRule)">
      <summary>Sets an audit rule in the System Access Control List (SACL) that is associated with the current <see cref="T:System.IO.Pipes.PipeSecurity"></see> object.</summary>
      <param name="rule">The rule to set.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="rule">rule</paramref> parameter is null.</exception>
    </member>
  </members>
</doc>