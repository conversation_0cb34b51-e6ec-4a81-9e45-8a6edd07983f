﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>System.Windows.Forms.Design</name>
  </assembly>
  <members>
    <member name="T:System.ComponentModel.Design.ComponentDesigner">
      <summary>Extends the design mode behavior of a component.</summary>
    </member>
    <member name="M:System.ComponentModel.Design.ComponentDesigner.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.ComponentModel.Design.ComponentDesigner" /> class.</summary>
    </member>
    <member name="P:System.ComponentModel.Design.ComponentDesigner.ActionLists">
      <summary>Gets the design-time action lists supported by the component associated with the designer.</summary>
      <returns>The design-time action lists supported by the component associated with the designer.</returns>
    </member>
    <member name="P:System.ComponentModel.Design.ComponentDesigner.AssociatedComponents">
      <summary>Gets the collection of components associated with the component managed by the designer.</summary>
      <returns>The components that are associated with the component managed by the designer.</returns>
    </member>
    <member name="P:System.ComponentModel.Design.ComponentDesigner.Component">
      <summary>Gets the component this designer is designing.</summary>
      <returns>The component managed by the designer.</returns>
    </member>
    <member name="M:System.ComponentModel.Design.ComponentDesigner.Dispose">
      <summary>Releases all resources used by the <see cref="T:System.ComponentModel.Design.ComponentDesigner" />.</summary>
    </member>
    <member name="M:System.ComponentModel.Design.ComponentDesigner.Dispose(System.Boolean)">
      <summary>Releases the unmanaged resources used by the <see cref="T:System.ComponentModel.Design.ComponentDesigner" /> and optionally releases the managed resources.</summary>
      <param name="disposing">
        <see langword="true" /> to release both managed and unmanaged resources; <see langword="false" /> to release only unmanaged resources.</param>
    </member>
    <member name="M:System.ComponentModel.Design.ComponentDesigner.DoDefaultAction">
      <summary>Creates a method signature in the source code file for the default event on the component and navigates the user's cursor to that location.</summary>
      <exception cref="T:System.ComponentModel.Design.CheckoutException">An attempt to check out a file that is checked into a source code management program failed.</exception>
    </member>
    <member name="M:System.ComponentModel.Design.ComponentDesigner.GetService(System.Type)">
      <summary>Attempts to retrieve the specified type of service from the design mode site of the designer's component.</summary>
      <param name="serviceType">The type of service to request.</param>
      <returns>An object implementing the requested service, or <see langword="null" /> if the service cannot be resolved.</returns>
    </member>
    <member name="P:System.ComponentModel.Design.ComponentDesigner.InheritanceAttribute">
      <summary>Gets an attribute that indicates the type of inheritance of the associated component.</summary>
      <returns>The <see cref="T:System.ComponentModel.InheritanceAttribute" /> for the associated component.</returns>
    </member>
    <member name="P:System.ComponentModel.Design.ComponentDesigner.Inherited">
      <summary>Gets a value indicating whether this component is inherited.</summary>
      <returns>
        <see langword="true" /> if the component is inherited; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.ComponentModel.Design.ComponentDesigner.Initialize(System.ComponentModel.IComponent)">
      <summary>Prepares the designer to view, edit, and design the specified component.</summary>
      <param name="component">The component for this designer.</param>
    </member>
    <member name="M:System.ComponentModel.Design.ComponentDesigner.InitializeExistingComponent(System.Collections.IDictionary)">
      <summary>Reinitializes an existing component.</summary>
      <param name="defaultValues">A name/value dictionary of default values to apply to properties. May be <see langword="null" /> if no default values are specified.</param>
    </member>
    <member name="M:System.ComponentModel.Design.ComponentDesigner.InitializeNewComponent(System.Collections.IDictionary)">
      <summary>Initializes a newly created component.</summary>
      <param name="defaultValues">A name/value dictionary of default values to apply to properties. May be <see langword="null" /> if no default values are specified.</param>
    </member>
    <member name="M:System.ComponentModel.Design.ComponentDesigner.InitializeNonDefault">
      <summary>Initializes the settings for an imported component that is already initialized to settings other than the defaults.</summary>
    </member>
    <member name="M:System.ComponentModel.Design.ComponentDesigner.InvokeGetInheritanceAttribute(System.ComponentModel.Design.ComponentDesigner)">
      <summary>Gets the <see cref="T:System.ComponentModel.InheritanceAttribute" /> of the specified <see cref="T:System.ComponentModel.Design.ComponentDesigner" />.</summary>
      <param name="toInvoke">The <see cref="T:System.ComponentModel.Design.ComponentDesigner" /> whose inheritance attribute to retrieve.</param>
      <returns>The <see cref="T:System.ComponentModel.InheritanceAttribute" /> of the specified designer.</returns>
    </member>
    <member name="M:System.ComponentModel.Design.ComponentDesigner.OnSetComponentDefaults">
      <summary>Sets the default properties for the component.</summary>
    </member>
    <member name="P:System.ComponentModel.Design.ComponentDesigner.ParentComponent">
      <summary>Gets the parent component for this designer.</summary>
      <returns>The parent component for this designer, or <see langword="null" /> if this designer is the root component.</returns>
    </member>
    <member name="M:System.ComponentModel.Design.ComponentDesigner.PostFilterAttributes(System.Collections.IDictionary)">
      <summary>Allows a designer to change or remove items from the set of attributes that it exposes through a <see cref="T:System.ComponentModel.TypeDescriptor" />.</summary>
      <param name="attributes">The attributes for the class of the component.</param>
    </member>
    <member name="M:System.ComponentModel.Design.ComponentDesigner.PostFilterEvents(System.Collections.IDictionary)">
      <summary>Allows a designer to change or remove items from the set of events that it exposes through a <see cref="T:System.ComponentModel.TypeDescriptor" />.</summary>
      <param name="events">The events for the class of the component.</param>
    </member>
    <member name="M:System.ComponentModel.Design.ComponentDesigner.PostFilterProperties(System.Collections.IDictionary)">
      <summary>Allows a designer to change or remove items from the set of properties that it exposes through a <see cref="T:System.ComponentModel.TypeDescriptor" />.</summary>
      <param name="properties">The properties for the class of the component.</param>
    </member>
    <member name="M:System.ComponentModel.Design.ComponentDesigner.PreFilterAttributes(System.Collections.IDictionary)">
      <summary>Allows a designer to add to the set of attributes that it exposes through a <see cref="T:System.ComponentModel.TypeDescriptor" />.</summary>
      <param name="attributes">The attributes for the class of the component.</param>
    </member>
    <member name="M:System.ComponentModel.Design.ComponentDesigner.PreFilterEvents(System.Collections.IDictionary)">
      <summary>Allows a designer to add to the set of events that it exposes through a <see cref="T:System.ComponentModel.TypeDescriptor" />.</summary>
      <param name="events">The events for the class of the component.</param>
    </member>
    <member name="M:System.ComponentModel.Design.ComponentDesigner.PreFilterProperties(System.Collections.IDictionary)">
      <summary>Allows a designer to add to the set of properties that it exposes through a <see cref="T:System.ComponentModel.TypeDescriptor" />.</summary>
      <param name="properties">The properties for the class of the component.</param>
    </member>
    <member name="M:System.ComponentModel.Design.ComponentDesigner.RaiseComponentChanged(System.ComponentModel.MemberDescriptor,System.Object,System.Object)">
      <summary>Notifies the <see cref="T:System.ComponentModel.Design.IComponentChangeService" /> that this component has been changed.</summary>
      <param name="member">A <see cref="T:System.ComponentModel.MemberDescriptor" /> that indicates the member that has been changed.</param>
      <param name="oldValue">The old value of the member.</param>
      <param name="newValue">The new value of the member.</param>
    </member>
    <member name="M:System.ComponentModel.Design.ComponentDesigner.RaiseComponentChanging(System.ComponentModel.MemberDescriptor)">
      <summary>Notifies the <see cref="T:System.ComponentModel.Design.IComponentChangeService" /> that this component is about to be changed.</summary>
      <param name="member">A <see cref="T:System.ComponentModel.MemberDescriptor" /> that indicates the member that is about to be changed.</param>
    </member>
    <member name="P:System.ComponentModel.Design.ComponentDesigner.ShadowProperties">
      <summary>Gets a collection of property values that override user settings.</summary>
      <returns>A <see cref="T:System.ComponentModel.Design.ComponentDesigner.ShadowPropertyCollection" /> that indicates the shadow properties of the design document.</returns>
    </member>
    <member name="T:System.ComponentModel.Design.ComponentDesigner.ShadowPropertyCollection">
      <summary>Represents a collection of shadow properties that should override inherited default or assigned values for specific properties. This class cannot be inherited.</summary>
    </member>
    <member name="M:System.ComponentModel.Design.ComponentDesigner.ShadowPropertyCollection.Contains(System.String)">
      <summary>Indicates whether a property matching the specified name exists in the collection.</summary>
      <param name="propertyName">The name of the property to check for in the collection.</param>
      <returns>
        <see langword="true" /> if the property exists in the collection; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.ComponentModel.Design.ComponentDesigner.ShadowPropertyCollection.Item(System.String)">
      <summary>Gets or sets the object at the specified index.</summary>
      <param name="propertyName">The name of the property to access in the collection.</param>
      <returns>The value of the specified property, if it exists in the collection. Otherwise, the value is retrieved from the current value of the nonshadowed property.</returns>
    </member>
    <member name="M:System.ComponentModel.Design.ComponentDesigner.System#ComponentModel#Design#IDesignerFilter#PostFilterAttributes(System.Collections.IDictionary)">
      <summary>For a description of this member, see the <see cref="M:System.ComponentModel.Design.IDesignerFilter.PostFilterAttributes(System.Collections.IDictionary)" /> method.</summary>
      <param name="attributes">The <see cref="T:System.Attribute" /> objects for the class of the component. The keys in the dictionary of attributes are the <see cref="P:System.Attribute.TypeId" /> values of the attributes.</param>
    </member>
    <member name="M:System.ComponentModel.Design.ComponentDesigner.System#ComponentModel#Design#IDesignerFilter#PostFilterEvents(System.Collections.IDictionary)">
      <summary>For a description of this member, see the <see cref="M:System.ComponentModel.Design.IDesignerFilter.PostFilterEvents(System.Collections.IDictionary)" /> method.</summary>
      <param name="events">The <see cref="T:System.ComponentModel.EventDescriptor" /> objects that represent the events of the class of the component. The keys in the dictionary of events are event names.</param>
    </member>
    <member name="M:System.ComponentModel.Design.ComponentDesigner.System#ComponentModel#Design#IDesignerFilter#PostFilterProperties(System.Collections.IDictionary)">
      <summary>For a description of this member, see the <see cref="M:System.ComponentModel.Design.IDesignerFilter.PostFilterProperties(System.Collections.IDictionary)" /> method.</summary>
      <param name="properties">The <see cref="T:System.ComponentModel.PropertyDescriptor" /> objects that represent the properties of the class of the component. The keys in the dictionary of properties are property names.</param>
    </member>
    <member name="M:System.ComponentModel.Design.ComponentDesigner.System#ComponentModel#Design#IDesignerFilter#PreFilterAttributes(System.Collections.IDictionary)">
      <summary>For a description of this member, see the <see cref="M:System.ComponentModel.Design.IDesignerFilter.PreFilterAttributes(System.Collections.IDictionary)" /> method.</summary>
      <param name="attributes">The <see cref="T:System.Attribute" /> objects for the class of the component. The keys in the dictionary of attributes are the <see cref="P:System.Attribute.TypeId" /> values of the attributes.</param>
    </member>
    <member name="M:System.ComponentModel.Design.ComponentDesigner.System#ComponentModel#Design#IDesignerFilter#PreFilterEvents(System.Collections.IDictionary)">
      <summary>For a description of this member, see the <see cref="M:System.ComponentModel.Design.IDesignerFilter.PreFilterEvents(System.Collections.IDictionary)" /> method.</summary>
      <param name="events">The <see cref="T:System.ComponentModel.EventDescriptor" /> objects that represent the events of the class of the component. The keys in the dictionary of events are event names.</param>
    </member>
    <member name="M:System.ComponentModel.Design.ComponentDesigner.System#ComponentModel#Design#IDesignerFilter#PreFilterProperties(System.Collections.IDictionary)">
      <summary>For a description of this member, see the <see cref="M:System.ComponentModel.Design.IDesignerFilter.PreFilterProperties(System.Collections.IDictionary)" /> method.</summary>
      <param name="properties">The <see cref="T:System.ComponentModel.PropertyDescriptor" /> objects that represent the properties of the class of the component. The keys in the dictionary of properties are property names.</param>
    </member>
    <member name="P:System.ComponentModel.Design.ComponentDesigner.System#ComponentModel#Design#ITreeDesigner#Children">
      <summary>For a description of this member, see the <see cref="P:System.ComponentModel.Design.ITreeDesigner.Children" /> property.</summary>
      <returns>An <see cref="T:System.Collections.ICollection" /> that contains the collection of <see cref="T:System.ComponentModel.Design.IDesigner" /> designers contained in the current parent designer.</returns>
    </member>
    <member name="P:System.ComponentModel.Design.ComponentDesigner.System#ComponentModel#Design#ITreeDesigner#Parent">
      <summary>For a description of this member, see the <see cref="P:System.ComponentModel.Design.ITreeDesigner.Parent" /> property.</summary>
      <returns>An <see cref="T:System.ComponentModel.Design.IDesigner" /> representing the parent designer, or <see langword="null" /> if there is no parent.</returns>
    </member>
    <member name="P:System.ComponentModel.Design.ComponentDesigner.Verbs">
      <summary>Gets the design-time verbs supported by the component that is associated with the designer.</summary>
      <returns>A <see cref="T:System.ComponentModel.Design.DesignerVerbCollection" /> of <see cref="T:System.ComponentModel.Design.DesignerVerb" /> objects, or <see langword="null" /> if no designer verbs are available. This default implementation always returns <see langword="null" />.</returns>
    </member>
    <member name="T:System.ComponentModel.Design.DesignerActionItem">
      <summary>Provides the base class for types that represent a panel item on a smart tag panel.</summary>
    </member>
    <member name="M:System.ComponentModel.Design.DesignerActionItem.#ctor(System.String,System.String,System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.ComponentModel.Design.DesignerActionItem" /> class.</summary>
      <param name="displayName">The panel text for this item.</param>
      <param name="category">The case-sensitive <see cref="T:System.String" /> that defines the groupings of panel entries.</param>
      <param name="description">Supplemental text for this item, potentially used in ToolTips or the status bar.</param>
    </member>
    <member name="P:System.ComponentModel.Design.DesignerActionItem.AllowAssociate">
      <summary>Gets or sets a value indicating whether to allow this item to be placed into a group of items that have the same <see cref="P:System.ComponentModel.Design.DesignerActionItem.Category" /> property value.</summary>
      <returns>
        <see langword="true" /> if the item can be grouped; otherwise, <see langword="false" />. The default is <see langword="false" />.</returns>
    </member>
    <member name="P:System.ComponentModel.Design.DesignerActionItem.Category">
      <summary>Gets the group name for an item.</summary>
      <returns>A string that represents the group that the item is a member of.</returns>
    </member>
    <member name="P:System.ComponentModel.Design.DesignerActionItem.Description">
      <summary>Gets the supplemental text for the item.</summary>
      <returns>A <see cref="T:System.String" /> that contains the descriptive text for the item.</returns>
    </member>
    <member name="P:System.ComponentModel.Design.DesignerActionItem.DisplayName">
      <summary>Gets the text for this item.</summary>
      <returns>A <see cref="T:System.String" /> that contains the display text for the item.</returns>
    </member>
    <member name="P:System.ComponentModel.Design.DesignerActionItem.Properties">
      <summary>Gets a reference to a collection that can be used to store programmer-defined key/value pairs.</summary>
      <returns>A collection that implements <see cref="T:System.Collections.IDictionary" />.</returns>
    </member>
    <member name="P:System.ComponentModel.Design.DesignerActionItem.ShowInSourceView">
      <summary>Gets or sets a value that indicates whether this item appears in source code view.</summary>
      <returns>
        <see langword="true" /> if this item appears in source code view; otherwise, <see langword="false" />. The default is <see langword="true" />.</returns>
    </member>
    <member name="T:System.ComponentModel.Design.DesignerActionItemCollection">
      <summary>Represents a collection of <see cref="T:System.ComponentModel.Design.DesignerActionItem" /> objects.</summary>
    </member>
    <member name="M:System.ComponentModel.Design.DesignerActionItemCollection.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.ComponentModel.Design.DesignerActionItemCollection" /> class.</summary>
    </member>
    <member name="M:System.ComponentModel.Design.DesignerActionItemCollection.Add(System.ComponentModel.Design.DesignerActionItem)">
      <summary>Adds the supplied <see cref="T:System.ComponentModel.Design.DesignerActionItem" /> to the current collection.</summary>
      <param name="value">The <see cref="T:System.ComponentModel.Design.DesignerActionItem" /> to add.</param>
      <returns>The <see cref="T:System.ComponentModel.Design.DesignerActionItemCollection" /> index at which the value has been added.</returns>
    </member>
    <member name="M:System.ComponentModel.Design.DesignerActionItemCollection.Contains(System.ComponentModel.Design.DesignerActionItem)">
      <summary>Determines whether the <see cref="T:System.ComponentModel.Design.DesignerActionItemCollection" /> contains a specific element.</summary>
      <param name="value">The <see cref="T:System.ComponentModel.Design.DesignerActionItem" /> to locate in the <see cref="T:System.ComponentModel.Design.DesignerActionItemCollection" />.</param>
      <returns>
        <see langword="true" /> if the <see cref="T:System.ComponentModel.Design.DesignerActionItemCollection" /> contains the specified value; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.ComponentModel.Design.DesignerActionItemCollection.CopyTo(System.ComponentModel.Design.DesignerActionItem[],System.Int32)">
      <summary>Copies the elements of the current collection into the supplied array, starting at the specified array index.</summary>
      <param name="array">The one-dimensional <see cref="T:System.ComponentModel.Design.DesignerActionItem" /> array that is the destination of the elements copied from the current collection. The array must have zero-based indexing.</param>
      <param name="index">The zero-based index in <paramref name="array" /> at which copying begins.</param>
    </member>
    <member name="M:System.ComponentModel.Design.DesignerActionItemCollection.IndexOf(System.ComponentModel.Design.DesignerActionItem)">
      <summary>Determines the index of a specific item in the collection.</summary>
      <param name="value">The <see cref="T:System.ComponentModel.Design.DesignerActionItem" /> to locate in the collection.</param>
      <returns>The zero-based index of the first occurrence of <paramref name="value" /> within the entire <see cref="T:System.ComponentModel.Design.DesignerActionItemCollection" />, if found; otherwise, -1.</returns>
    </member>
    <member name="M:System.ComponentModel.Design.DesignerActionItemCollection.Insert(System.Int32,System.ComponentModel.Design.DesignerActionItem)">
      <summary>Inserts an element into the <see cref="T:System.ComponentModel.Design.DesignerActionItemCollection" /> at the specified index.</summary>
      <param name="index">The zero-based index at which <paramref name="value" /> should be inserted.</param>
      <param name="value">The <see cref="T:System.ComponentModel.Design.DesignerActionItem" /> to insert.</param>
    </member>
    <member name="P:System.ComponentModel.Design.DesignerActionItemCollection.Item(System.Int32)">
      <summary>Gets or sets the element at the specified index.</summary>
      <param name="index">The zero-based index of the element.</param>
      <returns>The <see cref="T:System.ComponentModel.Design.DesignerActionItem" /> at the specified index.</returns>
    </member>
    <member name="M:System.ComponentModel.Design.DesignerActionItemCollection.Remove(System.ComponentModel.Design.DesignerActionItem)">
      <summary>Removes the first occurrence of a specific object from the <see cref="T:System.ComponentModel.Design.DesignerActionItemCollection" />.</summary>
      <param name="value">The <see cref="T:System.ComponentModel.Design.DesignerActionItem" /> to remove from the <see cref="T:System.ComponentModel.Design.DesignerActionItemCollection" />.</param>
    </member>
    <member name="T:System.ComponentModel.Design.DesignerActionList">
      <summary>Provides the base class for types that define a list of items used to create a smart tag panel.</summary>
    </member>
    <member name="M:System.ComponentModel.Design.DesignerActionList.#ctor(System.ComponentModel.IComponent)">
      <summary>Initializes a new instance of the <see cref="T:System.ComponentModel.Design.DesignerActionList" /> class.</summary>
      <param name="component">A component related to the <see cref="T:System.ComponentModel.Design.DesignerActionList" />.</param>
    </member>
    <member name="P:System.ComponentModel.Design.DesignerActionList.AutoShow">
      <summary>Gets or sets a value indicating whether the smart tag panel should automatically be displayed when it is created.</summary>
      <returns>
        <see langword="true" /> if the panel should be shown when the owning component is created; otherwise, <see langword="false" />. The default is <see langword="false" />.</returns>
    </member>
    <member name="P:System.ComponentModel.Design.DesignerActionList.Component">
      <summary>Gets the component related to <see cref="T:System.ComponentModel.Design.DesignerActionList" />.</summary>
      <returns>A component related to <see cref="T:System.ComponentModel.Design.DesignerActionList" />.</returns>
    </member>
    <member name="M:System.ComponentModel.Design.DesignerActionList.GetService(System.Type)">
      <summary>Returns an object that represents a service provided by the component associated with the <see cref="T:System.ComponentModel.Design.DesignerActionList" />.</summary>
      <param name="serviceType">A service provided by the <see cref="T:System.ComponentModel.Component" />.</param>
      <returns>An <see cref="T:System.Object" /> that represents a service provided by the <see cref="T:System.ComponentModel.Component" />. This value is <see langword="null" /> if the <see cref="T:System.ComponentModel.Component" /> does not provide the specified service.</returns>
    </member>
    <member name="M:System.ComponentModel.Design.DesignerActionList.GetSortedActionItems">
      <summary>Returns the collection of <see cref="T:System.ComponentModel.Design.DesignerActionItem" /> objects contained in the list.</summary>
      <returns>A <see cref="T:System.ComponentModel.Design.DesignerActionItem" /> array that contains the items in this list.</returns>
    </member>
    <member name="T:System.ComponentModel.Design.DesignerActionListCollection">
      <summary>Represents a collection of <see cref="T:System.ComponentModel.Design.DesignerActionList" /> objects.</summary>
    </member>
    <member name="M:System.ComponentModel.Design.DesignerActionListCollection.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.ComponentModel.Design.DesignerActionListCollection" /> class with default settings.</summary>
    </member>
    <member name="M:System.ComponentModel.Design.DesignerActionListCollection.#ctor(System.ComponentModel.Design.DesignerActionList[])">
      <summary>Initializes a new instance of the <see cref="T:System.ComponentModel.Design.DesignerActionListCollection" /> class with the specified panel items.</summary>
      <param name="value">The array of <see cref="T:System.ComponentModel.Design.DesignerActionList" /> objects to populate the collection.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.ComponentModel.Design.DesignerActionListCollection.Add(System.ComponentModel.Design.DesignerActionList)">
      <summary>Adds the supplied <see cref="T:System.ComponentModel.Design.DesignerActionList" /> to the current collection.</summary>
      <param name="value">The <see cref="T:System.ComponentModel.Design.DesignerActionList" /> to add.</param>
      <returns>The position into which the new element is inserted into the collection's internal list.</returns>
    </member>
    <member name="M:System.ComponentModel.Design.DesignerActionListCollection.AddRange(System.ComponentModel.Design.DesignerActionList[])">
      <summary>Adds the elements of the supplied <see cref="T:System.ComponentModel.Design.DesignerActionList" /> array to the end of the current collection.</summary>
      <param name="value">The array of <see cref="T:System.ComponentModel.Design.DesignerActionList" /> objects to add.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.ComponentModel.Design.DesignerActionListCollection.AddRange(System.ComponentModel.Design.DesignerActionListCollection)">
      <summary>Adds the elements of the supplied <see cref="T:System.ComponentModel.Design.DesignerActionListCollection" /> to the end of the current collection.</summary>
      <param name="value">The <see cref="T:System.ComponentModel.Design.DesignerActionListCollection" /> to add.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.ComponentModel.Design.DesignerActionListCollection.Contains(System.ComponentModel.Design.DesignerActionList)">
      <summary>Indicates whether the collection contains a specific value.</summary>
      <param name="value">The <see cref="T:System.ComponentModel.Design.DesignerActionList" /> to search for.</param>
      <returns>
        <see langword="true" /> if the collection contains the specified value; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.ComponentModel.Design.DesignerActionListCollection.CopyTo(System.ComponentModel.Design.DesignerActionList[],System.Int32)">
      <summary>Copies the elements of the current collection into the supplied array, starting at the specified array index.</summary>
      <param name="array">The one-dimensional array of type <see cref="T:System.ComponentModel.Design.DesignerActionList" /> that is the destination of the elements copied from the current collection. The array must have zero-based indexing.</param>
      <param name="index">The zero-based index in <paramref name="array" /> at which copying begins.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> is less than 0.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="array" /> is multidimensional.  
-or-  
The number of elements in the current collection is greater than the available space from <paramref name="index" /> to the end of the destination array.</exception>
      <exception cref="T:System.InvalidCastException">A problem occurred casting the elements of the current collection to the type of the destination array, perhaps as the result of a failed downcast.</exception>
    </member>
    <member name="M:System.ComponentModel.Design.DesignerActionListCollection.IndexOf(System.ComponentModel.Design.DesignerActionList)">
      <summary>Determines the index of a specific item in the collection.</summary>
      <param name="value">The <see cref="T:System.ComponentModel.Design.DesignerActionList" /> to locate in the collection.</param>
      <returns>The index of <paramref name="value" /> if found in the internal list; otherwise, -1.</returns>
    </member>
    <member name="M:System.ComponentModel.Design.DesignerActionListCollection.Insert(System.Int32,System.ComponentModel.Design.DesignerActionList)">
      <summary>Inserts the supplied <see cref="T:System.ComponentModel.Design.DesignerActionList" /> into the collection at the specified position.</summary>
      <param name="index">The zero-based index at which <paramref name="value" /> should be inserted.</param>
      <param name="value">The <see cref="T:System.ComponentModel.Design.DesignerActionList" /> to insert into the collection.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> is less than 0 or greater than the count of elements in the current collection.</exception>
    </member>
    <member name="P:System.ComponentModel.Design.DesignerActionListCollection.Item(System.Int32)">
      <summary>Gets or sets the element at the specified index.</summary>
      <param name="index">The zero-based index of the element.</param>
      <returns>The <see cref="T:System.ComponentModel.Design.DesignerActionList" /> at the specified index.</returns>
    </member>
    <member name="M:System.ComponentModel.Design.DesignerActionListCollection.OnValidate(System.Object)">
      <summary>Performs additional custom processes when validating a value.</summary>
      <param name="value">The object to validate.</param>
    </member>
    <member name="M:System.ComponentModel.Design.DesignerActionListCollection.Remove(System.ComponentModel.Design.DesignerActionList)">
      <summary>Removes the first occurrence of a specific <see cref="T:System.ComponentModel.Design.DesignerActionList" /> from the collection.</summary>
      <param name="value">The <see cref="T:System.ComponentModel.Design.DesignerActionList" /> to remove from the current collection.</param>
    </member>
    <member name="T:System.ComponentModel.Design.DesignerActionListsChangedEventArgs">
      <summary>Provides data for the <see cref="E:System.ComponentModel.Design.DesignerActionService.DesignerActionListsChanged" /> event.</summary>
    </member>
    <member name="M:System.ComponentModel.Design.DesignerActionListsChangedEventArgs.#ctor(System.Object,System.ComponentModel.Design.DesignerActionListsChangedType,System.ComponentModel.Design.DesignerActionListCollection)">
      <summary>Initializes a new instance of the <see cref="T:System.ComponentModel.Design.DesignerActionListsChangedEventArgs" /> class.</summary>
      <param name="relatedObject">The object that is associated with the collection.</param>
      <param name="changeType">A value that specifies whether a <see cref="T:System.ComponentModel.Design.DesignerActionList" /> has been added or removed from the collection.</param>
      <param name="actionLists">The collection of list elements after the action has been applied.</param>
    </member>
    <member name="P:System.ComponentModel.Design.DesignerActionListsChangedEventArgs.ActionLists">
      <summary>Gets the collection of <see cref="T:System.ComponentModel.Design.DesignerActionList" /> objects associated with this event.</summary>
      <returns>A <see cref="T:System.ComponentModel.Design.DesignerActionListCollection" /> that represents the current state of the collection.</returns>
    </member>
    <member name="P:System.ComponentModel.Design.DesignerActionListsChangedEventArgs.ChangeType">
      <summary>Gets a flag indicating whether an element has been added or removed from the collection of <see cref="T:System.ComponentModel.Design.DesignerActionList" /> objects.</summary>
      <returns>A <see cref="T:System.ComponentModel.Design.DesignerActionListsChangedType" /> that indicates the type of change.</returns>
    </member>
    <member name="P:System.ComponentModel.Design.DesignerActionListsChangedEventArgs.RelatedObject">
      <summary>Gets the object that is associated with the collection of <see cref="T:System.ComponentModel.Design.DesignerActionList" /> objects.</summary>
      <returns>The <see cref="T:System.Object" /> associated with the managed <see cref="T:System.ComponentModel.Design.DesignerActionListCollection" />.</returns>
    </member>
    <member name="T:System.ComponentModel.Design.DesignerActionListsChangedEventHandler">
      <summary>Represents the method that will handle the <see cref="E:System.ComponentModel.Design.DesignerActionService.DesignerActionListsChanged" /> event of a <see cref="T:System.ComponentModel.Design.DesignerActionService" />. This class cannot be inherited.</summary>
      <param name="sender">The source of the event.</param>
      <param name="e">A <see cref="T:System.ComponentModel.Design.DesignerActionListsChangedEventArgs" /> that contains the event data.</param>
    </member>
    <member name="T:System.ComponentModel.Design.DesignerActionListsChangedType">
      <summary>Specifies the type of change occurring in a collection of <see cref="T:System.ComponentModel.Design.DesignerActionList" /> objects.</summary>
    </member>
    <member name="F:System.ComponentModel.Design.DesignerActionListsChangedType.ActionListsAdded">
      <summary>One or more <see cref="T:System.ComponentModel.Design.DesignerActionList" /> objects have been added to the collection.</summary>
    </member>
    <member name="F:System.ComponentModel.Design.DesignerActionListsChangedType.ActionListsRemoved">
      <summary>One or more <see cref="T:System.ComponentModel.Design.DesignerActionList" /> objects have been removed from the collection.</summary>
    </member>
    <member name="T:System.ComponentModel.Design.DesignerActionMethodItem">
      <summary>Represents a smart tag panel item that is associated with a method in a class derived from <see cref="T:System.ComponentModel.Design.DesignerActionList" />.</summary>
    </member>
    <member name="M:System.ComponentModel.Design.DesignerActionMethodItem.#ctor(System.ComponentModel.Design.DesignerActionList,System.String,System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.ComponentModel.Design.DesignerActionMethodItem" /> class, with the specified method and display names.</summary>
      <param name="actionList">The <see cref="T:System.ComponentModel.Design.DesignerActionList" /> that contains the method this item is associated with.</param>
      <param name="memberName">The case-sensitive name of the method in the class derived from <see cref="T:System.ComponentModel.Design.DesignerActionList" /> to invoke through the panel item.</param>
      <param name="displayName">The panel text for this item.</param>
    </member>
    <member name="M:System.ComponentModel.Design.DesignerActionMethodItem.#ctor(System.ComponentModel.Design.DesignerActionList,System.String,System.String,System.Boolean)">
      <summary>Initializes a new instance of the <see cref="T:System.ComponentModel.Design.DesignerActionMethodItem" /> class, with the specified method and display names, and a flag that indicates whether the item should appear in other user interface contexts.</summary>
      <param name="actionList">The <see cref="T:System.ComponentModel.Design.DesignerActionList" /> that contains the method this item is associated with.</param>
      <param name="memberName">The case-sensitive name of the method in the class derived from <see cref="T:System.ComponentModel.Design.DesignerActionList" /> to invoke through the panel item.</param>
      <param name="displayName">The panel text for this item.</param>
      <param name="includeAsDesignerVerb">A flag that specifies whether to also treat the associated method as a designer verb.</param>
    </member>
    <member name="M:System.ComponentModel.Design.DesignerActionMethodItem.#ctor(System.ComponentModel.Design.DesignerActionList,System.String,System.String,System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.ComponentModel.Design.DesignerActionMethodItem" /> class, with the specified method, display, and category names.</summary>
      <param name="actionList">The <see cref="T:System.ComponentModel.Design.DesignerActionList" /> that contains the method this item is associated with.</param>
      <param name="memberName">The case-sensitive name of the method in the class derived from <see cref="T:System.ComponentModel.Design.DesignerActionList" /> to invoke through the panel item.</param>
      <param name="displayName">The panel text for this item.</param>
      <param name="category">The case-sensitive <see cref="T:System.String" /> used to group similar items on the panel.</param>
    </member>
    <member name="M:System.ComponentModel.Design.DesignerActionMethodItem.#ctor(System.ComponentModel.Design.DesignerActionList,System.String,System.String,System.String,System.Boolean)">
      <summary>Initializes a new instance of the <see cref="T:System.ComponentModel.Design.DesignerActionMethodItem" /> class, with the specified method, display, and category names, and a flag that indicates whether the item should appear in other user interface contexts.</summary>
      <param name="actionList">The <see cref="T:System.ComponentModel.Design.DesignerActionList" /> that contains the method this item is associated with.</param>
      <param name="memberName">The case-sensitive name of the method in the class derived from <see cref="T:System.ComponentModel.Design.DesignerActionList" /> to invoke through the panel item.</param>
      <param name="displayName">The panel text for this item.</param>
      <param name="category">The case-sensitive <see cref="T:System.String" /> used to group similar items on the panel.</param>
      <param name="includeAsDesignerVerb">A flag that specifies whether to also treat the associated method as a designer verb for the associated component.</param>
    </member>
    <member name="M:System.ComponentModel.Design.DesignerActionMethodItem.#ctor(System.ComponentModel.Design.DesignerActionList,System.String,System.String,System.String,System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.ComponentModel.Design.DesignerActionMethodItem" /> class, with the specified method and category names, and display and description text.</summary>
      <param name="actionList">The <see cref="T:System.ComponentModel.Design.DesignerActionList" /> that contains the method this item is associated with.</param>
      <param name="memberName">The case-sensitive name of the method in the class derived from <see cref="T:System.ComponentModel.Design.DesignerActionList" /> to invoke through the panel item.</param>
      <param name="displayName">The panel text for this item.</param>
      <param name="category">The case-sensitive <see cref="T:System.String" /> used to group similar items on the panel.</param>
      <param name="description">Supplemental text for this item, used in ToolTips or the status bar.</param>
    </member>
    <member name="M:System.ComponentModel.Design.DesignerActionMethodItem.#ctor(System.ComponentModel.Design.DesignerActionList,System.String,System.String,System.String,System.String,System.Boolean)">
      <summary>Initializes a new instance of the <see cref="T:System.ComponentModel.Design.DesignerActionMethodItem" /> class, with the specified method and category names, display and description text, and a flag that indicates whether the item should appear in other user interface contexts.</summary>
      <param name="actionList">The <see cref="T:System.ComponentModel.Design.DesignerActionList" /> that contains the method this item is associated with.</param>
      <param name="memberName">The case-sensitive name of the method in the class derived from <see cref="T:System.ComponentModel.Design.DesignerActionList" /> to invoke through the panel item.</param>
      <param name="displayName">The panel text for this item.</param>
      <param name="category">The case-sensitive <see cref="T:System.String" /> used to group similar items on the panel.</param>
      <param name="description">Supplemental text for this item, used in ToolTips or the status bar.</param>
      <param name="includeAsDesignerVerb">A flag that specifies whether to also treat the associated method as a designer verb for the associated component.</param>
    </member>
    <member name="P:System.ComponentModel.Design.DesignerActionMethodItem.IncludeAsDesignerVerb">
      <summary>Gets a value that indicates the <see cref="T:System.ComponentModel.Design.DesignerActionMethodItem" /> should appear in other user interface contexts.</summary>
      <returns>
        <see langword="true" /> if the item is to be used in shortcut menus; otherwise, <see langword="false" />. The default is <see langword="false" />.</returns>
    </member>
    <member name="M:System.ComponentModel.Design.DesignerActionMethodItem.Invoke">
      <summary>Programmatically executes the method associated with the <see cref="T:System.ComponentModel.Design.DesignerActionMethodItem" />.</summary>
      <exception cref="T:System.InvalidOperationException">The method, named in <see cref="P:System.ComponentModel.Design.DesignerActionMethodItem.MemberName" /> cannot be found.</exception>
    </member>
    <member name="P:System.ComponentModel.Design.DesignerActionMethodItem.MemberName">
      <summary>Gets the name of the method that this <see cref="T:System.ComponentModel.Design.DesignerActionMethodItem" /> is associated with.</summary>
      <returns>A <see cref="T:System.String" /> that contains the name of the associated method.</returns>
    </member>
    <member name="P:System.ComponentModel.Design.DesignerActionMethodItem.RelatedComponent">
      <summary>Gets or sets a component that contributes its <see cref="T:System.ComponentModel.Design.DesignerActionMethodItem" /> objects to the current panel.</summary>
      <returns>The contributing component, which should have an associated designer that supplies items.</returns>
    </member>
    <member name="T:System.ComponentModel.Design.DesignerActionPropertyItem">
      <summary>Represents a panel item that is associated with a property in a class derived from <see cref="T:System.ComponentModel.Design.DesignerActionList" />. This class cannot be inherited.</summary>
    </member>
    <member name="M:System.ComponentModel.Design.DesignerActionPropertyItem.#ctor(System.String,System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.ComponentModel.Design.DesignerActionPropertyItem" /> class, with the specified property and display names.</summary>
      <param name="memberName">The case-sensitive name of the property associated with this panel item.</param>
      <param name="displayName">The panel text for this item.</param>
    </member>
    <member name="M:System.ComponentModel.Design.DesignerActionPropertyItem.#ctor(System.String,System.String,System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.ComponentModel.Design.DesignerActionPropertyItem" /> class, with the specified property and category names, and display text.</summary>
      <param name="memberName">The case-sensitive name of the property associated with this panel item.</param>
      <param name="displayName">The panel text for this item.</param>
      <param name="category">The case-sensitive <see cref="T:System.String" /> used to group similar items on the panel.</param>
    </member>
    <member name="M:System.ComponentModel.Design.DesignerActionPropertyItem.#ctor(System.String,System.String,System.String,System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.ComponentModel.Design.DesignerActionPropertyItem" /> class, with the specified property and category names, and display and description text.</summary>
      <param name="memberName">The case-sensitive name of the property associated with this panel item.</param>
      <param name="displayName">The panel text for this item.</param>
      <param name="category">The case-sensitive <see cref="T:System.String" /> used to group similar items on the panel.</param>
      <param name="description">Supplemental text for this item, used in ToolTips or the status bar.</param>
    </member>
    <member name="P:System.ComponentModel.Design.DesignerActionPropertyItem.MemberName">
      <summary>Gets the name of the property that this item is associated with.</summary>
      <returns>A <see cref="T:System.String" /> that contains the name of the associated property.</returns>
    </member>
    <member name="P:System.ComponentModel.Design.DesignerActionPropertyItem.RelatedComponent">
      <summary>Gets or sets a component that contributes its items to the current panel.</summary>
      <returns>The contributing component, which should have an associated designer that supplies <see cref="T:System.ComponentModel.Design.DesignerActionItem" /> objects.</returns>
    </member>
    <member name="T:System.ComponentModel.Design.DesignerActionService">
      <summary>Establishes a design-time service that manages the collection of <see cref="T:System.ComponentModel.Design.DesignerActionItem" /> objects for components.</summary>
    </member>
    <member name="M:System.ComponentModel.Design.DesignerActionService.#ctor(System.IServiceProvider)">
      <summary>Initializes a new instance of the <see cref="T:System.ComponentModel.Design.DesignerActionService" /> class.</summary>
      <param name="serviceProvider">The service provider for the current design-time environment.</param>
    </member>
    <member name="M:System.ComponentModel.Design.DesignerActionService.Add(System.ComponentModel.IComponent,System.ComponentModel.Design.DesignerActionList)">
      <summary>Adds a <see cref="T:System.ComponentModel.Design.DesignerActionList" /> to the current collection of managed smart tags.</summary>
      <param name="comp">The <see cref="T:System.ComponentModel.IComponent" /> to associate the smart tags with.</param>
      <param name="actionList">The <see cref="T:System.ComponentModel.Design.DesignerActionList" /> that contains the new smart tag items to be added.</param>
      <exception cref="T:System.ArgumentNullException">One or both of the parameters are <see langword="null" />.</exception>
    </member>
    <member name="M:System.ComponentModel.Design.DesignerActionService.Add(System.ComponentModel.IComponent,System.ComponentModel.Design.DesignerActionListCollection)">
      <summary>Adds a <see cref="T:System.ComponentModel.Design.DesignerActionListCollection" /> to the current collection of managed smart tags.</summary>
      <param name="comp">The <see cref="T:System.ComponentModel.IComponent" /> to associate the smart tags with.</param>
      <param name="designerActionListCollection">The <see cref="T:System.ComponentModel.Design.DesignerActionListCollection" /> that contains the new smart tag items to be added.</param>
      <exception cref="T:System.ArgumentNullException">One or both of the parameters are <see langword="null" />.</exception>
    </member>
    <member name="M:System.ComponentModel.Design.DesignerActionService.Clear">
      <summary>Releases all components from management and clears all push-model smart tag lists.</summary>
    </member>
    <member name="M:System.ComponentModel.Design.DesignerActionService.Contains(System.ComponentModel.IComponent)">
      <summary>Determines whether the current smart tag service manages the action lists for the specified component.</summary>
      <param name="comp">The <see cref="T:System.ComponentModel.IComponent" /> to search for.</param>
      <returns>
        <see langword="true" /> if the component is managed by the current service; otherwise, <see langword="false" />.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="comp" /> is <see langword="null" />.</exception>
    </member>
    <member name="E:System.ComponentModel.Design.DesignerActionService.DesignerActionListsChanged">
      <summary>Occurs when a <see cref="T:System.ComponentModel.Design.DesignerActionList" /> is removed or added for any component.</summary>
    </member>
    <member name="M:System.ComponentModel.Design.DesignerActionService.Dispose">
      <summary>Releases all resources used by the <see cref="T:System.ComponentModel.Design.DesignerActionService" /> class.</summary>
    </member>
    <member name="M:System.ComponentModel.Design.DesignerActionService.Dispose(System.Boolean)">
      <summary>Releases the unmanaged resources used by the <see cref="T:System.ComponentModel.Design.DesignerActionService" /> and optionally releases the managed resources.</summary>
      <param name="disposing">
        <see langword="true" /> to release both managed and unmanaged resources; <see langword="false" /> to release only unmanaged resources.</param>
    </member>
    <member name="M:System.ComponentModel.Design.DesignerActionService.GetComponentActions(System.ComponentModel.IComponent)">
      <summary>Returns the collection of smart tag item lists associated with a component.</summary>
      <param name="component">The component that the smart tags are associated with.</param>
      <returns>The collection of smart tags for the specified component.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="comp" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.ComponentModel.Design.DesignerActionService.GetComponentActions(System.ComponentModel.IComponent,System.Windows.Forms.Design.ComponentActionsType)">
      <param name="component" />
      <param name="type" />
    </member>
    <member name="M:System.ComponentModel.Design.DesignerActionService.GetComponentDesignerActions(System.ComponentModel.IComponent,System.ComponentModel.Design.DesignerActionListCollection)">
      <summary>Retrieves the pull-model smart tags associated with a component.</summary>
      <param name="component">The component that the smart tags are associated with.</param>
      <param name="actionLists">The collection to add the associated smart tags to.</param>
      <exception cref="T:System.ArgumentNullException">One or both of the parameters are <see langword="null" />.</exception>
    </member>
    <member name="M:System.ComponentModel.Design.DesignerActionService.GetComponentServiceActions(System.ComponentModel.IComponent,System.ComponentModel.Design.DesignerActionListCollection)">
      <summary>Retrieves the push-model smart tags associated with a component.</summary>
      <param name="component">The component that the smart tags are associated with.</param>
      <param name="actionLists">The collection to add the associated smart tags to.</param>
      <exception cref="T:System.ArgumentNullException">One or both of the parameters are <see langword="null" />.</exception>
    </member>
    <member name="M:System.ComponentModel.Design.DesignerActionService.Remove(System.ComponentModel.Design.DesignerActionList)">
      <summary>Removes the specified smart tag list from all components managed by the current service.</summary>
      <param name="actionList">The list of smart tags to be removed.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="actionList" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.ComponentModel.Design.DesignerActionService.Remove(System.ComponentModel.IComponent)">
      <summary>Removes all the smart tag lists associated with the specified component.</summary>
      <param name="comp">The component to disassociate the smart tags from.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="comp" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.ComponentModel.Design.DesignerActionService.Remove(System.ComponentModel.IComponent,System.ComponentModel.Design.DesignerActionList)">
      <summary>Removes the specified smart tag list from the specified component.</summary>
      <param name="comp">The component to disassociate the smart tags from.</param>
      <param name="actionList">The smart tag list to remove.</param>
      <exception cref="T:System.ArgumentNullException">One or both of the parameters are <see langword="null" />.</exception>
    </member>
    <member name="T:System.ComponentModel.Design.DesignerActionUIService">
      <summary>Manages the user interface (UI) for a smart tag panel. This class cannot be inherited.</summary>
    </member>
    <member name="E:System.ComponentModel.Design.DesignerActionUIService.DesignerActionUIStateChange">
      <summary>Occurs when a request is made to show or hide a smart tag panel.</summary>
    </member>
    <member name="M:System.ComponentModel.Design.DesignerActionUIService.Dispose">
      <summary>Releases all resources used by the <see cref="T:System.ComponentModel.Design.DesignerActionUIService" /> class.</summary>
    </member>
    <member name="M:System.ComponentModel.Design.DesignerActionUIService.HideUI(System.ComponentModel.IComponent)">
      <summary>Hides the smart tag panel for a component.</summary>
      <param name="component">The component whose smart tag panel should be hidden.</param>
    </member>
    <member name="M:System.ComponentModel.Design.DesignerActionUIService.Refresh(System.ComponentModel.IComponent)">
      <summary>Updates the smart tag panel.</summary>
      <param name="component">The <see cref="T:System.ComponentModel.IComponent" /> to refresh.</param>
    </member>
    <member name="M:System.ComponentModel.Design.DesignerActionUIService.ShouldAutoShow(System.ComponentModel.IComponent)">
      <summary>Indicates whether to automatically show the smart tag panel.</summary>
      <param name="component">The <see cref="T:System.ComponentModel.IComponent" /> to automatically show.</param>
      <returns>
        <see langword="true" /> to automatically show the smart tag panel; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.ComponentModel.Design.DesignerActionUIService.ShowUI(System.ComponentModel.IComponent)">
      <summary>Displays the smart tag panel for a component.</summary>
      <param name="component">The component whose smart tag panel should be displayed.</param>
    </member>
    <member name="T:System.ComponentModel.Design.DesignerActionUIStateChangeEventArgs">
      <summary>Provides data for the <see cref="E:System.ComponentModel.Design.DesignerActionUIService.DesignerActionUIStateChange" /> event.</summary>
    </member>
    <member name="M:System.ComponentModel.Design.DesignerActionUIStateChangeEventArgs.#ctor(System.Object,System.ComponentModel.Design.DesignerActionUIStateChangeType)">
      <summary>Initializes a new instance of the <see cref="T:System.ComponentModel.Design.DesignerActionUIStateChangeEventArgs" /> class.</summary>
      <param name="relatedObject">The object that is associated with the panel.</param>
      <param name="changeType">A value that specifies whether the panel is being displayed or hidden.</param>
    </member>
    <member name="P:System.ComponentModel.Design.DesignerActionUIStateChangeEventArgs.ChangeType">
      <summary>Gets a flag indicating whether the smart tag panel is being displayed or hidden.</summary>
      <returns>A <see cref="T:System.ComponentModel.Design.DesignerActionUIStateChangeType" /> that indicates the state of the panel.</returns>
    </member>
    <member name="P:System.ComponentModel.Design.DesignerActionUIStateChangeEventArgs.RelatedObject">
      <summary>Gets the object that is associated with the smart tag panel.</summary>
      <returns>The <see cref="T:System.Object" /> associated with the smart tag panel.</returns>
    </member>
    <member name="T:System.ComponentModel.Design.DesignerActionUIStateChangeEventHandler">
      <summary>Represents the method that will handle the <see cref="E:System.ComponentModel.Design.DesignerActionUIService.DesignerActionUIStateChange" /> event of a <see cref="T:System.ComponentModel.Design.DesignerActionUIService" />.</summary>
      <param name="sender">The <see cref="T:System.Object" /> that raised the event.</param>
      <param name="e">The event-specific information.</param>
    </member>
    <member name="T:System.ComponentModel.Design.DesignerActionUIStateChangeType">
      <summary>Specifies the display state of a smart tag panel.</summary>
    </member>
    <member name="F:System.ComponentModel.Design.DesignerActionUIStateChangeType.Hide">
      <summary>The smart tag panel is being hidden.</summary>
    </member>
    <member name="F:System.ComponentModel.Design.DesignerActionUIStateChangeType.Refresh">
      <summary>The smart tag panel is being refreshed.</summary>
    </member>
    <member name="F:System.ComponentModel.Design.DesignerActionUIStateChangeType.Show">
      <summary>The smart tag panel is being displayed.</summary>
    </member>
    <member name="T:System.ComponentModel.Design.DesignerCommandSet">
      <summary>Represents a base class for design-time tools, not derived from <see cref="T:System.ComponentModel.Design.ComponentDesigner" />, that provide smart tag or designer verb capabilities.</summary>
    </member>
    <member name="M:System.ComponentModel.Design.DesignerCommandSet.#ctor">
      <summary>Initializes an instance of the <see cref="T:System.ComponentModel.Design.DesignerCommandSet" /> class.</summary>
    </member>
    <member name="P:System.ComponentModel.Design.DesignerCommandSet.ActionLists">
      <summary>Gets the collection of all the smart tags associated with the designed component.</summary>
      <returns>A <see cref="T:System.ComponentModel.Design.DesignerActionListCollection" /> that contains the smart tags for the associated designed component.</returns>
    </member>
    <member name="M:System.ComponentModel.Design.DesignerCommandSet.GetCommands(System.String)">
      <summary>Returns a collection of command objects.</summary>
      <param name="name">The type of collection to return, indicating either a <see cref="T:System.ComponentModel.Design.DesignerActionListCollection" /> or a <see cref="T:System.ComponentModel.Design.DesignerVerbCollection" />.</param>
      <returns>A collection that contains the specified type - either <see cref="T:System.ComponentModel.Design.DesignerActionList" /> or <see cref="T:System.ComponentModel.Design.DesignerVerb" /> - of command objects. The base implementation always returns <see langword="null" />.</returns>
    </member>
    <member name="P:System.ComponentModel.Design.DesignerCommandSet.Verbs">
      <summary>Gets the collection of all the designer verbs associated with the designed component.</summary>
      <returns>A <see cref="T:System.ComponentModel.Design.DesignerVerbCollection" /> that contains the designer verbs for the associated designed component.</returns>
    </member>
    <member name="T:System.ComponentModel.Design.DesignSurface">
      <summary>Presents a user interface for designing components.</summary>
    </member>
    <member name="M:System.ComponentModel.Design.DesignSurface.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.ComponentModel.Design.DesignSurface" /> class.</summary>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.ComponentModel.Design.IDesignerHost" /> attached to the <see cref="T:System.ComponentModel.Design.DesignSurface" /> has been disposed.</exception>
    </member>
    <member name="M:System.ComponentModel.Design.DesignSurface.#ctor(System.IServiceProvider)">
      <summary>Initializes a new instance of the <see cref="T:System.ComponentModel.Design.DesignSurface" /> class.</summary>
      <param name="parentProvider">The parent service provider, or <see langword="null" /> if there is no parent used to resolve services.</param>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.ComponentModel.Design.IDesignerHost" /> attached to the <see cref="T:System.ComponentModel.Design.DesignSurface" /> has been disposed.</exception>
    </member>
    <member name="M:System.ComponentModel.Design.DesignSurface.#ctor(System.IServiceProvider,System.Type)">
      <summary>Initializes a new instance of the <see cref="T:System.ComponentModel.Design.DesignSurface" /> class.</summary>
      <param name="parentProvider">The parent service provider, or <see langword="null" /> if there is no parent used to resolve services.</param>
      <param name="rootComponentType">The type of root component to create.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="rootComponent" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.ComponentModel.Design.IDesignerHost" /> attached to the <see cref="T:System.ComponentModel.Design.DesignSurface" /> has been disposed.</exception>
    </member>
    <member name="M:System.ComponentModel.Design.DesignSurface.#ctor(System.Type)">
      <summary>Initializes a new instance of the <see cref="T:System.ComponentModel.Design.DesignSurface" /> class.</summary>
      <param name="rootComponentType">The type of root component to create.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="rootComponent" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.ComponentModel.Design.IDesignerHost" /> attached to the <see cref="T:System.ComponentModel.Design.DesignSurface" /> has been disposed.</exception>
    </member>
    <member name="M:System.ComponentModel.Design.DesignSurface.BeginLoad(System.ComponentModel.Design.Serialization.DesignerLoader)">
      <summary>Begins the loading process with the given designer loader.</summary>
      <param name="loader">The designer loader to use for loading the designer.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="loader" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.ComponentModel.Design.IDesignerHost" /> attached to the <see cref="T:System.ComponentModel.Design.DesignSurface" /> has been disposed.</exception>
    </member>
    <member name="M:System.ComponentModel.Design.DesignSurface.BeginLoad(System.Type)">
      <summary>Begins the loading process.</summary>
      <param name="rootComponentType">The type of component to create in design mode.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="rootComponentType" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.ComponentModel.Design.IDesignerHost" /> attached to the <see cref="T:System.ComponentModel.Design.DesignSurface" /> has been disposed.</exception>
    </member>
    <member name="P:System.ComponentModel.Design.DesignSurface.ComponentContainer">
      <summary>Gets the <see cref="T:System.ComponentModel.IContainer" /> implementation within the design surface.</summary>
      <returns>The <see cref="T:System.ComponentModel.IContainer" /> implementation within the design surface.</returns>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.ComponentModel.Design.IDesignerHost" /> attached to the <see cref="T:System.ComponentModel.Design.DesignSurface" /> has been disposed.</exception>
    </member>
    <member name="M:System.ComponentModel.Design.DesignSurface.CreateComponent(System.Type)">
      <summary>Creates an instance of a component.</summary>
      <param name="componentType">The type of component to create.</param>
      <returns>The newly created component.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="componentType" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.ComponentModel.Design.IDesignerHost" /> attached to the <see cref="T:System.ComponentModel.Design.DesignSurface" /> has been disposed.</exception>
    </member>
    <member name="M:System.ComponentModel.Design.DesignSurface.CreateDesigner(System.ComponentModel.IComponent,System.Boolean)">
      <summary>Creates a designer when a component is added to the container.</summary>
      <param name="component">The component for which the designer should be created.</param>
      <param name="rootDesigner">
        <see langword="true" /> to create a root designer; <see langword="false" /> to create a normal designer.</param>
      <returns>An instance of the requested designer, or <see langword="null" /> if no matching designer could be found.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="component" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.ComponentModel.Design.IDesignerHost" /> attached to the <see cref="T:System.ComponentModel.Design.DesignSurface" /> has been disposed.</exception>
    </member>
    <member name="M:System.ComponentModel.Design.DesignSurface.CreateInstance(System.Type)">
      <summary>Creates an instance of the given type.</summary>
      <param name="type">The type to create.</param>
      <returns>The newly created object.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="type" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.ComponentModel.Design.IDesignerHost" /> attached to the <see cref="T:System.ComponentModel.Design.DesignSurface" /> has been disposed.</exception>
    </member>
    <member name="M:System.ComponentModel.Design.DesignSurface.CreateNestedContainer(System.ComponentModel.IComponent)">
      <summary>Creates a container suitable for nesting controls or components.</summary>
      <param name="owningComponent">The component that manages the nested container.</param>
      <returns>The nested container.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="owningComponent" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.ComponentModel.Design.IDesignerHost" /> attached to the <see cref="T:System.ComponentModel.Design.DesignSurface" /> has been disposed.</exception>
    </member>
    <member name="M:System.ComponentModel.Design.DesignSurface.CreateNestedContainer(System.ComponentModel.IComponent,System.String)">
      <summary>Creates a container suitable for nesting controls or components.</summary>
      <param name="owningComponent">The component that manages the nested container.</param>
      <param name="containerName">An additional name for the nested container.</param>
      <returns>The nested container.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="owningComponent" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.ComponentModel.Design.IDesignerHost" /> attached to the <see cref="T:System.ComponentModel.Design.DesignSurface" /> has been disposed.</exception>
    </member>
    <member name="M:System.ComponentModel.Design.DesignSurface.Dispose">
      <summary>Releases the resources used by the <see cref="T:System.ComponentModel.Design.DesignSurface" />.</summary>
    </member>
    <member name="M:System.ComponentModel.Design.DesignSurface.Dispose(System.Boolean)">
      <summary>Releases the resources used by the <see cref="T:System.ComponentModel.Design.DesignSurface" />.</summary>
      <param name="disposing">
        <see langword="true" /> to release both managed and unmanaged resources; <see langword="false" /> to release only unmanaged resources.</param>
    </member>
    <member name="E:System.ComponentModel.Design.DesignSurface.Disposed">
      <summary>Occurs when the design surface is disposed.</summary>
    </member>
    <member name="P:System.ComponentModel.Design.DesignSurface.DtelLoading">
      <summary>Gets a value indicating whether the Design-time Error List is loading.</summary>
      <returns>
        <see langword="true" /> if the Design-time Error List is loading; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.ComponentModel.Design.DesignSurface.Flush">
      <summary>Serializes changes to the design surface.</summary>
    </member>
    <member name="E:System.ComponentModel.Design.DesignSurface.Flushed">
      <summary>Occurs when a call is made to the <see cref="M:System.ComponentModel.Design.DesignSurface.Flush" /> method of <see cref="T:System.ComponentModel.Design.DesignSurface" />.</summary>
    </member>
    <member name="M:System.ComponentModel.Design.DesignSurface.GetService(System.Type)">
      <summary>Gets a service from the service container.</summary>
      <param name="serviceType">The type of service to retrieve.</param>
      <returns>An object that implements, or is a derived class of, <paramref name="serviceType" />, or <see langword="null" /> if the service does not exist in the service container.</returns>
    </member>
    <member name="P:System.ComponentModel.Design.DesignSurface.IsLoaded">
      <summary>Gets a value indicating whether the design surface is currently loaded.</summary>
      <returns>
        <see langword="true" /> if the design surface is currently loaded; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="E:System.ComponentModel.Design.DesignSurface.Loaded">
      <summary>Occurs when the designer load has completed.</summary>
    </member>
    <member name="P:System.ComponentModel.Design.DesignSurface.LoadErrors">
      <summary>Returns a collection of loading errors or a void collection.</summary>
      <returns>A <see cref="T:System.Collections.ICollection" /> of loading errors.</returns>
    </member>
    <member name="E:System.ComponentModel.Design.DesignSurface.Loading">
      <summary>Occurs when the designer is about to be loaded.</summary>
    </member>
    <member name="M:System.ComponentModel.Design.DesignSurface.OnLoaded(System.ComponentModel.Design.LoadedEventArgs)">
      <summary>Raises the <see cref="E:System.ComponentModel.Design.DesignSurface.Loaded" /> event.</summary>
      <param name="e">A <see cref="T:System.ComponentModel.Design.LoadedEventArgs" /> that contains the event data.</param>
    </member>
    <member name="M:System.ComponentModel.Design.DesignSurface.OnLoading(System.EventArgs)">
      <summary>Raises the <see cref="E:System.ComponentModel.Design.DesignSurface.Loading" /> event.</summary>
      <param name="e">An <see cref="T:System.EventArgs" /> that contains the event data.</param>
    </member>
    <member name="M:System.ComponentModel.Design.DesignSurface.OnUnloaded(System.EventArgs)">
      <summary>Raises the <see cref="E:System.ComponentModel.Design.DesignSurface.Unloaded" /> event.</summary>
      <param name="e">An <see cref="T:System.EventArgs" /> that contains the event data.</param>
    </member>
    <member name="M:System.ComponentModel.Design.DesignSurface.OnUnloading(System.EventArgs)">
      <summary>Raises the <see cref="E:System.ComponentModel.Design.DesignSurface.Unloading" /> event.</summary>
      <param name="e">An <see cref="T:System.EventArgs" /> that contains the event data.</param>
    </member>
    <member name="M:System.ComponentModel.Design.DesignSurface.OnViewActivate(System.EventArgs)">
      <summary>Raises the <see cref="E:System.ComponentModel.Design.DesignSurface.ViewActivated" /> event.</summary>
      <param name="e">An <see cref="T:System.EventArgs" /> that contains the event data.</param>
    </member>
    <member name="P:System.ComponentModel.Design.DesignSurface.ServiceContainer">
      <summary>Gets the service container.</summary>
      <returns>The service container that provides all services to designers contained within the design surface.</returns>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.ComponentModel.Design.IDesignerHost" /> attached to the <see cref="T:System.ComponentModel.Design.DesignSurface" /> has been disposed.</exception>
    </member>
    <member name="E:System.ComponentModel.Design.DesignSurface.Unloaded">
      <summary>Occurs when a designer has finished unloading.</summary>
    </member>
    <member name="E:System.ComponentModel.Design.DesignSurface.Unloading">
      <summary>Occurs when a designer is about to unload.</summary>
    </member>
    <member name="P:System.ComponentModel.Design.DesignSurface.View">
      <summary>Gets the view for the root designer.</summary>
      <returns>The view for the root designer.</returns>
      <exception cref="T:System.InvalidOperationException">The design surface is not loading, the designer loader has not yet created a root designer, or the design surface finished the load, but failed. More information may available in the <see cref="P:System.Exception.InnerException" />.</exception>
      <exception cref="T:System.NotSupportedException">The designer loaded, but it does not offer a view compatible with this design surface.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.ComponentModel.Design.IDesignerHost" /> attached to the <see cref="T:System.ComponentModel.Design.DesignSurface" /> has been disposed.</exception>
    </member>
    <member name="E:System.ComponentModel.Design.DesignSurface.ViewActivated">
      <summary>Occurs when the <see cref="M:System.ComponentModel.Design.IDesignerHost.Activate" /> method has been called on <see cref="T:System.ComponentModel.Design.IDesignerHost" />.</summary>
    </member>
    <member name="T:System.ComponentModel.Design.ExceptionCollection">
      <summary>Represents the collection of exceptions.</summary>
    </member>
    <member name="M:System.ComponentModel.Design.ExceptionCollection.#ctor(System.Collections.ArrayList)">
      <summary>Initializes a new instance of the <see cref="T:System.ComponentModel.Design.ExceptionCollection" /> class.</summary>
      <param name="exceptions">An array of type <see cref="T:System.Exception" />, containing the objects to populate the collection.</param>
    </member>
    <member name="P:System.ComponentModel.Design.ExceptionCollection.Exceptions">
      <summary>Gets the array of <see cref="T:System.Exception" /> objects that represent the collection of exceptions.</summary>
      <returns>An <see cref="T:System.Exception" /> array that represent the collection of exceptions.</returns>
    </member>
    <member name="M:System.ComponentModel.Design.ExceptionCollection.GetObjectData(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
      <summary>Populates a <see cref="T:System.Runtime.Serialization.SerializationInfo" /> with the data needed to serialize the <see cref="T:System.ComponentModel.Design.ExceptionCollection" />.</summary>
      <param name="info">The <see cref="T:System.Runtime.Serialization.SerializationInfo" /> to populate with data.</param>
      <param name="context">The destination (see <see cref="T:System.Runtime.Serialization.StreamingContext" />) for this serialization.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="info" /> is <see langword="null" />.</exception>
    </member>
    <member name="T:System.ComponentModel.Design.IMultitargetHelperService">
      <summary>Defines multi-target type name resolution services in a design-time environment.</summary>
    </member>
    <member name="M:System.ComponentModel.Design.IMultitargetHelperService.GetAssemblyQualifiedName(System.Type)">
      <summary>Resolves a type for the target framework to an assembly-qualified name.</summary>
      <param name="type">The type to resolve.</param>
      <returns>The <see cref="P:System.Type.AssemblyQualifiedName" /> for <paramref name="type" /> in the target framework.</returns>
    </member>
    <member name="T:System.ComponentModel.Design.InheritanceService">
      <summary>Provides a set of methods for identifying inherited components.</summary>
    </member>
    <member name="M:System.ComponentModel.Design.InheritanceService.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.ComponentModel.Design.InheritanceService" /> class.</summary>
    </member>
    <member name="M:System.ComponentModel.Design.InheritanceService.AddInheritedComponents(System.ComponentModel.IComponent,System.ComponentModel.IContainer)">
      <summary>Adds the components inherited by the specified component to the <see cref="T:System.ComponentModel.Design.InheritanceService" />.</summary>
      <param name="component">The component to search for inherited components to add to the specified container.</param>
      <param name="container">The container to add the inherited components to.</param>
    </member>
    <member name="M:System.ComponentModel.Design.InheritanceService.AddInheritedComponents(System.Type,System.ComponentModel.IComponent,System.ComponentModel.IContainer)">
      <summary>Adds the components of the specified type that are inherited by the specified component to the <see cref="T:System.ComponentModel.Design.InheritanceService" />.</summary>
      <param name="type">The base type to search for.</param>
      <param name="component">The component to search for inherited components to add to the specified container.</param>
      <param name="container">The container to add the inherited components to.</param>
    </member>
    <member name="M:System.ComponentModel.Design.InheritanceService.Dispose">
      <summary>Releases all resources used by the <see cref="T:System.ComponentModel.Design.InheritanceService" />.</summary>
    </member>
    <member name="M:System.ComponentModel.Design.InheritanceService.Dispose(System.Boolean)">
      <summary>Releases the unmanaged resources used by the <see cref="T:System.ComponentModel.Design.InheritanceService" /> and optionally releases the managed resources.</summary>
      <param name="disposing">
        <see langword="true" /> to release both managed and unmanaged resources; <see langword="false" /> to release only unmanaged resources.</param>
    </member>
    <member name="M:System.ComponentModel.Design.InheritanceService.GetInheritanceAttribute(System.ComponentModel.IComponent)">
      <summary>Gets the inheritance attribute of the specified component.</summary>
      <param name="component">The component to retrieve the inheritance attribute for.</param>
      <returns>An <see cref="T:System.ComponentModel.InheritanceAttribute" /> that describes the level of inheritance that this component comes from.</returns>
    </member>
    <member name="M:System.ComponentModel.Design.InheritanceService.IgnoreInheritedMember(System.Reflection.MemberInfo,System.ComponentModel.IComponent)">
      <summary>Indicates whether to ignore the specified member.</summary>
      <param name="member">The member to check. This member is either a <see cref="T:System.Reflection.FieldInfo" /> or a <see cref="T:System.Reflection.MethodInfo" />.</param>
      <param name="component">The component instance this member is bound to.</param>
      <returns>
        <see langword="true" /> if the specified member should be included in the set of inherited components; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="T:System.ComponentModel.Design.LoadedEventArgs">
      <summary>Provides data for the <see cref="E:System.ComponentModel.Design.DesignSurface.Loaded" /> event. This class cannot be inherited.</summary>
    </member>
    <member name="M:System.ComponentModel.Design.LoadedEventArgs.#ctor(System.Boolean,System.Collections.ICollection)">
      <summary>Initializes a new instance of the <see cref="T:System.ComponentModel.Design.LoadedEventArgs" /> class.</summary>
      <param name="succeeded">
        <see langword="true" /> to indicate that the designer load was successful; otherwise, <see langword="false" />.</param>
      <param name="errors">A collection of errors that occurred while the designer was loading.</param>
    </member>
    <member name="P:System.ComponentModel.Design.LoadedEventArgs.Errors">
      <summary>Gets a collection of errors that occurred while the designer was loading.</summary>
      <returns>A collection of errors that occurred while the designer was loading.</returns>
    </member>
    <member name="P:System.ComponentModel.Design.LoadedEventArgs.HasSucceeded">
      <summary>Gets a value that indicates whether the designer load was successful.</summary>
      <returns>
        <see langword="true" /> if the designer load was successful; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="T:System.ComponentModel.Design.LoadedEventHandler">
      <summary>Represents the method that will handle the <see cref="E:System.ComponentModel.Design.DesignSurface.Loaded" /> event of the <see cref="T:System.ComponentModel.Design.DesignSurface" /> class. This class cannot be inherited.</summary>
      <param name="sender">The source of the event.</param>
      <param name="e">A <see cref="T:System.ComponentModel.Design.LoadedEventArgs" /> that contains the event data.</param>
    </member>
    <member name="T:System.ComponentModel.Design.ProjectTargetFrameworkAttribute">
      <summary>Specifies the target framework for a project.</summary>
    </member>
    <member name="M:System.ComponentModel.Design.ProjectTargetFrameworkAttribute.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.ComponentModel.Design.ProjectTargetFrameworkAttribute" /> class.</summary>
      <param name="targetFrameworkMoniker">The target framework for the project.</param>
    </member>
    <member name="P:System.ComponentModel.Design.ProjectTargetFrameworkAttribute.TargetFrameworkMoniker">
      <summary>Gets the target framework for the project.</summary>
      <returns>The target framework for the project.</returns>
    </member>
    <member name="T:System.ComponentModel.Design.Serialization.CodeDomComponentSerializationService">
      <summary>Serializes a set of components into a serialization store.</summary>
    </member>
    <member name="M:System.ComponentModel.Design.Serialization.CodeDomComponentSerializationService.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.ComponentModel.Design.Serialization.CodeDomComponentSerializationService" /> class.</summary>
    </member>
    <member name="M:System.ComponentModel.Design.Serialization.CodeDomComponentSerializationService.#ctor(System.IServiceProvider)">
      <summary>Initializes a new instance of the <see cref="T:System.ComponentModel.Design.Serialization.CodeDomComponentSerializationService" /> class using the given service provider to resolve services.</summary>
      <param name="provider">An <see cref="T:System.IServiceProvider" /> to use for resolving services.</param>
    </member>
    <member name="M:System.ComponentModel.Design.Serialization.CodeDomComponentSerializationService.CreateStore">
      <summary>Creates a new <see cref="T:System.ComponentModel.Design.Serialization.SerializationStore" />.</summary>
      <returns>A new serialization store.</returns>
    </member>
    <member name="M:System.ComponentModel.Design.Serialization.CodeDomComponentSerializationService.Deserialize(System.ComponentModel.Design.Serialization.SerializationStore)">
      <summary>Deserializes the given store to produce a collection of objects.</summary>
      <param name="store">The <see cref="T:System.ComponentModel.Design.Serialization.SerializationStore" /> from which objects will be deserialized.</param>
      <returns>A collection of deserialized components.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="store" /> is <see langword="null" />.</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="store" /> is not a supported type of serialization store. Use a store returned by <see cref="M:System.ComponentModel.Design.Serialization.CodeDomComponentSerializationService.CreateStore" />.</exception>
    </member>
    <member name="M:System.ComponentModel.Design.Serialization.CodeDomComponentSerializationService.Deserialize(System.ComponentModel.Design.Serialization.SerializationStore,System.ComponentModel.IContainer)">
      <summary>Deserializes the given store and populates the given <see cref="T:System.ComponentModel.IContainer" /> with deserialized <see cref="T:System.ComponentModel.IComponent" /> objects.</summary>
      <param name="store">The <see cref="T:System.ComponentModel.Design.Serialization.SerializationStore" /> from which objects will be deserialized.</param>
      <param name="container">A container to which <see cref="T:System.ComponentModel.IComponent" /> objects will be added.</param>
      <returns>A collection of deserialized components.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="store" /> or <paramref name="container" /> is <see langword="null" />.</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="store" /> is not a supported type of serialization store. Use a store returned by <see cref="M:System.ComponentModel.Design.Serialization.CodeDomComponentSerializationService.CreateStore" />.</exception>
    </member>
    <member name="M:System.ComponentModel.Design.Serialization.CodeDomComponentSerializationService.DeserializeTo(System.ComponentModel.Design.Serialization.SerializationStore,System.ComponentModel.IContainer,System.Boolean,System.Boolean)">
      <summary>Deserializes the given <see cref="T:System.ComponentModel.Design.Serialization.SerializationStore" /> to the given container, optionally applying default property values.</summary>
      <param name="store">The <see cref="T:System.ComponentModel.Design.Serialization.SerializationStore" /> from which the objects will be deserialized.</param>
      <param name="container">A container of objects to which data will be applied.</param>
      <param name="validateRecycledTypes">
        <see langword="true" /> to validate the recycled type; otherwise, <see langword="false" />.</param>
      <param name="applyDefaults">
        <see langword="true" /> to apply default property values; otherwise, <see langword="false" />.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="store" /> or <paramref name="container" /> is <see langword="null" />.</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="store" /> is not a supported type of serialization store. Use a store returned by <see cref="M:System.ComponentModel.Design.Serialization.CodeDomComponentSerializationService.CreateStore" />.</exception>
    </member>
    <member name="M:System.ComponentModel.Design.Serialization.CodeDomComponentSerializationService.LoadStore(System.IO.Stream)">
      <summary>Loads a <see cref="T:System.ComponentModel.Design.Serialization.SerializationStore" /> from the given stream.</summary>
      <param name="stream">The stream from which to load the <see cref="T:System.ComponentModel.Design.Serialization.SerializationStore" />.</param>
      <returns>The loaded <see cref="T:System.ComponentModel.Design.Serialization.SerializationStore" />.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="stream" /> is <see langword="null" />.</exception>
      <exception cref="T:System.Runtime.Serialization.SerializationException">
        <paramref name="stream" /> supports seeking, but its length is 0.</exception>
    </member>
    <member name="M:System.ComponentModel.Design.Serialization.CodeDomComponentSerializationService.Serialize(System.ComponentModel.Design.Serialization.SerializationStore,System.Object)">
      <summary>Serializes the given object to the given <see cref="T:System.ComponentModel.Design.Serialization.SerializationStore" />.</summary>
      <param name="store">The <see cref="T:System.ComponentModel.Design.Serialization.SerializationStore" /> to which <paramref name="value" /> will be serialized.</param>
      <param name="value">The object to serialize.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="store" /> or <paramref name="value" /> is <see langword="null" />.</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="store" /> is closed, or <paramref name="store" /> is not a supported type of serialization store. Use a store returned by <see cref="M:System.ComponentModel.Design.Serialization.CodeDomComponentSerializationService.CreateStore" />.</exception>
    </member>
    <member name="M:System.ComponentModel.Design.Serialization.CodeDomComponentSerializationService.SerializeAbsolute(System.ComponentModel.Design.Serialization.SerializationStore,System.Object)">
      <summary>Serializes the given object, accounting for default property values.</summary>
      <param name="store">The <see cref="T:System.ComponentModel.Design.Serialization.SerializationStore" /> to which <paramref name="value" /> will be serialized.</param>
      <param name="value">The object to serialize.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="store" /> or <paramref name="value" /> is <see langword="null" />.</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="store" /> is closed, or <paramref name="store" /> is not a supported type of serialization store. Use a store returned by <see cref="M:System.ComponentModel.Design.Serialization.CodeDomComponentSerializationService.CreateStore" />.</exception>
    </member>
    <member name="M:System.ComponentModel.Design.Serialization.CodeDomComponentSerializationService.SerializeMember(System.ComponentModel.Design.Serialization.SerializationStore,System.Object,System.ComponentModel.MemberDescriptor)">
      <summary>Serializes the given member on the given object.</summary>
      <param name="store">The <see cref="T:System.ComponentModel.Design.Serialization.SerializationStore" /> to which <paramref name="member" /> will be serialized.</param>
      <param name="owningObject">The object that owns the <paramref name="member" />.</param>
      <param name="member">The given member.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="store" />, <paramref name="owningObject" />, or <paramref name="member" /> is <see langword="null" />.</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="store" /> is closed, or <paramref name="store" /> is not a supported type of serialization store. Use a store returned by <see cref="M:System.ComponentModel.Design.Serialization.CodeDomComponentSerializationService.CreateStore" />.</exception>
    </member>
    <member name="M:System.ComponentModel.Design.Serialization.CodeDomComponentSerializationService.SerializeMemberAbsolute(System.ComponentModel.Design.Serialization.SerializationStore,System.Object,System.ComponentModel.MemberDescriptor)">
      <summary>Serializes the given member on the given object, but also serializes the member if it contains the default property value.</summary>
      <param name="store">The <see cref="T:System.ComponentModel.Design.Serialization.SerializationStore" /> to which <paramref name="member" /> will be serialized.</param>
      <param name="owningObject">The object that owns the <paramref name="member" />.</param>
      <param name="member">The given member.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="store" />, <paramref name="owningObject" />, or <paramref name="member" /> is <see langword="null" />.</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="store" /> is closed, or <paramref name="store" /> is not a supported type of serialization store. Use a store returned by <see cref="M:System.ComponentModel.Design.Serialization.CodeDomComponentSerializationService.CreateStore" />.</exception>
    </member>
    <member name="T:System.ComponentModel.Design.Serialization.CodeDomSerializer">
      <summary>Serializes an object graph to a series of CodeDOM statements. This class provides an abstract base class for a serializer.</summary>
    </member>
    <member name="M:System.ComponentModel.Design.Serialization.CodeDomSerializer.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.ComponentModel.Design.Serialization.CodeDomSerializer" /> class.</summary>
    </member>
    <member name="M:System.ComponentModel.Design.Serialization.CodeDomSerializer.Deserialize(System.ComponentModel.Design.Serialization.IDesignerSerializationManager,System.Object)">
      <summary>Deserializes the specified serialized CodeDOM object into an object.</summary>
      <param name="manager">A serialization manager interface that is used during the deserialization process.</param>
      <param name="codeObject">A serialized CodeDOM object to deserialize.</param>
      <returns>The deserialized CodeDOM object.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="manager" /> or <paramref name="codeObject" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="codeObject" /> is an unsupported code element.</exception>
    </member>
    <member name="M:System.ComponentModel.Design.Serialization.CodeDomSerializer.DeserializeStatementToInstance(System.ComponentModel.Design.Serialization.IDesignerSerializationManager,System.CodeDom.CodeStatement)">
      <summary>Deserializes a single statement.</summary>
      <param name="manager">The serialization manager to use for serialization.</param>
      <param name="statement">The statement to deserialize.</param>
      <returns>An object instance resulting from deserializing <paramref name="statement" />.</returns>
    </member>
    <member name="M:System.ComponentModel.Design.Serialization.CodeDomSerializer.GetTargetComponentName(System.CodeDom.CodeStatement,System.CodeDom.CodeExpression,System.Type)">
      <summary>Determines which statement group the given statement should belong to.</summary>
      <param name="statement">The <see cref="T:System.CodeDom.CodeStatement" /> for which to determine the group.</param>
      <param name="expression">A <see cref="T:System.CodeDom.CodeExpression" /> that <paramref name="statement" /> has been reduced to.</param>
      <param name="targetType">The <see cref="T:System.Type" /> of <paramref name="statement" />.</param>
      <returns>The name of the component with which <paramref name="statement" /> should be grouped.</returns>
    </member>
    <member name="M:System.ComponentModel.Design.Serialization.CodeDomSerializer.Serialize(System.ComponentModel.Design.Serialization.IDesignerSerializationManager,System.Object)">
      <summary>Serializes the specified object into a CodeDOM object.</summary>
      <param name="manager">The serialization manager to use during serialization.</param>
      <param name="value">The object to serialize.</param>
      <returns>A CodeDOM object representing the object that has been serialized.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="manager" /> or <paramref name="value" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.ComponentModel.Design.Serialization.CodeDomSerializer.SerializeAbsolute(System.ComponentModel.Design.Serialization.IDesignerSerializationManager,System.Object)">
      <summary>Serializes the given object, accounting for default values.</summary>
      <param name="manager">The serialization manager to use for serialization.</param>
      <param name="value">The object to serialize.</param>
      <returns>A CodeDom object representing <paramref name="value" />.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="manager" /> or <paramref name="value" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.ComponentModel.Design.Serialization.CodeDomSerializer.SerializeMember(System.ComponentModel.Design.Serialization.IDesignerSerializationManager,System.Object,System.ComponentModel.MemberDescriptor)">
      <summary>Serializes the given member on the given object.</summary>
      <param name="manager">The serialization manager to use for serialization.</param>
      <param name="owningObject">The object to which is <paramref name="member" /> attached.</param>
      <param name="member">The member to serialize.</param>
      <returns>A <see cref="T:System.CodeDom.CodeStatementCollection" /> representing the serialized state of <paramref name="member" />.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="manager" />, <paramref name="owningObject" />, or <paramref name="member" /> is <see langword="null" />.</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="member" /> is not a serializable type.</exception>
    </member>
    <member name="M:System.ComponentModel.Design.Serialization.CodeDomSerializer.SerializeMemberAbsolute(System.ComponentModel.Design.Serialization.IDesignerSerializationManager,System.Object,System.ComponentModel.MemberDescriptor)">
      <summary>Serializes the given member, accounting for default values.</summary>
      <param name="manager">The serialization manager to use for serialization.</param>
      <param name="owningObject">The object to which is <paramref name="member" /> attached.</param>
      <param name="member">The member to serialize.</param>
      <returns>A CodeDom object representing <paramref name="member" />.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="manager" />, <paramref name="owningObject" />, or <paramref name="member" /> is <see langword="null" />.</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="member" /> is not a serializable type.</exception>
    </member>
    <member name="M:System.ComponentModel.Design.Serialization.CodeDomSerializer.SerializeToReferenceExpression(System.ComponentModel.Design.Serialization.IDesignerSerializationManager,System.Object)">
      <summary>Serializes the specified value to a CodeDOM expression.</summary>
      <param name="manager">The serialization manager to use during serialization.</param>
      <param name="value">The object to serialize.</param>
      <returns>The serialized value. This returns <see langword="null" /> if no reference expression can be obtained for the specified value, or the value cannot be serialized.</returns>
    </member>
    <member name="T:System.ComponentModel.Design.Serialization.CodeDomSerializerBase">
      <summary>Provides a base class for <see cref="T:System.ComponentModel.Design.Serialization.CodeDomSerializer" /> classes.</summary>
    </member>
    <member name="M:System.ComponentModel.Design.Serialization.CodeDomSerializerBase.DeserializeExpression(System.ComponentModel.Design.Serialization.IDesignerSerializationManager,System.String,System.CodeDom.CodeExpression)">
      <summary>Deserializes the given expression into an in-memory object.</summary>
      <param name="manager">The <see cref="T:System.ComponentModel.Design.Serialization.IDesignerSerializationManager" /> to use for serialization.</param>
      <param name="name">The name of the object that results from the expression. Can be <see langword="null" /> if there is no need to name the object.</param>
      <param name="expression">The <see cref="T:System.CodeDom.CodeExpression" /> to interpret.</param>
      <returns>An object resulting from interpretation of <paramref name="expression" />.</returns>
    </member>
    <member name="M:System.ComponentModel.Design.Serialization.CodeDomSerializerBase.DeserializeInstance(System.ComponentModel.Design.Serialization.IDesignerSerializationManager,System.Type,System.Object[],System.String,System.Boolean)">
      <summary>Returns an instance of the given type.</summary>
      <param name="manager">The <see cref="T:System.ComponentModel.Design.Serialization.IDesignerSerializationManager" /> to use for serialization.</param>
      <param name="type">The <see cref="T:System.Type" /> of the instance to return.</param>
      <param name="parameters">The parameters to pass to the constructor for <paramref name="type" />.</param>
      <param name="name">The name of the deserialized object.</param>
      <param name="addToContainer">
        <see langword="true" /> to add this object to the design container; otherwise, <see langword="false" />. The object must implement <see cref="T:System.ComponentModel.IComponent" /> for this to have any effect.</param>
      <returns>An instance of <paramref name="type" />.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="manager" /> or <paramref name="type" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.ComponentModel.Design.Serialization.CodeDomSerializerBase.DeserializePropertiesFromResources(System.ComponentModel.Design.Serialization.IDesignerSerializationManager,System.Object,System.Attribute[])">
      <summary>Deserializes properties on the given object from the invariant culture's resource bundle.</summary>
      <param name="manager">The <see cref="T:System.ComponentModel.Design.Serialization.IDesignerSerializationManager" /> to use for serialization.</param>
      <param name="value">The object from which the properties are to be deserialized.</param>
      <param name="filter">An <see cref="T:System.Attribute" /> array that filters which properties will be deserialized.</param>
    </member>
    <member name="M:System.ComponentModel.Design.Serialization.CodeDomSerializerBase.DeserializeStatement(System.ComponentModel.Design.Serialization.IDesignerSerializationManager,System.CodeDom.CodeStatement)">
      <summary>Deserializes a statement by interpreting and executing a CodeDOM statement.</summary>
      <param name="manager">The <see cref="T:System.ComponentModel.Design.Serialization.IDesignerSerializationManager" /> to use for serialization.</param>
      <param name="statement">The <see cref="T:System.CodeDom.CodeStatement" /> to deserialize.</param>
    </member>
    <member name="M:System.ComponentModel.Design.Serialization.CodeDomSerializerBase.GetAttributesFromTypeHelper(System.ComponentModel.Design.Serialization.IDesignerSerializationManager,System.Type)">
      <summary>Gets a collection of attributes as defined in the project's target version of the .NET Framework.</summary>
      <param name="manager">The serialization manager.</param>
      <param name="type">The target type.</param>
      <returns>A collection of attributes as defined in the project's target version of the .NET Framework.</returns>
    </member>
    <member name="M:System.ComponentModel.Design.Serialization.CodeDomSerializerBase.GetAttributesHelper(System.ComponentModel.Design.Serialization.IDesignerSerializationManager,System.Object)">
      <summary>Gets a collection of attributes as defined in the project's target version of the .NET Framework.</summary>
      <param name="manager">The serialization manager.</param>
      <param name="instance">An object of the target type.</param>
      <returns>A collection of attributes as defined in the project's target version of the .NET Framework.</returns>
    </member>
    <member name="M:System.ComponentModel.Design.Serialization.CodeDomSerializerBase.GetEventsHelper(System.ComponentModel.Design.Serialization.IDesignerSerializationManager,System.Object,System.Attribute[])">
      <summary>Gets a collection of events as defined in the project's target version of the .NET Framework.</summary>
      <param name="manager">The serialization manager.</param>
      <param name="instance">An object of the target type.</param>
      <param name="attributes">An array of attributes to pass to the target version of the .NET Framework.</param>
      <returns>A collection of events as defined in the project's target version of the .NET Framework.</returns>
    </member>
    <member name="M:System.ComponentModel.Design.Serialization.CodeDomSerializerBase.GetExpression(System.ComponentModel.Design.Serialization.IDesignerSerializationManager,System.Object)">
      <summary>Returns an expression for the given object.</summary>
      <param name="manager">The <see cref="T:System.ComponentModel.Design.Serialization.IDesignerSerializationManager" /> to use for serialization.</param>
      <param name="value">The object for which to get an expression.</param>
      <returns>A <see cref="T:System.CodeDom.CodeExpression" /> representing <paramref name="value" />, or <see langword="null" /> if there is no existing expression for <paramref name="value" />.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="manager" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.ComponentModel.Design.Serialization.CodeDomSerializerBase.GetPropertiesHelper(System.ComponentModel.Design.Serialization.IDesignerSerializationManager,System.Object,System.Attribute[])">
      <summary>Gets a collection of properties as defined in the project's target version of the .NET Framework.</summary>
      <param name="manager">The serialization manager.</param>
      <param name="instance">An object of the target type.</param>
      <param name="attributes">An array of attributes to pass to the target version of the .NET Framework.</param>
      <returns>A collection of properties as defined in the project's target version of the .NET Framework.</returns>
    </member>
    <member name="M:System.ComponentModel.Design.Serialization.CodeDomSerializerBase.GetReflectionTypeFromTypeHelper(System.ComponentModel.Design.Serialization.IDesignerSerializationManager,System.Type)">
      <summary>Gets a reflection type generated from type metadata.</summary>
      <param name="manager">The serialization manager.</param>
      <param name="type">The type to use metadata from.</param>
      <returns>A reflection type generated from the metadata of <paramref name="type" />.</returns>
    </member>
    <member name="M:System.ComponentModel.Design.Serialization.CodeDomSerializerBase.GetReflectionTypeHelper(System.ComponentModel.Design.Serialization.IDesignerSerializationManager,System.Object)">
      <summary>Gets a reflection type generated from object metadata.</summary>
      <param name="manager">The serialization manager.</param>
      <param name="instance">The object to use metadata from.</param>
      <returns>A reflection type generated from the metadata of <paramref name="object" />.</returns>
    </member>
    <member name="M:System.ComponentModel.Design.Serialization.CodeDomSerializerBase.GetSerializer(System.ComponentModel.Design.Serialization.IDesignerSerializationManager,System.Object)">
      <summary>Locates a serializer for the given object value.</summary>
      <param name="manager">The <see cref="T:System.ComponentModel.Design.Serialization.IDesignerSerializationManager" /> to use for serialization.</param>
      <param name="value">The object specifying the serializer to retrieve.</param>
      <returns>A <see cref="T:System.ComponentModel.Design.Serialization.CodeDomSerializer" /> that is appropriate for <paramref name="value" />.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="manager" /> or <paramref name="value" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.ComponentModel.Design.Serialization.CodeDomSerializerBase.GetSerializer(System.ComponentModel.Design.Serialization.IDesignerSerializationManager,System.Type)">
      <summary>Locates a serializer for the given type.</summary>
      <param name="manager">The <see cref="T:System.ComponentModel.Design.Serialization.IDesignerSerializationManager" /> to use for serialization.</param>
      <param name="valueType">The <see cref="T:System.Type" /> specifying the serializer to retrieve.</param>
      <returns>A <see cref="T:System.ComponentModel.Design.Serialization.CodeDomSerializer" /> that is appropriate for <paramref name="valueType" />.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="manager" /> or <paramref name="valueType" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.ComponentModel.Design.Serialization.CodeDomSerializerBase.GetTargetFrameworkProvider(System.IServiceProvider,System.Object)">
      <summary>Gets a <see cref="T:System.ComponentModel.TypeDescriptionProvider" /> that is aware of the target version of the .NET Framework, for use in type filtering.</summary>
      <param name="provider">The type description provider service.</param>
      <param name="instance">An object from which the type description provider service can be derived, if <paramref name="provider" /> is <see langword="null" />.</param>
      <returns>A .NET Framework-aware type description provider.</returns>
    </member>
    <member name="M:System.ComponentModel.Design.Serialization.CodeDomSerializerBase.GetUniqueName(System.ComponentModel.Design.Serialization.IDesignerSerializationManager,System.Object)">
      <summary>Returns a unique name for the given object.</summary>
      <param name="manager">The <see cref="T:System.ComponentModel.Design.Serialization.IDesignerSerializationManager" /> to use for serialization.</param>
      <param name="value">The object for which the name will be retrieved.</param>
      <returns>A unique name for <paramref name="value" />.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="manager" /> or <paramref name="value" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.ComponentModel.Design.Serialization.CodeDomSerializerBase.IsSerialized(System.ComponentModel.Design.Serialization.IDesignerSerializationManager,System.Object)">
      <summary>Returns a value indicating whether the given object has been serialized.</summary>
      <param name="manager">The <see cref="T:System.ComponentModel.Design.Serialization.IDesignerSerializationManager" /> to use for serialization.</param>
      <param name="value">The object to test for previous serialization.</param>
      <returns>
        <see langword="true" /> if <paramref name="value" /> has been serialized; otherwise, <see langword="false" />.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="manager" /> or <paramref name="value" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.ComponentModel.Design.Serialization.CodeDomSerializerBase.IsSerialized(System.ComponentModel.Design.Serialization.IDesignerSerializationManager,System.Object,System.Boolean)">
      <summary>Returns a value indicating whether the given object has been serialized, optionally considering preset expressions.</summary>
      <param name="manager">The <see cref="T:System.ComponentModel.Design.Serialization.IDesignerSerializationManager" /> to use for serialization.</param>
      <param name="value">The object to test for previous serialization.</param>
      <param name="honorPreset">
        <see langword="true" /> to include preset expressions; otherwise, <see langword="false" />.</param>
      <returns>
        <see langword="true" /> if <paramref name="value" /> has been serialized; otherwise, <see langword="false" />.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="manager" /> or <paramref name="value" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.ComponentModel.Design.Serialization.CodeDomSerializerBase.SerializeCreationExpression(System.ComponentModel.Design.Serialization.IDesignerSerializationManager,System.Object,System.Boolean@)">
      <summary>Returns an expression representing the creation of the given object.</summary>
      <param name="manager">The <see cref="T:System.ComponentModel.Design.Serialization.IDesignerSerializationManager" /> to use for serialization.</param>
      <param name="value">The object to serialize.</param>
      <param name="isComplete">
        <see langword="true" /> if <paramref name="value" /> was fully serialized; otherwise, <see langword="false" />.</param>
      <returns>An expression representing the creation of <paramref name="value" />.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="manager" /> or <paramref name="value" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.ComponentModel.Design.Serialization.CodeDomSerializerBase.SerializeEvent(System.ComponentModel.Design.Serialization.IDesignerSerializationManager,System.CodeDom.CodeStatementCollection,System.Object,System.ComponentModel.EventDescriptor)">
      <summary>Serializes the given event into the given statement collection.</summary>
      <param name="manager">The <see cref="T:System.ComponentModel.Design.Serialization.IDesignerSerializationManager" /> to use for serialization.</param>
      <param name="statements">The <see cref="T:System.CodeDom.CodeStatementCollection" /> into which the event will be serialized.</param>
      <param name="value">The object to which <paramref name="descriptor" /> is bound.</param>
      <param name="descriptor">An <see cref="T:System.ComponentModel.EventDescriptor" /> specifying the event to serialize.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="manager" />, <paramref name="value" />, <paramref name="statements" />, or <paramref name="descriptor" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ComponentModel.Design.Serialization.CodeDomSerializerException">
        <see cref="T:System.ComponentModel.Design.IEventBindingService" /> is not available.</exception>
    </member>
    <member name="M:System.ComponentModel.Design.Serialization.CodeDomSerializerBase.SerializeEvents(System.ComponentModel.Design.Serialization.IDesignerSerializationManager,System.CodeDom.CodeStatementCollection,System.Object,System.Attribute[])">
      <summary>Serializes the specified events into the given statement collection.</summary>
      <param name="manager">The <see cref="T:System.ComponentModel.Design.Serialization.IDesignerSerializationManager" /> to use for serialization.</param>
      <param name="statements">The <see cref="T:System.CodeDom.CodeStatementCollection" /> into which the event will be serialized.</param>
      <param name="value">The object on which events will be serialized.</param>
      <param name="filter">An <see cref="T:System.Attribute" /> array that filters which events will be serialized.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="manager" />, <paramref name="value" />, or <paramref name="statements" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.ComponentModel.Design.Serialization.CodeDomSerializerBase.SerializeProperties(System.ComponentModel.Design.Serialization.IDesignerSerializationManager,System.CodeDom.CodeStatementCollection,System.Object,System.Attribute[])">
      <summary>Serializes the properties on the given object into the given statement collection.</summary>
      <param name="manager">The <see cref="T:System.ComponentModel.Design.Serialization.IDesignerSerializationManager" /> to use for serialization.</param>
      <param name="statements">The <see cref="T:System.CodeDom.CodeStatementCollection" /> into which the properties will be serialized.</param>
      <param name="value">The object on which the properties will be serialized.</param>
      <param name="filter">An <see cref="T:System.Attribute" /> array that filters which properties will be serialized.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="manager" />, <paramref name="value" />, or <paramref name="statements" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.ComponentModel.Design.Serialization.CodeDomSerializerBase.SerializePropertiesToResources(System.ComponentModel.Design.Serialization.IDesignerSerializationManager,System.CodeDom.CodeStatementCollection,System.Object,System.Attribute[])">
      <summary>Serializes the properties on the given object into the invariant culture's resource bundle.</summary>
      <param name="manager">The <see cref="T:System.ComponentModel.Design.Serialization.IDesignerSerializationManager" /> to use for serialization.</param>
      <param name="statements">Not used.</param>
      <param name="value">The object whose properties will be serialized.</param>
      <param name="filter">An <see cref="T:System.Attribute" /> array that filters which properties will be serialized.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="manager" />, <paramref name="value" />, or <paramref name="statements" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.ComponentModel.Design.Serialization.CodeDomSerializerBase.SerializeProperty(System.ComponentModel.Design.Serialization.IDesignerSerializationManager,System.CodeDom.CodeStatementCollection,System.Object,System.ComponentModel.PropertyDescriptor)">
      <summary>Serializes a property on the given object.</summary>
      <param name="manager">The <see cref="T:System.ComponentModel.Design.Serialization.IDesignerSerializationManager" /> to use for serialization.</param>
      <param name="statements">The <see cref="T:System.CodeDom.CodeStatementCollection" /> into which the property will be serialized.</param>
      <param name="value">The object on which the property will be serialized.</param>
      <param name="propertyToSerialize">The property to serialize.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="manager" />, <paramref name="value" />, <paramref name="statements" />, or <paramref name="propertyToSerialize" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.ComponentModel.Design.Serialization.CodeDomSerializerBase.SerializeResource(System.ComponentModel.Design.Serialization.IDesignerSerializationManager,System.String,System.Object)">
      <summary>Serializes the given object into a resource bundle using the given resource name.</summary>
      <param name="manager">The <see cref="T:System.ComponentModel.Design.Serialization.IDesignerSerializationManager" /> to use for serialization.</param>
      <param name="resourceName">The name of the resource bundle into which <paramref name="value" /> will be serialized.</param>
      <param name="value">The object to serialize.</param>
    </member>
    <member name="M:System.ComponentModel.Design.Serialization.CodeDomSerializerBase.SerializeResourceInvariant(System.ComponentModel.Design.Serialization.IDesignerSerializationManager,System.String,System.Object)">
      <summary>Serializes the given object into a resource bundle using the given resource name.</summary>
      <param name="manager">The <see cref="T:System.ComponentModel.Design.Serialization.IDesignerSerializationManager" /> to use for serialization.</param>
      <param name="resourceName">The name of the resource bundle into which <paramref name="value" /> will be serialized.</param>
      <param name="value">The object to serialize.</param>
    </member>
    <member name="M:System.ComponentModel.Design.Serialization.CodeDomSerializerBase.SerializeToExpression(System.ComponentModel.Design.Serialization.IDesignerSerializationManager,System.Object)">
      <summary>Serializes the given object into an expression.</summary>
      <param name="manager">The <see cref="T:System.ComponentModel.Design.Serialization.IDesignerSerializationManager" /> to use for serialization.</param>
      <param name="value">The object to serialize. Can be <see langword="null" />.</param>
      <returns>A <see cref="T:System.CodeDom.CodeExpression" /> object if <paramref name="value" /> can be serialized; otherwise, <see langword="null" />.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="manager" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.ComponentModel.Design.Serialization.CodeDomSerializerBase.SerializeToResourceExpression(System.ComponentModel.Design.Serialization.IDesignerSerializationManager,System.Object)">
      <summary>Serializes the given object into an expression.</summary>
      <param name="manager">The <see cref="T:System.ComponentModel.Design.Serialization.IDesignerSerializationManager" /> to use for serialization.</param>
      <param name="value">The object to serialize.</param>
      <returns>A <see cref="T:System.CodeDom.CodeExpression" /> containing <paramref name="value" /> as a serialized expression.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="manager" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.ComponentModel.Design.Serialization.CodeDomSerializerBase.SerializeToResourceExpression(System.ComponentModel.Design.Serialization.IDesignerSerializationManager,System.Object,System.Boolean)">
      <summary>Serializes the given object into an expression appropriate for the invariant culture.</summary>
      <param name="manager">The <see cref="T:System.ComponentModel.Design.Serialization.IDesignerSerializationManager" /> to use for serialization.</param>
      <param name="value">The object to serialize.</param>
      <param name="ensureInvariant">
        <see langword="true" /> to serialize into the invariant culture; otherwise, <see langword="false" />.</param>
      <returns>A <see cref="T:System.CodeDom.CodeExpression" /> containing <paramref name="value" /> as a serialized expression.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="manager" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.ComponentModel.Design.Serialization.CodeDomSerializerBase.SetExpression(System.ComponentModel.Design.Serialization.IDesignerSerializationManager,System.Object,System.CodeDom.CodeExpression)">
      <summary>Associates an object with an expression.</summary>
      <param name="manager">The <see cref="T:System.ComponentModel.Design.Serialization.IDesignerSerializationManager" /> to use for serialization.</param>
      <param name="value">The object to serialize.</param>
      <param name="expression">The <see cref="T:System.CodeDom.CodeExpression" /> with which to associate <paramref name="value" />.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="manager" />, <paramref name="value" />, or <paramref name="expression" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.ComponentModel.Design.Serialization.CodeDomSerializerBase.SetExpression(System.ComponentModel.Design.Serialization.IDesignerSerializationManager,System.Object,System.CodeDom.CodeExpression,System.Boolean)">
      <summary>Associates an object with an expression, optionally specifying a preset expression.</summary>
      <param name="manager">The <see cref="T:System.ComponentModel.Design.Serialization.IDesignerSerializationManager" /> to use for serialization.</param>
      <param name="value">The object to serialize.</param>
      <param name="expression">The <see cref="T:System.CodeDom.CodeExpression" /> with which to associate <paramref name="value" />.</param>
      <param name="isPreset">
        <see langword="true" /> to specify a preset expression; otherwise, <see langword="false" />.</param>
    </member>
    <member name="T:System.ComponentModel.Design.Serialization.CodeDomSerializerException">
      <summary>The exception that is thrown when line number information is available for a serialization error.</summary>
    </member>
    <member name="M:System.ComponentModel.Design.Serialization.CodeDomSerializerException.#ctor(System.Exception,System.CodeDom.CodeLinePragma)">
      <summary>Initializes a new instance of the <see cref="T:System.ComponentModel.Design.Serialization.CodeDomSerializerException" /> class using the specified exception and line information.</summary>
      <param name="ex">The exception to throw.</param>
      <param name="linePragma">A <see cref="T:System.CodeDom.CodeLinePragma" /> that indicates where the exception occurred.</param>
    </member>
    <member name="M:System.ComponentModel.Design.Serialization.CodeDomSerializerException.#ctor(System.Exception,System.ComponentModel.Design.Serialization.IDesignerSerializationManager)">
      <summary>Initializes a new instance of the <see cref="T:System.ComponentModel.Design.Serialization.CodeDomSerializerException" /> class.</summary>
      <param name="ex">The exception to throw.</param>
      <param name="manager">The <see cref="T:System.ComponentModel.Design.Serialization.IDesignerSerializationManager" /> object from which to extract the context.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="Manager" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.ComponentModel.Design.Serialization.CodeDomSerializerException.#ctor(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
      <summary>Initializes a new instance of the <see cref="T:System.ComponentModel.Design.Serialization.CodeDomSerializerException" /> class using the specified serialization data and context.</summary>
      <param name="info">Stores the data that was being used to serialize or deserialize the object that the <see cref="T:System.ComponentModel.Design.Serialization.CodeDomSerializer" /> was serializing or deserializing.</param>
      <param name="context">Describes the source and destination of the stream that generated the exception, as well as a means for serialization to retain that context and an additional caller-defined context.</param>
    </member>
    <member name="M:System.ComponentModel.Design.Serialization.CodeDomSerializerException.#ctor(System.String,System.CodeDom.CodeLinePragma)">
      <summary>Initializes a new instance of the <see cref="T:System.ComponentModel.Design.Serialization.CodeDomSerializerException" /> class using the specified message and line information.</summary>
      <param name="message">A message describing the exception.</param>
      <param name="linePragma">A <see cref="T:System.CodeDom.CodeLinePragma" /> that indicates where the exception occurred.</param>
    </member>
    <member name="M:System.ComponentModel.Design.Serialization.CodeDomSerializerException.#ctor(System.String,System.ComponentModel.Design.Serialization.IDesignerSerializationManager)">
      <summary>Initializes a new instance of the <see cref="T:System.ComponentModel.Design.Serialization.CodeDomSerializerException" /> class.</summary>
      <param name="message">A message describing the exception.</param>
      <param name="manager">The <see cref="T:System.ComponentModel.Design.Serialization.IDesignerSerializationManager" /> object from which to extract the context.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="Manager" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.ComponentModel.Design.Serialization.CodeDomSerializerException.GetObjectData(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
      <summary>Sets the <see cref="T:System.Runtime.Serialization.SerializationInfo" /> object with the parameter name and additional exception information.</summary>
      <param name="info">Stores the data that was being used to serialize or deserialize the object that the <see cref="T:System.ComponentModel.Design.Serialization.CodeDomSerializer" /> was serializing or deserializing.</param>
      <param name="context">Describes the source and destination of the stream that generated the exception, as well as a means for serialization to retain that context and an additional caller-defined context.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="info" /> parameter is <see langword="null" />.</exception>
    </member>
    <member name="P:System.ComponentModel.Design.Serialization.CodeDomSerializerException.LinePragma">
      <summary>Gets or sets the line information for the error associated with this exception.</summary>
      <returns>A <see cref="T:System.CodeDom.CodeLinePragma" /> that indicates the line information for the error.</returns>
    </member>
    <member name="T:System.ComponentModel.Design.Serialization.CollectionCodeDomSerializer">
      <summary>Serializes collections.</summary>
    </member>
    <member name="M:System.ComponentModel.Design.Serialization.CollectionCodeDomSerializer.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.ComponentModel.Design.Serialization.CollectionCodeDomSerializer" /> class.</summary>
    </member>
    <member name="M:System.ComponentModel.Design.Serialization.CollectionCodeDomSerializer.MethodSupportsSerialization(System.Reflection.MethodInfo)">
      <summary>Verifies serialization support by the <paramref name="method" />.</summary>
      <param name="method">The <see cref="T:System.Reflection.MethodInfo" /> to check for serialization attributes.</param>
      <returns>
        <see langword="true" /> if the <paramref name="method" /> supports serialization; otherwise, <see langword="false" />.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="method" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.ComponentModel.Design.Serialization.CollectionCodeDomSerializer.Serialize(System.ComponentModel.Design.Serialization.IDesignerSerializationManager,System.Object)">
      <summary>Serializes the given collection into a CodeDOM object.</summary>
      <param name="manager">The <see cref="T:System.ComponentModel.Design.Serialization.IDesignerSerializationManager" /> to use during serialization.</param>
      <param name="value">The object to serialize.</param>
      <returns>A CodeDOM object representing <paramref name="value" />.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="manager" /> or <paramref name="value" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.ComponentModel.Design.Serialization.CollectionCodeDomSerializer.SerializeCollection(System.ComponentModel.Design.Serialization.IDesignerSerializationManager,System.CodeDom.CodeExpression,System.Type,System.Collections.ICollection,System.Collections.ICollection)">
      <summary>Serializes the given collection.</summary>
      <param name="manager">The <see cref="T:System.ComponentModel.Design.Serialization.IDesignerSerializationManager" /> to use during serialization.</param>
      <param name="targetExpression">The <see cref="T:System.CodeDom.CodeExpression" /> that refers to the collection</param>
      <param name="targetType">The <see cref="T:System.Type" /> of the collection.</param>
      <param name="originalCollection">The collection to serialize.</param>
      <param name="valuesToSerialize">The values to serialize.</param>
      <returns>Serialized collection if the serialization process succeeded; otherwise, <see langword="null" />.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="manager" />, <paramref name="targetType" />, <paramref name="originalCollection" />, or <paramref name="valuesToSerialize" /> is <see langword="null" />.</exception>
    </member>
    <member name="T:System.ComponentModel.Design.Serialization.DesignerSerializationManager">
      <summary>Provides an implementation of the <see cref="T:System.ComponentModel.Design.Serialization.IDesignerSerializationManager" /> interface.</summary>
    </member>
    <member name="M:System.ComponentModel.Design.Serialization.DesignerSerializationManager.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.ComponentModel.Design.Serialization.DesignerSerializationManager" /> class.</summary>
    </member>
    <member name="M:System.ComponentModel.Design.Serialization.DesignerSerializationManager.#ctor(System.IServiceProvider)">
      <summary>Initializes a new instance of the <see cref="T:System.ComponentModel.Design.Serialization.DesignerSerializationManager" /> class with the given service provider.</summary>
      <param name="provider">An <see cref="T:System.IServiceProvider" />.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="provider" /> is <see langword="null" />.</exception>
    </member>
    <member name="P:System.ComponentModel.Design.Serialization.DesignerSerializationManager.Container">
      <summary>Gets or sets to the container for this serialization manager.</summary>
      <returns>The <see cref="T:System.ComponentModel.IContainer" /> to which the serialization manager will add components.</returns>
      <exception cref="T:System.InvalidOperationException">The serialization manager has an active serialization session.</exception>
    </member>
    <member name="M:System.ComponentModel.Design.Serialization.DesignerSerializationManager.CreateInstance(System.Type,System.Collections.ICollection,System.String,System.Boolean)">
      <summary>Creates an instance of a type.</summary>
      <param name="type">The type to create an instance of.</param>
      <param name="arguments">The parameters of the type's constructor. This can be <see langword="null" /> or an empty collection to invoke the default constructor.</param>
      <param name="name">A name to give the object. If <see langword="null" />, the object will not be given a name, unless the object is added to a container and the container gives the object a name.</param>
      <param name="addToContainer">
        <see langword="true" /> to add the object to the container if the object implements <see cref="T:System.ComponentModel.IComponent" />; otherwise, <see langword="false" />.</param>
      <returns>A new instance of the type specified by <paramref name="type" />.</returns>
      <exception cref="T:System.Runtime.Serialization.SerializationException">
        <paramref name="type" /> does not have a constructor that takes parameters contained in <paramref name="arguments" />.</exception>
    </member>
    <member name="M:System.ComponentModel.Design.Serialization.DesignerSerializationManager.CreateSession">
      <summary>Creates a new serialization session.</summary>
      <returns>An <see cref="T:System.IDisposable" /> that represents a new serialization session.</returns>
      <exception cref="T:System.InvalidOperationException">The serialization manager is already within a session. This version of <see cref="T:System.ComponentModel.Design.Serialization.DesignerSerializationManager" /> does not support simultaneous sessions.</exception>
    </member>
    <member name="P:System.ComponentModel.Design.Serialization.DesignerSerializationManager.Errors">
      <summary>Gets the list of errors that occurred during serialization or deserialization.</summary>
      <returns>The list of errors that occurred during serialization or deserialization.</returns>
      <exception cref="T:System.InvalidOperationException">This property was accessed outside of a serialization session.</exception>
    </member>
    <member name="M:System.ComponentModel.Design.Serialization.DesignerSerializationManager.GetRuntimeType(System.String)">
      <summary>Gets the type corresponding to the specified type name.</summary>
      <param name="typeName">The name of the type to get.</param>
      <returns>The specified type.</returns>
    </member>
    <member name="M:System.ComponentModel.Design.Serialization.DesignerSerializationManager.GetSerializer(System.Type,System.Type)">
      <summary>Gets the serializer for the given object type.</summary>
      <param name="objectType">The type of object for which to retrieve the serializer.</param>
      <param name="serializerType">The type of serializer to retrieve.</param>
      <returns>The serializer for <paramref name="objectType" />, or <see langword="null" />, if not found.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="objectType" /> or <paramref name="serializerType" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.ComponentModel.Design.Serialization.DesignerSerializationManager.GetService(System.Type)">
      <summary>Gets the requested service.</summary>
      <param name="serviceType">The type of service to retrieve.</param>
      <returns>The requested service, or <see langword="null" /> if the service cannot be resolved.</returns>
    </member>
    <member name="M:System.ComponentModel.Design.Serialization.DesignerSerializationManager.GetType(System.String)">
      <summary>Gets the requested type.</summary>
      <param name="typeName">The name of the type to retrieve.</param>
      <returns>The requested type, or <see langword="null" /> if the type cannot be resolved.</returns>
    </member>
    <member name="M:System.ComponentModel.Design.Serialization.DesignerSerializationManager.OnResolveName(System.ComponentModel.Design.Serialization.ResolveNameEventArgs)">
      <summary>Raises the <see cref="E:System.ComponentModel.Design.Serialization.IDesignerSerializationManager.ResolveName" /> event.</summary>
      <param name="e">A <see cref="T:System.ComponentModel.Design.Serialization.ResolveNameEventArgs" /> that contains the event data.</param>
    </member>
    <member name="M:System.ComponentModel.Design.Serialization.DesignerSerializationManager.OnSessionCreated(System.EventArgs)">
      <summary>Raises the <see cref="E:System.ComponentModel.Design.Serialization.DesignerSerializationManager.SessionCreated" /> event.</summary>
      <param name="e">An <see cref="T:System.EventArgs" /> that contains the event data.</param>
    </member>
    <member name="M:System.ComponentModel.Design.Serialization.DesignerSerializationManager.OnSessionDisposed(System.EventArgs)">
      <summary>Raises the <see cref="E:System.ComponentModel.Design.Serialization.DesignerSerializationManager.SessionDisposed" /> event.</summary>
      <param name="e">An <see cref="T:System.EventArgs" /> that contains the event data.</param>
    </member>
    <member name="P:System.ComponentModel.Design.Serialization.DesignerSerializationManager.PreserveNames">
      <summary>Gets or sets a value indicating whether the <see cref="M:System.ComponentModel.Design.Serialization.DesignerSerializationManager.CreateInstance(System.Type,System.Collections.ICollection,System.String,System.Boolean)" /> method should check for the presence of the given name in the container.</summary>
      <returns>
        <see langword="true" /> if <see cref="M:System.ComponentModel.Design.Serialization.DesignerSerializationManager.CreateInstance(System.Type,System.Collections.ICollection,System.String,System.Boolean)" /> will pass the given component name; <see langword="false" /> if <see cref="M:System.ComponentModel.Design.Serialization.DesignerSerializationManager.CreateInstance(System.Type,System.Collections.ICollection,System.String,System.Boolean)" /> will check for the presence of the given name in the container. The default is <see langword="true" />.</returns>
      <exception cref="T:System.InvalidOperationException">This property was changed from within a serialization session.</exception>
    </member>
    <member name="P:System.ComponentModel.Design.Serialization.DesignerSerializationManager.PropertyProvider">
      <summary>Gets the object that should be used to provide properties to the serialization manager's <see cref="P:System.ComponentModel.Design.Serialization.IDesignerSerializationManager.Properties" /> property.</summary>
      <returns>The object that should be used to provide properties to the serialization manager's <see cref="P:System.ComponentModel.Design.Serialization.IDesignerSerializationManager.Properties" /> property.</returns>
    </member>
    <member name="P:System.ComponentModel.Design.Serialization.DesignerSerializationManager.RecycleInstances">
      <summary>Gets or sets a value that indicates whether <see cref="M:System.ComponentModel.Design.Serialization.DesignerSerializationManager.CreateInstance(System.Type,System.Collections.ICollection,System.String,System.Boolean)" /> will always create a new instance of a type.</summary>
      <returns>
        <see langword="true" /> if <see cref="M:System.ComponentModel.Design.Serialization.DesignerSerializationManager.CreateInstance(System.Type,System.Collections.ICollection,System.String,System.Boolean)" /> will return the existing instance; <see langword="false" /> if <see cref="M:System.ComponentModel.Design.Serialization.DesignerSerializationManager.CreateInstance(System.Type,System.Collections.ICollection,System.String,System.Boolean)" /> will create a new instance of a type. The default is <see langword="false" />.</returns>
      <exception cref="T:System.InvalidOperationException">The serialization manager has an active serialization session.</exception>
    </member>
    <member name="E:System.ComponentModel.Design.Serialization.DesignerSerializationManager.SessionCreated">
      <summary>Occurs when a session is created.</summary>
    </member>
    <member name="E:System.ComponentModel.Design.Serialization.DesignerSerializationManager.SessionDisposed">
      <summary>Occurs when a session is disposed.</summary>
    </member>
    <member name="M:System.ComponentModel.Design.Serialization.DesignerSerializationManager.System#ComponentModel#Design#Serialization#IDesignerSerializationManager#AddSerializationProvider(System.ComponentModel.Design.Serialization.IDesignerSerializationProvider)">
      <summary>Adds a custom serialization provider to the serialization manager.</summary>
      <param name="provider">The serialization provider to add.</param>
    </member>
    <member name="P:System.ComponentModel.Design.Serialization.DesignerSerializationManager.System#ComponentModel#Design#Serialization#IDesignerSerializationManager#Context">
      <summary>Gets the context stack for this serialization session.</summary>
      <returns>A <see cref="T:System.ComponentModel.Design.Serialization.ContextStack" /> that stores data.</returns>
      <exception cref="T:System.InvalidOperationException">This property was accessed outside of a serialization session.</exception>
    </member>
    <member name="M:System.ComponentModel.Design.Serialization.DesignerSerializationManager.System#ComponentModel#Design#Serialization#IDesignerSerializationManager#CreateInstance(System.Type,System.Collections.ICollection,System.String,System.Boolean)">
      <summary>Implements the <see cref="M:System.ComponentModel.Design.Serialization.IDesignerSerializationManager.CreateInstance(System.Type,System.Collections.ICollection,System.String,System.Boolean)" /> method.</summary>
      <param name="type">The data type to create.</param>
      <param name="arguments">The arguments to pass to the constructor for this type.</param>
      <param name="name">The name of the object. This name can be used to access the object later through <see cref="M:System.ComponentModel.Design.Serialization.IDesignerSerializationManager.GetInstance(System.String)" />. If <see langword="null" /> is passed, the object is still created but cannot be accessed by name.</param>
      <param name="addToContainer">
        <see langword="true" /> to add this object to the design container. The object must implement <see cref="T:System.ComponentModel.IComponent" /> for this to have any effect.</param>
      <returns>The newly created object instance.</returns>
    </member>
    <member name="M:System.ComponentModel.Design.Serialization.DesignerSerializationManager.System#ComponentModel#Design#Serialization#IDesignerSerializationManager#GetInstance(System.String)">
      <summary>Retrieves an instance of a created object of the specified name.</summary>
      <param name="name">The name of the object to retrieve.</param>
      <returns>An instance of the object with the given name, or <see langword="null" /> if no object by that name can be found.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="name" /> is <see langword="null" />.</exception>
      <exception cref="T:System.InvalidOperationException">This property was accessed outside of a serialization session.</exception>
    </member>
    <member name="M:System.ComponentModel.Design.Serialization.DesignerSerializationManager.System#ComponentModel#Design#Serialization#IDesignerSerializationManager#GetName(System.Object)">
      <summary>Retrieves a name for the specified object.</summary>
      <param name="value">The object for which to retrieve the name.</param>
      <returns>The name of the object, or <see langword="null" /> if the object is unnamed.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" /> is <see langword="null" />.</exception>
      <exception cref="T:System.InvalidOperationException">This property was accessed outside of a serialization session.</exception>
    </member>
    <member name="M:System.ComponentModel.Design.Serialization.DesignerSerializationManager.System#ComponentModel#Design#Serialization#IDesignerSerializationManager#GetSerializer(System.Type,System.Type)">
      <summary>Gets a serializer of the requested type for the specified object type.</summary>
      <param name="objectType">The type of the object to get the serializer for.</param>
      <param name="serializerType">The type of the serializer to retrieve.</param>
      <returns>An instance of the requested serializer, or <see langword="null" /> if no appropriate serializer can be located.</returns>
    </member>
    <member name="M:System.ComponentModel.Design.Serialization.DesignerSerializationManager.System#ComponentModel#Design#Serialization#IDesignerSerializationManager#GetType(System.String)">
      <summary>Gets a type of the specified name.</summary>
      <param name="typeName">The fully qualified name of the type to load.</param>
      <returns>An instance of the type, or <see langword="null" /> if the type cannot be loaded.</returns>
      <exception cref="T:System.InvalidOperationException">This property was accessed outside of a serialization session.</exception>
    </member>
    <member name="P:System.ComponentModel.Design.Serialization.DesignerSerializationManager.System#ComponentModel#Design#Serialization#IDesignerSerializationManager#Properties">
      <summary>Implements the <see cref="P:System.ComponentModel.Design.Serialization.IDesignerSerializationManager.Properties" /> property.</summary>
      <returns>A <see cref="T:System.ComponentModel.PropertyDescriptorCollection" /> containing the properties to be serialized.</returns>
    </member>
    <member name="M:System.ComponentModel.Design.Serialization.DesignerSerializationManager.System#ComponentModel#Design#Serialization#IDesignerSerializationManager#RemoveSerializationProvider(System.ComponentModel.Design.Serialization.IDesignerSerializationProvider)">
      <summary>Removes a previously added serialization provider.</summary>
      <param name="provider">The <see cref="T:System.ComponentModel.Design.Serialization.IDesignerSerializationProvider" /> to remove.</param>
    </member>
    <member name="M:System.ComponentModel.Design.Serialization.DesignerSerializationManager.System#ComponentModel#Design#Serialization#IDesignerSerializationManager#ReportError(System.Object)">
      <summary>Used to report a recoverable error in serialization.</summary>
      <param name="errorInformation">An object containing the error information, usually of type <see cref="T:System.String" /> or <see cref="T:System.Exception" />.</param>
      <exception cref="T:System.InvalidOperationException">This property was accessed outside of a serialization session.</exception>
    </member>
    <member name="E:System.ComponentModel.Design.Serialization.DesignerSerializationManager.System#ComponentModel#Design#Serialization#IDesignerSerializationManager#ResolveName">
      <summary>Occurs when <see cref="M:System.ComponentModel.Design.Serialization.DesignerSerializationManager.System#ComponentModel#Design#Serialization#IDesignerSerializationManager#GetName(System.Object)" /> cannot locate the specified name in the serialization manager's name table.</summary>
      <exception cref="T:System.InvalidOperationException">The serialization manager does not have an active serialization session.</exception>
    </member>
    <member name="E:System.ComponentModel.Design.Serialization.DesignerSerializationManager.System#ComponentModel#Design#Serialization#IDesignerSerializationManager#SerializationComplete">
      <summary>Occurs when serialization is complete.</summary>
      <exception cref="T:System.InvalidOperationException">The serialization manager does not have an active serialization session.</exception>
    </member>
    <member name="M:System.ComponentModel.Design.Serialization.DesignerSerializationManager.System#ComponentModel#Design#Serialization#IDesignerSerializationManager#SetName(System.Object,System.String)">
      <summary>Sets the name for the specified object.</summary>
      <param name="instance">The object to set the name.</param>
      <param name="name">A <see cref="T:System.String" /> used as the name of the object.</param>
      <exception cref="T:System.ArgumentNullException">One or both of the parameters are <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentException">The object specified by instance already has a name, or <paramref name="name" /> is already used by another named object.</exception>
      <exception cref="T:System.InvalidOperationException">This property was accessed outside of a serialization session.</exception>
    </member>
    <member name="M:System.ComponentModel.Design.Serialization.DesignerSerializationManager.System#IServiceProvider#GetService(System.Type)">
      <summary>For a description of this member, see the <see cref="M:System.IServiceProvider.GetService(System.Type)" /> method.</summary>
      <param name="serviceType">An object that specifies the type of service object to get.</param>
      <returns>A service object of type <paramref name="serviceType" />.  
 -or-  
 <see langword="null" /> if there is no service object of type <paramref name="serviceType" />.</returns>
    </member>
    <member name="P:System.ComponentModel.Design.Serialization.DesignerSerializationManager.ValidateRecycledTypes">
      <summary>Gets or sets a value that indicates whether the <see cref="M:System.ComponentModel.Design.Serialization.DesignerSerializationManager.CreateInstance(System.Type,System.Collections.ICollection,System.String,System.Boolean)" /> method will verify that matching names refer to the same type.</summary>
      <returns>
        <see langword="true" /> if <see cref="M:System.ComponentModel.Design.Serialization.DesignerSerializationManager.CreateInstance(System.Type,System.Collections.ICollection,System.String,System.Boolean)" /> verifies types; otherwise, <see langword="false" /> if it does not. The default is <see langword="true" />.</returns>
      <exception cref="T:System.InvalidOperationException">The serialization manager has an active serialization session.</exception>
    </member>
    <member name="T:System.ComponentModel.Design.Serialization.ExpressionContext">
      <summary>Provides a means of passing context state among serializers. This class cannot be inherited.</summary>
    </member>
    <member name="M:System.ComponentModel.Design.Serialization.ExpressionContext.#ctor(System.CodeDom.CodeExpression,System.Type,System.Object)">
      <summary>Initializes a new instance of the <see cref="T:System.ComponentModel.Design.Serialization.ExpressionContext" /> class with the given expression and owner.</summary>
      <param name="expression">The given code expression.</param>
      <param name="expressionType">The given code expression type.</param>
      <param name="owner">The given code expression owner.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="expression" />, <paramref name="expressionType" />, or <paramref name="owner" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.ComponentModel.Design.Serialization.ExpressionContext.#ctor(System.CodeDom.CodeExpression,System.Type,System.Object,System.Object)">
      <summary>Initializes a new instance of the <see cref="T:System.ComponentModel.Design.Serialization.ExpressionContext" /> class with a current value.</summary>
      <param name="expression">The given code expression.</param>
      <param name="expressionType">The given code expression type.</param>
      <param name="owner">The given code expression owner.</param>
      <param name="presetValue">The given code expression preset value.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="expression" />, <paramref name="expressionType" />, or <paramref name="owner" /> is <see langword="null" />.</exception>
    </member>
    <member name="P:System.ComponentModel.Design.Serialization.ExpressionContext.Expression">
      <summary>Gets the expression this context represents.</summary>
      <returns>The expression this context represents.</returns>
    </member>
    <member name="P:System.ComponentModel.Design.Serialization.ExpressionContext.ExpressionType">
      <summary>Gets the <see cref="T:System.Type" /> of the expression.</summary>
      <returns>The <see cref="T:System.Type" /> of the expression.</returns>
    </member>
    <member name="P:System.ComponentModel.Design.Serialization.ExpressionContext.Owner">
      <summary>Gets the object owning this expression.</summary>
      <returns>The object owning this expression.</returns>
    </member>
    <member name="P:System.ComponentModel.Design.Serialization.ExpressionContext.PresetValue">
      <summary>Gets the preset value of an expression.</summary>
      <returns>The preset value of this expression, or <see langword="null" /> if not assigned.</returns>
    </member>
    <member name="T:System.ComponentModel.Design.Serialization.MemberCodeDomSerializer">
      <summary>Provides the base class for serializing a reflection primitive within the object graph.</summary>
    </member>
    <member name="M:System.ComponentModel.Design.Serialization.MemberCodeDomSerializer.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.ComponentModel.Design.Serialization.MemberCodeDomSerializer" /> class.</summary>
    </member>
    <member name="M:System.ComponentModel.Design.Serialization.MemberCodeDomSerializer.Serialize(System.ComponentModel.Design.Serialization.IDesignerSerializationManager,System.Object,System.ComponentModel.MemberDescriptor,System.CodeDom.CodeStatementCollection)">
      <summary>Serializes the given member descriptor on the given value to a statement collection.</summary>
      <param name="manager">The serialization manager to use for serialization.</param>
      <param name="value">The object to which the member is bound.</param>
      <param name="descriptor">The descriptor of the member to serialize.</param>
      <param name="statements">The <see cref="T:System.CodeDom.CodeStatementCollection" /> into which <paramref name="descriptor" /> is serialized.</param>
    </member>
    <member name="M:System.ComponentModel.Design.Serialization.MemberCodeDomSerializer.ShouldSerialize(System.ComponentModel.Design.Serialization.IDesignerSerializationManager,System.Object,System.ComponentModel.MemberDescriptor)">
      <summary>Determines if the given member should be serialized.</summary>
      <param name="manager">The serialization manager to use for serialization.</param>
      <param name="value">The object to which the member is bound.</param>
      <param name="descriptor">The descriptor of the member to serialize.</param>
      <returns>
        <see langword="true" />, if the member described by <paramref name="descriptor" /> should be serialized; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="T:System.ComponentModel.Design.Serialization.ObjectStatementCollection">
      <summary>Holds a table of statements that is offered by the <see cref="T:System.ComponentModel.Design.Serialization.StatementContext" />. This class cannot be inherited.</summary>
    </member>
    <member name="M:System.ComponentModel.Design.Serialization.ObjectStatementCollection.ContainsKey(System.Object)">
      <summary>Determines whether the table contains the given statement owner.</summary>
      <param name="statementOwner">The owner of the statement collection.</param>
      <returns>
        <see langword="true" /> if <paramref name="statementOwner" /> is in the table; otherwise, <see langword="false" />.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="statementOwner" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.ComponentModel.Design.Serialization.ObjectStatementCollection.GetEnumerator">
      <summary>Returns an <see cref="T:System.Collections.IEnumerator" /> for the <see cref="T:System.ComponentModel.Design.Serialization.ObjectStatementCollection" />.</summary>
      <returns>An <see cref="T:System.Collections.IEnumerator" /> for the <see cref="T:System.ComponentModel.Design.Serialization.ObjectStatementCollection" />.</returns>
    </member>
    <member name="P:System.ComponentModel.Design.Serialization.ObjectStatementCollection.Item(System.Object)">
      <summary>Gets the statement collection for the given owner.</summary>
      <param name="statementOwner">The owner of the statement collection.</param>
      <returns>The statement collection for <paramref name="statementOwner" />, or <see langword="null" /> if <paramref name="statementOwner" /> is not in the table.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="statementOwner" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.ComponentModel.Design.Serialization.ObjectStatementCollection.Populate(System.Collections.ICollection)">
      <summary>Populates the statement table with a collection of statement owners.</summary>
      <param name="statementOwners">A collection of statement owners to add to the table.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="statementOwner" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.ComponentModel.Design.Serialization.ObjectStatementCollection.Populate(System.Object)">
      <summary>Populates the statement table with a statement owner.</summary>
      <param name="owner">The statement owner to add to the table.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="owner" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.ComponentModel.Design.Serialization.ObjectStatementCollection.System#Collections#IEnumerable#GetEnumerator">
      <summary>For a description of this member, see the <see cref="M:System.Collections.IEnumerable.GetEnumerator" /> method.</summary>
      <returns>An <see cref="T:System.Collections.IEnumerator" /> that can be used to iterate through the collection.</returns>
    </member>
    <member name="T:System.ComponentModel.Design.Serialization.RootContext">
      <summary>A <see cref="T:System.ComponentModel.Design.Serialization.CodeDomSerializer" /> adds a root context to provide a definition of the root object. This class cannot be inherited</summary>
    </member>
    <member name="M:System.ComponentModel.Design.Serialization.RootContext.#ctor(System.CodeDom.CodeExpression,System.Object)">
      <summary>Initializes a new instance of the <see cref="T:System.ComponentModel.Design.Serialization.RootContext" /> class.</summary>
      <param name="expression">The expression representing the root object in the object graph.</param>
      <param name="value">The root object of the object graph.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="expression" /> or <paramref name="value" /> is <see langword="null" />.</exception>
    </member>
    <member name="P:System.ComponentModel.Design.Serialization.RootContext.Expression">
      <summary>Gets the expression representing the root object in the object graph.</summary>
      <returns>A <see cref="T:System.CodeDom.CodeExpression" /> representing the root object in the object graph.</returns>
    </member>
    <member name="P:System.ComponentModel.Design.Serialization.RootContext.Value">
      <summary>Gets the root object of the object graph.</summary>
      <returns>The root object of the object graph.</returns>
    </member>
    <member name="T:System.ComponentModel.Design.Serialization.SerializeAbsoluteContext">
      <summary>Specifies that serializers should handle default values. This class cannot be inherited.</summary>
    </member>
    <member name="M:System.ComponentModel.Design.Serialization.SerializeAbsoluteContext.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.ComponentModel.Design.Serialization.SerializeAbsoluteContext" /> class.</summary>
    </member>
    <member name="M:System.ComponentModel.Design.Serialization.SerializeAbsoluteContext.#ctor(System.ComponentModel.MemberDescriptor)">
      <summary>Initializes a new instance of the <see cref="T:System.ComponentModel.Design.Serialization.SerializeAbsoluteContext" /> class with the option of binding to a specific member.</summary>
      <param name="member">The member to which this context is bound. Can be <see langword="null" />.</param>
    </member>
    <member name="P:System.ComponentModel.Design.Serialization.SerializeAbsoluteContext.Member">
      <summary>Gets the member to which this context is bound.</summary>
      <returns>The member to which this context is bound, or <see langword="null" /> if the context is bound to all members of an object.</returns>
    </member>
    <member name="M:System.ComponentModel.Design.Serialization.SerializeAbsoluteContext.ShouldSerialize(System.ComponentModel.MemberDescriptor)">
      <summary>Gets a value indicating whether the given member should be serialized in this context.</summary>
      <param name="member">The member to be examined for serialization.</param>
      <returns>
        <see langword="true" /> if the given member should be serialized in this context; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="T:System.ComponentModel.Design.Serialization.StatementContext">
      <summary>Provides a location into which statements can be serialized. This class cannot be inherited.</summary>
    </member>
    <member name="M:System.ComponentModel.Design.Serialization.StatementContext.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.ComponentModel.Design.Serialization.StatementContext" /> class.</summary>
    </member>
    <member name="P:System.ComponentModel.Design.Serialization.StatementContext.StatementCollection">
      <summary>Gets a collection of statements offered by the statement context.</summary>
      <returns>An <see cref="T:System.ComponentModel.Design.Serialization.ObjectStatementCollection" /> containing statements offered by the statement context.</returns>
    </member>
    <member name="T:System.ComponentModel.Design.Serialization.TypeCodeDomSerializer">
      <summary>Serializes an object to a new type.</summary>
    </member>
    <member name="M:System.ComponentModel.Design.Serialization.TypeCodeDomSerializer.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.ComponentModel.Design.Serialization.TypeCodeDomSerializer" /> class.</summary>
    </member>
    <member name="M:System.ComponentModel.Design.Serialization.TypeCodeDomSerializer.Deserialize(System.ComponentModel.Design.Serialization.IDesignerSerializationManager,System.CodeDom.CodeTypeDeclaration)">
      <summary>Deserializes the given type declaration.</summary>
      <param name="manager">The serialization manager to use for serialization.</param>
      <param name="declaration">Type declaration to use for serialization.</param>
      <returns>The root object.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="manager" /> or <paramref name="typeDecl" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.ComponentModel.Design.Serialization.TypeCodeDomSerializer.GetInitializeMethod(System.ComponentModel.Design.Serialization.IDesignerSerializationManager,System.CodeDom.CodeTypeDeclaration,System.Object)">
      <summary>Returns the method where statements used to serialize a member are stored.</summary>
      <param name="manager">The serialization manager to use for serialization.</param>
      <param name="declaration">The type declaration to use for serialization.</param>
      <param name="value">The value to use for serialization.</param>
      <returns>The method used to emit all of the initialization code for the given member.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="manager" />, <paramref name="typeDecl" />, or <paramref name="value" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.ComponentModel.Design.Serialization.TypeCodeDomSerializer.GetInitializeMethods(System.ComponentModel.Design.Serialization.IDesignerSerializationManager,System.CodeDom.CodeTypeDeclaration)">
      <summary>Returns an array of methods to be interpreted during deserialization.</summary>
      <param name="manager">The serialization manager to use for serialization.</param>
      <param name="declaration">The type declaration to use for serialization.</param>
      <returns>A <see cref="T:System.CodeDom.CodeMemberMethod" /> array of methods to be interpreted during deserialization.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="manager" /> or <paramref name="typeDecl" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.ComponentModel.Design.Serialization.TypeCodeDomSerializer.Serialize(System.ComponentModel.Design.Serialization.IDesignerSerializationManager,System.Object,System.Collections.ICollection)">
      <summary>Serializes the object root by creating a new type declaration that defines root.</summary>
      <param name="manager">The serialization manager to use for serialization.</param>
      <param name="root">The object to serialize.</param>
      <param name="members">Optional collection of members. Can be <see langword="null" /> or empty.</param>
      <returns>A <see cref="T:System.CodeDom.CodeTypeDeclaration" /> that defines the root object.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="manager" /> or <paramref name="root" /> is <see langword="null" />.</exception>
    </member>
    <member name="T:System.ComponentModel.Design.UndoEngine">
      <summary>Specifies generic undo/redo functionality at design time.</summary>
    </member>
    <member name="M:System.ComponentModel.Design.UndoEngine.#ctor(System.IServiceProvider)">
      <summary>Initializes a new instance of the <see cref="T:System.ComponentModel.Design.UndoEngine" /> class.</summary>
      <param name="provider">A parenting service provider.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="provider" /> is <see langword="null" />.</exception>
      <exception cref="T:System.NotSupportedException">A required service cannot be found. See <see cref="T:System.ComponentModel.Design.UndoEngine" /> for required services. If you have removed this service, ensure that you provide a replacement.</exception>
    </member>
    <member name="M:System.ComponentModel.Design.UndoEngine.AddUndoUnit(System.ComponentModel.Design.UndoEngine.UndoUnit)">
      <summary>Adds an <see cref="T:System.ComponentModel.Design.UndoEngine.UndoUnit" /> to the undo stack.</summary>
      <param name="unit">The undo unit to add</param>
    </member>
    <member name="M:System.ComponentModel.Design.UndoEngine.CreateUndoUnit(System.String,System.Boolean)">
      <summary>Creates a new <see cref="T:System.ComponentModel.Design.UndoEngine.UndoUnit" />.</summary>
      <param name="name">The name of the unit to create.</param>
      <param name="primary">
        <see langword="true" /> to create the first of a series of nested units; <see langword="false" /> to create subsequent nested units.</param>
      <returns>A new <see cref="T:System.ComponentModel.Design.UndoEngine.UndoUnit" /> with a specified name.</returns>
    </member>
    <member name="M:System.ComponentModel.Design.UndoEngine.DiscardUndoUnit(System.ComponentModel.Design.UndoEngine.UndoUnit)">
      <summary>Discards an <see cref="T:System.ComponentModel.Design.UndoEngine.UndoUnit" />.</summary>
      <param name="unit">The unit to discard.</param>
    </member>
    <member name="M:System.ComponentModel.Design.UndoEngine.Dispose">
      <summary>Releases all resources used by the <see cref="T:System.ComponentModel.Design.UndoEngine" />.</summary>
    </member>
    <member name="M:System.ComponentModel.Design.UndoEngine.Dispose(System.Boolean)">
      <summary>Releases the unmanaged resources used by the <see cref="T:System.ComponentModel.Design.UndoEngine" /> and optionally releases the managed resources.</summary>
      <param name="disposing">
        <see langword="true" /> to release both managed and unmanaged resources; <see langword="false" /> to release only unmanaged resources.</param>
    </member>
    <member name="P:System.ComponentModel.Design.UndoEngine.Enabled">
      <summary>Enables or disables the <see cref="T:System.ComponentModel.Design.UndoEngine" />.</summary>
      <returns>
        <see langword="true" /> if the <see cref="T:System.ComponentModel.Design.UndoEngine" /> is enabled; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.ComponentModel.Design.UndoEngine.GetRequiredService(System.Type)">
      <summary>Gets the requested service.</summary>
      <param name="serviceType">The type of service to retrieve.</param>
      <returns>The requested service, if found.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="serviceType" /> is <see langword="null" />.</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="serviceType" /> is required but cannot be found. If you have removed this service, ensure that you provide a replacement.</exception>
    </member>
    <member name="M:System.ComponentModel.Design.UndoEngine.GetService(System.Type)">
      <summary>Gets the requested service.</summary>
      <param name="serviceType">The type of service to retrieve.</param>
      <returns>The requested service, or <see langword="null" /> if the requested service is not found.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="serviceType" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.ComponentModel.Design.UndoEngine.OnUndoing(System.EventArgs)">
      <summary>Raises the <see cref="E:System.ComponentModel.Design.UndoEngine.Undoing" /> event.</summary>
      <param name="e">An <see cref="T:System.EventArgs" /> that contains the event data.</param>
    </member>
    <member name="M:System.ComponentModel.Design.UndoEngine.OnUndone(System.EventArgs)">
      <summary>Raises the <see cref="E:System.ComponentModel.Design.UndoEngine.Undone" /> event.</summary>
      <param name="e">An <see cref="T:System.EventArgs" /> that contains the event data.</param>
    </member>
    <member name="E:System.ComponentModel.Design.UndoEngine.Undoing">
      <summary>Occurs immediately before an undo action is performed.</summary>
    </member>
    <member name="P:System.ComponentModel.Design.UndoEngine.UndoInProgress">
      <summary>Indicates if an undo action is in progress.</summary>
      <returns>
        <see langword="true" /> if an undo action is in progress; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="E:System.ComponentModel.Design.UndoEngine.Undone">
      <summary>Occurs immediately after an undo action is performed.</summary>
    </member>
    <member name="T:System.ComponentModel.Design.UndoEngine.UndoUnit">
      <summary>Encapsulates a unit of work that a user can undo.</summary>
    </member>
    <member name="M:System.ComponentModel.Design.UndoEngine.UndoUnit.#ctor(System.ComponentModel.Design.UndoEngine,System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.ComponentModel.Design.UndoEngine.UndoUnit" /> class.</summary>
      <param name="engine">The undo engine that owns this undo unit.</param>
      <param name="name">The name for this undo unit.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="engine" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.ComponentModel.Design.UndoEngine.UndoUnit.Close">
      <summary>Receives a call from the undo engine to close this unit.</summary>
    </member>
    <member name="M:System.ComponentModel.Design.UndoEngine.UndoUnit.ComponentAdded(System.ComponentModel.Design.ComponentEventArgs)">
      <summary>Receives a call from the <see cref="T:System.ComponentModel.Design.UndoEngine" /> in response to a <see cref="E:System.ComponentModel.Design.IComponentChangeService.ComponentAdded" /> event.</summary>
      <param name="e">A <see cref="T:System.ComponentModel.Design.ComponentEventArgs" /> that contains the event data.</param>
    </member>
    <member name="M:System.ComponentModel.Design.UndoEngine.UndoUnit.ComponentAdding(System.ComponentModel.Design.ComponentEventArgs)">
      <summary>Receives a call from the <see cref="T:System.ComponentModel.Design.UndoEngine" /> in response to a <see cref="E:System.ComponentModel.Design.IComponentChangeService.ComponentAdding" /> event.</summary>
      <param name="e">A <see cref="T:System.ComponentModel.Design.ComponentEventArgs" /> that contains the event data.</param>
    </member>
    <member name="M:System.ComponentModel.Design.UndoEngine.UndoUnit.ComponentChanged(System.ComponentModel.Design.ComponentChangedEventArgs)">
      <summary>Receives a call from the <see cref="T:System.ComponentModel.Design.UndoEngine" /> in response to a <see cref="E:System.ComponentModel.Design.IComponentChangeService.ComponentChanged" /> event.</summary>
      <param name="e">A <see cref="T:System.ComponentModel.Design.ComponentChangedEventArgs" /> that contains the event data.</param>
    </member>
    <member name="M:System.ComponentModel.Design.UndoEngine.UndoUnit.ComponentChanging(System.ComponentModel.Design.ComponentChangingEventArgs)">
      <summary>Receives a call from the <see cref="T:System.ComponentModel.Design.UndoEngine" /> in response to a <see cref="E:System.ComponentModel.Design.IComponentChangeService.ComponentChanging" /> event.</summary>
      <param name="e">A <see cref="T:System.ComponentModel.Design.ComponentChangedEventArgs" /> that contains the event data.</param>
    </member>
    <member name="M:System.ComponentModel.Design.UndoEngine.UndoUnit.ComponentRemoved(System.ComponentModel.Design.ComponentEventArgs)">
      <summary>Receives a call from the <see cref="T:System.ComponentModel.Design.UndoEngine" /> in response to a <see cref="E:System.ComponentModel.Design.IComponentChangeService.ComponentRemoved" /> event.</summary>
      <param name="e">A <see cref="T:System.ComponentModel.Design.ComponentEventArgs" /> that contains the event data.</param>
    </member>
    <member name="M:System.ComponentModel.Design.UndoEngine.UndoUnit.ComponentRemoving(System.ComponentModel.Design.ComponentEventArgs)">
      <summary>Receives a call from the <see cref="T:System.ComponentModel.Design.UndoEngine" /> in response to a <see cref="E:System.ComponentModel.Design.IComponentChangeService.ComponentRemoving" /> event.</summary>
      <param name="e">A <see cref="T:System.ComponentModel.Design.ComponentEventArgs" /> that contains the event data.</param>
    </member>
    <member name="M:System.ComponentModel.Design.UndoEngine.UndoUnit.ComponentRename(System.ComponentModel.Design.ComponentRenameEventArgs)">
      <summary>Receives a call from the <see cref="T:System.ComponentModel.Design.UndoEngine" /> in response to a <see cref="E:System.ComponentModel.Design.IComponentChangeService.ComponentRename" /> event.</summary>
      <param name="e">A <see cref="T:System.ComponentModel.Design.ComponentRenameEventArgs" /> that contains the event data.</param>
    </member>
    <member name="M:System.ComponentModel.Design.UndoEngine.UndoUnit.GetService(System.Type)">
      <summary>Gets an instance of the requested service.</summary>
      <param name="serviceType">The type of service to retrieve.</param>
      <returns>An instance of the given service, or <see langword="null" /> if the service cannot be resolved.</returns>
    </member>
    <member name="P:System.ComponentModel.Design.UndoEngine.UndoUnit.IsEmpty">
      <summary>Gets a value indicating whether the <see cref="T:System.ComponentModel.Design.UndoEngine.UndoUnit" /> contains no events.</summary>
      <returns>
        <see langword="true" /> if the <see cref="T:System.ComponentModel.Design.UndoEngine.UndoUnit" /> contains no events; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.ComponentModel.Design.UndoEngine.UndoUnit.Name">
      <summary>Gets the name of the <see cref="T:System.ComponentModel.Design.UndoEngine.UndoUnit" />.</summary>
      <returns>The name of the <see cref="T:System.ComponentModel.Design.UndoEngine.UndoUnit" />.</returns>
    </member>
    <member name="M:System.ComponentModel.Design.UndoEngine.UndoUnit.ToString">
      <summary>Returns a <see cref="T:System.String" /> that represents the current name of the unit.</summary>
      <returns>A <see cref="T:System.String" /> that represents the current name of the unit.</returns>
    </member>
    <member name="M:System.ComponentModel.Design.UndoEngine.UndoUnit.Undo">
      <summary>Performs an undo or redo action.</summary>
    </member>
    <member name="M:System.ComponentModel.Design.UndoEngine.UndoUnit.UndoCore">
      <summary>Called by <see cref="M:System.ComponentModel.Design.UndoEngine.UndoUnit.Undo" /> to perform an undo action.</summary>
    </member>
    <member name="P:System.ComponentModel.Design.UndoEngine.UndoUnit.UndoEngine">
      <summary>Gets the parent <see cref="P:System.ComponentModel.Design.UndoEngine.UndoUnit.UndoEngine" />.</summary>
      <returns>The <see cref="P:System.ComponentModel.Design.UndoEngine.UndoUnit.UndoEngine" /> to which this <see cref="T:System.ComponentModel.Design.UndoEngine.UndoUnit" /> is attached.</returns>
    </member>
    <member name="T:System.Drawing.Design.IToolboxItemProvider">
      <summary>Exposes a collection of toolbox items.</summary>
    </member>
    <member name="P:System.Drawing.Design.IToolboxItemProvider.Items">
      <summary>Gets a collection of <see cref="T:System.Drawing.Design.ToolboxItem" /> objects.</summary>
      <returns>A collection of <see cref="T:System.Drawing.Design.ToolboxItem" /> objects.</returns>
    </member>
    <member name="T:System.Drawing.Design.IToolboxService">
      <summary>Provides methods and properties to manage and query the toolbox in the development environment.</summary>
    </member>
    <member name="M:System.Drawing.Design.IToolboxService.AddCreator(System.Drawing.Design.ToolboxItemCreatorCallback,System.String)">
      <summary>Adds a new toolbox item creator for a specified data format.</summary>
      <param name="creator">A <see cref="T:System.Drawing.Design.ToolboxItemCreatorCallback" /> that can create a component when the toolbox item is invoked.</param>
      <param name="format">The data format that the creator handles.</param>
    </member>
    <member name="M:System.Drawing.Design.IToolboxService.AddCreator(System.Drawing.Design.ToolboxItemCreatorCallback,System.String,System.ComponentModel.Design.IDesignerHost)">
      <summary>Adds a new toolbox item creator for a specified data format and designer host.</summary>
      <param name="creator">A <see cref="T:System.Drawing.Design.ToolboxItemCreatorCallback" /> that can create a component when the toolbox item is invoked.</param>
      <param name="format">The data format that the creator handles.</param>
      <param name="host">The <see cref="T:System.ComponentModel.Design.IDesignerHost" /> that represents the designer host to associate with the creator.</param>
    </member>
    <member name="M:System.Drawing.Design.IToolboxService.AddLinkedToolboxItem(System.Drawing.Design.ToolboxItem,System.ComponentModel.Design.IDesignerHost)">
      <summary>Adds the specified project-linked toolbox item to the toolbox.</summary>
      <param name="toolboxItem">The linked <see cref="T:System.Drawing.Design.ToolboxItem" /> to add to the toolbox.</param>
      <param name="host">The <see cref="T:System.ComponentModel.Design.IDesignerHost" /> for the current design document.</param>
    </member>
    <member name="M:System.Drawing.Design.IToolboxService.AddLinkedToolboxItem(System.Drawing.Design.ToolboxItem,System.String,System.ComponentModel.Design.IDesignerHost)">
      <summary>Adds the specified project-linked toolbox item to the toolbox in the specified category.</summary>
      <param name="toolboxItem">The linked <see cref="T:System.Drawing.Design.ToolboxItem" /> to add to the toolbox.</param>
      <param name="category">The toolbox item category to add the toolbox item to.</param>
      <param name="host">The <see cref="T:System.ComponentModel.Design.IDesignerHost" /> for the current design document.</param>
    </member>
    <member name="M:System.Drawing.Design.IToolboxService.AddToolboxItem(System.Drawing.Design.ToolboxItem)">
      <summary>Adds the specified toolbox item to the toolbox.</summary>
      <param name="toolboxItem">The <see cref="T:System.Drawing.Design.ToolboxItem" /> to add to the toolbox.</param>
    </member>
    <member name="M:System.Drawing.Design.IToolboxService.AddToolboxItem(System.Drawing.Design.ToolboxItem,System.String)">
      <summary>Adds the specified toolbox item to the toolbox in the specified category.</summary>
      <param name="toolboxItem">The <see cref="T:System.Drawing.Design.ToolboxItem" /> to add to the toolbox.</param>
      <param name="category">The toolbox item category to add the <see cref="T:System.Drawing.Design.ToolboxItem" /> to.</param>
    </member>
    <member name="P:System.Drawing.Design.IToolboxService.CategoryNames">
      <summary>Gets the names of all the tool categories currently on the toolbox.</summary>
      <returns>A <see cref="T:System.Drawing.Design.CategoryNameCollection" /> containing the tool categories.</returns>
    </member>
    <member name="M:System.Drawing.Design.IToolboxService.DeserializeToolboxItem(System.Object)">
      <summary>Gets a toolbox item from the specified object that represents a toolbox item in serialized form.</summary>
      <param name="serializedObject">The object that contains the <see cref="T:System.Drawing.Design.ToolboxItem" /> to retrieve.</param>
      <returns>The <see cref="T:System.Drawing.Design.ToolboxItem" /> created from the serialized object.</returns>
    </member>
    <member name="M:System.Drawing.Design.IToolboxService.DeserializeToolboxItem(System.Object,System.ComponentModel.Design.IDesignerHost)">
      <summary>Gets a toolbox item from the specified object that represents a toolbox item in serialized form, using the specified designer host.</summary>
      <param name="serializedObject">The object that contains the <see cref="T:System.Drawing.Design.ToolboxItem" /> to retrieve.</param>
      <param name="host">The <see cref="T:System.ComponentModel.Design.IDesignerHost" /> to associate with this <see cref="T:System.Drawing.Design.ToolboxItem" />.</param>
      <returns>The <see cref="T:System.Drawing.Design.ToolboxItem" /> created from deserialization.</returns>
    </member>
    <member name="M:System.Drawing.Design.IToolboxService.GetSelectedToolboxItem">
      <summary>Gets the currently selected toolbox item.</summary>
      <returns>The <see cref="T:System.Drawing.Design.ToolboxItem" /> that is currently selected, or <see langword="null" /> if no toolbox item has been selected.</returns>
    </member>
    <member name="M:System.Drawing.Design.IToolboxService.GetSelectedToolboxItem(System.ComponentModel.Design.IDesignerHost)">
      <summary>Gets the currently selected toolbox item if it is available to all designers, or if it supports the specified designer.</summary>
      <param name="host">The <see cref="T:System.ComponentModel.Design.IDesignerHost" /> that the selected tool must be associated with for it to be returned.</param>
      <returns>The <see cref="T:System.Drawing.Design.ToolboxItem" /> that is currently selected, or <see langword="null" /> if no toolbox item is currently selected.</returns>
    </member>
    <member name="M:System.Drawing.Design.IToolboxService.GetToolboxItems">
      <summary>Gets the entire collection of toolbox items from the toolbox.</summary>
      <returns>A <see cref="T:System.Drawing.Design.ToolboxItemCollection" /> that contains the current toolbox items.</returns>
    </member>
    <member name="M:System.Drawing.Design.IToolboxService.GetToolboxItems(System.ComponentModel.Design.IDesignerHost)">
      <summary>Gets the collection of toolbox items that are associated with the specified designer host from the toolbox.</summary>
      <param name="host">The <see cref="T:System.ComponentModel.Design.IDesignerHost" /> that is associated with the toolbox items to retrieve.</param>
      <returns>A <see cref="T:System.Drawing.Design.ToolboxItemCollection" /> that contains the current toolbox items that are associated with the specified designer host.</returns>
    </member>
    <member name="M:System.Drawing.Design.IToolboxService.GetToolboxItems(System.String)">
      <summary>Gets a collection of toolbox items from the toolbox that match the specified category.</summary>
      <param name="category">The toolbox item category to retrieve all the toolbox items from.</param>
      <returns>A <see cref="T:System.Drawing.Design.ToolboxItemCollection" /> that contains the current toolbox items that are associated with the specified category.</returns>
    </member>
    <member name="M:System.Drawing.Design.IToolboxService.GetToolboxItems(System.String,System.ComponentModel.Design.IDesignerHost)">
      <summary>Gets the collection of toolbox items that are associated with the specified designer host and category from the toolbox.</summary>
      <param name="category">The toolbox item category to retrieve the toolbox items from.</param>
      <param name="host">The <see cref="T:System.ComponentModel.Design.IDesignerHost" /> that is associated with the toolbox items to retrieve.</param>
      <returns>A <see cref="T:System.Drawing.Design.ToolboxItemCollection" /> that contains the current toolbox items that are associated with the specified category and designer host.</returns>
    </member>
    <member name="M:System.Drawing.Design.IToolboxService.IsSupported(System.Object,System.Collections.ICollection)">
      <summary>Gets a value indicating whether the specified object which represents a serialized toolbox item matches the specified attributes.</summary>
      <param name="serializedObject">The object that contains the <see cref="T:System.Drawing.Design.ToolboxItem" /> to retrieve.</param>
      <param name="filterAttributes">An <see cref="T:System.Collections.ICollection" /> that contains the attributes to test the serialized object for.</param>
      <returns>
        <see langword="true" /> if the object matches the specified attributes; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Drawing.Design.IToolboxService.IsSupported(System.Object,System.ComponentModel.Design.IDesignerHost)">
      <summary>Gets a value indicating whether the specified object which represents a serialized toolbox item can be used by the specified designer host.</summary>
      <param name="serializedObject">The object that contains the <see cref="T:System.Drawing.Design.ToolboxItem" /> to retrieve.</param>
      <param name="host">The <see cref="T:System.ComponentModel.Design.IDesignerHost" /> to test for support for the <see cref="T:System.Drawing.Design.ToolboxItem" />.</param>
      <returns>
        <see langword="true" /> if the specified object is compatible with the specified designer host; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Drawing.Design.IToolboxService.IsToolboxItem(System.Object)">
      <summary>Gets a value indicating whether the specified object is a serialized toolbox item.</summary>
      <param name="serializedObject">The object to inspect.</param>
      <returns>
        <see langword="true" /> if the object contains a toolbox item object; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Drawing.Design.IToolboxService.IsToolboxItem(System.Object,System.ComponentModel.Design.IDesignerHost)">
      <summary>Gets a value indicating whether the specified object is a serialized toolbox item, using the specified designer host.</summary>
      <param name="serializedObject">The object to inspect.</param>
      <param name="host">The <see cref="T:System.ComponentModel.Design.IDesignerHost" /> that is making this request.</param>
      <returns>
        <see langword="true" /> if the object contains a toolbox item object; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Drawing.Design.IToolboxService.Refresh">
      <summary>Refreshes the state of the toolbox items.</summary>
    </member>
    <member name="M:System.Drawing.Design.IToolboxService.RemoveCreator(System.String)">
      <summary>Removes a previously added toolbox item creator of the specified data format.</summary>
      <param name="format">The data format of the creator to remove.</param>
    </member>
    <member name="M:System.Drawing.Design.IToolboxService.RemoveCreator(System.String,System.ComponentModel.Design.IDesignerHost)">
      <summary>Removes a previously added toolbox creator that is associated with the specified data format and the specified designer host.</summary>
      <param name="format">The data format of the creator to remove.</param>
      <param name="host">The <see cref="T:System.ComponentModel.Design.IDesignerHost" /> that is associated with the creator to remove.</param>
    </member>
    <member name="M:System.Drawing.Design.IToolboxService.RemoveToolboxItem(System.Drawing.Design.ToolboxItem)">
      <summary>Removes the specified toolbox item from the toolbox.</summary>
      <param name="toolboxItem">The <see cref="T:System.Drawing.Design.ToolboxItem" /> to remove from the toolbox.</param>
    </member>
    <member name="M:System.Drawing.Design.IToolboxService.RemoveToolboxItem(System.Drawing.Design.ToolboxItem,System.String)">
      <summary>Removes the specified toolbox item from the toolbox.</summary>
      <param name="toolboxItem">The <see cref="T:System.Drawing.Design.ToolboxItem" /> to remove from the toolbox.</param>
      <param name="category">The toolbox item category to remove the <see cref="T:System.Drawing.Design.ToolboxItem" /> from.</param>
    </member>
    <member name="P:System.Drawing.Design.IToolboxService.SelectedCategory">
      <summary>Gets or sets the name of the currently selected tool category from the toolbox.</summary>
      <returns>The name of the currently selected category.</returns>
    </member>
    <member name="M:System.Drawing.Design.IToolboxService.SelectedToolboxItemUsed">
      <summary>Notifies the toolbox service that the selected tool has been used.</summary>
    </member>
    <member name="M:System.Drawing.Design.IToolboxService.SerializeToolboxItem(System.Drawing.Design.ToolboxItem)">
      <summary>Gets a serializable object that represents the specified toolbox item.</summary>
      <param name="toolboxItem">The <see cref="T:System.Drawing.Design.ToolboxItem" /> to serialize.</param>
      <returns>An object that represents the specified <see cref="T:System.Drawing.Design.ToolboxItem" />.</returns>
    </member>
    <member name="M:System.Drawing.Design.IToolboxService.SetCursor">
      <summary>Sets the current application's cursor to a cursor that represents the currently selected tool.</summary>
      <returns>
        <see langword="true" /> if the cursor is set by the currently selected tool, <see langword="false" /> if there is no tool selected and the cursor is set to the standard windows cursor.</returns>
    </member>
    <member name="M:System.Drawing.Design.IToolboxService.SetSelectedToolboxItem(System.Drawing.Design.ToolboxItem)">
      <summary>Selects the specified toolbox item.</summary>
      <param name="toolboxItem">The <see cref="T:System.Drawing.Design.ToolboxItem" /> to select.</param>
    </member>
    <member name="T:System.Drawing.Design.IToolboxUser">
      <summary>Defines an interface for setting the currently selected toolbox item and indicating whether a designer supports a particular toolbox item.</summary>
    </member>
    <member name="M:System.Drawing.Design.IToolboxUser.GetToolSupported(System.Drawing.Design.ToolboxItem)">
      <summary>Gets a value indicating whether the specified tool is supported by the current designer.</summary>
      <param name="tool">The <see cref="T:System.Drawing.Design.ToolboxItem" /> to be tested for toolbox support.</param>
      <returns>
        <see langword="true" /> if the tool is supported by the toolbox and can be enabled; <see langword="false" /> if the document designer does not know how to use the tool.</returns>
    </member>
    <member name="M:System.Drawing.Design.IToolboxUser.ToolPicked(System.Drawing.Design.ToolboxItem)">
      <summary>Selects the specified tool.</summary>
      <param name="tool">The <see cref="T:System.Drawing.Design.ToolboxItem" /> to select.</param>
    </member>
    <member name="T:System.Drawing.Design.ToolboxComponentsCreatedEventArgs">
      <summary>Provides data for the <see cref="E:System.Drawing.Design.ToolboxItem.ComponentsCreated" /> event that occurs when components are added to the toolbox.</summary>
    </member>
    <member name="M:System.Drawing.Design.ToolboxComponentsCreatedEventArgs.#ctor(System.ComponentModel.IComponent[])">
      <summary>Initializes a new instance of the <see cref="T:System.Drawing.Design.ToolboxComponentsCreatedEventArgs" /> class.</summary>
      <param name="components">The components to include in the toolbox.</param>
    </member>
    <member name="P:System.Drawing.Design.ToolboxComponentsCreatedEventArgs.Components">
      <summary>Gets or sets an array containing the components to add to the toolbox.</summary>
      <returns>An array of type <see cref="T:System.ComponentModel.IComponent" /> indicating the components to add to the toolbox.</returns>
    </member>
    <member name="T:System.Drawing.Design.ToolboxComponentsCreatedEventHandler">
      <summary>Represents the method that handles the <see cref="E:System.Drawing.Design.ToolboxItem.ComponentsCreated" /> event.</summary>
      <param name="sender">The source of the event.</param>
      <param name="e">A <see cref="T:System.Drawing.Design.ToolboxComponentsCreatedEventArgs" /> that provides data for the event.</param>
    </member>
    <member name="T:System.Drawing.Design.ToolboxComponentsCreatingEventArgs">
      <summary>Provides data for the <see cref="E:System.Drawing.Design.ToolboxItem.ComponentsCreating" /> event that occurs when components are added to the toolbox.</summary>
    </member>
    <member name="M:System.Drawing.Design.ToolboxComponentsCreatingEventArgs.#ctor(System.ComponentModel.Design.IDesignerHost)">
      <summary>Initializes a new instance of the <see cref="T:System.Drawing.Design.ToolboxComponentsCreatingEventArgs" /> class.</summary>
      <param name="host">The designer host that is making the request.</param>
    </member>
    <member name="P:System.Drawing.Design.ToolboxComponentsCreatingEventArgs.DesignerHost">
      <summary>Gets or sets an instance of the <see cref="T:System.ComponentModel.Design.IDesignerHost" /> that made the request to create toolbox components.</summary>
      <returns>The <see cref="T:System.ComponentModel.Design.IDesignerHost" /> that made the request to create toolbox components, or <see langword="null" /> if no designer host was provided to the toolbox item.</returns>
    </member>
    <member name="T:System.Drawing.Design.ToolboxComponentsCreatingEventHandler">
      <summary>Represents the method that handles the <see cref="E:System.Drawing.Design.ToolboxItem.ComponentsCreating" /> event.</summary>
      <param name="sender">The source of the event.</param>
      <param name="e">A <see cref="T:System.Drawing.Design.ToolboxComponentsCreatingEventArgs" /> that provides data for the event.</param>
    </member>
    <member name="T:System.Drawing.Design.ToolboxItem">
      <summary>Provides a base implementation of a toolbox item.</summary>
    </member>
    <member name="M:System.Drawing.Design.ToolboxItem.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Drawing.Design.ToolboxItem" /> class.</summary>
    </member>
    <member name="M:System.Drawing.Design.ToolboxItem.#ctor(System.Type)">
      <summary>Initializes a new instance of the <see cref="T:System.Drawing.Design.ToolboxItem" /> class that creates the specified type of component.</summary>
      <param name="toolType">The type of <see cref="T:System.ComponentModel.IComponent" /> that the toolbox item creates.</param>
      <exception cref="T:System.InvalidOperationException">The <see cref="T:System.Drawing.Design.ToolboxItem" /> was locked.</exception>
    </member>
    <member name="P:System.Drawing.Design.ToolboxItem.AssemblyName">
      <summary>Gets or sets the name of the assembly that contains the type or types that the toolbox item creates.</summary>
      <returns>An <see cref="T:System.Reflection.AssemblyName" /> that indicates the assembly containing the type or types to create.</returns>
    </member>
    <member name="P:System.Drawing.Design.ToolboxItem.Bitmap">
      <summary>Gets or sets a bitmap to represent the toolbox item in the toolbox.</summary>
      <returns>A <see cref="T:System.Drawing.Bitmap" /> that represents the toolbox item in the toolbox.</returns>
    </member>
    <member name="M:System.Drawing.Design.ToolboxItem.CheckUnlocked">
      <summary>Throws an exception if the toolbox item is currently locked.</summary>
      <exception cref="T:System.InvalidOperationException">The <see cref="T:System.Drawing.Design.ToolboxItem" /> is locked.</exception>
    </member>
    <member name="P:System.Drawing.Design.ToolboxItem.Company">
      <summary>Gets or sets the company name for this <see cref="T:System.Drawing.Design.ToolboxItem" />.</summary>
      <returns>A <see cref="T:System.String" /> that specifies the company for this <see cref="T:System.Drawing.Design.ToolboxItem" />.</returns>
    </member>
    <member name="E:System.Drawing.Design.ToolboxItem.ComponentsCreated">
      <summary>Occurs immediately after components are created.</summary>
    </member>
    <member name="E:System.Drawing.Design.ToolboxItem.ComponentsCreating">
      <summary>Occurs when components are about to be created.</summary>
    </member>
    <member name="P:System.Drawing.Design.ToolboxItem.ComponentType">
      <summary>Gets the component type for this <see cref="T:System.Drawing.Design.ToolboxItem" />.</summary>
      <returns>A <see cref="T:System.String" /> that specifies the component type for this <see cref="T:System.Drawing.Design.ToolboxItem" />.</returns>
    </member>
    <member name="M:System.Drawing.Design.ToolboxItem.CreateComponents">
      <summary>Creates the components that the toolbox item is configured to create.</summary>
      <returns>An array of created <see cref="T:System.ComponentModel.IComponent" /> objects.</returns>
    </member>
    <member name="M:System.Drawing.Design.ToolboxItem.CreateComponents(System.ComponentModel.Design.IDesignerHost)">
      <summary>Creates the components that the toolbox item is configured to create, using the specified designer host.</summary>
      <param name="host">The <see cref="T:System.ComponentModel.Design.IDesignerHost" /> to use when creating the components.</param>
      <returns>An array of created <see cref="T:System.ComponentModel.IComponent" /> objects.</returns>
    </member>
    <member name="M:System.Drawing.Design.ToolboxItem.CreateComponents(System.ComponentModel.Design.IDesignerHost,System.Collections.IDictionary)">
      <summary>Creates the components that the toolbox item is configured to create, using the specified designer host and default values.</summary>
      <param name="host">The <see cref="T:System.ComponentModel.Design.IDesignerHost" /> to use when creating the components.</param>
      <param name="defaultValues">A dictionary of property name/value pairs of default values with which to initialize the component.</param>
      <returns>An array of created <see cref="T:System.ComponentModel.IComponent" /> objects.</returns>
    </member>
    <member name="M:System.Drawing.Design.ToolboxItem.CreateComponentsCore(System.ComponentModel.Design.IDesignerHost)">
      <summary>Creates a component or an array of components when the toolbox item is invoked.</summary>
      <param name="host">The <see cref="T:System.ComponentModel.Design.IDesignerHost" /> to host the toolbox item.</param>
      <returns>An array of created <see cref="T:System.ComponentModel.IComponent" /> objects.</returns>
    </member>
    <member name="M:System.Drawing.Design.ToolboxItem.CreateComponentsCore(System.ComponentModel.Design.IDesignerHost,System.Collections.IDictionary)">
      <summary>Creates an array of components when the toolbox item is invoked.</summary>
      <param name="host">The designer host to use when creating components.</param>
      <param name="defaultValues">A dictionary of property name/value pairs of default values with which to initialize the component.</param>
      <returns>An array of created <see cref="T:System.ComponentModel.IComponent" /> objects.</returns>
    </member>
    <member name="P:System.Drawing.Design.ToolboxItem.DependentAssemblies">
      <summary>Gets or sets the <see cref="T:System.Reflection.AssemblyName" /> for the toolbox item.</summary>
      <returns>An array of <see cref="T:System.Reflection.AssemblyName" /> objects.</returns>
    </member>
    <member name="P:System.Drawing.Design.ToolboxItem.Description">
      <summary>Gets or sets the description for this <see cref="T:System.Drawing.Design.ToolboxItem" />.</summary>
      <returns>A <see cref="T:System.String" /> that specifies the description for this <see cref="T:System.Drawing.Design.ToolboxItem" />.</returns>
    </member>
    <member name="M:System.Drawing.Design.ToolboxItem.Deserialize(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
      <summary>Loads the state of the toolbox item from the specified serialization information object.</summary>
      <param name="info">The <see cref="T:System.Runtime.Serialization.SerializationInfo" /> to load from.</param>
      <param name="context">A <see cref="T:System.Runtime.Serialization.StreamingContext" /> that indicates the stream characteristics.</param>
    </member>
    <member name="P:System.Drawing.Design.ToolboxItem.DisplayName">
      <summary>Gets or sets the display name for the toolbox item.</summary>
      <returns>The display name for the toolbox item.</returns>
    </member>
    <member name="M:System.Drawing.Design.ToolboxItem.Equals(System.Object)">
      <summary>Determines whether two <see cref="T:System.Drawing.Design.ToolboxItem" /> instances are equal.</summary>
      <param name="obj">The <see cref="T:System.Drawing.Design.ToolboxItem" /> to compare with the current <see cref="T:System.Drawing.Design.ToolboxItem" />.</param>
      <returns>
        <see langword="true" /> if the specified <see cref="T:System.Drawing.Design.ToolboxItem" /> is equal to the current <see cref="T:System.Drawing.Design.ToolboxItem" />; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Drawing.Design.ToolboxItem.Filter">
      <summary>Gets or sets the filter that determines whether the toolbox item can be used on a destination component.</summary>
      <returns>An <see cref="T:System.Collections.ICollection" /> of <see cref="T:System.ComponentModel.ToolboxItemFilterAttribute" /> objects.</returns>
    </member>
    <member name="M:System.Drawing.Design.ToolboxItem.FilterPropertyValue(System.String,System.Object)">
      <summary>Filters a property value before returning it.</summary>
      <param name="propertyName">The name of the property to filter.</param>
      <param name="value">The value against which to filter the property.</param>
      <returns>A filtered property value.</returns>
    </member>
    <member name="M:System.Drawing.Design.ToolboxItem.GetHashCode">
      <summary>Returns the hash code for this instance.</summary>
      <returns>A hash code for the current <see cref="T:System.Drawing.Design.ToolboxItem" />.</returns>
    </member>
    <member name="M:System.Drawing.Design.ToolboxItem.GetType(System.ComponentModel.Design.IDesignerHost)">
      <summary>Enables access to the type associated with the toolbox item.</summary>
      <param name="host">The designer host to query for <see cref="T:System.ComponentModel.Design.ITypeResolutionService" />.</param>
      <returns>The type associated with the toolbox item.</returns>
    </member>
    <member name="M:System.Drawing.Design.ToolboxItem.GetType(System.ComponentModel.Design.IDesignerHost,System.Reflection.AssemblyName,System.String,System.Boolean)">
      <summary>Creates an instance of the specified type, optionally using a specified designer host and assembly name.</summary>
      <param name="host">The <see cref="T:System.ComponentModel.Design.IDesignerHost" /> for the current document. This can be <see langword="null" />.</param>
      <param name="assemblyName">An <see cref="T:System.Reflection.AssemblyName" /> that indicates the assembly that contains the type to load. This can be <see langword="null" />.</param>
      <param name="typeName">The name of the type to create an instance of.</param>
      <param name="reference">A value indicating whether or not to add a reference to the assembly that contains the specified type to the designer host's set of references.</param>
      <returns>An instance of the specified type, if it can be located.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="typeName" /> is not specified.</exception>
    </member>
    <member name="M:System.Drawing.Design.ToolboxItem.Initialize(System.Type)">
      <summary>Initializes the current toolbox item with the specified type to create.</summary>
      <param name="type">The <see cref="T:System.Type" /> that the toolbox item creates.</param>
      <exception cref="T:System.InvalidOperationException">The <see cref="T:System.Drawing.Design.ToolboxItem" /> was locked.</exception>
    </member>
    <member name="P:System.Drawing.Design.ToolboxItem.IsTransient">
      <summary>Gets a value indicating whether the toolbox item is transient.</summary>
      <returns>
        <see langword="true" />, if this toolbox item should not be stored in any toolbox database when an application that is providing a toolbox closes; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Drawing.Design.ToolboxItem.Lock">
      <summary>Locks the toolbox item and prevents changes to its properties.</summary>
    </member>
    <member name="P:System.Drawing.Design.ToolboxItem.Locked">
      <summary>Gets a value indicating whether the <see cref="T:System.Drawing.Design.ToolboxItem" /> is currently locked.</summary>
      <returns>
        <see langword="true" /> if the toolbox item is locked; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Drawing.Design.ToolboxItem.OnComponentsCreated(System.Drawing.Design.ToolboxComponentsCreatedEventArgs)">
      <summary>Raises the <see cref="E:System.Drawing.Design.ToolboxItem.ComponentsCreated" /> event.</summary>
      <param name="args">A <see cref="T:System.Drawing.Design.ToolboxComponentsCreatedEventArgs" /> that provides data for the event.</param>
    </member>
    <member name="M:System.Drawing.Design.ToolboxItem.OnComponentsCreating(System.Drawing.Design.ToolboxComponentsCreatingEventArgs)">
      <summary>Raises the <see cref="E:System.Drawing.Design.ToolboxItem.ComponentsCreating" /> event.</summary>
      <param name="args">A <see cref="T:System.Drawing.Design.ToolboxComponentsCreatingEventArgs" /> that provides data for the event.</param>
    </member>
    <member name="P:System.Drawing.Design.ToolboxItem.OriginalBitmap">
      <summary>Gets or sets the original bitmap that will be used in the toolbox for this item.</summary>
      <returns>A <see cref="T:System.Drawing.Bitmap" /> that represents the toolbox item in the toolbox.</returns>
    </member>
    <member name="P:System.Drawing.Design.ToolboxItem.Properties">
      <summary>Gets a dictionary of properties.</summary>
      <returns>A dictionary of name/value pairs (the names are property names and the values are property values).</returns>
    </member>
    <member name="M:System.Drawing.Design.ToolboxItem.Serialize(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
      <summary>Saves the state of the toolbox item to the specified serialization information object.</summary>
      <param name="info">The <see cref="T:System.Runtime.Serialization.SerializationInfo" /> to save to.</param>
      <param name="context">A <see cref="T:System.Runtime.Serialization.StreamingContext" /> that indicates the stream characteristics.</param>
    </member>
    <member name="M:System.Drawing.Design.ToolboxItem.System#Runtime#Serialization#ISerializable#GetObjectData(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
      <summary>For a description of this member, see the <see cref="M:System.Runtime.Serialization.ISerializable.GetObjectData(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)" /> method.</summary>
      <param name="info">The <see cref="T:System.Runtime.Serialization.SerializationInfo" /> to populate with data.</param>
      <param name="context">The destination (see <see cref="T:System.Runtime.Serialization.StreamingContext" />) for this serialization.</param>
    </member>
    <member name="M:System.Drawing.Design.ToolboxItem.ToString">
      <summary>Returns a <see cref="T:System.String" /> that represents the current <see cref="T:System.Drawing.Design.ToolboxItem" />.</summary>
      <returns>A <see cref="T:System.String" /> that represents the current <see cref="T:System.Drawing.Design.ToolboxItem" />.</returns>
    </member>
    <member name="P:System.Drawing.Design.ToolboxItem.TypeName">
      <summary>Gets or sets the fully qualified name of the type of <see cref="T:System.ComponentModel.IComponent" /> that the toolbox item creates when invoked.</summary>
      <returns>The fully qualified type name of the type of component that this toolbox item creates.</returns>
    </member>
    <member name="M:System.Drawing.Design.ToolboxItem.ValidatePropertyType(System.String,System.Object,System.Type,System.Boolean)">
      <summary>Validates that an object is of a given type.</summary>
      <param name="propertyName">The name of the property to validate.</param>
      <param name="value">Optional value against which to validate.</param>
      <param name="expectedType">The expected type of the property.</param>
      <param name="allowNull">
        <see langword="true" /> to allow <see langword="null" />; otherwise, <see langword="false" />.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" /> is <see langword="null" />, and <paramref name="allowNull" /> is <see langword="false" />.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="value" /> is not the type specified by <paramref name="expectedType" />.</exception>
    </member>
    <member name="M:System.Drawing.Design.ToolboxItem.ValidatePropertyValue(System.String,System.Object)">
      <summary>Validates a property before it is assigned to the property dictionary.</summary>
      <param name="propertyName">The name of the property to validate.</param>
      <param name="value">The value against which to validate.</param>
      <returns>The value used to perform validation.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" /> is <see langword="null" />, and <paramref name="propertyName" /> is "IsTransient".</exception>
    </member>
    <member name="P:System.Drawing.Design.ToolboxItem.Version">
      <summary>Gets the version for this <see cref="T:System.Drawing.Design.ToolboxItem" />.</summary>
      <returns>A <see cref="T:System.String" /> that specifies the version for this <see cref="T:System.Drawing.Design.ToolboxItem" />.</returns>
    </member>
    <member name="T:System.Drawing.Design.ToolboxItemCollection">
      <summary>Represents a collection of toolbox items.</summary>
    </member>
    <member name="M:System.Drawing.Design.ToolboxItemCollection.#ctor(System.Drawing.Design.ToolboxItem[])">
      <summary>Initializes a new instance of the <see cref="T:System.Drawing.Design.ToolboxItemCollection" /> class using the specified array of toolbox items.</summary>
      <param name="value">An array of type <see cref="T:System.Drawing.Design.ToolboxItem" /> containing the toolbox items to fill the collection with.</param>
    </member>
    <member name="M:System.Drawing.Design.ToolboxItemCollection.#ctor(System.Drawing.Design.ToolboxItemCollection)">
      <summary>Initializes a new instance of the <see cref="T:System.Drawing.Design.ToolboxItemCollection" /> class using the specified collection.</summary>
      <param name="value">A <see cref="T:System.Drawing.Design.ToolboxItemCollection" /> to fill the new collection with.</param>
    </member>
    <member name="M:System.Drawing.Design.ToolboxItemCollection.Contains(System.Drawing.Design.ToolboxItem)">
      <summary>Indicates whether the collection contains the specified <see cref="T:System.Drawing.Design.ToolboxItem" />.</summary>
      <param name="value">A <see cref="T:System.Drawing.Design.ToolboxItem" /> to search the collection for.</param>
      <returns>
        <see langword="true" /> if the collection contains the specified object; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Drawing.Design.ToolboxItemCollection.CopyTo(System.Drawing.Design.ToolboxItem[],System.Int32)">
      <summary>Copies the collection to the specified array beginning with the specified destination index.</summary>
      <param name="array">The array to copy to.</param>
      <param name="index">The index to begin copying to.</param>
    </member>
    <member name="M:System.Drawing.Design.ToolboxItemCollection.IndexOf(System.Drawing.Design.ToolboxItem)">
      <summary>Gets the index of the specified <see cref="T:System.Drawing.Design.ToolboxItem" />, if it exists in the collection.</summary>
      <param name="value">A <see cref="T:System.Drawing.Design.ToolboxItem" /> to get the index of in the collection.</param>
      <returns>The index of the specified <see cref="T:System.Drawing.Design.ToolboxItem" />.</returns>
    </member>
    <member name="P:System.Drawing.Design.ToolboxItemCollection.Item(System.Int32)">
      <summary>Gets the <see cref="T:System.Drawing.Design.ToolboxItem" /> at the specified index.</summary>
      <param name="index">The index of the object to get or set.</param>
      <returns>A <see cref="T:System.Drawing.Design.ToolboxItem" /> at each valid index in the collection.</returns>
    </member>
    <member name="T:System.Drawing.Design.ToolboxItemCreatorCallback">
      <summary>Provides a callback mechanism that can create a <see cref="T:System.Drawing.Design.ToolboxItem" />.</summary>
      <param name="serializedObject">The object which contains the data to create a <see cref="T:System.Drawing.Design.ToolboxItem" /> for.</param>
      <param name="format">The name of the clipboard data format to create a <see cref="T:System.Drawing.Design.ToolboxItem" /> for.</param>
      <returns>The deserialized <see cref="T:System.Drawing.Design.ToolboxItem" /> object specified by <paramref name="serializedObject" />.</returns>
    </member>
    <member name="T:System.Runtime.InteropServices.UCOMITypeLib">
      <summary>Use <see cref="T:System.Runtime.InteropServices.ComTypes.ITypeLib" /> instead.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.UCOMITypeLib.GetDocumentation(System.Int32,System.String@,System.String@,System.Int32@,System.String@)">
      <summary>Retrieves the library's documentation string, the complete Help file name and path, and the context identifier for the library Help topic in the Help file.</summary>
      <param name="index">Index of the type description whose documentation is to be returned.</param>
      <param name="strName">Returns a string that contains the name of the specified item.</param>
      <param name="strDocString">Returns a string that contains the documentation string for the specified item.</param>
      <param name="dwHelpContext">Returns the Help context identifier associated with the specified item.</param>
      <param name="strHelpFile">Returns a string that contains the fully qualified name of the Help file.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.UCOMITypeLib.GetLibAttr(System.IntPtr@)">
      <summary>Retrieves the structure that contains the library's attributes.</summary>
      <param name="ppTLibAttr">On successful return, a structure that contains the library's attributes.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.UCOMITypeLib.GetTypeInfoCount">
      <summary>Returns the number of type descriptions in the type library.</summary>
      <returns>The number of type descriptions in the type library.</returns>
    </member>
    <member name="M:System.Runtime.InteropServices.UCOMITypeLib.GetTypeInfoType(System.Int32,System.Runtime.InteropServices.ComTypes.TYPEKIND@)">
      <param name="index" />
      <param name="pTKind" />
    </member>
    <member name="M:System.Runtime.InteropServices.UCOMITypeLib.IsName(System.String,System.Int32)">
      <summary>Indicates whether a passed-in string contains the name of a type or member described in the library.</summary>
      <param name="szNameBuf">The string to test.</param>
      <param name="lHashVal">The hash value of <paramref name="szNameBuf" />.</param>
      <returns>
        <see langword="true" /> if <paramref name="szNameBuf" /> was found in the type library; otherwise <see langword="false" />.</returns>
    </member>
    <member name="M:System.Runtime.InteropServices.UCOMITypeLib.ReleaseTLibAttr(System.IntPtr)">
      <summary>Releases the <see cref="T:System.Runtime.InteropServices.TYPELIBATTR" /> originally obtained from <see cref="M:System.Runtime.InteropServices.UCOMITypeLib.GetLibAttr(System.IntPtr@)" />.</summary>
      <param name="pTLibAttr">The <see langword="TLIBATTR" /> to release.</param>
    </member>
    <member name="T:System.Windows.Forms.Design.AxImporter">
      <summary>Imports ActiveX controls and generates a wrapper that can be accessed by a designer.</summary>
    </member>
    <member name="M:System.Windows.Forms.Design.AxImporter.#ctor(System.Windows.Forms.Design.AxImporter.Options)">
      <summary>Initializes a new instance of the <see cref="T:System.Windows.Forms.Design.AxImporter" /> class.</summary>
      <param name="options">An <see cref="T:System.Windows.Forms.Design.AxImporter.Options" /> that indicates the options for the ActiveX control importer to use.</param>
    </member>
    <member name="T:System.Windows.Forms.Design.AxImporter.IReferenceResolver">
      <summary>Provides methods to resolve references to ActiveX libraries, COM type libraries or assemblies, or managed assemblies.</summary>
    </member>
    <member name="M:System.Windows.Forms.Design.AxImporter.IReferenceResolver.ResolveActiveXReference(System.Runtime.InteropServices.UCOMITypeLib)">
      <summary>Resolves a reference to the specified type library that contains an ActiveX control.</summary>
      <param name="typeLib">A <see cref="T:System.Runtime.InteropServices.UCOMITypeLib" /> to resolve a reference to.</param>
      <returns>A fully qualified path to an assembly.</returns>
    </member>
    <member name="M:System.Windows.Forms.Design.AxImporter.IReferenceResolver.ResolveComReference(System.Reflection.AssemblyName)">
      <summary>Resolves a reference to the specified assembly that contains a COM component.</summary>
      <param name="name">An <see cref="T:System.Reflection.AssemblyName" /> that indicates the assembly to resolve a reference to.</param>
      <returns>A fully qualified path to an assembly.</returns>
    </member>
    <member name="M:System.Windows.Forms.Design.AxImporter.IReferenceResolver.ResolveComReference(System.Runtime.InteropServices.UCOMITypeLib)">
      <summary>Resolves a reference to the specified type library that contains an COM component.</summary>
      <param name="typeLib">A <see cref="T:System.Runtime.InteropServices.UCOMITypeLib" /> to resolve a reference to.</param>
      <returns>A fully qualified path to an assembly.</returns>
    </member>
    <member name="M:System.Windows.Forms.Design.AxImporter.IReferenceResolver.ResolveManagedReference(System.String)">
      <summary>Resolves a reference to the specified assembly.</summary>
      <param name="assemName">The name of the assembly to resolve a reference to.</param>
      <returns>A fully qualified path to an assembly.</returns>
    </member>
    <member name="T:System.Windows.Forms.Design.AxImporter.Options">
      <summary>Represents a set of options for an <see cref="T:System.Windows.Forms.Design.AxImporter" />.</summary>
    </member>
    <member name="M:System.Windows.Forms.Design.AxImporter.Options.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Windows.Forms.Design.AxImporter.Options" /> class.</summary>
    </member>
    <member name="F:System.Windows.Forms.Design.AxImporter.Options.delaySign">
      <summary>Specifies whether the generated assembly is strongly named and will be signed later.</summary>
    </member>
    <member name="F:System.Windows.Forms.Design.AxImporter.Options.genSources">
      <summary>Specifies whether sources for the type library wrapper should be generated.</summary>
    </member>
    <member name="F:System.Windows.Forms.Design.AxImporter.Options.ignoreRegisteredOcx">
      <summary>Specifies whether to use only input from the command line instead relying on a registered version.</summary>
    </member>
    <member name="F:System.Windows.Forms.Design.AxImporter.Options.keyContainer">
      <summary>Specifies the path to the file that contains the strong name key container for the generated assemblies.</summary>
    </member>
    <member name="F:System.Windows.Forms.Design.AxImporter.Options.keyFile">
      <summary>Specifies the path to the file that contains the strong name key for the generated assemblies.</summary>
    </member>
    <member name="F:System.Windows.Forms.Design.AxImporter.Options.keyPair">
      <summary>Specifies the strong name used for the generated assemblies.</summary>
    </member>
    <member name="F:System.Windows.Forms.Design.AxImporter.Options.msBuildErrors">
      <summary>Specifies whether errors are output in the Microsoft Build Engine (MSBuild) format.</summary>
    </member>
    <member name="F:System.Windows.Forms.Design.AxImporter.Options.noLogo">
      <summary>Indicates whether the ActiveX importer tool logo will be displayed when the control is imported.</summary>
    </member>
    <member name="F:System.Windows.Forms.Design.AxImporter.Options.outputDirectory">
      <summary>Specifies the path to the directory that the generated assemblies will be created in.</summary>
    </member>
    <member name="F:System.Windows.Forms.Design.AxImporter.Options.outputName">
      <summary>Specifies the filename to generate the ActiveX control wrapper to.</summary>
    </member>
    <member name="F:System.Windows.Forms.Design.AxImporter.Options.overwriteRCW">
      <summary>Specifies whether to overwrite existing files when generating assemblies.</summary>
    </member>
    <member name="F:System.Windows.Forms.Design.AxImporter.Options.publicKey">
      <summary>Specifies the public key used to sign the generated assemblies.</summary>
    </member>
    <member name="F:System.Windows.Forms.Design.AxImporter.Options.references">
      <summary>Specifies the <see cref="T:System.Windows.Forms.Design.AxImporter.IReferenceResolver" /> to use to resolve types and references when generating assemblies.</summary>
    </member>
    <member name="F:System.Windows.Forms.Design.AxImporter.Options.silentMode">
      <summary>Specifies whether to compile in silent mode, which generates less displayed information at compile time.</summary>
    </member>
    <member name="F:System.Windows.Forms.Design.AxImporter.Options.verboseMode">
      <summary>Specifies whether to compile in verbose mode, which generates more displayed information at compile time.</summary>
    </member>
    <member name="T:System.Windows.Forms.Design.Behavior.Adorner">
      <summary>Manages a collection of user-interface related <see cref="T:System.Windows.Forms.Design.Behavior.Glyph" /> objects. This class cannot be inherited.</summary>
    </member>
    <member name="M:System.Windows.Forms.Design.Behavior.Adorner.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Windows.Forms.Design.Behavior.Adorner" /> class.</summary>
    </member>
    <member name="P:System.Windows.Forms.Design.Behavior.Adorner.BehaviorService">
      <summary>Gets or sets the <see cref="T:System.Windows.Forms.Design.Behavior.BehaviorService" /> associated with the <see cref="T:System.Windows.Forms.Design.Behavior.Adorner" />.</summary>
      <returns>The <see cref="T:System.Windows.Forms.Design.Behavior.BehaviorService" /> associated with the <see cref="T:System.Windows.Forms.Design.Behavior.Adorner" />.</returns>
    </member>
    <member name="P:System.Windows.Forms.Design.Behavior.Adorner.Enabled">
      <summary>Gets or sets a value indicating if the <see cref="T:System.Windows.Forms.Design.Behavior.Adorner" /> is enabled.</summary>
      <returns>
        <see langword="true" />, if the <see cref="T:System.Windows.Forms.Design.Behavior.Adorner" /> is enabled; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Windows.Forms.Design.Behavior.Adorner.Glyphs">
      <summary>Gets the <see cref="T:System.Windows.Forms.Design.Behavior.Glyph" /> collection.</summary>
      <returns>A collection of <see cref="T:System.Windows.Forms.Design.Behavior.Glyph" /> objects.</returns>
    </member>
    <member name="M:System.Windows.Forms.Design.Behavior.Adorner.Invalidate">
      <summary>Forces the <see cref="T:System.Windows.Forms.Design.Behavior.BehaviorService" /> to refresh its adorner window.</summary>
    </member>
    <member name="M:System.Windows.Forms.Design.Behavior.Adorner.Invalidate(System.Drawing.Rectangle)">
      <summary>Forces the <see cref="T:System.Windows.Forms.Design.Behavior.BehaviorService" /> to refresh its adorner window within the given <see cref="T:System.Drawing.Rectangle" />.</summary>
      <param name="rectangle">The area to invalidate.</param>
    </member>
    <member name="M:System.Windows.Forms.Design.Behavior.Adorner.Invalidate(System.Drawing.Region)">
      <summary>Forces the <see cref="T:System.Windows.Forms.Design.Behavior.BehaviorService" /> to refresh its adorner window within the given <see cref="T:System.Drawing.Region" />.</summary>
      <param name="region">The <see cref="T:System.Drawing.Region" /> to invalidate.</param>
    </member>
    <member name="T:System.Windows.Forms.Design.Behavior.Behavior">
      <summary>Represents the <see cref="T:System.Windows.Forms.Design.Behavior.Behavior" /> objects that are managed by a <see cref="T:System.Windows.Forms.Design.Behavior.BehaviorService" />.</summary>
    </member>
    <member name="M:System.Windows.Forms.Design.Behavior.Behavior.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Windows.Forms.Design.Behavior.Behavior" /> class.</summary>
    </member>
    <member name="M:System.Windows.Forms.Design.Behavior.Behavior.#ctor(System.Boolean,System.Windows.Forms.Design.Behavior.BehaviorService)">
      <summary>Initializes a new instance of the <see cref="T:System.Windows.Forms.Design.Behavior.Behavior" /> class with the given <see cref="T:System.Windows.Forms.Design.Behavior.BehaviorService" />.</summary>
      <param name="callParentBehavior">
        <see langword="true" /> if the parent behavior should be called if it exists; otherwise, <see langword="false" />.</param>
      <param name="behaviorService">The <see cref="T:System.Windows.Forms.Design.Behavior.BehaviorService" /> to use.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="callParentBehavior" /> is <see langword="true" />, and <paramref name="behaviorService" /> is <see langword="null" />.</exception>
    </member>
    <member name="P:System.Windows.Forms.Design.Behavior.Behavior.Cursor">
      <summary>Gets the cursor that should be displayed for this behavior.</summary>
      <returns>A <see cref="T:System.Windows.Forms.Cursor" /> that represents the cursor that should be displayed for this behavior.</returns>
    </member>
    <member name="P:System.Windows.Forms.Design.Behavior.Behavior.DisableAllCommands">
      <summary>Gets a value indicating whether <see cref="T:System.ComponentModel.Design.MenuCommand" /> objects should be disabled.</summary>
      <returns>
        <see langword="true" /> if all <see cref="T:System.ComponentModel.Design.MenuCommand" /> objects the designer receives should have states set to <c>Enabled = false</c> when this <see cref="T:System.Windows.Forms.Design.Behavior.Behavior" /> is active; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Windows.Forms.Design.Behavior.Behavior.FindCommand(System.ComponentModel.Design.CommandID)">
      <summary>Intercepts commands.</summary>
      <param name="commandId">A <see cref="T:System.ComponentModel.Design.CommandID" /> object.</param>
      <returns>A <see cref="T:System.ComponentModel.Design.MenuCommand" />. By default, <see cref="M:System.Windows.Forms.Design.Behavior.Behavior.FindCommand(System.ComponentModel.Design.CommandID)" /> returns <see langword="null" />.</returns>
    </member>
    <member name="M:System.Windows.Forms.Design.Behavior.Behavior.OnDragDrop(System.Windows.Forms.Design.Behavior.Glyph,System.Windows.Forms.DragEventArgs)">
      <summary>Permits custom drag-and-drop behavior.</summary>
      <param name="g">A <see cref="T:System.Windows.Forms.Design.Behavior.Glyph" /> object on which to invoke drag-and-drop behavior.</param>
      <param name="e">A <see cref="T:System.Windows.Forms.DragEventArgs" /> that contains the event data.</param>
    </member>
    <member name="M:System.Windows.Forms.Design.Behavior.Behavior.OnDragEnter(System.Windows.Forms.Design.Behavior.Glyph,System.Windows.Forms.DragEventArgs)">
      <summary>Permits custom drag-enter behavior.</summary>
      <param name="g">A <see cref="T:System.Windows.Forms.Design.Behavior.Glyph" /> on which to invoke drag-enter behavior.</param>
      <param name="e">A <see cref="T:System.Windows.Forms.DragEventArgs" /> that contains the event data.</param>
    </member>
    <member name="M:System.Windows.Forms.Design.Behavior.Behavior.OnDragLeave(System.Windows.Forms.Design.Behavior.Glyph,System.EventArgs)">
      <summary>Permits custom drag-leave behavior.</summary>
      <param name="g">A <see cref="T:System.Windows.Forms.Design.Behavior.Glyph" /> on which to invoke drag-leave behavior.</param>
      <param name="e">A <see cref="T:System.Windows.Forms.DragEventArgs" /> that contains the event data.</param>
    </member>
    <member name="M:System.Windows.Forms.Design.Behavior.Behavior.OnDragOver(System.Windows.Forms.Design.Behavior.Glyph,System.Windows.Forms.DragEventArgs)">
      <summary>Permits custom drag-over behavior.</summary>
      <param name="g">A <see cref="T:System.Windows.Forms.Design.Behavior.Glyph" /> on which to invoke drag-over behavior.</param>
      <param name="e">A <see cref="T:System.Windows.Forms.DragEventArgs" /> that contains the event data.</param>
    </member>
    <member name="M:System.Windows.Forms.Design.Behavior.Behavior.OnGiveFeedback(System.Windows.Forms.Design.Behavior.Glyph,System.Windows.Forms.GiveFeedbackEventArgs)">
      <summary>Permits custom drag-and-drop feedback behavior.</summary>
      <param name="g">A <see cref="T:System.Windows.Forms.Design.Behavior.Glyph" /> on which to invoke drag-and-drop behavior.</param>
      <param name="e">A <see cref="T:System.Windows.Forms.GiveFeedbackEventArgs" /> that contains the event data.</param>
    </member>
    <member name="M:System.Windows.Forms.Design.Behavior.Behavior.OnLoseCapture(System.Windows.Forms.Design.Behavior.Glyph,System.EventArgs)">
      <summary>Called by the adorner window when it loses mouse capture.</summary>
      <param name="g">A <see cref="T:System.Windows.Forms.Design.Behavior.Glyph" /> on which to invoke drag-and-drop behavior.</param>
      <param name="e">An <see cref="T:System.EventArgs" /> that contains the event data.</param>
    </member>
    <member name="M:System.Windows.Forms.Design.Behavior.Behavior.OnMouseDoubleClick(System.Windows.Forms.Design.Behavior.Glyph,System.Windows.Forms.MouseButtons,System.Drawing.Point)">
      <summary>Called when any double-click message enters the adorner window of the <see cref="T:System.Windows.Forms.Design.Behavior.BehaviorService" />.</summary>
      <param name="g">A <see cref="T:System.Windows.Forms.Design.Behavior.Glyph" />.</param>
      <param name="button">A <see cref="T:System.Windows.Forms.MouseButtons" /> value indicating which button was clicked.</param>
      <param name="mouseLoc">The location at which the click occurred.</param>
      <returns>
        <see langword="true" /> if the message was handled; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Windows.Forms.Design.Behavior.Behavior.OnMouseDown(System.Windows.Forms.Design.Behavior.Glyph,System.Windows.Forms.MouseButtons,System.Drawing.Point)">
      <summary>Called when any mouse-down message enters the adorner window of the <see cref="T:System.Windows.Forms.Design.Behavior.BehaviorService" />.</summary>
      <param name="g">A <see cref="T:System.Windows.Forms.Design.Behavior.Glyph" />.</param>
      <param name="button">A <see cref="T:System.Windows.Forms.MouseButtons" /> value indicating which button was clicked.</param>
      <param name="mouseLoc">The location at which the click occurred.</param>
      <returns>
        <see langword="true" /> if the message was handled; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Windows.Forms.Design.Behavior.Behavior.OnMouseEnter(System.Windows.Forms.Design.Behavior.Glyph)">
      <summary>Called when any mouse-enter message enters the adorner window of the <see cref="T:System.Windows.Forms.Design.Behavior.BehaviorService" />.</summary>
      <param name="g">A <see cref="T:System.Windows.Forms.Design.Behavior.Glyph" />.</param>
      <returns>
        <see langword="true" /> if the message was handled; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Windows.Forms.Design.Behavior.Behavior.OnMouseHover(System.Windows.Forms.Design.Behavior.Glyph,System.Drawing.Point)">
      <summary>Called when any mouse-hover message enters the adorner window of the <see cref="T:System.Windows.Forms.Design.Behavior.BehaviorService" />.</summary>
      <param name="g">A <see cref="T:System.Windows.Forms.Design.Behavior.Glyph" />.</param>
      <param name="mouseLoc">The location at which the hover occurred.</param>
      <returns>
        <see langword="true" /> if the message was handled; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Windows.Forms.Design.Behavior.Behavior.OnMouseLeave(System.Windows.Forms.Design.Behavior.Glyph)">
      <summary>Called when any mouse-leave message enters the adorner window of the <see cref="T:System.Windows.Forms.Design.Behavior.BehaviorService" />.</summary>
      <param name="g">A <see cref="T:System.Windows.Forms.Design.Behavior.Glyph" />.</param>
      <returns>
        <see langword="true" /> if the message was handled; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Windows.Forms.Design.Behavior.Behavior.OnMouseMove(System.Windows.Forms.Design.Behavior.Glyph,System.Windows.Forms.MouseButtons,System.Drawing.Point)">
      <summary>Called when any mouse-move message enters the adorner window of the <see cref="T:System.Windows.Forms.Design.Behavior.BehaviorService" />.</summary>
      <param name="g">A <see cref="T:System.Windows.Forms.Design.Behavior.Glyph" />.</param>
      <param name="button">A <see cref="T:System.Windows.Forms.MouseButtons" /> value indicating which button was clicked.</param>
      <param name="mouseLoc">The location at which the move occurred.</param>
      <returns>
        <see langword="true" /> if the message was handled; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Windows.Forms.Design.Behavior.Behavior.OnMouseUp(System.Windows.Forms.Design.Behavior.Glyph,System.Windows.Forms.MouseButtons)">
      <summary>Called when any mouse-up message enters the adorner window of the <see cref="T:System.Windows.Forms.Design.Behavior.BehaviorService" />.</summary>
      <param name="g">A <see cref="T:System.Windows.Forms.Design.Behavior.Glyph" />.</param>
      <param name="button">A <see cref="T:System.Windows.Forms.MouseButtons" /> value indicating which button was clicked.</param>
      <returns>
        <see langword="true" /> if the message was handled; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Windows.Forms.Design.Behavior.Behavior.OnQueryContinueDrag(System.Windows.Forms.Design.Behavior.Glyph,System.Windows.Forms.QueryContinueDragEventArgs)">
      <summary>Sends this drag-and-drop event from the adorner window to the appropriate <see cref="T:System.Windows.Forms.Design.Behavior.Behavior" /> or hit-tested <see cref="T:System.Windows.Forms.Design.Behavior.Glyph" />.</summary>
      <param name="g">A <see cref="T:System.Windows.Forms.Design.Behavior.Glyph" />.</param>
      <param name="e">A <see cref="T:System.Windows.Forms.QueryContinueDragEventArgs" /> that contains the event data.</param>
    </member>
    <member name="T:System.Windows.Forms.Design.Behavior.BehaviorDragDropEventArgs">
      <summary>Provides data for the <see cref="E:System.Windows.Forms.Design.Behavior.BehaviorService.BeginDrag" /> and <see cref="E:System.Windows.Forms.Design.Behavior.BehaviorService.EndDrag" /> events.</summary>
    </member>
    <member name="M:System.Windows.Forms.Design.Behavior.BehaviorDragDropEventArgs.#ctor(System.Collections.ICollection)">
      <summary>Initializes a new instance of the <see cref="T:System.Windows.Forms.Design.Behavior.BehaviorDragDropEventArgs" /> class.</summary>
      <param name="dragComponents">The <see cref="T:System.Collections.ICollection" /> of <see cref="T:System.ComponentModel.IComponent" /> objects currently being dragged.</param>
    </member>
    <member name="P:System.Windows.Forms.Design.Behavior.BehaviorDragDropEventArgs.DragComponents">
      <summary>Gets the list of <see cref="T:System.ComponentModel.IComponent" /> objects currently being dragged.</summary>
      <returns>The <see cref="T:System.Collections.ICollection" /> of <see cref="T:System.ComponentModel.IComponent" /> objects currently being dragged.</returns>
    </member>
    <member name="T:System.Windows.Forms.Design.Behavior.BehaviorDragDropEventHandler">
      <summary>Represents the methods that will handle the <see cref="E:System.Windows.Forms.Design.Behavior.BehaviorService.BeginDrag" /> and <see cref="E:System.Windows.Forms.Design.Behavior.BehaviorService.EndDrag" /> events of a <see cref="T:System.Windows.Forms.Design.Behavior.BehaviorService" />. This class cannot be inherited.</summary>
      <param name="sender">The source of the event.</param>
      <param name="e">A <see cref="T:System.Windows.Forms.Design.Behavior.BehaviorDragDropEventArgs" /> that contains the event data.</param>
    </member>
    <member name="T:System.Windows.Forms.Design.Behavior.BehaviorService">
      <summary>Manages user interface in the designer. This class cannot be inherited.</summary>
    </member>
    <member name="P:System.Windows.Forms.Design.Behavior.BehaviorService.Adorners">
      <summary>Gets the <see cref="T:System.Windows.Forms.Design.Behavior.BehaviorServiceAdornerCollection" />.</summary>
      <returns>A collection of adorner.</returns>
    </member>
    <member name="P:System.Windows.Forms.Design.Behavior.BehaviorService.AdornerWindowGraphics">
      <summary>Gets the <see cref="T:System.Drawing.Graphics" /> for the adorner window.</summary>
      <returns>The <see cref="T:System.Drawing.Graphics" /> for the adorner window.</returns>
    </member>
    <member name="M:System.Windows.Forms.Design.Behavior.BehaviorService.AdornerWindowPointToScreen(System.Drawing.Point)">
      <summary>Translates a <see cref="T:System.Drawing.Point" /> in the adorner window to screen coordinates.</summary>
      <param name="p">The <see cref="T:System.Drawing.Point" /> value to transform.</param>
      <returns>The transformed <see cref="T:System.Drawing.Point" /> value, in screen coordinates.</returns>
    </member>
    <member name="M:System.Windows.Forms.Design.Behavior.BehaviorService.AdornerWindowToScreen">
      <summary>Gets the location of the adorner window in screen coordinates.</summary>
      <returns>The location, from the upper-left corner of the adorner window, in screen coordinates.</returns>
    </member>
    <member name="E:System.Windows.Forms.Design.Behavior.BehaviorService.BeginDrag">
      <summary>Occurs when the <see cref="T:System.Windows.Forms.Design.Behavior.BehaviorService" /> starts a drag-and-drop operation.</summary>
    </member>
    <member name="M:System.Windows.Forms.Design.Behavior.BehaviorService.ControlRectInAdornerWindow(System.Windows.Forms.Control)">
      <summary>Returns the bounding <see cref="T:System.Drawing.Rectangle" /> of a <see cref="T:System.Windows.Forms.Control" />.</summary>
      <param name="c">The <see cref="T:System.Windows.Forms.Control" /> to translate.</param>
      <returns>The bounding <see cref="T:System.Drawing.Rectangle" /> of a <see cref="T:System.Windows.Forms.Control" /> translated to the adorner window coordinates.</returns>
    </member>
    <member name="M:System.Windows.Forms.Design.Behavior.BehaviorService.ControlToAdornerWindow(System.Windows.Forms.Control)">
      <summary>Returns the location of a <see cref="T:System.Windows.Forms.Control" /> translated to adorner window coordinates.</summary>
      <param name="c">The <see cref="T:System.Windows.Forms.Control" /> to translate.</param>
      <returns>A <see cref="T:System.Drawing.Point" /> value indicating the location of <paramref name="c" /> in adorner window coordinates.</returns>
    </member>
    <member name="P:System.Windows.Forms.Design.Behavior.BehaviorService.CurrentBehavior">
      <summary>Gets the <see cref="T:System.Windows.Forms.Design.Behavior.Behavior" /> at the top of the behavior stack without removing it.</summary>
      <returns>The <see cref="T:System.Windows.Forms.Design.Behavior.Behavior" /> at the top of the behavior stack.</returns>
    </member>
    <member name="M:System.Windows.Forms.Design.Behavior.BehaviorService.Dispose">
      <summary>Releases all resources used by the <see cref="T:System.Windows.Forms.Design.Behavior.BehaviorService" />.</summary>
    </member>
    <member name="E:System.Windows.Forms.Design.Behavior.BehaviorService.EndDrag">
      <summary>Occurs when the <see cref="T:System.Windows.Forms.Design.Behavior.BehaviorService" /> completes a drag operation.</summary>
    </member>
    <member name="M:System.Windows.Forms.Design.Behavior.BehaviorService.GetNextBehavior(System.Windows.Forms.Design.Behavior.Behavior)">
      <summary>Returns the <see cref="T:System.Windows.Forms.Design.Behavior.Behavior" /> immediately after the given <see cref="T:System.Windows.Forms.Design.Behavior.Behavior" /> in the behavior stack.</summary>
      <param name="behavior">The <see cref="T:System.Windows.Forms.Design.Behavior.Behavior" /> preceding the <see cref="T:System.Windows.Forms.Design.Behavior.Behavior" /> to be returned.</param>
      <returns>The <see cref="T:System.Windows.Forms.Design.Behavior.Behavior" /> immediately after <paramref name="behavior" /> in the behavior stack, or <see langword="null" /> if there is no following behavior.</returns>
    </member>
    <member name="M:System.Windows.Forms.Design.Behavior.BehaviorService.Invalidate">
      <summary>Invalidates the adorner window of the <see cref="T:System.Windows.Forms.Design.Behavior.BehaviorService" />.</summary>
    </member>
    <member name="M:System.Windows.Forms.Design.Behavior.BehaviorService.Invalidate(System.Drawing.Rectangle)">
      <summary>Invalidates, within the adorner window, the specified area of the <see cref="T:System.Windows.Forms.Design.Behavior.BehaviorService" />.</summary>
      <param name="rect">The rectangular area to invalidate.</param>
    </member>
    <member name="M:System.Windows.Forms.Design.Behavior.BehaviorService.Invalidate(System.Drawing.Region)">
      <summary>Invalidates, within the adorner window, the specified area of the <see cref="T:System.Windows.Forms.Design.Behavior.BehaviorService" />.</summary>
      <param name="r">The region to invalidate.</param>
    </member>
    <member name="M:System.Windows.Forms.Design.Behavior.BehaviorService.MapAdornerWindowPoint(System.IntPtr,System.Drawing.Point)">
      <summary>Converts a point in a handle's coordinate system to the adorner window coordinates.</summary>
      <param name="handle">An adorner window's handle.</param>
      <param name="pt">A <see cref="T:System.Drawing.Point" /> in a handle's coordinate system.</param>
      <returns>A <see cref="T:System.Drawing.Point" /> in the adorner window coordinates.</returns>
    </member>
    <member name="M:System.Windows.Forms.Design.Behavior.BehaviorService.PopBehavior(System.Windows.Forms.Design.Behavior.Behavior)">
      <summary>Removes and returns the <see cref="T:System.Windows.Forms.Design.Behavior.Behavior" /> at the top of the stack.</summary>
      <param name="behavior">The <see cref="T:System.Windows.Forms.Design.Behavior.Behavior" /> to remove from the stack.</param>
      <returns>The <see cref="T:System.Windows.Forms.Design.Behavior.Behavior" /> that was removed from the stack.</returns>
      <exception cref="T:System.InvalidOperationException">The <see cref="T:System.Windows.Forms.Design.Behavior.Behavior" /> stack is empty.</exception>
    </member>
    <member name="M:System.Windows.Forms.Design.Behavior.BehaviorService.PushBehavior(System.Windows.Forms.Design.Behavior.Behavior)">
      <summary>Pushes a <see cref="T:System.Windows.Forms.Design.Behavior.Behavior" /> onto the behavior stack.</summary>
      <param name="behavior">The <see cref="T:System.Windows.Forms.Design.Behavior.Behavior" /> to push.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="behavior" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Windows.Forms.Design.Behavior.BehaviorService.PushCaptureBehavior(System.Windows.Forms.Design.Behavior.Behavior)">
      <summary>Pushes a <see cref="T:System.Windows.Forms.Design.Behavior.Behavior" /> onto the behavior stack and assigns mouse capture to the behavior.</summary>
      <param name="behavior">The <see cref="T:System.Windows.Forms.Design.Behavior.Behavior" /> to push.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="behavior" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Windows.Forms.Design.Behavior.BehaviorService.ScreenToAdornerWindow(System.Drawing.Point)">
      <summary>Translates a point in screen coordinates into the adorner window coordinates of the <see cref="T:System.Windows.Forms.Design.Behavior.BehaviorService" />.</summary>
      <param name="p">The <see cref="T:System.Drawing.Point" /> value to transform.</param>
      <returns>The transformed <see cref="T:System.Drawing.Point" /> value, in adorner window coordinates.</returns>
    </member>
    <member name="E:System.Windows.Forms.Design.Behavior.BehaviorService.Synchronize">
      <summary>Occurs when the current selection should be refreshed.</summary>
    </member>
    <member name="M:System.Windows.Forms.Design.Behavior.BehaviorService.SyncSelection">
      <summary>Synchronizes all selection glyphs.</summary>
    </member>
    <member name="T:System.Windows.Forms.Design.Behavior.BehaviorServiceAdornerCollection">
      <summary>Stores <see cref="T:System.Windows.Forms.Design.Behavior.Adorner" /> objects in a strongly typed collection.</summary>
    </member>
    <member name="M:System.Windows.Forms.Design.Behavior.BehaviorServiceAdornerCollection.#ctor(System.Windows.Forms.Design.Behavior.Adorner[])">
      <summary>Initializes a new instance of the <see cref="T:System.Windows.Forms.Design.Behavior.BehaviorServiceAdornerCollection" /> class with the given array.</summary>
      <param name="value">An array of type <see cref="T:System.Windows.Forms.Design.Behavior.Adorner" /> to populate the collection.</param>
    </member>
    <member name="M:System.Windows.Forms.Design.Behavior.BehaviorServiceAdornerCollection.#ctor(System.Windows.Forms.Design.Behavior.BehaviorService)">
      <summary>Initializes a new instance of the <see cref="T:System.Windows.Forms.Design.Behavior.BehaviorServiceAdornerCollection" /> class with the given <see cref="T:System.Windows.Forms.Design.Behavior.BehaviorService" /> reference.</summary>
      <param name="behaviorService">A <see cref="T:System.Windows.Forms.Design.Behavior.BehaviorService" /> reference.</param>
    </member>
    <member name="M:System.Windows.Forms.Design.Behavior.BehaviorServiceAdornerCollection.#ctor(System.Windows.Forms.Design.Behavior.BehaviorServiceAdornerCollection)">
      <summary>Initializes a new instance of the <see cref="T:System.Windows.Forms.Design.Behavior.BehaviorServiceAdornerCollection" /> class from an existing <see cref="T:System.Windows.Forms.Design.Behavior.BehaviorServiceAdornerCollection" />.</summary>
      <param name="value">A <see cref="T:System.Windows.Forms.Design.Behavior.BehaviorServiceAdornerCollection" /> from which to populate the collection.</param>
    </member>
    <member name="M:System.Windows.Forms.Design.Behavior.BehaviorServiceAdornerCollection.Add(System.Windows.Forms.Design.Behavior.Adorner)">
      <summary>Adds an <see cref="T:System.Windows.Forms.Design.Behavior.Adorner" /> with the specified value to the <see cref="T:System.Windows.Forms.Design.Behavior.BehaviorServiceAdornerCollection" />.</summary>
      <param name="value">An <see cref="T:System.Windows.Forms.Design.Behavior.Adorner" /> to add to the end of the collection.</param>
      <returns>The index at which the new element was inserted.</returns>
    </member>
    <member name="M:System.Windows.Forms.Design.Behavior.BehaviorServiceAdornerCollection.AddRange(System.Windows.Forms.Design.Behavior.Adorner[])">
      <summary>Copies the elements of an array to the end of the <see cref="T:System.Windows.Forms.Design.Behavior.BehaviorServiceAdornerCollection" />.</summary>
      <param name="value">An array of type <see cref="T:System.Windows.Forms.Design.Behavior.Adorner" /> to copy to the end of the collection</param>
    </member>
    <member name="M:System.Windows.Forms.Design.Behavior.BehaviorServiceAdornerCollection.AddRange(System.Windows.Forms.Design.Behavior.BehaviorServiceAdornerCollection)">
      <summary>Adds the contents of another <see cref="T:System.Windows.Forms.Design.Behavior.BehaviorServiceAdornerCollection" /> to the end of the collection.</summary>
      <param name="value">A <see cref="T:System.Windows.Forms.Design.Behavior.BehaviorServiceAdornerCollection" /> to add to the end of the collection.</param>
    </member>
    <member name="M:System.Windows.Forms.Design.Behavior.BehaviorServiceAdornerCollection.Contains(System.Windows.Forms.Design.Behavior.Adorner)">
      <summary>Gets a value indicating whether the <see cref="T:System.Windows.Forms.Design.Behavior.BehaviorServiceAdornerCollection" /> contains the specified <see cref="T:System.Windows.Forms.Design.Behavior.Adorner" />.</summary>
      <param name="value">The <see cref="T:System.Windows.Forms.Design.Behavior.Adorner" /> to locate.</param>
      <returns>
        <see langword="true" /> if the <see cref="T:System.Windows.Forms.Design.Behavior.Adorner" /> is contained in the collection; otherwise, <see langword="false" /></returns>
    </member>
    <member name="M:System.Windows.Forms.Design.Behavior.BehaviorServiceAdornerCollection.CopyTo(System.Windows.Forms.Design.Behavior.Adorner[],System.Int32)">
      <summary>Copies the <see cref="T:System.Windows.Forms.Design.Behavior.BehaviorServiceAdornerCollection" /> values to a one-dimensional <see cref="T:System.Array" /> at the specified index.</summary>
      <param name="array">The one-dimensional <see cref="T:System.Array" /> that is the destination of the values copied from <see cref="T:System.Windows.Forms.Design.Behavior.BehaviorServiceAdornerCollection" />.</param>
      <param name="index">The index in <paramref name="array" /> where copying begins.</param>
    </member>
    <member name="M:System.Windows.Forms.Design.Behavior.BehaviorServiceAdornerCollection.GetEnumerator">
      <summary>Returns an enumerator that can iterate through the <see cref="T:System.Windows.Forms.Design.Behavior.BehaviorServiceAdornerCollection" /> instance.</summary>
      <returns>An <see cref="T:System.Collections.IEnumerator" /> for the <see cref="T:System.Windows.Forms.Design.Behavior.BehaviorServiceAdornerCollection" /> instance.</returns>
    </member>
    <member name="M:System.Windows.Forms.Design.Behavior.BehaviorServiceAdornerCollection.IndexOf(System.Windows.Forms.Design.Behavior.Adorner)">
      <summary>Returns the index of an <see cref="T:System.Windows.Forms.Design.Behavior.Adorner" /> in the <see cref="T:System.Windows.Forms.Design.Behavior.BehaviorServiceAdornerCollection" />.</summary>
      <param name="value">The <see cref="T:System.Windows.Forms.Design.Behavior.Adorner" /> to locate.</param>
      <returns>The index of the <see cref="T:System.Windows.Forms.Design.Behavior.Adorner" /> of <paramref name="value" /> in the <see cref="T:System.Windows.Forms.Design.Behavior.BehaviorServiceAdornerCollection" />, if found; otherwise, -1.</returns>
    </member>
    <member name="M:System.Windows.Forms.Design.Behavior.BehaviorServiceAdornerCollection.Insert(System.Int32,System.Windows.Forms.Design.Behavior.Adorner)">
      <summary>Inserts an <see cref="T:System.Windows.Forms.Design.Behavior.Adorner" /> into the <see cref="T:System.Windows.Forms.Design.Behavior.BehaviorServiceAdornerCollection" /> at the specified index.</summary>
      <param name="index">The zero-based index where <paramref name="value" /> should be inserted.</param>
      <param name="value">The <see cref="T:System.Windows.Forms.Design.Behavior.Adorner" /> to insert.</param>
    </member>
    <member name="P:System.Windows.Forms.Design.Behavior.BehaviorServiceAdornerCollection.Item(System.Int32)">
      <summary>Gets or sets the element at the specified index.</summary>
      <param name="index">The zero-based index of the element.</param>
      <returns>The <see cref="T:System.Windows.Forms.Design.Behavior.Adorner" /> element specified by <paramref name="index" />.</returns>
    </member>
    <member name="M:System.Windows.Forms.Design.Behavior.BehaviorServiceAdornerCollection.Remove(System.Windows.Forms.Design.Behavior.Adorner)">
      <summary>Removes a specific <see cref="T:System.Windows.Forms.Design.Behavior.Adorner" /> from the <see cref="T:System.Windows.Forms.Design.Behavior.BehaviorServiceAdornerCollection" />.</summary>
      <param name="value">The <see cref="T:System.Windows.Forms.Design.Behavior.Adorner" /> to remove from the <see cref="T:System.Windows.Forms.Design.Behavior.BehaviorServiceAdornerCollection" />.</param>
    </member>
    <member name="T:System.Windows.Forms.Design.Behavior.BehaviorServiceAdornerCollectionEnumerator">
      <summary>Supports iteration over a <see cref="T:System.Windows.Forms.Design.Behavior.BehaviorServiceAdornerCollection" />.</summary>
    </member>
    <member name="M:System.Windows.Forms.Design.Behavior.BehaviorServiceAdornerCollectionEnumerator.#ctor(System.Windows.Forms.Design.Behavior.BehaviorServiceAdornerCollection)">
      <summary>Initializes a new instance of the <see cref="T:System.Windows.Forms.Design.Behavior.BehaviorServiceAdornerCollectionEnumerator" /> class.</summary>
      <param name="mappings">The <see cref="T:System.Windows.Forms.Design.Behavior.BehaviorServiceAdornerCollection" /> for which to create the enumerator.</param>
    </member>
    <member name="P:System.Windows.Forms.Design.Behavior.BehaviorServiceAdornerCollectionEnumerator.Current">
      <summary>Gets the current element in the <see cref="T:System.Windows.Forms.Design.Behavior.BehaviorServiceAdornerCollection" />.</summary>
      <returns>The current element in the <see cref="T:System.Windows.Forms.Design.Behavior.BehaviorServiceAdornerCollection" />.</returns>
    </member>
    <member name="M:System.Windows.Forms.Design.Behavior.BehaviorServiceAdornerCollectionEnumerator.MoveNext">
      <summary>Advances the enumerator to the next element of the <see cref="T:System.Windows.Forms.Design.Behavior.BehaviorServiceAdornerCollection" />.</summary>
      <returns>
        <see langword="true" /> if the enumerator was successfully advanced to the next element; <see langword="false" /> if the enumerator was past the end of the collection.</returns>
    </member>
    <member name="M:System.Windows.Forms.Design.Behavior.BehaviorServiceAdornerCollectionEnumerator.Reset">
      <summary>Sets the enumerator to its initial position, which is before the first element in the collection.</summary>
    </member>
    <member name="P:System.Windows.Forms.Design.Behavior.BehaviorServiceAdornerCollectionEnumerator.System#Collections#IEnumerator#Current">
      <summary>For a description of this member, see the <see cref="P:System.Collections.IEnumerator.Current" /> property.</summary>
      <returns>The current <see cref="T:System.Windows.Forms.Design.Behavior.Adorner" /> in the collection.</returns>
    </member>
    <member name="T:System.Windows.Forms.Design.Behavior.ComponentGlyph">
      <summary>Associates a <see cref="T:System.Windows.Forms.Design.Behavior.Glyph" /> with its component.</summary>
    </member>
    <member name="M:System.Windows.Forms.Design.Behavior.ComponentGlyph.#ctor(System.ComponentModel.IComponent)">
      <summary>Initializes a new instance of the <see cref="T:System.Windows.Forms.Design.Behavior.ComponentGlyph" /> class.</summary>
      <param name="relatedComponent">The component with which the <see cref="T:System.Windows.Forms.Design.Behavior.Glyph" /> is associated.</param>
    </member>
    <member name="M:System.Windows.Forms.Design.Behavior.ComponentGlyph.#ctor(System.ComponentModel.IComponent,System.Windows.Forms.Design.Behavior.Behavior)">
      <summary>Initializes a new instance of the <see cref="T:System.Windows.Forms.Design.Behavior.ComponentGlyph" /> class.</summary>
      <param name="relatedComponent">The component with which the <see cref="T:System.Windows.Forms.Design.Behavior.Glyph" /> is associated.</param>
      <param name="behavior">The <see cref="T:System.Windows.Forms.Design.Behavior.Behavior" /> with which the <see cref="T:System.Windows.Forms.Design.Behavior.Glyph" /> is associated.</param>
    </member>
    <member name="M:System.Windows.Forms.Design.Behavior.ComponentGlyph.GetHitTest(System.Drawing.Point)">
      <summary>Indicates whether a mouse click at the specified point should be handled by the <see cref="T:System.Windows.Forms.Design.Behavior.ComponentGlyph" />.</summary>
      <param name="p">A point to hit-test.</param>
      <returns>A <see cref="T:System.Windows.Forms.Cursor" /> if the <see cref="T:System.Windows.Forms.Design.Behavior.Glyph" /> is associated with <paramref name="p" />; otherwise, <see langword="null" />.</returns>
    </member>
    <member name="M:System.Windows.Forms.Design.Behavior.ComponentGlyph.Paint(System.Windows.Forms.PaintEventArgs)">
      <summary>Provides paint logic.</summary>
      <param name="pe">A <see cref="T:System.Windows.Forms.PaintEventArgs" /> containing the <see cref="P:System.Windows.Forms.Design.Behavior.BehaviorService.AdornerWindowGraphics" /> of the <see cref="T:System.Windows.Forms.Design.Behavior.BehaviorService" />.</param>
    </member>
    <member name="P:System.Windows.Forms.Design.Behavior.ComponentGlyph.RelatedComponent">
      <summary>Gets the component that is associated with the <see cref="T:System.Windows.Forms.Design.Behavior.Glyph" /> .</summary>
      <returns>An <see cref="T:System.ComponentModel.IComponent" /> associated with the <see cref="T:System.Windows.Forms.Design.Behavior.Glyph" />.</returns>
    </member>
    <member name="T:System.Windows.Forms.Design.Behavior.ControlBodyGlyph">
      <summary>Associates a <see cref="T:System.Windows.Forms.Design.Behavior.Glyph" /> with its control.</summary>
    </member>
    <member name="M:System.Windows.Forms.Design.Behavior.ControlBodyGlyph.#ctor(System.Drawing.Rectangle,System.Windows.Forms.Cursor,System.ComponentModel.IComponent,System.Windows.Forms.Design.Behavior.Behavior)">
      <summary>Initializes a new instance of the <see cref="T:System.Windows.Forms.Design.Behavior.ControlBodyGlyph" /> class.</summary>
      <param name="bounds">A <see cref="T:System.Drawing.Rectangle" /> that represents the bounds of the <see cref="T:System.Windows.Forms.Design.Behavior.Glyph" />.</param>
      <param name="cursor">A <see cref="T:System.Windows.Forms.Cursor" /> that represents the cursor to display when the mouse pointer is over the <see cref="T:System.Windows.Forms.Design.Behavior.Glyph" />.</param>
      <param name="relatedComponent">The component with which the <see cref="T:System.Windows.Forms.Design.Behavior.Glyph" /> is associated.</param>
      <param name="behavior">The <see cref="T:System.Windows.Forms.Design.Behavior.Behavior" /> with which the <see cref="T:System.Windows.Forms.Design.Behavior.Glyph" /> is associated.</param>
    </member>
    <member name="M:System.Windows.Forms.Design.Behavior.ControlBodyGlyph.#ctor(System.Drawing.Rectangle,System.Windows.Forms.Cursor,System.ComponentModel.IComponent,System.Windows.Forms.Design.ControlDesigner)">
      <summary>Initializes a new instance of the <see cref="T:System.Windows.Forms.Design.Behavior.ControlBodyGlyph" /> class.</summary>
      <param name="bounds">A <see cref="T:System.Drawing.Rectangle" /> that represents the bounds of the <see cref="T:System.Windows.Forms.Design.Behavior.Glyph" />.</param>
      <param name="cursor">A <see cref="T:System.Windows.Forms.Cursor" /> that represents the cursor to display when the mouse pointer is over the <see cref="T:System.Windows.Forms.Design.Behavior.Glyph" />.</param>
      <param name="relatedComponent">The component with which the <see cref="T:System.Windows.Forms.Design.Behavior.Glyph" /> is associated.</param>
      <param name="designer">A <see cref="T:System.Windows.Forms.Design.ControlDesigner" /> with which the <see cref="T:System.Windows.Forms.Design.Behavior.Glyph" /> is associated.</param>
    </member>
    <member name="P:System.Windows.Forms.Design.Behavior.ControlBodyGlyph.Bounds">
      <summary>Gets the bounds of the <see cref="T:System.Windows.Forms.Design.Behavior.Glyph" />.</summary>
      <returns>A <see cref="T:System.Drawing.Rectangle" /> representing the bounds of the <see cref="T:System.Windows.Forms.Design.Behavior.Glyph" />.</returns>
    </member>
    <member name="M:System.Windows.Forms.Design.Behavior.ControlBodyGlyph.GetHitTest(System.Drawing.Point)">
      <summary>Indicates whether a mouse click at the specified point should be handled by the <see cref="T:System.Windows.Forms.Design.Behavior.ControlBodyGlyph" />.</summary>
      <param name="p">A point to hit test.</param>
      <returns>A <see cref="T:System.Windows.Forms.Cursor" /> if the <see cref="T:System.Windows.Forms.Design.Behavior.Glyph" /> is associated with <paramref name="p" />; otherwise, <see langword="null" />.</returns>
    </member>
    <member name="T:System.Windows.Forms.Design.Behavior.Glyph">
      <summary>Represents a single user interface (UI) entity managed by an <see cref="T:System.Windows.Forms.Design.Behavior.Adorner" />.</summary>
    </member>
    <member name="M:System.Windows.Forms.Design.Behavior.Glyph.#ctor(System.Windows.Forms.Design.Behavior.Behavior)">
      <summary>Initializes a new instance of the <see cref="T:System.Windows.Forms.Design.Behavior.Glyph" /> class.</summary>
      <param name="behavior">The <see cref="T:System.Windows.Forms.Design.Behavior.Behavior" /> associated with the <see cref="T:System.Windows.Forms.Design.Behavior.Glyph" />. Can be <see langword="null" />.</param>
    </member>
    <member name="P:System.Windows.Forms.Design.Behavior.Glyph.Behavior">
      <summary>Gets the <see cref="T:System.Windows.Forms.Design.Behavior.Behavior" /> associated with the <see cref="T:System.Windows.Forms.Design.Behavior.Glyph" />.</summary>
      <returns>The <see cref="T:System.Windows.Forms.Design.Behavior.Behavior" /> associated with the <see cref="T:System.Windows.Forms.Design.Behavior.Glyph" />, or <see langword="null" /> if there is no behavior.</returns>
    </member>
    <member name="P:System.Windows.Forms.Design.Behavior.Glyph.Bounds">
      <summary>Gets the bounds of the <see cref="T:System.Windows.Forms.Design.Behavior.Glyph" />.</summary>
      <returns>A <see cref="T:System.Drawing.Rectangle" /> representing the bounds of the <see cref="T:System.Windows.Forms.Design.Behavior.Glyph" />.</returns>
    </member>
    <member name="M:System.Windows.Forms.Design.Behavior.Glyph.GetHitTest(System.Drawing.Point)">
      <summary>Provides hit test logic.</summary>
      <param name="p">A point to hit-test.</param>
      <returns>A <see cref="T:System.Windows.Forms.Cursor" /> if the <see cref="T:System.Windows.Forms.Design.Behavior.Glyph" /> is associated with <paramref name="p" />; otherwise, <see langword="null" />.</returns>
    </member>
    <member name="M:System.Windows.Forms.Design.Behavior.Glyph.Paint(System.Windows.Forms.PaintEventArgs)">
      <summary>Provides paint logic.</summary>
      <param name="pe">A <see cref="T:System.Windows.Forms.PaintEventArgs" /> that contains the event data.</param>
    </member>
    <member name="M:System.Windows.Forms.Design.Behavior.Glyph.SetBehavior(System.Windows.Forms.Design.Behavior.Behavior)">
      <summary>Changes the <see cref="T:System.Windows.Forms.Design.Behavior.Behavior" /> associated with the <see cref="T:System.Windows.Forms.Design.Behavior.Glyph" />.</summary>
      <param name="behavior">A <see cref="T:System.Windows.Forms.Design.Behavior.Behavior" /> to associate with the <see cref="T:System.Windows.Forms.Design.Behavior.Glyph" />.</param>
    </member>
    <member name="T:System.Windows.Forms.Design.Behavior.GlyphCollection">
      <summary>Stores <see cref="T:System.Windows.Forms.Design.Behavior.Glyph" /> objects in a strongly typed collection.</summary>
    </member>
    <member name="M:System.Windows.Forms.Design.Behavior.GlyphCollection.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Windows.Forms.Design.Behavior.GlyphCollection" /> class.</summary>
    </member>
    <member name="M:System.Windows.Forms.Design.Behavior.GlyphCollection.#ctor(System.Windows.Forms.Design.Behavior.Glyph[])">
      <summary>Initializes a new instance of the <see cref="T:System.Windows.Forms.Design.Behavior.GlyphCollection" /> class with the given <see cref="T:System.Windows.Forms.Design.Behavior.Glyph" /> array.</summary>
      <param name="value">An array of type <see cref="T:System.Windows.Forms.Design.Behavior.Glyph" /> to populate the collection.</param>
    </member>
    <member name="M:System.Windows.Forms.Design.Behavior.GlyphCollection.#ctor(System.Windows.Forms.Design.Behavior.GlyphCollection)">
      <summary>Initializes a new instance of the <see cref="T:System.Windows.Forms.Design.Behavior.GlyphCollection" /> class based on another <see cref="T:System.Windows.Forms.Design.Behavior.GlyphCollection" />.</summary>
      <param name="value">A <see cref="T:System.Windows.Forms.Design.Behavior.GlyphCollection" /> to populate the collection.</param>
    </member>
    <member name="M:System.Windows.Forms.Design.Behavior.GlyphCollection.Add(System.Windows.Forms.Design.Behavior.Glyph)">
      <summary>Adds a <see cref="T:System.Windows.Forms.Design.Behavior.Glyph" /> with the specified value to the <see cref="T:System.Windows.Forms.Design.Behavior.GlyphCollection" />.</summary>
      <param name="value">A <see cref="T:System.Windows.Forms.Design.Behavior.Glyph" /> to add to the end of the collection.</param>
      <returns>The index at which the new element was inserted.</returns>
    </member>
    <member name="M:System.Windows.Forms.Design.Behavior.GlyphCollection.AddRange(System.Windows.Forms.Design.Behavior.Glyph[])">
      <summary>Copies the elements of an array to the end of the <see cref="T:System.Windows.Forms.Design.Behavior.GlyphCollection" />.</summary>
      <param name="value">An array of type <see cref="T:System.Windows.Forms.Design.Behavior.Glyph" /> to copy to the end of the collection.</param>
    </member>
    <member name="M:System.Windows.Forms.Design.Behavior.GlyphCollection.AddRange(System.Windows.Forms.Design.Behavior.GlyphCollection)">
      <summary>Adds the contents of another <see cref="T:System.Windows.Forms.Design.Behavior.GlyphCollection" /> to the end of the collection.</summary>
      <param name="value">A <see cref="T:System.Windows.Forms.Design.Behavior.GlyphCollection" /> to add to the end of the collection.</param>
    </member>
    <member name="M:System.Windows.Forms.Design.Behavior.GlyphCollection.Contains(System.Windows.Forms.Design.Behavior.Glyph)">
      <summary>Gets a value indicating whether the <see cref="T:System.Windows.Forms.Design.Behavior.GlyphCollection" /> contains the specified <see cref="T:System.Windows.Forms.Design.Behavior.Glyph" />.</summary>
      <param name="value">The <see cref="T:System.Windows.Forms.Design.Behavior.Glyph" /> to locate.</param>
      <returns>
        <see langword="true" /> if the <see cref="T:System.Windows.Forms.Design.Behavior.Glyph" /> is contained in the collection; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Windows.Forms.Design.Behavior.GlyphCollection.CopyTo(System.Windows.Forms.Design.Behavior.Glyph[],System.Int32)">
      <summary>Copies the <see cref="T:System.Windows.Forms.Design.Behavior.GlyphCollection" /> values to a one-dimensional <see cref="T:System.Array" /> at the specified index.</summary>
      <param name="array">The one-dimensional <see cref="T:System.Array" /> that is the destination of the values copied from <see cref="T:System.Windows.Forms.Design.Behavior.GlyphCollection" />.</param>
      <param name="index">The index in <paramref name="array" /> where copying begins.</param>
    </member>
    <member name="M:System.Windows.Forms.Design.Behavior.GlyphCollection.IndexOf(System.Windows.Forms.Design.Behavior.Glyph)">
      <summary>Returns the index of a <see cref="T:System.Windows.Forms.Design.Behavior.Glyph" /> in the <see cref="T:System.Windows.Forms.Design.Behavior.GlyphCollection" />.</summary>
      <param name="value">The <see cref="T:System.Windows.Forms.Design.Behavior.Glyph" /> to locate.</param>
      <returns>The index of the <see cref="T:System.Windows.Forms.Design.Behavior.Glyph" /> of <paramref name="value" /> in the <see cref="T:System.Windows.Forms.Design.Behavior.GlyphCollection" />, if found; otherwise, -1.</returns>
    </member>
    <member name="M:System.Windows.Forms.Design.Behavior.GlyphCollection.Insert(System.Int32,System.Windows.Forms.Design.Behavior.Glyph)">
      <summary>Inserts a <see cref="T:System.Windows.Forms.Design.Behavior.Glyph" /> into the <see cref="T:System.Windows.Forms.Design.Behavior.GlyphCollection" /> at the specified index.</summary>
      <param name="index">The zero-based index where <paramref name="value" /> should be inserted.</param>
      <param name="value">The <see cref="T:System.Windows.Forms.Design.Behavior.Glyph" /> to insert.</param>
    </member>
    <member name="P:System.Windows.Forms.Design.Behavior.GlyphCollection.Item(System.Int32)">
      <summary>Gets or sets the element at the specified index.</summary>
      <param name="index">The zero-based index of the element.</param>
      <returns>The <see cref="T:System.Windows.Forms.Design.Behavior.Glyph" /> element at the specified index.</returns>
    </member>
    <member name="M:System.Windows.Forms.Design.Behavior.GlyphCollection.Remove(System.Windows.Forms.Design.Behavior.Glyph)">
      <summary>Removes a specific <see cref="T:System.Windows.Forms.Design.Behavior.Glyph" /> from the <see cref="T:System.Windows.Forms.Design.Behavior.GlyphCollection" />.</summary>
      <param name="value">The <see cref="T:System.Windows.Forms.Design.Behavior.Glyph" /> to remove from the <see cref="T:System.Windows.Forms.Design.Behavior.GlyphCollection" />.</param>
    </member>
    <member name="T:System.Windows.Forms.Design.Behavior.GlyphSelectionType">
      <summary>Describes the designer selection state of a <see cref="T:System.Windows.Forms.Design.Behavior.Glyph" />.</summary>
    </member>
    <member name="F:System.Windows.Forms.Design.Behavior.GlyphSelectionType.NotSelected">
      <summary>The <see cref="T:System.Windows.Forms.Design.Behavior.Glyph" /> is not selected.</summary>
    </member>
    <member name="F:System.Windows.Forms.Design.Behavior.GlyphSelectionType.Selected">
      <summary>The <see cref="T:System.Windows.Forms.Design.Behavior.Glyph" /> is selected.</summary>
    </member>
    <member name="F:System.Windows.Forms.Design.Behavior.GlyphSelectionType.SelectedPrimary">
      <summary>The <see cref="T:System.Windows.Forms.Design.Behavior.Glyph" /> is the primary selection.</summary>
    </member>
    <member name="T:System.Windows.Forms.Design.Behavior.SnapLine">
      <summary>Represents the horizontal and vertical line segments that are dynamically created in the user interface (UI) to assist in the design-time layout of controls in a container. This class cannot be inherited.</summary>
    </member>
    <member name="M:System.Windows.Forms.Design.Behavior.SnapLine.#ctor(System.Windows.Forms.Design.Behavior.SnapLineType,System.Int32)">
      <summary>Initializes a new instance of the <see cref="T:System.Windows.Forms.Design.Behavior.SnapLine" /> class using the specified snapline type and offset.</summary>
      <param name="type">The <see cref="T:System.Windows.Forms.Design.Behavior.SnapLineType" /> to create. Describes the relative position and orientation of the snapline.</param>
      <param name="offset">The position of the snapline, in pixels, relative to the upper-left origin of the owning control.</param>
    </member>
    <member name="M:System.Windows.Forms.Design.Behavior.SnapLine.#ctor(System.Windows.Forms.Design.Behavior.SnapLineType,System.Int32,System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Windows.Forms.Design.Behavior.SnapLine" /> class using the specified snapline type, offset, and filter name.</summary>
      <param name="type">The <see cref="T:System.Windows.Forms.Design.Behavior.SnapLineType" /> to create. Describes the relative position and orientation of the snapline.</param>
      <param name="offset">The position of the snapline, in pixels, relative to the upper-left origin of the owning control.</param>
      <param name="filter">A <see cref="T:System.String" /> used to specify a programmer-defined category of snaplines.</param>
    </member>
    <member name="M:System.Windows.Forms.Design.Behavior.SnapLine.#ctor(System.Windows.Forms.Design.Behavior.SnapLineType,System.Int32,System.String,System.Windows.Forms.Design.Behavior.SnapLinePriority)">
      <summary>Initializes a new instance of the <see cref="T:System.Windows.Forms.Design.Behavior.SnapLine" /> class using the specified snapline type, offset, filter name, and priority.</summary>
      <param name="type">The <see cref="T:System.Windows.Forms.Design.Behavior.SnapLineType" /> to create. Describes the relative position and orientation of the snapline.</param>
      <param name="offset">The position of the snapline, in pixels, relative to the upper-left origin of the owning control.</param>
      <param name="filter">A <see cref="T:System.String" /> used to specify a programmer-defined category of snaplines.</param>
      <param name="priority">The <see cref="T:System.Windows.Forms.Design.Behavior.SnapLinePriority" /> of the snapline.</param>
    </member>
    <member name="M:System.Windows.Forms.Design.Behavior.SnapLine.#ctor(System.Windows.Forms.Design.Behavior.SnapLineType,System.Int32,System.Windows.Forms.Design.Behavior.SnapLinePriority)">
      <summary>Initializes a new instance of the <see cref="T:System.Windows.Forms.Design.Behavior.SnapLine" /> class using the specified snapline type, offset, and priority.</summary>
      <param name="type">The <see cref="T:System.Windows.Forms.Design.Behavior.SnapLineType" /> to create. Describes the relative position and orientation of the snapline.</param>
      <param name="offset">The position of the snapline, in pixels, relative to the upper-left origin of the owning control.</param>
      <param name="priority">The <see cref="T:System.Windows.Forms.Design.Behavior.SnapLinePriority" /> of the snapline.</param>
    </member>
    <member name="M:System.Windows.Forms.Design.Behavior.SnapLine.AdjustOffset(System.Int32)">
      <summary>Adjusts the <see cref="P:System.Windows.Forms.Design.Behavior.SnapLine.Offset" /> property of the snapline.</summary>
      <param name="adjustment">The number of pixels to change the snapline offset by.</param>
    </member>
    <member name="P:System.Windows.Forms.Design.Behavior.SnapLine.Filter">
      <summary>Gets the programmer-defined filter category associated with this snapline.</summary>
      <returns>A <see cref="T:System.String" /> that defines the filter category. The default is <see langword="null" />.</returns>
    </member>
    <member name="P:System.Windows.Forms.Design.Behavior.SnapLine.IsHorizontal">
      <summary>Gets a value indicating whether the snapline has a horizontal orientation.</summary>
      <returns>
        <see langword="true" /> if the snapline is horizontal; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Windows.Forms.Design.Behavior.SnapLine.IsVertical">
      <summary>Gets a value indicating whether the snapline has a vertical orientation.</summary>
      <returns>
        <see langword="true" /> if the snapline is vertical; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Windows.Forms.Design.Behavior.SnapLine.Offset">
      <summary>Gets the number of pixels that the snapline is offset from the origin of the associated control.</summary>
      <returns>The offset, in pixels, of the snapline.</returns>
    </member>
    <member name="P:System.Windows.Forms.Design.Behavior.SnapLine.Priority">
      <summary>Gets a value indicating the relative importance of the snapline.</summary>
      <returns>A <see cref="T:System.Windows.Forms.Design.Behavior.SnapLinePriority" /> that represents the priority category of a snapline.</returns>
    </member>
    <member name="M:System.Windows.Forms.Design.Behavior.SnapLine.ShouldSnap(System.Windows.Forms.Design.Behavior.SnapLine,System.Windows.Forms.Design.Behavior.SnapLine)">
      <summary>Returns a value indicating whether the specified <see cref="T:System.Windows.Forms.Design.Behavior.SnapLine" /> should snap to another <see cref="T:System.Windows.Forms.Design.Behavior.SnapLine" />.</summary>
      <param name="line1">The specified <see cref="T:System.Windows.Forms.Design.Behavior.SnapLine" />.</param>
      <param name="line2">The <see cref="T:System.Windows.Forms.Design.Behavior.SnapLine" /> to which the specified <see cref="T:System.Windows.Forms.Design.Behavior.SnapLine" /> is expected to snap.</param>
      <returns>
        <see langword="true" /> if <paramref name="line1" /> should snap to <paramref name="line2" />; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Windows.Forms.Design.Behavior.SnapLine.SnapLineType">
      <summary>Gets the type of a snapline, which indicates the general location and orientation.</summary>
      <returns>A <see cref="T:System.Windows.Forms.Design.Behavior.SnapLineType" /> that represents the orientation and general location, relative to control edges, of a snapline.</returns>
    </member>
    <member name="M:System.Windows.Forms.Design.Behavior.SnapLine.ToString">
      <summary>Returns a string representation of the current snapline.</summary>
      <returns>A <see cref="T:System.String" /> that represents the current <see cref="T:System.Windows.Forms.Design.Behavior.SnapLine" />.</returns>
    </member>
    <member name="T:System.Windows.Forms.Design.Behavior.SnapLinePriority">
      <summary>Specifies the relative importance of a snapline.</summary>
    </member>
    <member name="F:System.Windows.Forms.Design.Behavior.SnapLinePriority.Always">
      <summary>The priority category that is equivalent to the highest priority of all the current snaplines. Indicates that this category of snapline should always be active.</summary>
    </member>
    <member name="F:System.Windows.Forms.Design.Behavior.SnapLinePriority.High">
      <summary>The highest priority category.</summary>
    </member>
    <member name="F:System.Windows.Forms.Design.Behavior.SnapLinePriority.Low">
      <summary>The lowest priority category.</summary>
    </member>
    <member name="F:System.Windows.Forms.Design.Behavior.SnapLinePriority.Medium">
      <summary>The middle priority category.</summary>
    </member>
    <member name="T:System.Windows.Forms.Design.Behavior.SnapLineType">
      <summary>Specifies the orientation and relative location of a snapline.</summary>
    </member>
    <member name="F:System.Windows.Forms.Design.Behavior.SnapLineType.Baseline">
      <summary>A horizontal snapline typically associated with a primary internal feature of a control; for example, the base of the text string in a <see cref="T:System.Windows.Forms.Label" /> control.</summary>
    </member>
    <member name="F:System.Windows.Forms.Design.Behavior.SnapLineType.Bottom">
      <summary>A horizontal snapline typically aligned to the bottom edge of a control.</summary>
    </member>
    <member name="F:System.Windows.Forms.Design.Behavior.SnapLineType.Horizontal">
      <summary>A horizontal snapline typically not associated with an edge of a control.</summary>
    </member>
    <member name="F:System.Windows.Forms.Design.Behavior.SnapLineType.Left">
      <summary>A vertical snapline typically aligned to the left edge of a control.</summary>
    </member>
    <member name="F:System.Windows.Forms.Design.Behavior.SnapLineType.Right">
      <summary>A vertical snapline typically aligned to the right edge of a control.</summary>
    </member>
    <member name="F:System.Windows.Forms.Design.Behavior.SnapLineType.Top">
      <summary>A horizontal snapline typically aligned to the top edge of a control.</summary>
    </member>
    <member name="F:System.Windows.Forms.Design.Behavior.SnapLineType.Vertical">
      <summary>A vertical snapline typically not associated with an edge of a control.</summary>
    </member>
    <member name="T:System.Windows.Forms.Design.ComponentActionsType" />
    <member name="F:System.Windows.Forms.Design.ComponentActionsType.All" />
    <member name="F:System.Windows.Forms.Design.ComponentActionsType.Component" />
    <member name="F:System.Windows.Forms.Design.ComponentActionsType.Service" />
    <member name="T:System.Windows.Forms.Design.ComponentDocumentDesigner">
      <summary>Base designer class for extending the design mode behavior of a root design document that supports nested components.</summary>
    </member>
    <member name="M:System.Windows.Forms.Design.ComponentDocumentDesigner.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Windows.Forms.Design.ComponentDocumentDesigner" /> class.</summary>
    </member>
    <member name="P:System.Windows.Forms.Design.ComponentDocumentDesigner.Control">
      <summary>Gets the control for the designer.</summary>
      <returns>The <see cref="T:System.Windows.Forms.Control" /> the designer is editing.</returns>
    </member>
    <member name="M:System.Windows.Forms.Design.ComponentDocumentDesigner.Dispose(System.Boolean)">
      <summary>Releases the unmanaged resources used by the <see cref="T:System.Windows.Forms.Design.ComponentDocumentDesigner" /> and optionally releases the managed resources.</summary>
      <param name="disposing">
        <see langword="true" /> to release both managed and unmanaged resources; <see langword="false" /> to release only unmanaged resources.</param>
    </member>
    <member name="M:System.Windows.Forms.Design.ComponentDocumentDesigner.GetToolSupported(System.Drawing.Design.ToolboxItem)">
      <summary>Gets a value indicating whether the specified tool is supported by the designer.</summary>
      <param name="tool">The <see cref="T:System.Drawing.Design.ToolboxItem" /> to test for toolbox support.</param>
      <returns>
        <see langword="true" /> if the tool should be enabled on the toolbox; <see langword="false" /> if the document designer doesn't know how to use the tool.</returns>
    </member>
    <member name="M:System.Windows.Forms.Design.ComponentDocumentDesigner.Initialize(System.ComponentModel.IComponent)">
      <summary>Initializes the designer with the specified component.</summary>
      <param name="component">The <see cref="T:System.ComponentModel.IComponent" /> to associate with the designer.</param>
    </member>
    <member name="M:System.Windows.Forms.Design.ComponentDocumentDesigner.PreFilterProperties(System.Collections.IDictionary)">
      <summary>Adjusts the set of properties the component will expose through a <see cref="T:System.ComponentModel.TypeDescriptor" />.</summary>
      <param name="properties">An <see cref="T:System.Collections.IDictionary" /> that contains the properties for the class of the component.</param>
    </member>
    <member name="M:System.Windows.Forms.Design.ComponentDocumentDesigner.System#ComponentModel#Design#IRootDesigner#GetView(System.ComponentModel.Design.ViewTechnology)">
      <summary>For a description of this member, see <see cref="T:System.ComponentModel.Design.ViewTechnology" />.</summary>
      <param name="technology">A <see cref="T:System.ComponentModel.Design.ViewTechnology" /> that indicates a particular view technology.</param>
      <returns>An object that represents the view for this designer.</returns>
    </member>
    <member name="P:System.Windows.Forms.Design.ComponentDocumentDesigner.System#ComponentModel#Design#IRootDesigner#SupportedTechnologies">
      <summary>For a description of this member, see <see cref="P:System.ComponentModel.Design.IRootDesigner.SupportedTechnologies" />.</summary>
      <returns>An array of supported <see cref="T:System.ComponentModel.Design.ViewTechnology" /> values.</returns>
    </member>
    <member name="M:System.Windows.Forms.Design.ComponentDocumentDesigner.System#ComponentModel#Design#ITypeDescriptorFilterService#FilterAttributes(System.ComponentModel.IComponent,System.Collections.IDictionary)">
      <summary>For a description of this member, see <see cref="M:System.ComponentModel.Design.ITypeDescriptorFilterService.FilterAttributes(System.ComponentModel.IComponent,System.Collections.IDictionary)" />.</summary>
      <param name="component">The component to filter the attributes of.</param>
      <param name="attributes">A dictionary of attributes that can be modified.</param>
      <returns>
        <see langword="true" /> if the set of filtered attributes is to be cached; <see langword="false" /> if the filter service must query again.</returns>
    </member>
    <member name="M:System.Windows.Forms.Design.ComponentDocumentDesigner.System#ComponentModel#Design#ITypeDescriptorFilterService#FilterEvents(System.ComponentModel.IComponent,System.Collections.IDictionary)">
      <summary>For a description of this member, see <see cref="M:System.ComponentModel.Design.ITypeDescriptorFilterService.FilterEvents(System.ComponentModel.IComponent,System.Collections.IDictionary)" />.</summary>
      <param name="component">The component to filter events for.</param>
      <param name="events">A dictionary of events that can be modified.</param>
      <returns>
        <see langword="true" /> if the set of filtered events is to be cached; <see langword="false" /> if the filter service must query again.</returns>
    </member>
    <member name="M:System.Windows.Forms.Design.ComponentDocumentDesigner.System#ComponentModel#Design#ITypeDescriptorFilterService#FilterProperties(System.ComponentModel.IComponent,System.Collections.IDictionary)">
      <summary>For a description of this member, see <see cref="M:System.ComponentModel.Design.ITypeDescriptorFilterService.FilterProperties(System.ComponentModel.IComponent,System.Collections.IDictionary)" />.</summary>
      <param name="component">The component to filter properties for.</param>
      <param name="properties">A dictionary of properties that can be modified.</param>
      <returns>
        <see langword="true" /> if the set of filtered properties is to be cached; <see langword="false" /> if the filter service must query again.</returns>
    </member>
    <member name="M:System.Windows.Forms.Design.ComponentDocumentDesigner.System#Drawing#Design#IToolboxUser#GetToolSupported(System.Drawing.Design.ToolboxItem)">
      <summary>For a description of this member, see <see cref="M:System.Drawing.Design.IToolboxUser.GetToolSupported(System.Drawing.Design.ToolboxItem)" />.</summary>
      <param name="tool">The <see cref="T:System.Drawing.Design.ToolboxItem" /> to be tested for toolbox support.</param>
      <returns>
        <see langword="true" /> if the tool is supported by the toolbox and can be enabled; <see langword="false" /> if the document designer does not know how to use the tool.</returns>
    </member>
    <member name="M:System.Windows.Forms.Design.ComponentDocumentDesigner.System#Drawing#Design#IToolboxUser#ToolPicked(System.Drawing.Design.ToolboxItem)">
      <summary>For a description of this member, see <see cref="M:System.Drawing.Design.IToolboxUser.ToolPicked(System.Drawing.Design.ToolboxItem)" />.</summary>
      <param name="tool">The <see cref="T:System.Drawing.Design.ToolboxItem" /> to select.</param>
    </member>
    <member name="P:System.Windows.Forms.Design.ComponentDocumentDesigner.TrayAutoArrange">
      <summary>Gets or sets a value indicating whether the component tray for the designer is in auto-arrange mode.</summary>
      <returns>
        <see langword="true" /> if the component tray for the designer is in auto-arrange mode; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Windows.Forms.Design.ComponentDocumentDesigner.TrayLargeIcon">
      <summary>Gets or sets a value indicating whether the component tray for the designer is in large icon mode.</summary>
      <returns>
        <see langword="true" /> if the component tray for the designer is in large icon mode; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="T:System.Windows.Forms.Design.ComponentTray">
      <summary>Provides behavior for the component tray of a designer.</summary>
    </member>
    <member name="M:System.Windows.Forms.Design.ComponentTray.#ctor(System.ComponentModel.Design.IDesigner,System.IServiceProvider)">
      <summary>Initializes a new instance of the <see cref="T:System.Windows.Forms.Design.ComponentTray" /> class using the specified designer and service provider.</summary>
      <param name="mainDesigner">The <see cref="T:System.ComponentModel.Design.IDesigner" /> that is the main or document designer for the current project.</param>
      <param name="serviceProvider">An <see cref="T:System.IServiceProvider" /> that can be used to obtain design-time services.</param>
    </member>
    <member name="M:System.Windows.Forms.Design.ComponentTray.AddComponent(System.ComponentModel.IComponent)">
      <summary>Adds a component to the tray.</summary>
      <param name="component">The <see cref="T:System.ComponentModel.IComponent" /> to add to the tray.</param>
    </member>
    <member name="P:System.Windows.Forms.Design.ComponentTray.AutoArrange">
      <summary>Gets or sets a value indicating whether the tray items are automatically aligned.</summary>
      <returns>
        <see langword="true" /> if the tray items are automatically arranged; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Windows.Forms.Design.ComponentTray.CanCreateComponentFromTool(System.Drawing.Design.ToolboxItem)">
      <summary>Gets a value indicating whether the specified tool can be used to create a new component.</summary>
      <param name="tool">The <see cref="T:System.Drawing.Design.ToolboxItem" /> to test.</param>
      <returns>
        <see langword="true" /> if the specified tool can be used to create a component; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Windows.Forms.Design.ComponentTray.CanDisplayComponent(System.ComponentModel.IComponent)">
      <summary>Gets a value indicating whether the specified component can be displayed.</summary>
      <param name="component">The <see cref="T:System.ComponentModel.IComponent" /> to test.</param>
      <returns>
        <see langword="true" /> if the component can be displayed; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Windows.Forms.Design.ComponentTray.ComponentCount">
      <summary>Gets the number of components contained in the tray.</summary>
      <returns>The number of components in the tray.</returns>
    </member>
    <member name="M:System.Windows.Forms.Design.ComponentTray.CreateComponentFromTool(System.Drawing.Design.ToolboxItem)">
      <summary>Creates a component from the specified toolbox item, adds the component to the current document, and displays a representation for the component in the component tray.</summary>
      <param name="tool">The <see cref="T:System.Drawing.Design.ToolboxItem" /> to create a component from.</param>
    </member>
    <member name="M:System.Windows.Forms.Design.ComponentTray.DisplayError(System.Exception)">
      <summary>Displays an error message to the user with information about the specified exception.</summary>
      <param name="e">The exception about which to display information.</param>
    </member>
    <member name="M:System.Windows.Forms.Design.ComponentTray.Dispose(System.Boolean)">
      <summary>Releases the unmanaged resources used by the <see cref="T:System.Windows.Forms.Design.ComponentTray" /> and optionally releases the managed resources.</summary>
      <param name="disposing">
        <see langword="true" /> to release both managed and unmanaged resources; <see langword="false" /> to release only unmanaged resources.</param>
    </member>
    <member name="M:System.Windows.Forms.Design.ComponentTray.GetLocation(System.ComponentModel.IComponent)">
      <summary>Gets the location of the specified component, relative to the upper-left corner of the component tray.</summary>
      <param name="receiver">The <see cref="T:System.ComponentModel.IComponent" /> to retrieve the location of.</param>
      <returns>A <see cref="T:System.Drawing.Point" /> indicating the coordinates of the specified component, or an empty <see cref="T:System.Drawing.Point" /> if the specified component could not be found in the component tray. An empty <see cref="T:System.Drawing.Point" /> has an <see cref="P:System.Drawing.Point.IsEmpty" /> property equal to <see langword="true" /> and typically has <see cref="P:System.Drawing.Point.X" /> and <see cref="P:System.Drawing.Point.Y" /> properties that are each equal to zero.</returns>
    </member>
    <member name="M:System.Windows.Forms.Design.ComponentTray.GetNextComponent(System.ComponentModel.IComponent,System.Boolean)">
      <summary>Similar to <see cref="M:System.Windows.Forms.Control.GetNextControl(System.Windows.Forms.Control,System.Boolean)" />, this method returns the next component in the tray, given a starting component.</summary>
      <param name="component">The component from which to start enumerating.</param>
      <param name="forward">
        <see langword="true" /> to enumerate forward through the list; otherwise, <see langword="false" /> to enumerate backward.</param>
      <returns>The next component in the component tray, or <see langword="null" />, if the end of the list is encountered (or the beginning, if <paramref name="forward" /> is <see langword="false" />).</returns>
    </member>
    <member name="M:System.Windows.Forms.Design.ComponentTray.GetService(System.Type)">
      <summary>Gets the requested service type.</summary>
      <param name="serviceType">The type of the service to retrieve.</param>
      <returns>An instance of the requested service, or <see langword="null" /> if the service could not be found.</returns>
    </member>
    <member name="M:System.Windows.Forms.Design.ComponentTray.GetTrayLocation(System.ComponentModel.IComponent)">
      <summary>Gets the value of the <see langword="Location" /> extender property.</summary>
      <param name="receiver">The <see cref="T:System.ComponentModel.IComponent" /> that receives the location extender property.</param>
      <returns>A <see cref="T:System.Drawing.Point" /> representing the location of <paramref name="receiver" />.</returns>
    </member>
    <member name="M:System.Windows.Forms.Design.ComponentTray.IsTrayComponent(System.ComponentModel.IComponent)">
      <summary>Tests a component for presence in the component tray.</summary>
      <param name="comp">The component to test for presence in the component tray.</param>
      <returns>
        <see langword="true" /> if <paramref name="comp" /> is being shown on the tray; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Windows.Forms.Design.ComponentTray.OnDragDrop(System.Windows.Forms.DragEventArgs)">
      <summary>Called when an object that has been dragged is dropped on the component tray.</summary>
      <param name="de">A <see cref="T:System.Windows.Forms.DragEventArgs" /> that provides data for the event.</param>
    </member>
    <member name="M:System.Windows.Forms.Design.ComponentTray.OnDragEnter(System.Windows.Forms.DragEventArgs)">
      <summary>Called when an object is dragged over, and has entered the area over, the component tray.</summary>
      <param name="de">A <see cref="T:System.Windows.Forms.DragEventArgs" /> that provides data for the event.</param>
    </member>
    <member name="M:System.Windows.Forms.Design.ComponentTray.OnDragLeave(System.EventArgs)">
      <summary>Called when an object is dragged out of the area over the component tray.</summary>
      <param name="e">An <see cref="T:System.EventArgs" /> that provides data for the event.</param>
    </member>
    <member name="M:System.Windows.Forms.Design.ComponentTray.OnDragOver(System.Windows.Forms.DragEventArgs)">
      <summary>Called when an object is dragged over the component tray.</summary>
      <param name="de">A <see cref="T:System.Windows.Forms.DragEventArgs" /> that provides data for the event.</param>
    </member>
    <member name="M:System.Windows.Forms.Design.ComponentTray.OnGiveFeedback(System.Windows.Forms.GiveFeedbackEventArgs)">
      <summary>Called during an OLE drag and drop operation to provide an opportunity for the component tray to give feedback to the user about the results of dropping the object at a specific point.</summary>
      <param name="gfevent">A <see cref="T:System.Windows.Forms.GiveFeedbackEventArgs" /> that provides data for the event.</param>
    </member>
    <member name="M:System.Windows.Forms.Design.ComponentTray.OnLayout(System.Windows.Forms.LayoutEventArgs)">
      <summary>Raises the <see cref="E:System.Windows.Forms.Control.Layout" /> event.</summary>
      <param name="levent">A <see cref="T:System.Windows.Forms.LayoutEventArgs" /> that contains the event data.</param>
    </member>
    <member name="M:System.Windows.Forms.Design.ComponentTray.OnLostCapture">
      <summary>Called when a mouse drag selection operation is canceled.</summary>
    </member>
    <member name="M:System.Windows.Forms.Design.ComponentTray.OnMouseDoubleClick(System.Windows.Forms.MouseEventArgs)">
      <summary>Called when the mouse is double clicked over the component tray.</summary>
      <param name="e">An <see cref="T:System.EventArgs" /> that provides data for the event.</param>
    </member>
    <member name="M:System.Windows.Forms.Design.ComponentTray.OnMouseDown(System.Windows.Forms.MouseEventArgs)">
      <summary>Called when the mouse button is pressed.</summary>
      <param name="e">A <see cref="T:System.Windows.Forms.MouseEventArgs" /> that provides data for the event.</param>
    </member>
    <member name="M:System.Windows.Forms.Design.ComponentTray.OnMouseMove(System.Windows.Forms.MouseEventArgs)">
      <summary>Called when the mouse cursor position has changed.</summary>
      <param name="e">A <see cref="T:System.Windows.Forms.MouseEventArgs" /> that provides data for the event.</param>
    </member>
    <member name="M:System.Windows.Forms.Design.ComponentTray.OnMouseUp(System.Windows.Forms.MouseEventArgs)">
      <summary>Called when the mouse button has been released.</summary>
      <param name="e">A <see cref="T:System.Windows.Forms.MouseEventArgs" /> that provides data for the event.</param>
    </member>
    <member name="M:System.Windows.Forms.Design.ComponentTray.OnPaint(System.Windows.Forms.PaintEventArgs)">
      <summary>Called when the view for the component tray should be refreshed.</summary>
      <param name="pe">A <see cref="T:System.Windows.Forms.PaintEventArgs" /> that provides data for the event.</param>
    </member>
    <member name="M:System.Windows.Forms.Design.ComponentTray.OnSetCursor">
      <summary>Called to set the mouse cursor.</summary>
    </member>
    <member name="M:System.Windows.Forms.Design.ComponentTray.RemoveComponent(System.ComponentModel.IComponent)">
      <summary>Removes the specified component from the tray.</summary>
      <param name="component">The <see cref="T:System.ComponentModel.IComponent" /> to remove from the tray.</param>
    </member>
    <member name="M:System.Windows.Forms.Design.ComponentTray.SetLocation(System.ComponentModel.IComponent,System.Drawing.Point)">
      <summary>Sets the location of the specified component to the specified location.</summary>
      <param name="receiver">The <see cref="T:System.ComponentModel.IComponent" /> to set the location of.</param>
      <param name="location">A <see cref="T:System.Drawing.Point" /> indicating the new location for the specified component.</param>
    </member>
    <member name="M:System.Windows.Forms.Design.ComponentTray.SetTrayLocation(System.ComponentModel.IComponent,System.Drawing.Point)">
      <summary>Sets the value of the <see langword="Location" /> extender property.</summary>
      <param name="receiver">The <see cref="T:System.ComponentModel.IComponent" /> that receives the location extender property.</param>
      <param name="location">A <see cref="T:System.Drawing.Point" /> representing the location of <paramref name="receiver" />.</param>
    </member>
    <member name="P:System.Windows.Forms.Design.ComponentTray.ShowLargeIcons">
      <summary>Gets or sets a value indicating whether the tray displays a large icon to represent each component in the tray.</summary>
      <returns>
        <see langword="true" /> if large icons are displayed; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Windows.Forms.Design.ComponentTray.System#ComponentModel#IExtenderProvider#CanExtend(System.Object)">
      <summary>For a description of this member, see <see cref="M:System.ComponentModel.IExtenderProvider.CanExtend(System.Object)" />.</summary>
      <param name="extendee">The object to receive the extender properties.</param>
      <returns>
        <see langword="true" /> if this object can provide extender properties to the specified object; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Windows.Forms.Design.ComponentTray.WndProc(System.Windows.Forms.Message@)">
      <summary>Processes Windows messages.</summary>
      <param name="m">The <see cref="T:System.Windows.Forms.Message" /> to process.</param>
    </member>
    <member name="T:System.Windows.Forms.Design.ControlDesigner">
      <summary>Extends the design mode behavior of a <see cref="T:System.Windows.Forms.Control" />.</summary>
    </member>
    <member name="M:System.Windows.Forms.Design.ControlDesigner.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Windows.Forms.Design.ControlDesigner" /> class.</summary>
    </member>
    <member name="F:System.Windows.Forms.Design.ControlDesigner.accessibilityObj">
      <summary>Specifies the accessibility object for the designer.</summary>
    </member>
    <member name="P:System.Windows.Forms.Design.ControlDesigner.AccessibilityObject">
      <summary>Gets the <see cref="T:System.Windows.Forms.AccessibleObject" /> assigned to the control.</summary>
      <returns>The <see cref="T:System.Windows.Forms.AccessibleObject" /> assigned to the control.</returns>
    </member>
    <member name="P:System.Windows.Forms.Design.ControlDesigner.AssociatedComponents">
      <summary>Gets the collection of components associated with the component managed by the designer.</summary>
      <returns>The components that are associated with the component managed by the designer.</returns>
    </member>
    <member name="P:System.Windows.Forms.Design.ControlDesigner.AutoResizeHandles">
      <summary>Gets or sets a value indicating whether resize handle allocation depends on the value of the <see cref="P:System.Windows.Forms.Control.AutoSize" /> property.</summary>
      <returns>
        <see langword="true" /> if resize handle allocation depends on the value of the <see cref="P:System.Windows.Forms.Control.AutoSize" /> and <see langword="AutoSizeMode" /> properties; otherwise, <see langword="false" />. The default is <see langword="false" />.</returns>
    </member>
    <member name="M:System.Windows.Forms.Design.ControlDesigner.BaseWndProc(System.Windows.Forms.Message@)">
      <summary>Processes Windows messages.</summary>
      <param name="m">The <see cref="T:System.Windows.Forms.Message" /> to process.</param>
    </member>
    <member name="P:System.Windows.Forms.Design.ControlDesigner.BehaviorService">
      <summary>Gets the <see cref="T:System.Windows.Forms.Design.Behavior.BehaviorService" /> from the design environment.</summary>
      <returns>The <see cref="T:System.Windows.Forms.Design.Behavior.BehaviorService" />, or <see langword="null" /> if the service is unavailable.</returns>
    </member>
    <member name="M:System.Windows.Forms.Design.ControlDesigner.CanBeParentedTo(System.ComponentModel.Design.IDesigner)">
      <summary>Indicates if this designer's control can be parented by the control of the specified designer.</summary>
      <param name="parentDesigner">The <see cref="T:System.ComponentModel.Design.IDesigner" /> that manages the control to check.</param>
      <returns>
        <see langword="true" /> if the control managed by the specified designer can parent the control managed by this designer; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Windows.Forms.Design.ControlDesigner.Control">
      <summary>Gets the control that the designer is designing.</summary>
      <returns>The control that the designer is designing.</returns>
    </member>
    <member name="T:System.Windows.Forms.Design.ControlDesigner.ControlDesignerAccessibleObject">
      <summary>Provides an <see cref="T:System.Windows.Forms.AccessibleObject" /> for <see cref="T:System.Windows.Forms.Design.ControlDesigner" />.</summary>
    </member>
    <member name="M:System.Windows.Forms.Design.ControlDesigner.ControlDesignerAccessibleObject.#ctor(System.Windows.Forms.Design.ControlDesigner,System.Windows.Forms.Control)">
      <summary>Initializes a new instance of the <see cref="T:System.Windows.Forms.Design.ControlDesigner.ControlDesignerAccessibleObject" /> class using the specified designer and control.</summary>
      <param name="designer">The <see cref="T:System.Windows.Forms.Design.ControlDesigner" /> for the accessible object.</param>
      <param name="control">The <see cref="T:System.Windows.Forms.Control" /> for the accessible object.</param>
    </member>
    <member name="P:System.Windows.Forms.Design.ControlDesigner.ControlDesignerAccessibleObject.Bounds">
      <summary>Gets the points that define the boundaries of the accessible object for the designer.</summary>
      <returns>A <see cref="T:System.Drawing.Rectangle" /> that indicates the boundaries of the accessible object for the designer.</returns>
    </member>
    <member name="P:System.Windows.Forms.Design.ControlDesigner.ControlDesignerAccessibleObject.DefaultAction">
      <summary>Gets a string that describes the default action of the specified object.</summary>
      <returns>A description of the default action for a specified object.</returns>
    </member>
    <member name="P:System.Windows.Forms.Design.ControlDesigner.ControlDesignerAccessibleObject.Description">
      <summary>Gets a string that describes the visual appearance of the specified object.</summary>
      <returns>A description of the object's visual appearance to the user, or <see langword="null" /> if the object does not have a description.</returns>
    </member>
    <member name="M:System.Windows.Forms.Design.ControlDesigner.ControlDesignerAccessibleObject.GetChild(System.Int32)">
      <summary>Retrieves the accessible child corresponding to the specified index.</summary>
      <param name="index">The zero-based index of the accessible child.</param>
      <returns>An <see cref="T:System.Windows.Forms.AccessibleObject" /> that represents the accessible child corresponding to the specified index.</returns>
    </member>
    <member name="M:System.Windows.Forms.Design.ControlDesigner.ControlDesignerAccessibleObject.GetChildCount">
      <summary>Retrieves the number of children belonging to an accessible object.</summary>
      <returns>The number of children belonging to an accessible object.</returns>
    </member>
    <member name="M:System.Windows.Forms.Design.ControlDesigner.ControlDesignerAccessibleObject.GetFocused">
      <summary>Retrieves the object that has the keyboard focus.</summary>
      <returns>An <see cref="T:System.Windows.Forms.AccessibleObject" /> that specifies the currently focused child. This method returns the calling object if the object itself is focused. Returns <see langword="null" /> if no object has focus.</returns>
    </member>
    <member name="M:System.Windows.Forms.Design.ControlDesigner.ControlDesignerAccessibleObject.GetSelected">
      <summary>Retrieves the currently selected child.</summary>
      <returns>An <see cref="T:System.Windows.Forms.AccessibleObject" /> that represents the currently selected child. This method returns the calling object if the object itself is selected. Returns <see langword="null" /> if is no child is currently selected and the object itself does not have focus.</returns>
    </member>
    <member name="M:System.Windows.Forms.Design.ControlDesigner.ControlDesignerAccessibleObject.HitTest(System.Int32,System.Int32)">
      <summary>Retrieves the child object at the specified screen coordinates.</summary>
      <param name="x">The horizontal screen coordinate.</param>
      <param name="y">The vertical screen coordinate.</param>
      <returns>An <see cref="T:System.Windows.Forms.AccessibleObject" /> that represents the child object at the given screen coordinates. This method returns the calling object if the object itself is at the location specified. Returns <see langword="null" /> if no object is at the tested location.</returns>
    </member>
    <member name="P:System.Windows.Forms.Design.ControlDesigner.ControlDesignerAccessibleObject.Name">
      <summary>Gets or sets the object name.</summary>
      <returns>The object name, or <see langword="null" /> if the property has not been set.</returns>
    </member>
    <member name="P:System.Windows.Forms.Design.ControlDesigner.ControlDesignerAccessibleObject.Parent">
      <summary>Gets the parent of an accessible object.</summary>
      <returns>An <see cref="T:System.Windows.Forms.AccessibleObject" /> that represents the parent of an accessible object, or <see langword="null" /> if there is no parent object.</returns>
    </member>
    <member name="P:System.Windows.Forms.Design.ControlDesigner.ControlDesignerAccessibleObject.Role">
      <summary>Gets the role of this accessible object.</summary>
      <returns>One of the <see cref="T:System.Windows.Forms.AccessibleRole" /> values, or <see cref="F:System.Windows.Forms.AccessibleRole.None" /> if no role has been specified.</returns>
    </member>
    <member name="P:System.Windows.Forms.Design.ControlDesigner.ControlDesignerAccessibleObject.State">
      <summary>Gets the state of this accessible object.</summary>
      <returns>One of the <see cref="T:System.Windows.Forms.AccessibleStates" /> values, or <see cref="F:System.Windows.Forms.AccessibleStates.None" />, if no state has been set.</returns>
    </member>
    <member name="P:System.Windows.Forms.Design.ControlDesigner.ControlDesignerAccessibleObject.Value">
      <summary>Gets or sets the value of an accessible object.</summary>
      <returns>The value of an accessible object, or <see langword="null" /> if the object has no value set.</returns>
    </member>
    <member name="M:System.Windows.Forms.Design.ControlDesigner.DefWndProc(System.Windows.Forms.Message@)">
      <summary>Provides default processing for Windows messages.</summary>
      <param name="m">The <see cref="T:System.Windows.Forms.Message" /> to process.</param>
    </member>
    <member name="M:System.Windows.Forms.Design.ControlDesigner.DisplayError(System.Exception)">
      <summary>Displays information about the specified exception to the user.</summary>
      <param name="e">The <see cref="T:System.Exception" /> to display.</param>
    </member>
    <member name="M:System.Windows.Forms.Design.ControlDesigner.Dispose(System.Boolean)">
      <summary>Releases the unmanaged resources used by the <see cref="T:System.Windows.Forms.Design.ControlDesigner" /> and optionally releases the managed resources.</summary>
      <param name="disposing">
        <see langword="true" /> to release both managed and unmanaged resources; <see langword="false" /> to release only unmanaged resources.</param>
    </member>
    <member name="M:System.Windows.Forms.Design.ControlDesigner.EnableDesignMode(System.Windows.Forms.Control,System.String)">
      <summary>Enables design time functionality for a child control.</summary>
      <param name="child">The child control for which design mode will be enabled.</param>
      <param name="name">The name of <paramref name="child" /> as exposed to the end user.</param>
      <returns>
        <see langword="true" /> if the child control could be enabled for design time; <see langword="false" /> if the hosting infrastructure does not support it.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="child" /> or <paramref name="name" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Windows.Forms.Design.ControlDesigner.EnableDragDrop(System.Boolean)">
      <summary>Enables or disables drag-and-drop support for the control being designed.</summary>
      <param name="value">
        <see langword="true" /> to enable drag-and-drop support for the control; <see langword="false" /> if the control should not have drag-and-drop support. The default is <see langword="false" />.</param>
    </member>
    <member name="P:System.Windows.Forms.Design.ControlDesigner.EnableDragRect">
      <summary>Gets a value indicating whether drag rectangles can be drawn on this designer component.</summary>
      <returns>
        <see langword="true" /> if drag rectangles can be drawn; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Windows.Forms.Design.ControlDesigner.GetControlGlyph(System.Windows.Forms.Design.Behavior.GlyphSelectionType)">
      <summary>Returns a <see cref="T:System.Windows.Forms.Design.Behavior.ControlBodyGlyph" /> representing the bounds of this control.</summary>
      <param name="selectionType">A <see cref="T:System.Windows.Forms.Design.Behavior.GlyphSelectionType" /> value that specifies the selection state.</param>
      <returns>A <see cref="T:System.Windows.Forms.Design.Behavior.ControlBodyGlyph" /> representing the bounds of this control.</returns>
    </member>
    <member name="M:System.Windows.Forms.Design.ControlDesigner.GetGlyphs(System.Windows.Forms.Design.Behavior.GlyphSelectionType)">
      <summary>Gets a collection of <see cref="T:System.Windows.Forms.Design.Behavior.Glyph" /> objects representing the selection borders and grab handles for a standard control.</summary>
      <param name="selectionType">A <see cref="T:System.Windows.Forms.Design.Behavior.GlyphSelectionType" /> value that specifies the selection state.</param>
      <returns>A collection of <see cref="T:System.Windows.Forms.Design.Behavior.Glyph" /> objects.</returns>
    </member>
    <member name="M:System.Windows.Forms.Design.ControlDesigner.GetHitTest(System.Drawing.Point)">
      <summary>Indicates whether a mouse click at the specified point should be handled by the control.</summary>
      <param name="point">A <see cref="T:System.Drawing.Point" /> indicating the position at which the mouse was clicked, in screen coordinates.</param>
      <returns>
        <see langword="true" /> if a click at the specified point is to be handled by the control; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Windows.Forms.Design.ControlDesigner.HookChildControls(System.Windows.Forms.Control)">
      <summary>Routes messages from the child controls of the specified control to the designer.</summary>
      <param name="firstChild">The first child <see cref="T:System.Windows.Forms.Control" /> to process. This method may recursively call itself for children of the control.</param>
    </member>
    <member name="P:System.Windows.Forms.Design.ControlDesigner.InheritanceAttribute">
      <summary>Gets the <see cref="T:System.ComponentModel.InheritanceAttribute" /> of the designer.</summary>
      <returns>
        <see cref="F:System.ComponentModel.InheritanceAttribute.Inherited" /> if the designer is a root designer; otherwise, the value of the parent designer's <see cref="P:System.ComponentModel.Design.ComponentDesigner.InheritanceAttribute" /> property.</returns>
    </member>
    <member name="M:System.Windows.Forms.Design.ControlDesigner.Initialize(System.ComponentModel.IComponent)">
      <summary>Initializes the designer with the specified component.</summary>
      <param name="component">The <see cref="T:System.ComponentModel.IComponent" /> to associate the designer with. This component must always be an instance of, or derive from, <see cref="T:System.Windows.Forms.Control" />.</param>
    </member>
    <member name="M:System.Windows.Forms.Design.ControlDesigner.InitializeExistingComponent(System.Collections.IDictionary)">
      <summary>Re-initializes an existing component.</summary>
      <param name="defaultValues">A name/value dictionary of default values to apply to properties. May be <see langword="null" /> if no default values are specified.</param>
    </member>
    <member name="M:System.Windows.Forms.Design.ControlDesigner.InitializeNewComponent(System.Collections.IDictionary)">
      <summary>Initializes a newly created component.</summary>
      <param name="defaultValues">A name/value dictionary of default values to apply to properties. May be <see langword="null" /> if no default values are specified.</param>
    </member>
    <member name="M:System.Windows.Forms.Design.ControlDesigner.InternalControlDesigner(System.Int32)">
      <summary>Returns the internal control designer with the specified index in the <see cref="T:System.Windows.Forms.Design.ControlDesigner" />.</summary>
      <param name="internalControlIndex">A specified index to select the internal control designer. This index is zero-based.</param>
      <returns>A <see cref="T:System.Windows.Forms.Design.ControlDesigner" /> at the specified index.</returns>
    </member>
    <member name="F:System.Windows.Forms.Design.ControlDesigner.InvalidPoint">
      <summary>Defines a local <see cref="T:System.Drawing.Point" /> that represents the values of an invalid <see cref="T:System.Drawing.Point" />.</summary>
    </member>
    <member name="M:System.Windows.Forms.Design.ControlDesigner.NumberOfInternalControlDesigners">
      <summary>Returns the number of internal control designers in the <see cref="T:System.Windows.Forms.Design.ControlDesigner" />.</summary>
      <returns>The number of internal control designers in the <see cref="T:System.Windows.Forms.Design.ControlDesigner" />.</returns>
    </member>
    <member name="M:System.Windows.Forms.Design.ControlDesigner.OnContextMenu(System.Int32,System.Int32)">
      <summary>Shows the context menu and provides an opportunity to perform additional processing when the context menu is about to be displayed.</summary>
      <param name="x">The x coordinate at which to display the context menu.</param>
      <param name="y">The y coordinate at which to display the context menu.</param>
    </member>
    <member name="M:System.Windows.Forms.Design.ControlDesigner.OnCreateHandle">
      <summary>Provides an opportunity to perform additional processing immediately after the control handle has been created.</summary>
    </member>
    <member name="M:System.Windows.Forms.Design.ControlDesigner.OnDragComplete(System.Windows.Forms.DragEventArgs)">
      <summary>Receives a call to clean up a drag-and-drop operation.</summary>
      <param name="de">A <see cref="T:System.Windows.Forms.DragEventArgs" /> that provides data for the event.</param>
    </member>
    <member name="M:System.Windows.Forms.Design.ControlDesigner.OnDragDrop(System.Windows.Forms.DragEventArgs)">
      <summary>Receives a call when a drag-and-drop object is dropped onto the control designer view.</summary>
      <param name="de">A <see cref="T:System.Windows.Forms.DragEventArgs" /> that provides data for the event.</param>
    </member>
    <member name="M:System.Windows.Forms.Design.ControlDesigner.OnDragEnter(System.Windows.Forms.DragEventArgs)">
      <summary>Receives a call when a drag-and-drop operation enters the control designer view.</summary>
      <param name="de">A <see cref="T:System.Windows.Forms.DragEventArgs" /> that provides data for the event.</param>
    </member>
    <member name="M:System.Windows.Forms.Design.ControlDesigner.OnDragLeave(System.EventArgs)">
      <summary>Receives a call when a drag-and-drop operation leaves the control designer view.</summary>
      <param name="e">An <see cref="T:System.EventArgs" /> that provides data for the event.</param>
    </member>
    <member name="M:System.Windows.Forms.Design.ControlDesigner.OnDragOver(System.Windows.Forms.DragEventArgs)">
      <summary>Receives a call when a drag-and-drop object is dragged over the control designer view.</summary>
      <param name="de">A <see cref="T:System.Windows.Forms.DragEventArgs" /> that provides data for the event.</param>
    </member>
    <member name="M:System.Windows.Forms.Design.ControlDesigner.OnGiveFeedback(System.Windows.Forms.GiveFeedbackEventArgs)">
      <summary>Receives a call when a drag-and-drop operation is in progress to provide visual cues based on the location of the mouse while a drag operation is in progress.</summary>
      <param name="e">A <see cref="T:System.Windows.Forms.GiveFeedbackEventArgs" /> that provides data for the event.</param>
    </member>
    <member name="M:System.Windows.Forms.Design.ControlDesigner.OnMouseDragBegin(System.Int32,System.Int32)">
      <summary>Receives a call in response to the left mouse button being pressed and held while over the component.</summary>
      <param name="x">The x position of the mouse in screen coordinates.</param>
      <param name="y">The y position of the mouse in screen coordinates.</param>
    </member>
    <member name="M:System.Windows.Forms.Design.ControlDesigner.OnMouseDragEnd(System.Boolean)">
      <summary>Receives a call at the end of a drag-and-drop operation to complete or cancel the operation.</summary>
      <param name="cancel">
        <see langword="true" /> to cancel the drag; <see langword="false" /> to commit it.</param>
    </member>
    <member name="M:System.Windows.Forms.Design.ControlDesigner.OnMouseDragMove(System.Int32,System.Int32)">
      <summary>Receives a call for each movement of the mouse during a drag-and-drop operation.</summary>
      <param name="x">The x position of the mouse in screen coordinates.</param>
      <param name="y">The y position of the mouse in screen coordinates.</param>
    </member>
    <member name="M:System.Windows.Forms.Design.ControlDesigner.OnMouseEnter">
      <summary>Receives a call when the mouse first enters the control.</summary>
    </member>
    <member name="M:System.Windows.Forms.Design.ControlDesigner.OnMouseHover">
      <summary>Receives a call after the mouse hovers over the control.</summary>
    </member>
    <member name="M:System.Windows.Forms.Design.ControlDesigner.OnMouseLeave">
      <summary>Receives a call when the mouse first enters the control.</summary>
    </member>
    <member name="M:System.Windows.Forms.Design.ControlDesigner.OnPaintAdornments(System.Windows.Forms.PaintEventArgs)">
      <summary>Receives a call when the control that the designer is managing has painted its surface so the designer can paint any additional adornments on top of the control.</summary>
      <param name="pe">A <see cref="T:System.Windows.Forms.PaintEventArgs" /> the designer can use to draw on the control.</param>
    </member>
    <member name="M:System.Windows.Forms.Design.ControlDesigner.OnSetComponentDefaults">
      <summary>Called when the designer is initialized.</summary>
    </member>
    <member name="M:System.Windows.Forms.Design.ControlDesigner.OnSetCursor">
      <summary>Receives a call each time the cursor needs to be set.</summary>
    </member>
    <member name="P:System.Windows.Forms.Design.ControlDesigner.ParentComponent">
      <summary>Gets the parent component for the <see cref="T:System.Windows.Forms.Design.ControlDesigner" />.</summary>
      <returns>The parent component for the <see cref="T:System.Windows.Forms.Design.ControlDesigner" />; otherwise, <see langword="null" /> if there is no parent component.</returns>
    </member>
    <member name="P:System.Windows.Forms.Design.ControlDesigner.ParticipatesWithSnapLines">
      <summary>Gets a value indicating whether the <see cref="T:System.Windows.Forms.Design.ControlDesigner" /> will allow snapline alignment during a drag operation.</summary>
      <returns>
        <see langword="true" /> if the <see cref="T:System.Windows.Forms.Design.ControlDesigner" /> will allow snapline alignment during a drag operation when the primary drag control is over this designer; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Windows.Forms.Design.ControlDesigner.PreFilterProperties(System.Collections.IDictionary)">
      <summary>Adjusts the set of properties the component exposes through a <see cref="T:System.ComponentModel.TypeDescriptor" />.</summary>
      <param name="properties">An <see cref="T:System.Collections.IDictionary" /> containing the properties for the class of the component.</param>
    </member>
    <member name="P:System.Windows.Forms.Design.ControlDesigner.SelectionRules">
      <summary>Gets the selection rules that indicate the movement capabilities of a component.</summary>
      <returns>A bitwise combination of <see cref="T:System.Windows.Forms.Design.SelectionRules" /> values.</returns>
    </member>
    <member name="P:System.Windows.Forms.Design.ControlDesigner.SnapLines">
      <summary>Gets a list of <see cref="T:System.Windows.Forms.Design.Behavior.SnapLine" /> objects representing significant alignment points for this control.</summary>
      <returns>A list of <see cref="T:System.Windows.Forms.Design.Behavior.SnapLine" /> objects representing significant alignment points for this control.</returns>
    </member>
    <member name="M:System.Windows.Forms.Design.ControlDesigner.UnhookChildControls(System.Windows.Forms.Control)">
      <summary>Routes messages for the children of the specified control to each control rather than to a parent designer.</summary>
      <param name="firstChild">The first child <see cref="T:System.Windows.Forms.Control" /> to process. This method may recursively call itself for children of the control.</param>
    </member>
    <member name="M:System.Windows.Forms.Design.ControlDesigner.WndProc(System.Windows.Forms.Message@)">
      <summary>Processes Windows messages and optionally routes them to the control.</summary>
      <param name="m">The <see cref="T:System.Windows.Forms.Message" /> to process.</param>
    </member>
    <member name="T:System.Windows.Forms.Design.DesignerActionListsChangedEventArgs" />
    <member name="M:System.Windows.Forms.Design.DesignerActionListsChangedEventArgs.#ctor(System.Object,System.Windows.Forms.Design.DesignerActionListsChangedType,System.ComponentModel.Design.DesignerActionListCollection)">
      <param name="relatedObject" />
      <param name="changeType" />
      <param name="actionLists" />
    </member>
    <member name="P:System.Windows.Forms.Design.DesignerActionListsChangedEventArgs.ActionLists" />
    <member name="P:System.Windows.Forms.Design.DesignerActionListsChangedEventArgs.ChangeType" />
    <member name="P:System.Windows.Forms.Design.DesignerActionListsChangedEventArgs.RelatedObject" />
    <member name="T:System.Windows.Forms.Design.DesignerActionListsChangedEventHandler">
      <param name="sender" />
      <param name="e" />
    </member>
    <member name="T:System.Windows.Forms.Design.DesignerActionListsChangedType" />
    <member name="F:System.Windows.Forms.Design.DesignerActionListsChangedType.ActionListsAdded" />
    <member name="F:System.Windows.Forms.Design.DesignerActionListsChangedType.ActionListsRemoved" />
    <member name="T:System.Windows.Forms.Design.DesignerActionUIService" />
    <member name="E:System.Windows.Forms.Design.DesignerActionUIService.DesignerActionUIStateChange" />
    <member name="M:System.Windows.Forms.Design.DesignerActionUIService.Dispose" />
    <member name="M:System.Windows.Forms.Design.DesignerActionUIService.HideUI(System.ComponentModel.IComponent)">
      <param name="component" />
    </member>
    <member name="M:System.Windows.Forms.Design.DesignerActionUIService.Refresh(System.ComponentModel.IComponent)">
      <param name="component" />
    </member>
    <member name="M:System.Windows.Forms.Design.DesignerActionUIService.ShouldAutoShow(System.ComponentModel.IComponent)">
      <param name="component" />
    </member>
    <member name="M:System.Windows.Forms.Design.DesignerActionUIService.ShowUI(System.ComponentModel.IComponent)">
      <param name="component" />
    </member>
    <member name="T:System.Windows.Forms.Design.DesignerCommandSet" />
    <member name="M:System.Windows.Forms.Design.DesignerCommandSet.#ctor" />
    <member name="P:System.Windows.Forms.Design.DesignerCommandSet.ActionLists" />
    <member name="M:System.Windows.Forms.Design.DesignerCommandSet.GetCommands(System.String)">
      <param name="name" />
    </member>
    <member name="P:System.Windows.Forms.Design.DesignerCommandSet.Verbs" />
    <member name="T:System.Windows.Forms.Design.DesignerOptions">
      <summary>Provides access to get and set option values for a designer.</summary>
    </member>
    <member name="M:System.Windows.Forms.Design.DesignerOptions.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Windows.Forms.Design.DesignerOptions" /> class.</summary>
    </member>
    <member name="P:System.Windows.Forms.Design.DesignerOptions.EnableInSituEditing">
      <summary>Gets or sets a value that enables or disables in-place editing for <see cref="T:System.Windows.Forms.ToolStrip" /> controls.</summary>
      <returns>
        <see langword="true" /> if in-place editing for <see cref="T:System.Windows.Forms.ToolStrip" /> controls is enabled; otherwise, <see langword="false" />. The default is <see langword="true" />.</returns>
    </member>
    <member name="P:System.Windows.Forms.Design.DesignerOptions.GridSize">
      <summary>Gets or sets a <see cref="T:System.Drawing.Size" /> representing the dimensions of a grid unit.</summary>
      <returns>A <see cref="T:System.Drawing.Size" /> representing the dimensions of a grid unit.</returns>
    </member>
    <member name="P:System.Windows.Forms.Design.DesignerOptions.ObjectBoundSmartTagAutoShow">
      <summary>Gets or sets a value that specifies whether a designer shows a component's smart tag panel automatically on creation.</summary>
      <returns>
        <see langword="true" /> to allow a component's smart tag panel to open automatically upon creation; otherwise, <see langword="false" />. The default is <see langword="true" />.</returns>
    </member>
    <member name="P:System.Windows.Forms.Design.DesignerOptions.ShowGrid">
      <summary>Gets or sets a value that enables or disables the grid in the designer.</summary>
      <returns>
        <see langword="true" /> if the grid is enabled; otherwise, <see langword="false" />. The default is <see langword="true" />.</returns>
    </member>
    <member name="P:System.Windows.Forms.Design.DesignerOptions.SnapToGrid">
      <summary>Gets or sets a value that enables or disables whether controls are automatically placed at grid coordinates.</summary>
      <returns>
        <see langword="true" /> if snapping is enabled; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Windows.Forms.Design.DesignerOptions.UseOptimizedCodeGeneration">
      <summary>Gets or sets a value that enables or disables the component cache.</summary>
      <returns>
        <see langword="true" /> if the component cache is enabled; otherwise, <see langword="false" />. The default is <see langword="true" />.</returns>
    </member>
    <member name="P:System.Windows.Forms.Design.DesignerOptions.UseSmartTags">
      <summary>Gets or sets a value that enables or disables smart tags in the designer.</summary>
      <returns>
        <see langword="true" /> if smart tags in the designer are enabled; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Windows.Forms.Design.DesignerOptions.UseSnapLines">
      <summary>Gets or sets a value that enables or disables snaplines in the designer.</summary>
      <returns>
        <see langword="true" /> if snaplines in the designer are enabled; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="T:System.Windows.Forms.Design.DocumentDesigner">
      <summary>Base designer class for extending the design mode behavior of, and providing a root-level design mode view for, a <see cref="T:System.Windows.Forms.Control" /> that supports nested controls and should receive scroll messages.</summary>
    </member>
    <member name="M:System.Windows.Forms.Design.DocumentDesigner.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Windows.Forms.Design.DocumentDesigner" /> class.</summary>
    </member>
    <member name="M:System.Windows.Forms.Design.DocumentDesigner.Dispose(System.Boolean)">
      <summary>Releases the unmanaged resources used by the <see cref="T:System.Windows.Forms.Design.DocumentDesigner" /> and optionally releases the managed resources.</summary>
      <param name="disposing">
        <see langword="true" /> to release both managed and unmanaged resources; <see langword="false" /> to release only unmanaged resources.</param>
    </member>
    <member name="M:System.Windows.Forms.Design.DocumentDesigner.EnsureMenuEditorService(System.ComponentModel.IComponent)">
      <summary>Checks for the existence of a menu editor service and creates one if one does not already exist.</summary>
      <param name="c">The <see cref="T:System.ComponentModel.IComponent" /> to ensure has a context menu service.</param>
    </member>
    <member name="M:System.Windows.Forms.Design.DocumentDesigner.GetGlyphs(System.Windows.Forms.Design.Behavior.GlyphSelectionType)">
      <summary>Gets a <see cref="T:System.Windows.Forms.Design.Behavior.GlyphCollection" /> representing the <see cref="T:System.Windows.Forms.Design.Behavior.Glyph" /> objects.</summary>
      <param name="selectionType">A <see cref="T:System.Windows.Forms.Design.Behavior.GlyphSelectionType" /> value that specifies the selection state.</param>
      <returns>A collection of <see cref="T:System.Windows.Forms.Design.Behavior.Glyph" /> objects.</returns>
    </member>
    <member name="M:System.Windows.Forms.Design.DocumentDesigner.GetToolSupported(System.Drawing.Design.ToolboxItem)">
      <summary>Indicates whether the specified tool is supported by the designer.</summary>
      <param name="tool">The <see cref="T:System.Drawing.Design.ToolboxItem" /> to test for toolbox support.</param>
      <returns>
        <see langword="true" /> if the tool should be enabled on the toolbox; <see langword="false" /> if the document designer doesn't know how to use the tool.</returns>
    </member>
    <member name="M:System.Windows.Forms.Design.DocumentDesigner.Initialize(System.ComponentModel.IComponent)">
      <summary>Initializes the designer with the specified component.</summary>
      <param name="component">The <see cref="T:System.ComponentModel.IComponent" /> to associate with the designer.</param>
    </member>
    <member name="F:System.Windows.Forms.Design.DocumentDesigner.menuEditorService">
      <summary>Initializes the menuEditorService variable to <see langword="null" />.</summary>
    </member>
    <member name="M:System.Windows.Forms.Design.DocumentDesigner.OnContextMenu(System.Int32,System.Int32)">
      <summary>Called when the context menu should be displayed.</summary>
      <param name="x">The horizontal screen coordinate to display the context menu at.</param>
      <param name="y">The vertical screen coordinate to display the context menu at.</param>
    </member>
    <member name="M:System.Windows.Forms.Design.DocumentDesigner.OnCreateHandle">
      <summary>Called immediately after the handle for the designer has been created.</summary>
    </member>
    <member name="M:System.Windows.Forms.Design.DocumentDesigner.PreFilterProperties(System.Collections.IDictionary)">
      <summary>Adjusts the set of properties the component exposes through a <see cref="T:System.ComponentModel.TypeDescriptor" />.</summary>
      <param name="properties">An <see cref="T:System.Collections.IDictionary" /> that contains the properties for the class of the component.</param>
    </member>
    <member name="P:System.Windows.Forms.Design.DocumentDesigner.SelectionRules">
      <summary>Gets the <see cref="T:System.Windows.Forms.Design.SelectionRules" /> for the designer.</summary>
      <returns>A bitwise combination of <see cref="T:System.Windows.Forms.Design.SelectionRules" /> values.</returns>
    </member>
    <member name="M:System.Windows.Forms.Design.DocumentDesigner.System#ComponentModel#Design#IRootDesigner#GetView(System.ComponentModel.Design.ViewTechnology)">
      <summary>For a description of this member, see <see cref="T:System.ComponentModel.Design.ViewTechnology" />.</summary>
      <param name="technology">A <see cref="T:System.ComponentModel.Design.ViewTechnology" /> that indicates a particular view technology.</param>
      <returns>An object that represents the view for this designer.</returns>
    </member>
    <member name="P:System.Windows.Forms.Design.DocumentDesigner.System#ComponentModel#Design#IRootDesigner#SupportedTechnologies">
      <summary>For a description of this member, see <see cref="P:System.ComponentModel.Design.IRootDesigner.SupportedTechnologies" />.</summary>
      <returns>An array of supported <see cref="T:System.ComponentModel.Design.ViewTechnology" /> values.</returns>
    </member>
    <member name="M:System.Windows.Forms.Design.DocumentDesigner.System#Drawing#Design#IToolboxUser#GetToolSupported(System.Drawing.Design.ToolboxItem)">
      <summary>For a description of this member, see <see cref="M:System.Drawing.Design.IToolboxUser.GetToolSupported(System.Drawing.Design.ToolboxItem)" />.</summary>
      <param name="tool">The <see cref="T:System.Drawing.Design.ToolboxItem" /> to be tested for toolbox support.</param>
      <returns>
        <see langword="true" /> if the tool is supported by the toolbox and can be enabled; <see langword="false" /> if the document designer does not know how to use the tool.</returns>
    </member>
    <member name="M:System.Windows.Forms.Design.DocumentDesigner.System#Drawing#Design#IToolboxUser#ToolPicked(System.Drawing.Design.ToolboxItem)">
      <summary>For a description of this member, see <see cref="M:System.Drawing.Design.IToolboxUser.ToolPicked(System.Drawing.Design.ToolboxItem)" />.</summary>
      <param name="tool">The <see cref="T:System.Drawing.Design.ToolboxItem" /> to select.</param>
    </member>
    <member name="M:System.Windows.Forms.Design.DocumentDesigner.ToolPicked(System.Drawing.Design.ToolboxItem)">
      <summary>Selects the specified tool.</summary>
      <param name="tool">The <see cref="T:System.Drawing.Design.ToolboxItem" /> to create a component for.</param>
    </member>
    <member name="M:System.Windows.Forms.Design.DocumentDesigner.WndProc(System.Windows.Forms.Message@)">
      <summary>Processes Windows messages.</summary>
      <param name="m">The <see cref="T:System.Windows.Forms.Message" /> to process.</param>
    </member>
    <member name="T:System.Windows.Forms.Design.EventHandlerService">
      <summary>Provides a systematic way to manage event handlers for the current document.</summary>
    </member>
    <member name="M:System.Windows.Forms.Design.EventHandlerService.#ctor(System.Windows.Forms.Control)">
      <summary>Initializes a new instance of the <see cref="T:System.Windows.Forms.Design.EventHandlerService" /> class.</summary>
      <param name="focusWnd">The <see cref="T:System.Windows.Forms.Control" /> which is being designed.</param>
    </member>
    <member name="E:System.Windows.Forms.Design.EventHandlerService.EventHandlerChanged">
      <summary>Fires an OnEventHandlerChanged event.</summary>
    </member>
    <member name="P:System.Windows.Forms.Design.EventHandlerService.FocusWindow">
      <summary>Gets the control to which event handlers are attached.</summary>
      <returns>The <see cref="T:System.Windows.Forms.Control" /> which was attached through the constructor.</returns>
    </member>
    <member name="M:System.Windows.Forms.Design.EventHandlerService.GetHandler(System.Type)">
      <summary>Gets the currently active event handler of the specified type.</summary>
      <param name="handlerType">The type of the handler to get.</param>
      <returns>An instance of the handler, or <see langword="null" /> if there is no handler of the requested type.</returns>
    </member>
    <member name="M:System.Windows.Forms.Design.EventHandlerService.PopHandler(System.Object)">
      <summary>Pops the given handler off of the stack.</summary>
      <param name="handler">The handler to remove from the stack.</param>
    </member>
    <member name="M:System.Windows.Forms.Design.EventHandlerService.PushHandler(System.Object)">
      <summary>Pushes a new event handler on the stack.</summary>
      <param name="handler">The handler to add to the stack.</param>
    </member>
    <member name="T:System.Windows.Forms.Design.IContainsThemedScrollbarWindows">
      <summary>Defines a method for getting information about how the scrollbars of windows need to be themed when displayed in the Visual Studio designer.</summary>
    </member>
    <member name="M:System.Windows.Forms.Design.IContainsThemedScrollbarWindows.ThemedScrollbarWindows">
      <summary>Gets an enumeration of <see cref="T:System.Windows.Forms.Design.ThemedScrollbarWindow" /> objects that represent windows and how their scrollbars need to be themed when displayed in the Visual Studio designer.</summary>
      <returns>An enumeration of objects that represent windows and how their scrollbars need to be themed when displayed in the Visual Studio designer.</returns>
    </member>
    <member name="T:System.Windows.Forms.Design.ImageListCodeDomSerializer">
      <summary>Serializes string dictionaries.</summary>
    </member>
    <member name="M:System.Windows.Forms.Design.ImageListCodeDomSerializer.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Windows.Forms.Design.ImageListCodeDomSerializer" /> class.</summary>
    </member>
    <member name="M:System.Windows.Forms.Design.ImageListCodeDomSerializer.Deserialize(System.ComponentModel.Design.Serialization.IDesignerSerializationManager,System.Object)">
      <summary>Deserializes the specified serialized Code Document Object Model (CodeDOM) object into an object.</summary>
      <param name="manager">A serialization manager interface that is used during the deserialization process.</param>
      <param name="codeObject">A serialized CodeDOM object to deserialize.</param>
      <returns>The deserialized CodeDOM object.</returns>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="manager" /> or <paramref name="codeObject" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Windows.Forms.Design.ImageListCodeDomSerializer.Serialize(System.ComponentModel.Design.Serialization.IDesignerSerializationManager,System.Object)">
      <summary>Serializes the specified object into a Code Document Object Model (CodeDOM) object.</summary>
      <param name="manager">The serialization manager to use during serialization.</param>
      <param name="value">The object to serialize.</param>
      <returns>A CodeDOM object representing the object that has been serialized.</returns>
    </member>
    <member name="T:System.Windows.Forms.Design.IMenuEditorService">
      <summary>Provides access to the menu editing service.</summary>
    </member>
    <member name="M:System.Windows.Forms.Design.IMenuEditorService.GetMenu">
      <summary>Gets the current menu.</summary>
      <returns>The current <see cref="T:System.Windows.Forms.Menu" />.</returns>
    </member>
    <member name="M:System.Windows.Forms.Design.IMenuEditorService.IsActive">
      <summary>Indicates whether the current menu is active.</summary>
      <returns>
        <see langword="true" /> if the current menu is currently active; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Windows.Forms.Design.IMenuEditorService.MessageFilter(System.Windows.Forms.Message@)">
      <summary>Allows the editor service to intercept Win32 messages.</summary>
      <param name="m">The <see cref="T:System.Windows.Forms.Message" /> to process.</param>
      <returns>
        <see langword="true" /> if the message is for the control; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Windows.Forms.Design.IMenuEditorService.SetMenu(System.Windows.Forms.Menu)">
      <summary>Sets the specified menu visible on the form.</summary>
      <param name="menu">The <see cref="T:System.Windows.Forms.Menu" /> to render.</param>
    </member>
    <member name="M:System.Windows.Forms.Design.IMenuEditorService.SetSelection(System.Windows.Forms.MenuItem)">
      <summary>Sets the selected menu item of the current menu.</summary>
      <param name="item">A <see cref="T:System.Windows.Forms.MenuItem" /> to set as the currently selected menu item.</param>
    </member>
    <member name="T:System.Windows.Forms.Design.MenuCommands">
      <summary>Defines a set of <see cref="T:System.ComponentModel.Design.CommandID" /> fields that each correspond to a command function provided by the host environment.</summary>
    </member>
    <member name="M:System.Windows.Forms.Design.MenuCommands.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Windows.Forms.Design.MenuCommands" /> class.</summary>
    </member>
    <member name="F:System.Windows.Forms.Design.MenuCommands.ComponentTrayMenu">
      <summary>A <see cref="T:System.ComponentModel.Design.CommandID" /> that can be used to access the component tray menu.</summary>
    </member>
    <member name="F:System.Windows.Forms.Design.MenuCommands.ContainerMenu">
      <summary>A <see cref="T:System.ComponentModel.Design.CommandID" /> that can be used to access the container menu.</summary>
    </member>
    <member name="F:System.Windows.Forms.Design.MenuCommands.DesignerProperties">
      <summary>A <see cref="T:System.ComponentModel.Design.CommandID" /> that can be used to access the properties page for the designer.</summary>
    </member>
    <member name="F:System.Windows.Forms.Design.MenuCommands.EditLabel">
      <summary>A <see cref="T:System.ComponentModel.Design.CommandID" /> that can be used to access the edit label handler.</summary>
    </member>
    <member name="F:System.Windows.Forms.Design.MenuCommands.KeyCancel">
      <summary>A <see cref="T:System.ComponentModel.Design.CommandID" /> that can be used to access the cancel key handler.</summary>
    </member>
    <member name="F:System.Windows.Forms.Design.MenuCommands.KeyDefaultAction">
      <summary>A <see cref="T:System.ComponentModel.Design.CommandID" /> that can be used to access the default key handler.</summary>
    </member>
    <member name="F:System.Windows.Forms.Design.MenuCommands.KeyEnd">
      <summary>A <see cref="T:System.ComponentModel.Design.CommandID" /> that can be used to access the end key handler.</summary>
    </member>
    <member name="F:System.Windows.Forms.Design.MenuCommands.KeyHome">
      <summary>A <see cref="T:System.ComponentModel.Design.CommandID" /> that can be used to access the home key handler.</summary>
    </member>
    <member name="F:System.Windows.Forms.Design.MenuCommands.KeyInvokeSmartTag">
      <summary>A <see cref="T:System.ComponentModel.Design.CommandID" /> that can be used to access the smart tag invocation handler.</summary>
    </member>
    <member name="F:System.Windows.Forms.Design.MenuCommands.KeyMoveDown">
      <summary>A <see cref="T:System.ComponentModel.Design.CommandID" /> that can be used to access the move down key handler.</summary>
    </member>
    <member name="F:System.Windows.Forms.Design.MenuCommands.KeyMoveLeft">
      <summary>A <see cref="T:System.ComponentModel.Design.CommandID" /> that can be used to access the move left key handler.</summary>
    </member>
    <member name="F:System.Windows.Forms.Design.MenuCommands.KeyMoveRight">
      <summary>A <see cref="T:System.ComponentModel.Design.CommandID" /> that can be used to access the move right key handler.</summary>
    </member>
    <member name="F:System.Windows.Forms.Design.MenuCommands.KeyMoveUp">
      <summary>A <see cref="T:System.ComponentModel.Design.CommandID" /> that can be used to access the move up key handler.</summary>
    </member>
    <member name="F:System.Windows.Forms.Design.MenuCommands.KeyNudgeDown">
      <summary>A <see cref="T:System.ComponentModel.Design.CommandID" /> that can be used to access the nudge down key handler.</summary>
    </member>
    <member name="F:System.Windows.Forms.Design.MenuCommands.KeyNudgeHeightDecrease">
      <summary>A <see cref="T:System.ComponentModel.Design.CommandID" /> that can be used to access the nudge height decrease key handler.</summary>
    </member>
    <member name="F:System.Windows.Forms.Design.MenuCommands.KeyNudgeHeightIncrease">
      <summary>A <see cref="T:System.ComponentModel.Design.CommandID" /> that can be used to access the nudge height increase key handler.</summary>
    </member>
    <member name="F:System.Windows.Forms.Design.MenuCommands.KeyNudgeLeft">
      <summary>A <see cref="T:System.ComponentModel.Design.CommandID" /> that can be used to access the nudge left key handler.</summary>
    </member>
    <member name="F:System.Windows.Forms.Design.MenuCommands.KeyNudgeRight">
      <summary>A <see cref="T:System.ComponentModel.Design.CommandID" /> that can be used to access the nudge right key handler.</summary>
    </member>
    <member name="F:System.Windows.Forms.Design.MenuCommands.KeyNudgeUp">
      <summary>A <see cref="T:System.ComponentModel.Design.CommandID" /> that can be used to access the nudge up key handler.</summary>
    </member>
    <member name="F:System.Windows.Forms.Design.MenuCommands.KeyNudgeWidthDecrease">
      <summary>A <see cref="T:System.ComponentModel.Design.CommandID" /> that can be used to access the nudge width decrease key handler.</summary>
    </member>
    <member name="F:System.Windows.Forms.Design.MenuCommands.KeyNudgeWidthIncrease">
      <summary>A <see cref="T:System.ComponentModel.Design.CommandID" /> that can be used to access the nudge width increase key handler.</summary>
    </member>
    <member name="F:System.Windows.Forms.Design.MenuCommands.KeyReverseCancel">
      <summary>A <see cref="T:System.ComponentModel.Design.CommandID" /> that can be used to access the reverse cancel key handler.</summary>
    </member>
    <member name="F:System.Windows.Forms.Design.MenuCommands.KeySelectNext">
      <summary>A <see cref="T:System.ComponentModel.Design.CommandID" /> that can be used to access the select next key handler.</summary>
    </member>
    <member name="F:System.Windows.Forms.Design.MenuCommands.KeySelectPrevious">
      <summary>A <see cref="T:System.ComponentModel.Design.CommandID" /> that can be used to access the select previous key handler.</summary>
    </member>
    <member name="F:System.Windows.Forms.Design.MenuCommands.KeyShiftEnd">
      <summary>A <see cref="T:System.ComponentModel.Design.CommandID" /> that can be used to access the SHIFT-END key handler.</summary>
    </member>
    <member name="F:System.Windows.Forms.Design.MenuCommands.KeyShiftHome">
      <summary>A <see cref="T:System.ComponentModel.Design.CommandID" /> that can be used to access the SHIFT-HOME key handler.</summary>
    </member>
    <member name="F:System.Windows.Forms.Design.MenuCommands.KeySizeHeightDecrease">
      <summary>A <see cref="T:System.ComponentModel.Design.CommandID" /> that can be used to access the size height decrease key handler.</summary>
    </member>
    <member name="F:System.Windows.Forms.Design.MenuCommands.KeySizeHeightIncrease">
      <summary>A <see cref="T:System.ComponentModel.Design.CommandID" /> that can be used to access the size height increase key handler.</summary>
    </member>
    <member name="F:System.Windows.Forms.Design.MenuCommands.KeySizeWidthDecrease">
      <summary>A <see cref="T:System.ComponentModel.Design.CommandID" /> that can be used to access the size width decrease key handler.</summary>
    </member>
    <member name="F:System.Windows.Forms.Design.MenuCommands.KeySizeWidthIncrease">
      <summary>A <see cref="T:System.ComponentModel.Design.CommandID" /> that can be used to access the size width increase key handler.</summary>
    </member>
    <member name="F:System.Windows.Forms.Design.MenuCommands.KeyTabOrderSelect">
      <summary>A <see cref="T:System.ComponentModel.Design.CommandID" /> that can be used to access the tab order select key handler.</summary>
    </member>
    <member name="F:System.Windows.Forms.Design.MenuCommands.SelectionMenu">
      <summary>A <see cref="T:System.ComponentModel.Design.CommandID" /> that can be used to access the selection menu.</summary>
    </member>
    <member name="F:System.Windows.Forms.Design.MenuCommands.SetStatusRectangle">
      <summary>A <see cref="T:System.ComponentModel.Design.CommandID" /> that can be used to set the status rectangle.</summary>
    </member>
    <member name="F:System.Windows.Forms.Design.MenuCommands.SetStatusText">
      <summary>A <see cref="T:System.ComponentModel.Design.CommandID" /> that can be used to set the status rectangle text.</summary>
    </member>
    <member name="F:System.Windows.Forms.Design.MenuCommands.TraySelectionMenu">
      <summary>A <see cref="T:System.ComponentModel.Design.CommandID" /> that can be used to access the tray selection menu.</summary>
    </member>
    <member name="T:System.Windows.Forms.Design.ParentControlDesigner">
      <summary>Extends the design mode behavior of a <see cref="T:System.Windows.Forms.Control" /> that supports nested controls.</summary>
    </member>
    <member name="M:System.Windows.Forms.Design.ParentControlDesigner.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Windows.Forms.Design.ParentControlDesigner" /> class.</summary>
    </member>
    <member name="M:System.Windows.Forms.Design.ParentControlDesigner.AddPaddingSnapLines(System.Collections.ArrayList@)">
      <summary>Adds padding snaplines.</summary>
      <param name="snapLines">An <see cref="T:System.Collections.ArrayList" /> that contains <see cref="T:System.Windows.Forms.Design.Behavior.SnapLine" /> objects.</param>
    </member>
    <member name="P:System.Windows.Forms.Design.ParentControlDesigner.AllowControlLasso">
      <summary>Gets a value indicating whether selected controls will be re-parented.</summary>
      <returns>
        <see langword="true" /> if the controls that were selected by lassoing on the designer's surface will be re-parented to this designer's control.</returns>
    </member>
    <member name="P:System.Windows.Forms.Design.ParentControlDesigner.AllowGenericDragBox">
      <summary>Gets a value indicating whether a generic drag box should be drawn when dragging a toolbox item over the designer's surface.</summary>
      <returns>
        <see langword="true" /> if a generic drag box should be drawn when dragging a toolbox item over the designer's surface; otherwise, <see langword="false" />. The default is <see langword="true" />.</returns>
    </member>
    <member name="P:System.Windows.Forms.Design.ParentControlDesigner.AllowSetChildIndexOnDrop">
      <summary>Gets a value indicating whether the z-order of dragged controls should be maintained when dropped on a <see cref="T:System.Windows.Forms.Design.ParentControlDesigner" />.</summary>
      <returns>
        <see langword="true" /> if the z-order of dragged controls should be maintained when dropped on a <see cref="T:System.Windows.Forms.Design.ParentControlDesigner" />; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Windows.Forms.Design.ParentControlDesigner.CanAddComponent(System.ComponentModel.IComponent)">
      <summary>Called when a component is added to the parent container.</summary>
      <param name="component">The component to test for errors.</param>
      <returns>
        <see langword="true" /> if <paramref name="component" /> can be added; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Windows.Forms.Design.ParentControlDesigner.CanParent(System.Windows.Forms.Control)">
      <summary>Indicates whether the specified control can be a child of the control managed by this designer.</summary>
      <param name="control">The <see cref="T:System.Windows.Forms.Control" /> to test.</param>
      <returns>
        <see langword="true" /> if the specified control can be a child of the control managed by this designer; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Windows.Forms.Design.ParentControlDesigner.CanParent(System.Windows.Forms.Design.ControlDesigner)">
      <summary>Indicates whether the control managed by the specified designer can be a child of the control managed by this designer.</summary>
      <param name="controlDesigner">The designer for the control to test.</param>
      <returns>
        <see langword="true" /> if the control managed by the specified designer can be a child of the control managed by this designer; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Windows.Forms.Design.ParentControlDesigner.CreateTool(System.Drawing.Design.ToolboxItem)">
      <summary>Creates a component or control from the specified tool and adds it to the current design document.</summary>
      <param name="tool">The <see cref="T:System.Drawing.Design.ToolboxItem" /> to create a component from.</param>
    </member>
    <member name="M:System.Windows.Forms.Design.ParentControlDesigner.CreateTool(System.Drawing.Design.ToolboxItem,System.Drawing.Point)">
      <summary>Creates a component or control from the specified tool and adds it to the current design document at the specified location.</summary>
      <param name="tool">The <see cref="T:System.Drawing.Design.ToolboxItem" /> to create a component from.</param>
      <param name="location">The <see cref="T:System.Drawing.Point" />, in design-time view screen coordinates, at which to center the component.</param>
    </member>
    <member name="M:System.Windows.Forms.Design.ParentControlDesigner.CreateTool(System.Drawing.Design.ToolboxItem,System.Drawing.Rectangle)">
      <summary>Creates a component or control from the specified tool and adds it to the current design document within the bounds of the specified rectangle.</summary>
      <param name="tool">The <see cref="T:System.Drawing.Design.ToolboxItem" /> to create a component from.</param>
      <param name="bounds">A <see cref="T:System.Drawing.Rectangle" /> indicating the location and size for the component created from the tool. The <see cref="P:System.Drawing.Rectangle.X" /> and <see cref="P:System.Drawing.Rectangle.Y" /> values of the <see cref="T:System.Drawing.Rectangle" /> indicate the design-time view screen coordinates of the upper-left corner of the component.</param>
    </member>
    <member name="M:System.Windows.Forms.Design.ParentControlDesigner.CreateToolCore(System.Drawing.Design.ToolboxItem,System.Int32,System.Int32,System.Int32,System.Int32,System.Boolean,System.Boolean)">
      <summary>Provides core functionality for all the <see cref="M:System.Windows.Forms.Design.ParentControlDesigner.CreateTool(System.Drawing.Design.ToolboxItem)" /> methods.</summary>
      <param name="tool">The <see cref="T:System.Drawing.Design.ToolboxItem" /> to create a component from.</param>
      <param name="x">The horizontal position, in design-time view coordinates, of the location of the left edge of the tool, if a size is specified; the horizontal position of the center of the tool, if no size is specified.</param>
      <param name="y">The vertical position, in design-time view coordinates, of the location of the top edge of the tool, if a size is specified; the vertical position of the center of the tool, if no size is specified.</param>
      <param name="width">The width of the tool. This parameter is ignored if the <paramref name="hasSize" /> parameter is set to <see langword="false" />.</param>
      <param name="height">The height of the tool. This parameter is ignored if the <paramref name="hasSize" /> parameter is set to <see langword="false" />.</param>
      <param name="hasLocation">
        <see langword="true" /> if a location for the component is specified; <see langword="false" /> if the component is to be positioned in the center of the currently selected control.</param>
      <param name="hasSize">
        <see langword="true" /> if a size for the component is specified; <see langword="false" /> if the default height and width values for the component are to be used.</param>
      <returns>An array of components created from the tool.</returns>
    </member>
    <member name="P:System.Windows.Forms.Design.ParentControlDesigner.DefaultControlLocation">
      <summary>Gets the default location for a control added to the designer.</summary>
      <returns>A <see cref="T:System.Drawing.Point" /> that indicates the default location for a control added to the designer.</returns>
    </member>
    <member name="M:System.Windows.Forms.Design.ParentControlDesigner.Dispose(System.Boolean)">
      <summary>Releases the unmanaged resources used by the <see cref="T:System.Windows.Forms.Design.ParentControlDesigner" />, and optionally releases the managed resources.</summary>
      <param name="disposing">
        <see langword="true" /> to release both managed and unmanaged resources; <see langword="false" /> to release only unmanaged resources.</param>
    </member>
    <member name="P:System.Windows.Forms.Design.ParentControlDesigner.DrawGrid">
      <summary>Gets or sets a value indicating whether a grid should be drawn on the control for this designer.</summary>
      <returns>
        <see langword="true" /> if a grid should be drawn on the control in the designer; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Windows.Forms.Design.ParentControlDesigner.EnableDragRect">
      <summary>Gets a value indicating whether drag rectangles are drawn by the designer.</summary>
      <returns>
        <see langword="true" /> if drag rectangles are drawn; otherwise, <see langword="false" />. The default is <see langword="true" />.</returns>
    </member>
    <member name="M:System.Windows.Forms.Design.ParentControlDesigner.GetControl(System.Object)">
      <summary>Gets the control from the designer of the specified component.</summary>
      <param name="component">The component to retrieve the control for.</param>
      <returns>The <see cref="T:System.Windows.Forms.Control" /> that the specified component belongs to.</returns>
    </member>
    <member name="M:System.Windows.Forms.Design.ParentControlDesigner.GetControlGlyph(System.Windows.Forms.Design.Behavior.GlyphSelectionType)">
      <summary>Gets a body glyph that represents the bounds of the control.</summary>
      <param name="selectionType">A <see cref="T:System.Windows.Forms.Design.Behavior.GlyphSelectionType" /> value that specifies the selection state.</param>
      <returns>A body glyph that represents the bounds of the control.</returns>
    </member>
    <member name="M:System.Windows.Forms.Design.ParentControlDesigner.GetGlyphs(System.Windows.Forms.Design.Behavior.GlyphSelectionType)">
      <summary>Gets a collection of <see cref="T:System.Windows.Forms.Design.Behavior.Glyph" /> objects representing the selection borders and grab handles for a standard control.</summary>
      <param name="selectionType">A <see cref="T:System.Windows.Forms.Design.Behavior.GlyphSelectionType" /> value that specifies the selection state.</param>
      <returns>A collection of <see cref="T:System.Windows.Forms.Design.Behavior.Glyph" /> objects.</returns>
    </member>
    <member name="M:System.Windows.Forms.Design.ParentControlDesigner.GetParentForComponent(System.ComponentModel.IComponent)">
      <summary>Used by deriving classes to determine if it returns the control being designed or some other <see cref="T:System.ComponentModel.Container" /> while adding a component to it.</summary>
      <param name="component">The component for which to retrieve the parent <see cref="T:System.Windows.Forms.Control" />.</param>
      <returns>The parent <see cref="T:System.Windows.Forms.Control" /> for the component.</returns>
    </member>
    <member name="M:System.Windows.Forms.Design.ParentControlDesigner.GetUpdatedRect(System.Drawing.Rectangle,System.Drawing.Rectangle,System.Boolean)">
      <summary>Updates the position of the specified rectangle, adjusting it for grid alignment if grid alignment mode is enabled.</summary>
      <param name="originalRect">A <see cref="T:System.Drawing.Rectangle" /> indicating the initial position of the component being updated.</param>
      <param name="dragRect">A <see cref="T:System.Drawing.Rectangle" /> indicating the new position of the component.</param>
      <param name="updateSize">
        <see langword="true" /> to update the size of the rectangle, if there has been any change; otherwise, <see langword="false" />.</param>
      <returns>A rectangle indicating the position of the component in design-time view screen coordinates. If no changes have been made, this method returns the original rectangle.</returns>
    </member>
    <member name="P:System.Windows.Forms.Design.ParentControlDesigner.GridSize">
      <summary>Gets or sets the size of each square of the grid that is drawn when the designer is in grid draw mode.</summary>
      <returns>A <see cref="T:System.Drawing.Size" /> that represents the size of each square of the grid drawn on a form or user control.</returns>
      <exception cref="T:System.ArgumentException">
        <see cref="T:System.Drawing.Size" /> is outside the allowed range for <see cref="P:System.Windows.Forms.Design.ParentControlDesigner.GridSize" />. The default minimum value is 2, and the default maximum value is 200.</exception>
    </member>
    <member name="M:System.Windows.Forms.Design.ParentControlDesigner.Initialize(System.ComponentModel.IComponent)">
      <summary>Initializes the designer with the specified component.</summary>
      <param name="component">The <see cref="T:System.ComponentModel.IComponent" /> to associate with the designer.</param>
    </member>
    <member name="M:System.Windows.Forms.Design.ParentControlDesigner.InitializeNewComponent(System.Collections.IDictionary)">
      <summary>Initializes a newly created component.</summary>
      <param name="defaultValues">A name/value dictionary of default values to apply to properties. May be <see langword="null" /> if no default values are specified.</param>
    </member>
    <member name="M:System.Windows.Forms.Design.ParentControlDesigner.InvokeCreateTool(System.Windows.Forms.Design.ParentControlDesigner,System.Drawing.Design.ToolboxItem)">
      <summary>Creates a tool from the specified <see cref="T:System.Drawing.Design.ToolboxItem" />.</summary>
      <param name="toInvoke">The <see cref="T:System.Windows.Forms.Design.ParentControlDesigner" /> that the tool is to be used with.</param>
      <param name="tool">The <see cref="T:System.Drawing.Design.ToolboxItem" /> to create a tool from.</param>
    </member>
    <member name="P:System.Windows.Forms.Design.ParentControlDesigner.MouseDragTool">
      <summary>Gets a value indicating whether the designer has a valid tool during a drag operation.</summary>
      <returns>The tool being dragged, if creating a component, or <see langword="null" /> if there is no tool.</returns>
    </member>
    <member name="M:System.Windows.Forms.Design.ParentControlDesigner.OnDragComplete(System.Windows.Forms.DragEventArgs)">
      <summary>Called in order to clean up a drag-and-drop operation.</summary>
      <param name="de">A <see cref="T:System.Windows.Forms.DragEventArgs" /> that provides data for the event.</param>
    </member>
    <member name="M:System.Windows.Forms.Design.ParentControlDesigner.OnDragDrop(System.Windows.Forms.DragEventArgs)">
      <summary>Called when a drag-and-drop object is dropped onto the control designer view.</summary>
      <param name="de">A <see cref="T:System.Windows.Forms.DragEventArgs" /> that provides data for the event.</param>
    </member>
    <member name="M:System.Windows.Forms.Design.ParentControlDesigner.OnDragEnter(System.Windows.Forms.DragEventArgs)">
      <summary>Called when a drag-and-drop operation enters the control designer view.</summary>
      <param name="de">A <see cref="T:System.Windows.Forms.DragEventArgs" /> that provides data for the event.</param>
    </member>
    <member name="M:System.Windows.Forms.Design.ParentControlDesigner.OnDragLeave(System.EventArgs)">
      <summary>Called when a drag-and-drop operation leaves the control designer view.</summary>
      <param name="e">An <see cref="T:System.EventArgs" /> that provides data for the event.</param>
    </member>
    <member name="M:System.Windows.Forms.Design.ParentControlDesigner.OnDragOver(System.Windows.Forms.DragEventArgs)">
      <summary>Called when a drag-and-drop object is dragged over the control designer view.</summary>
      <param name="de">A <see cref="T:System.Windows.Forms.DragEventArgs" /> that provides data for the event.</param>
    </member>
    <member name="M:System.Windows.Forms.Design.ParentControlDesigner.OnMouseDragBegin(System.Int32,System.Int32)">
      <summary>Called in response to the left mouse button being pressed and held while over the component.</summary>
      <param name="x">The x-coordinate of the mouse in screen coordinates.</param>
      <param name="y">The y-coordinate of the mouse in screen coordinates.</param>
    </member>
    <member name="M:System.Windows.Forms.Design.ParentControlDesigner.OnMouseDragEnd(System.Boolean)">
      <summary>Called at the end of a drag-and-drop operation to complete or cancel the operation.</summary>
      <param name="cancel">
        <see langword="true" /> to cancel the drag operation; <see langword="false" /> to commit it.</param>
    </member>
    <member name="M:System.Windows.Forms.Design.ParentControlDesigner.OnMouseDragMove(System.Int32,System.Int32)">
      <summary>Called for each movement of the mouse during a drag-and-drop operation.</summary>
      <param name="x">The x-coordinate of the mouse in screen coordinates.</param>
      <param name="y">The y-coordinate of the mouse in screen coordinates.</param>
    </member>
    <member name="M:System.Windows.Forms.Design.ParentControlDesigner.OnPaintAdornments(System.Windows.Forms.PaintEventArgs)">
      <summary>Called when the control that the designer is managing has painted its surface so the designer can paint any additional adornments on top of the control.</summary>
      <param name="pe">A <see cref="T:System.Windows.Forms.PaintEventArgs" /> that provides data for the event.</param>
    </member>
    <member name="M:System.Windows.Forms.Design.ParentControlDesigner.OnSetCursor">
      <summary>Provides an opportunity to change the current mouse cursor.</summary>
    </member>
    <member name="M:System.Windows.Forms.Design.ParentControlDesigner.PreFilterProperties(System.Collections.IDictionary)">
      <summary>Adjusts the set of properties the component will expose through a <see cref="T:System.ComponentModel.TypeDescriptor" />.</summary>
      <param name="properties">An <see cref="T:System.Collections.IDictionary" /> that contains the properties for the class of the component.</param>
    </member>
    <member name="P:System.Windows.Forms.Design.ParentControlDesigner.SnapLines">
      <summary>Gets a list of <see cref="T:System.Windows.Forms.Design.Behavior.SnapLine" /> objects representing significant alignment points for this control.</summary>
      <returns>A list of <see cref="T:System.Windows.Forms.Design.Behavior.SnapLine" /> objects representing significant alignment points for this control.</returns>
    </member>
    <member name="T:System.Windows.Forms.Design.Resources.SR" />
    <member name="P:System.Windows.Forms.Design.Resources.SR.BehaviorServiceCopyControl" />
    <member name="P:System.Windows.Forms.Design.Resources.SR.BehaviorServiceCopyControls" />
    <member name="P:System.Windows.Forms.Design.Resources.SR.BehaviorServiceMoveControl" />
    <member name="P:System.Windows.Forms.Design.Resources.SR.BehaviorServiceMoveControls" />
    <member name="P:System.Windows.Forms.Design.Resources.SR.BehaviorServiceResizeControl" />
    <member name="P:System.Windows.Forms.Design.Resources.SR.BehaviorServiceResizeControls" />
    <member name="P:System.Windows.Forms.Design.Resources.SR.CannotConvertDoubleToDate" />
    <member name="P:System.Windows.Forms.Design.Resources.SR.CannotConvertIntToFloat" />
    <member name="P:System.Windows.Forms.Design.Resources.SR.CodeDomComponentSerializationServiceClosedStore" />
    <member name="P:System.Windows.Forms.Design.Resources.SR.CodeDomComponentSerializationServiceDeserializationError" />
    <member name="P:System.Windows.Forms.Design.Resources.SR.CodeDomComponentSerializationServiceUnknownStore" />
    <member name="P:System.Windows.Forms.Design.Resources.SR.COM2UnhandledVT" />
    <member name="P:System.Windows.Forms.Design.Resources.SR.CommandSetAlignByPrimary" />
    <member name="P:System.Windows.Forms.Design.Resources.SR.CommandSetAlignToGrid" />
    <member name="P:System.Windows.Forms.Design.Resources.SR.CommandSetCutMultiple" />
    <member name="P:System.Windows.Forms.Design.Resources.SR.CommandSetDelete" />
    <member name="P:System.Windows.Forms.Design.Resources.SR.CommandSetError" />
    <member name="P:System.Windows.Forms.Design.Resources.SR.CommandSetFormatSpacing" />
    <member name="P:System.Windows.Forms.Design.Resources.SR.CommandSetPaste" />
    <member name="P:System.Windows.Forms.Design.Resources.SR.CommandSetSize" />
    <member name="P:System.Windows.Forms.Design.Resources.SR.CommandSetSizeToGrid" />
    <member name="P:System.Windows.Forms.Design.Resources.SR.CommandSetUnknownSpacingCommand" />
    <member name="P:System.Windows.Forms.Design.Resources.SR.ComponentDesignerAddEvent" />
    <member name="P:System.Windows.Forms.Design.Resources.SR.ContextMenuAlignToGrid" />
    <member name="P:System.Windows.Forms.Design.Resources.SR.ContextMenuBringToFront" />
    <member name="P:System.Windows.Forms.Design.Resources.SR.ContextMenuCopy" />
    <member name="P:System.Windows.Forms.Design.Resources.SR.ContextMenuCut" />
    <member name="P:System.Windows.Forms.Design.Resources.SR.ContextMenuDelete" />
    <member name="P:System.Windows.Forms.Design.Resources.SR.ContextMenuDocumentOutline" />
    <member name="P:System.Windows.Forms.Design.Resources.SR.ContextMenuLockControls" />
    <member name="P:System.Windows.Forms.Design.Resources.SR.ContextMenuPaste" />
    <member name="P:System.Windows.Forms.Design.Resources.SR.ContextMenuProperties" />
    <member name="P:System.Windows.Forms.Design.Resources.SR.ContextMenuSelect" />
    <member name="P:System.Windows.Forms.Design.Resources.SR.ContextMenuSendToBack" />
    <member name="P:System.Windows.Forms.Design.Resources.SR.ContextMenuStripActionList_ShowCheckMargin" />
    <member name="P:System.Windows.Forms.Design.Resources.SR.ContextMenuStripActionList_ShowCheckMarginDesc" />
    <member name="P:System.Windows.Forms.Design.Resources.SR.ContextMenuStripActionList_ShowImageMargin" />
    <member name="P:System.Windows.Forms.Design.Resources.SR.ContextMenuStripActionList_ShowImageMarginDesc" />
    <member name="P:System.Windows.Forms.Design.Resources.SR.ContextMenuViewCode" />
    <member name="P:System.Windows.Forms.Design.Resources.SR.ControlDesigner_WndProcException" />
    <member name="P:System.Windows.Forms.Design.Resources.SR.Culture" />
    <member name="P:System.Windows.Forms.Design.Resources.SR.DesignerActionPanel_CouldNotConvertValue" />
    <member name="P:System.Windows.Forms.Design.Resources.SR.DesignerActionPanel_CouldNotFindMethod" />
    <member name="P:System.Windows.Forms.Design.Resources.SR.DesignerActionPanel_CouldNotFindProperty" />
    <member name="P:System.Windows.Forms.Design.Resources.SR.DesignerActionPanel_DefaultPanelTitle" />
    <member name="P:System.Windows.Forms.Design.Resources.SR.DesignerActionPanel_ErrorActivatingDropDown" />
    <member name="P:System.Windows.Forms.Design.Resources.SR.DesignerActionPanel_ErrorInvokingAction" />
    <member name="P:System.Windows.Forms.Design.Resources.SR.DesignerActionPanel_ErrorSettingValue" />
    <member name="P:System.Windows.Forms.Design.Resources.SR.DesignerBeginDragNotCalled" />
    <member name="P:System.Windows.Forms.Design.Resources.SR.DesignerHostCantDestroyInheritedComponent" />
    <member name="P:System.Windows.Forms.Design.Resources.SR.DesignerHostCyclicAdd" />
    <member name="P:System.Windows.Forms.Design.Resources.SR.DesignerHostDesignerNeedsComponent" />
    <member name="P:System.Windows.Forms.Design.Resources.SR.DesignerHostDestroyComponentTransaction" />
    <member name="P:System.Windows.Forms.Design.Resources.SR.DesignerHostDuplicateName" />
    <member name="P:System.Windows.Forms.Design.Resources.SR.DesignerHostFailedComponentCreate" />
    <member name="P:System.Windows.Forms.Design.Resources.SR.DesignerHostGenericTransactionName" />
    <member name="P:System.Windows.Forms.Design.Resources.SR.DesignerHostLoaderSpecified" />
    <member name="P:System.Windows.Forms.Design.Resources.SR.DesignerHostNestedTransaction" />
    <member name="P:System.Windows.Forms.Design.Resources.SR.DesignerHostNoBaseClass" />
    <member name="P:System.Windows.Forms.Design.Resources.SR.DesignerHostNoTopLevelDesigner" />
    <member name="P:System.Windows.Forms.Design.Resources.SR.DesignerHostUnloading" />
    <member name="P:System.Windows.Forms.Design.Resources.SR.DesignerInherited" />
    <member name="P:System.Windows.Forms.Design.Resources.SR.DesignerInheritedReadOnly" />
    <member name="P:System.Windows.Forms.Design.Resources.SR.DesignerOptions_CodeGenDisplay" />
    <member name="P:System.Windows.Forms.Design.Resources.SR.DesignerOptions_CodeGenSettings" />
    <member name="P:System.Windows.Forms.Design.Resources.SR.DesignerOptions_EnableInSituEditingCat" />
    <member name="P:System.Windows.Forms.Design.Resources.SR.DesignerOptions_EnableInSituEditingDesc" />
    <member name="P:System.Windows.Forms.Design.Resources.SR.DesignerOptions_EnableInSituEditingDisplay" />
    <member name="P:System.Windows.Forms.Design.Resources.SR.DesignerOptions_GridSizeDesc" />
    <member name="P:System.Windows.Forms.Design.Resources.SR.DesignerOptions_GridSizeDisplayName" />
    <member name="P:System.Windows.Forms.Design.Resources.SR.DesignerOptions_LayoutSettings" />
    <member name="P:System.Windows.Forms.Design.Resources.SR.DesignerOptions_ObjectBoundSmartTagAutoShow" />
    <member name="P:System.Windows.Forms.Design.Resources.SR.DesignerOptions_ObjectBoundSmartTagAutoShowDisplayName" />
    <member name="P:System.Windows.Forms.Design.Resources.SR.DesignerOptions_ObjectBoundSmartTagSettings" />
    <member name="P:System.Windows.Forms.Design.Resources.SR.DesignerOptions_OptimizedCodeGen" />
    <member name="P:System.Windows.Forms.Design.Resources.SR.DesignerOptions_ShowGridDesc" />
    <member name="P:System.Windows.Forms.Design.Resources.SR.DesignerOptions_ShowGridDisplayName" />
    <member name="P:System.Windows.Forms.Design.Resources.SR.DesignerOptions_SnapToGridDesc" />
    <member name="P:System.Windows.Forms.Design.Resources.SR.DesignerOptions_SnapToGridDisplayName" />
    <member name="P:System.Windows.Forms.Design.Resources.SR.DesignerOptions_UseSmartTags" />
    <member name="P:System.Windows.Forms.Design.Resources.SR.DesignerOptions_UseSnapLines" />
    <member name="P:System.Windows.Forms.Design.Resources.SR.DesignerShortcutDockInParent" />
    <member name="P:System.Windows.Forms.Design.Resources.SR.DesignerShortcutUndockInParent" />
    <member name="P:System.Windows.Forms.Design.Resources.SR.DesignSurfaceContainerDispose" />
    <member name="P:System.Windows.Forms.Design.Resources.SR.DesignSurfaceDesignerNotLoaded" />
    <member name="P:System.Windows.Forms.Design.Resources.SR.DesignSurfaceFatalError" />
    <member name="P:System.Windows.Forms.Design.Resources.SR.DesignSurfaceNoRootComponent" />
    <member name="P:System.Windows.Forms.Design.Resources.SR.DesignSurfaceNoSupportedTechnology" />
    <member name="P:System.Windows.Forms.Design.Resources.SR.DesignSurfaceServiceIsFixed" />
    <member name="P:System.Windows.Forms.Design.Resources.SR.DotNET_ComponentType" />
    <member name="P:System.Windows.Forms.Design.Resources.SR.DragDropDragComponents" />
    <member name="P:System.Windows.Forms.Design.Resources.SR.DragDropMoveComponent" />
    <member name="P:System.Windows.Forms.Design.Resources.SR.DragDropMoveComponents" />
    <member name="P:System.Windows.Forms.Design.Resources.SR.DragDropSetDataError" />
    <member name="P:System.Windows.Forms.Design.Resources.SR.DragDropSizeComponent" />
    <member name="P:System.Windows.Forms.Design.Resources.SR.DragDropSizeComponents" />
    <member name="P:System.Windows.Forms.Design.Resources.SR.ExtenderProviderServiceDuplicateProvider" />
    <member name="P:System.Windows.Forms.Design.Resources.SR.InheritanceServiceReadOnlyCollection" />
    <member name="P:System.Windows.Forms.Design.Resources.SR.InvalidArgument" />
    <member name="P:System.Windows.Forms.Design.Resources.SR.InvalidBoundArgument" />
    <member name="P:System.Windows.Forms.Design.Resources.SR.lockedDescr" />
    <member name="P:System.Windows.Forms.Design.Resources.SR.NotImplementedByDesign" />
    <member name="P:System.Windows.Forms.Design.Resources.SR.PlatformNotSupported_WinformsDesigner" />
    <member name="P:System.Windows.Forms.Design.Resources.SR.ResourceManager" />
    <member name="P:System.Windows.Forms.Design.Resources.SR.RTL" />
    <member name="P:System.Windows.Forms.Design.Resources.SR.SerializationManagerAreadyInSession" />
    <member name="P:System.Windows.Forms.Design.Resources.SR.SerializationManagerDuplicateComponentDecl" />
    <member name="P:System.Windows.Forms.Design.Resources.SR.SerializationManagerNameInUse" />
    <member name="P:System.Windows.Forms.Design.Resources.SR.SerializationManagerNoMatchingCtor" />
    <member name="P:System.Windows.Forms.Design.Resources.SR.SerializationManagerNoSession" />
    <member name="P:System.Windows.Forms.Design.Resources.SR.SerializationManagerObjectHasName" />
    <member name="P:System.Windows.Forms.Design.Resources.SR.SerializationManagerWithinSession" />
    <member name="P:System.Windows.Forms.Design.Resources.SR.SerializerBadElementTypes" />
    <member name="P:System.Windows.Forms.Design.Resources.SR.SerializerFieldTargetEvalFailed" />
    <member name="P:System.Windows.Forms.Design.Resources.SR.SerializerInvalidArrayRank" />
    <member name="P:System.Windows.Forms.Design.Resources.SR.SerializerLostStatements" />
    <member name="P:System.Windows.Forms.Design.Resources.SR.SerializerMemberTypeNotSerializable" />
    <member name="P:System.Windows.Forms.Design.Resources.SR.SerializerNoRootExpression" />
    <member name="P:System.Windows.Forms.Design.Resources.SR.SerializerNoSerializerForComponent" />
    <member name="P:System.Windows.Forms.Design.Resources.SR.SerializerNoSuchEvent" />
    <member name="P:System.Windows.Forms.Design.Resources.SR.SerializerNoSuchField" />
    <member name="P:System.Windows.Forms.Design.Resources.SR.SerializerNoSuchProperty" />
    <member name="P:System.Windows.Forms.Design.Resources.SR.SerializerResourceException" />
    <member name="P:System.Windows.Forms.Design.Resources.SR.SerializerResourceExceptionInvariant" />
    <member name="P:System.Windows.Forms.Design.Resources.SR.SerializerTypeNotFound" />
    <member name="P:System.Windows.Forms.Design.Resources.SR.SerializerUndeclaredName" />
    <member name="P:System.Windows.Forms.Design.Resources.SR.StandardMenuAbout" />
    <member name="P:System.Windows.Forms.Design.Resources.SR.StandardMenuContents" />
    <member name="P:System.Windows.Forms.Design.Resources.SR.StandardMenuCopy" />
    <member name="P:System.Windows.Forms.Design.Resources.SR.StandardMenuCreateDesc" />
    <member name="P:System.Windows.Forms.Design.Resources.SR.StandardMenuCustomize" />
    <member name="P:System.Windows.Forms.Design.Resources.SR.StandardMenuCut" />
    <member name="P:System.Windows.Forms.Design.Resources.SR.StandardMenuEdit" />
    <member name="P:System.Windows.Forms.Design.Resources.SR.StandardMenuExit" />
    <member name="P:System.Windows.Forms.Design.Resources.SR.StandardMenuFile" />
    <member name="P:System.Windows.Forms.Design.Resources.SR.StandardMenuHelp" />
    <member name="P:System.Windows.Forms.Design.Resources.SR.StandardMenuIndex" />
    <member name="P:System.Windows.Forms.Design.Resources.SR.StandardMenuNew" />
    <member name="P:System.Windows.Forms.Design.Resources.SR.StandardMenuOpen" />
    <member name="P:System.Windows.Forms.Design.Resources.SR.StandardMenuOptions" />
    <member name="P:System.Windows.Forms.Design.Resources.SR.StandardMenuPaste" />
    <member name="P:System.Windows.Forms.Design.Resources.SR.StandardMenuPrint" />
    <member name="P:System.Windows.Forms.Design.Resources.SR.StandardMenuPrintPreview" />
    <member name="P:System.Windows.Forms.Design.Resources.SR.StandardMenuRedo" />
    <member name="P:System.Windows.Forms.Design.Resources.SR.StandardMenuSave" />
    <member name="P:System.Windows.Forms.Design.Resources.SR.StandardMenuSaveAs" />
    <member name="P:System.Windows.Forms.Design.Resources.SR.StandardMenuSearch" />
    <member name="P:System.Windows.Forms.Design.Resources.SR.StandardMenuSelectAll" />
    <member name="P:System.Windows.Forms.Design.Resources.SR.StandardMenuTools" />
    <member name="P:System.Windows.Forms.Design.Resources.SR.StandardMenuUndo" />
    <member name="P:System.Windows.Forms.Design.Resources.SR.StandardToolCut" />
    <member name="P:System.Windows.Forms.Design.Resources.SR.StandardToolHelp" />
    <member name="P:System.Windows.Forms.Design.Resources.SR.ToolboxItemInvalidKey" />
    <member name="P:System.Windows.Forms.Design.Resources.SR.ToolboxItemInvalidPropertyType" />
    <member name="P:System.Windows.Forms.Design.Resources.SR.ToolboxItemLocked" />
    <member name="P:System.Windows.Forms.Design.Resources.SR.ToolboxItemValueNotSerializable" />
    <member name="P:System.Windows.Forms.Design.Resources.SR.ToolStripActionList_Dock" />
    <member name="P:System.Windows.Forms.Design.Resources.SR.ToolStripActionList_DockDesc" />
    <member name="P:System.Windows.Forms.Design.Resources.SR.ToolStripActionList_GripStyle" />
    <member name="P:System.Windows.Forms.Design.Resources.SR.ToolStripActionList_GripStyleDesc" />
    <member name="P:System.Windows.Forms.Design.Resources.SR.ToolStripActionList_Layout" />
    <member name="P:System.Windows.Forms.Design.Resources.SR.ToolStripActionList_RenderMode" />
    <member name="P:System.Windows.Forms.Design.Resources.SR.ToolStripActionList_RenderModeDesc" />
    <member name="P:System.Windows.Forms.Design.Resources.SR.ToolStripAddingItem" />
    <member name="P:System.Windows.Forms.Design.Resources.SR.ToolStripAllowItemReorderAndAllowDropCannotBeSetToTrue" />
    <member name="P:System.Windows.Forms.Design.Resources.SR.ToolStripCreatingNewItemTransaction" />
    <member name="P:System.Windows.Forms.Design.Resources.SR.ToolStripDesignerEmbedVerb" />
    <member name="P:System.Windows.Forms.Design.Resources.SR.ToolStripDesignerEmbedVerbDesc" />
    <member name="P:System.Windows.Forms.Design.Resources.SR.ToolStripDesignerStandardItemsVerb" />
    <member name="P:System.Windows.Forms.Design.Resources.SR.ToolStripDesignerStandardItemsVerbDesc" />
    <member name="P:System.Windows.Forms.Design.Resources.SR.ToolStripDesignerTemplateNodeEnterText" />
    <member name="P:System.Windows.Forms.Design.Resources.SR.ToolStripDesignerTemplateNodeLabelToolTip" />
    <member name="P:System.Windows.Forms.Design.Resources.SR.ToolStripDesignerTemplateNodeSplitButtonStatusStripAccessibleName" />
    <member name="P:System.Windows.Forms.Design.Resources.SR.ToolStripDesignerTemplateNodeSplitButtonStatusStripToolTip" />
    <member name="P:System.Windows.Forms.Design.Resources.SR.ToolStripDesignerTemplateNodeSplitButtonToolTip" />
    <member name="P:System.Windows.Forms.Design.Resources.SR.ToolStripDesignerToolStripAccessibleName" />
    <member name="P:System.Windows.Forms.Design.Resources.SR.ToolStripDesignerTransactionAddingItem" />
    <member name="P:System.Windows.Forms.Design.Resources.SR.ToolStripDesignerTransactionRemovingItem" />
    <member name="P:System.Windows.Forms.Design.Resources.SR.ToolStripDropDownItemCollectionEditorVerb" />
    <member name="P:System.Windows.Forms.Design.Resources.SR.ToolStripInsertingIntoDropDownTransaction" />
    <member name="P:System.Windows.Forms.Design.Resources.SR.ToolStripItemCollectionEditorVerb" />
    <member name="P:System.Windows.Forms.Design.Resources.SR.ToolStripItemContextMenuConvertTo" />
    <member name="P:System.Windows.Forms.Design.Resources.SR.ToolStripItemContextMenuInsert" />
    <member name="P:System.Windows.Forms.Design.Resources.SR.ToolStripItemContextMenuSetImage" />
    <member name="P:System.Windows.Forms.Design.Resources.SR.ToolStripItemPropertyChangeTransaction" />
    <member name="P:System.Windows.Forms.Design.Resources.SR.ToolStripMorphingItemTransaction" />
    <member name="P:System.Windows.Forms.Design.Resources.SR.ToolStripSelectMenuItem" />
    <member name="P:System.Windows.Forms.Design.Resources.SR.ToolStripSeparatorError" />
    <member name="P:System.Windows.Forms.Design.Resources.SR.TrayAutoArrange" />
    <member name="P:System.Windows.Forms.Design.Resources.SR.TrayLineUpIcons" />
    <member name="P:System.Windows.Forms.Design.Resources.SR.TrayShowLargeIcons" />
    <member name="P:System.Windows.Forms.Design.Resources.SR.TypeNotFoundInTargetFramework" />
    <member name="P:System.Windows.Forms.Design.Resources.SR.UIServiceHelper_ErrorCaption" />
    <member name="P:System.Windows.Forms.Design.Resources.SR.UndoEngineComponentAdd0" />
    <member name="P:System.Windows.Forms.Design.Resources.SR.UndoEngineComponentAdd1" />
    <member name="P:System.Windows.Forms.Design.Resources.SR.UndoEngineComponentChange0" />
    <member name="P:System.Windows.Forms.Design.Resources.SR.UndoEngineComponentChange1" />
    <member name="P:System.Windows.Forms.Design.Resources.SR.UndoEngineComponentChange2" />
    <member name="P:System.Windows.Forms.Design.Resources.SR.UndoEngineComponentRemove0" />
    <member name="P:System.Windows.Forms.Design.Resources.SR.UndoEngineComponentRemove1" />
    <member name="P:System.Windows.Forms.Design.Resources.SR.UndoEngineComponentRename" />
    <member name="P:System.Windows.Forms.Design.Resources.SR.UndoEngineMissingService" />
    <member name="P:System.Windows.Forms.Design.Resources.SR.UnsafeNativeMethodsNotImplemented" />
    <member name="P:System.Windows.Forms.Design.Resources.SR.WindowsFormsAddEvent" />
    <member name="P:System.Windows.Forms.Design.Resources.SR.WindowsFormsCommandCenterX" />
    <member name="P:System.Windows.Forms.Design.Resources.SR.WindowsFormsCommandCenterY" />
    <member name="T:System.Windows.Forms.Design.ScrollableControlDesigner">
      <summary>Base designer class for extending the design mode behavior of a <see cref="T:System.Windows.Forms.Control" /> which should receive scroll messages.</summary>
    </member>
    <member name="M:System.Windows.Forms.Design.ScrollableControlDesigner.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Windows.Forms.Design.ScrollableControlDesigner" /> class.</summary>
    </member>
    <member name="M:System.Windows.Forms.Design.ScrollableControlDesigner.GetHitTest(System.Drawing.Point)">
      <summary>Indicates whether a mouse click at the specified point should be handled by the control.</summary>
      <param name="pt">A <see cref="T:System.Drawing.Point" /> indicating the position at which the mouse was clicked, in screen coordinates.</param>
      <returns>
        <see langword="true" /> if a click at the specified point is to be handled by the control; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Windows.Forms.Design.ScrollableControlDesigner.WndProc(System.Windows.Forms.Message@)">
      <summary>Processes Windows messages and passes WM_HSCROLL and WM_VSCROLL messages to the control at design time.</summary>
      <param name="m">The <see cref="T:System.Windows.Forms.Message" /> to process.</param>
    </member>
    <member name="T:System.Windows.Forms.Design.SelectionRules">
      <summary>Defines identifiers that are used to indicate selection rules for a component.</summary>
    </member>
    <member name="F:System.Windows.Forms.Design.SelectionRules.AllSizeable">
      <summary>Indicates the component supports sizing in all directions.</summary>
    </member>
    <member name="F:System.Windows.Forms.Design.SelectionRules.BottomSizeable">
      <summary>Indicates the component supports resize from the bottom.</summary>
    </member>
    <member name="F:System.Windows.Forms.Design.SelectionRules.LeftSizeable">
      <summary>Indicates the component supports resize from the left.</summary>
    </member>
    <member name="F:System.Windows.Forms.Design.SelectionRules.Locked">
      <summary>Indicates the component is locked to its container. Overrides the <see cref="F:System.Windows.Forms.Design.SelectionRules.Moveable" />, <see cref="F:System.Windows.Forms.Design.SelectionRules.AllSizeable" />, <see cref="F:System.Windows.Forms.Design.SelectionRules.BottomSizeable" />, <see cref="F:System.Windows.Forms.Design.SelectionRules.LeftSizeable" />, <see cref="F:System.Windows.Forms.Design.SelectionRules.RightSizeable" />, and <see cref="F:System.Windows.Forms.Design.SelectionRules.TopSizeable" /> bit flags of this enumeration.</summary>
    </member>
    <member name="F:System.Windows.Forms.Design.SelectionRules.Moveable">
      <summary>Indicates the component supports a location property that allows it to be moved on the screen.</summary>
    </member>
    <member name="F:System.Windows.Forms.Design.SelectionRules.None">
      <summary>Indicates no special selection attributes.</summary>
    </member>
    <member name="F:System.Windows.Forms.Design.SelectionRules.RightSizeable">
      <summary>Indicates the component supports resize from the right.</summary>
    </member>
    <member name="F:System.Windows.Forms.Design.SelectionRules.TopSizeable">
      <summary>Indicates the component supports resize from the top.</summary>
    </member>
    <member name="F:System.Windows.Forms.Design.SelectionRules.Visible">
      <summary>Indicates the component has some form of visible user interface and the selection service is drawing a selection border around this user interface. If a selected component has this rule set, you can assume that the component implements <see cref="T:System.ComponentModel.IComponent" /> and that it is associated with a corresponding designer instance.</summary>
    </member>
    <member name="T:System.Windows.Forms.Design.ThemedScrollbarMode">
      <summary>A value that indicates whether the scrollbars of a window and its children will be themed when displayed in the Visual Studio designer.</summary>
    </member>
    <member name="F:System.Windows.Forms.Design.ThemedScrollbarMode.All">
      <summary>The window and all of its children will have themed scrollbars.</summary>
    </member>
    <member name="F:System.Windows.Forms.Design.ThemedScrollbarMode.None">
      <summary>The window and all of its children will not be themed.</summary>
    </member>
    <member name="F:System.Windows.Forms.Design.ThemedScrollbarMode.OnlyTopLevel">
      <summary>The window will have themed scrollbars but all of its children will not be themed.</summary>
    </member>
    <member name="T:System.Windows.Forms.Design.ThemedScrollbarWindow">
      <summary>Represents a window and a value that indicates how its scrollbars should be themed when displayed in the Visual Studio designer.</summary>
    </member>
    <member name="F:System.Windows.Forms.Design.ThemedScrollbarWindow.Handle">
      <summary>The window handle.</summary>
    </member>
    <member name="F:System.Windows.Forms.Design.ThemedScrollbarWindow.Mode">
      <summary>A value that indicates how the window scrollbars should be themed when displayed in the Visual Studio designer.</summary>
    </member>
    <member name="T:System.Windows.Forms.Design.WindowsFormsDesignerOptionService">
      <summary>Provides access to get and set option values for a Windows Forms designer.</summary>
    </member>
    <member name="M:System.Windows.Forms.Design.WindowsFormsDesignerOptionService.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Windows.Forms.Design.WindowsFormsDesignerOptionService" /> class.</summary>
    </member>
    <member name="P:System.Windows.Forms.Design.WindowsFormsDesignerOptionService.CompatibilityOptions">
      <summary>Gets the <see cref="T:System.Windows.Forms.Design.DesignerOptions" /> exposed by the <see cref="T:System.Windows.Forms.Design.WindowsFormsDesignerOptionService" />.</summary>
      <returns>The <see cref="T:System.Windows.Forms.Design.DesignerOptions" /> exposed by the <see cref="T:System.Windows.Forms.Design.WindowsFormsDesignerOptionService" />.</returns>
    </member>
    <member name="M:System.Windows.Forms.Design.WindowsFormsDesignerOptionService.PopulateOptionCollection(System.ComponentModel.Design.DesignerOptionService.DesignerOptionCollection)">
      <summary>Populates a <see cref="T:System.ComponentModel.Design.DesignerOptionService.DesignerOptionCollection" />.</summary>
      <param name="options">The collection to populate.</param>
    </member>
  </members>
</doc>