{"runtimeTarget": {"name": ".NETCoreApp,Version=v3.1", "signature": ""}, "compilationOptions": {}, "targets": {".NETCoreApp,Version=v3.1": {"dotnet-dev-certs/3.1.32-servicing.22566.10": {"dependencies": {"Internal.AspNetCore.BuildTasks": "3.0.0-build-20190530.3", "MicroBuild.Core": "0.3.0", "Microsoft.DotNet.GenAPI": "1.0.0-beta.22558.6", "Microsoft.Extensions.CommandLineUtils.Sources": "3.1.32-servicing.22566.2", "Microsoft.Internal.Extensions.Refs": "3.1.8-servicing.20420.4", "Microsoft.Net.Compilers.Toolset": "3.4.1-beta4-20127-10", "Microsoft.SourceLink.AzureRepos.Git": "1.1.0-beta-20206-02", "Microsoft.SourceLink.GitHub": "1.1.0-beta-20206-02", "System.Security.Cryptography.Cng": "4.7.0"}, "runtime": {"dotnet-dev-certs.dll": {}}}, "Internal.AspNetCore.BuildTasks/3.0.0-build-20190530.3": {}, "MicroBuild.Core/0.3.0": {}, "Microsoft.Build.Tasks.Git/1.1.0-beta-20206-02": {}, "Microsoft.DotNet.GenAPI/1.0.0-beta.22558.6": {}, "Microsoft.Extensions.CommandLineUtils.Sources/3.1.32-servicing.22566.2": {}, "Microsoft.Internal.Extensions.Refs/3.1.8-servicing.20420.4": {}, "Microsoft.Net.Compilers.Toolset/3.4.1-beta4-20127-10": {}, "Microsoft.SourceLink.AzureRepos.Git/1.1.0-beta-20206-02": {"dependencies": {"Microsoft.Build.Tasks.Git": "1.1.0-beta-20206-02", "Microsoft.SourceLink.Common": "1.1.0-beta-20206-02"}}, "Microsoft.SourceLink.Common/1.1.0-beta-20206-02": {}, "Microsoft.SourceLink.GitHub/1.1.0-beta-20206-02": {"dependencies": {"Microsoft.Build.Tasks.Git": "1.1.0-beta-20206-02", "Microsoft.SourceLink.Common": "1.1.0-beta-20206-02"}}, "System.Security.Cryptography.Cng/4.7.0": {}}}, "libraries": {"dotnet-dev-certs/3.1.32-servicing.22566.10": {"type": "project", "serviceable": false, "sha512": ""}, "Internal.AspNetCore.BuildTasks/3.0.0-build-20190530.3": {"type": "package", "serviceable": true, "sha512": "sha512-CU705hF0Pj7+5BWlRoRvcWm+11ATz2is8bEn4l4C70v31oV2mH+UJuPb1PGlqbYgLMhEie2JqvMhIdGYpvjaJg==", "path": "internal.aspnetcore.buildtasks/3.0.0-build-20190530.3", "hashPath": "internal.aspnetcore.buildtasks.3.0.0-build-20190530.3.nupkg.sha512"}, "MicroBuild.Core/0.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-71NxN8xP8+n08w7XuEOpbzuerL45QRodeLfrjs51qCT8LbjARyfPEcSE30YqLjFtFl3km5eH5Oaqnq2p1hQbAw==", "path": "microbuild.core/0.3.0", "hashPath": "microbuild.core.0.3.0.nupkg.sha512"}, "Microsoft.Build.Tasks.Git/1.1.0-beta-20206-02": {"type": "package", "serviceable": true, "sha512": "sha512-hZ9leS9Yd9MHpqvviMftSJFDcLYu2h1DrapW1TDm1s1fgOy71c8HvArNMd3fseVkXmp3VTfGnkgcw0FR+TI6xw==", "path": "microsoft.build.tasks.git/1.1.0-beta-20206-02", "hashPath": "microsoft.build.tasks.git.1.1.0-beta-20206-02.nupkg.sha512"}, "Microsoft.DotNet.GenAPI/1.0.0-beta.22558.6": {"type": "package", "serviceable": true, "sha512": "sha512-VHHQPMhVzZu9HDlN3lULIsaJWqyBNcUjcNxni8QYab7N3q6bR2EJOJNlkX0pot5lIu14aWjO4gCrmsIU+mUuCg==", "path": "microsoft.dotnet.genapi/1.0.0-beta.22558.6", "hashPath": "microsoft.dotnet.genapi.1.0.0-beta.22558.6.nupkg.sha512"}, "Microsoft.Extensions.CommandLineUtils.Sources/3.1.32-servicing.22566.2": {"type": "package", "serviceable": true, "sha512": "sha512-RT0N/gmPlIq6yC0LcGhhbKA5cIarvcYSLgminxcpmmSHno20dyjF7iopYAOGCPKW16nTvzpliGquQdt1Toe+BQ==", "path": "microsoft.extensions.commandlineutils.sources/3.1.32-servicing.22566.2", "hashPath": "microsoft.extensions.commandlineutils.sources.3.1.32-servicing.22566.2.nupkg.sha512"}, "Microsoft.Internal.Extensions.Refs/3.1.8-servicing.20420.4": {"type": "package", "serviceable": true, "sha512": "sha512-WKeOFsIioIylP+1YIj9PfmOVbpauO2oDfkrAME6/GcbJiaw8pnTDKfDH7N9kLKaLkVD9KVCYEj90q6WjHaMLXQ==", "path": "microsoft.internal.extensions.refs/3.1.8-servicing.20420.4", "hashPath": "microsoft.internal.extensions.refs.3.1.8-servicing.20420.4.nupkg.sha512"}, "Microsoft.Net.Compilers.Toolset/3.4.1-beta4-20127-10": {"type": "package", "serviceable": true, "sha512": "sha512-qeWtJRbOMcHf7KGmhODZuh4B3vD0Wh3mlnQeWyqlQ7qvvP0OGhFHPQsrUb33ibJ7Xz4dYQtXyAwf8iqp3617uA==", "path": "microsoft.net.compilers.toolset/3.4.1-beta4-20127-10", "hashPath": "microsoft.net.compilers.toolset.3.4.1-beta4-20127-10.nupkg.sha512"}, "Microsoft.SourceLink.AzureRepos.Git/1.1.0-beta-20206-02": {"type": "package", "serviceable": true, "sha512": "sha512-vVYhSds9TfraTQkGHHMDMVWnr3kCkTZ7vmqUmrXQBDJFXiWTuMoP5RRa9s1M/KmgB4szi5TOb7sOaHWKDT9qDA==", "path": "microsoft.sourcelink.azurerepos.git/1.1.0-beta-20206-02", "hashPath": "microsoft.sourcelink.azurerepos.git.1.1.0-beta-20206-02.nupkg.sha512"}, "Microsoft.SourceLink.Common/1.1.0-beta-20206-02": {"type": "package", "serviceable": true, "sha512": "sha512-aek0RTQ+4Bf11WvqaXajwYoaBWkX2edBjAr5XJOvhAsHX6/9vPOb7IpHAiE/NyCse7IcpGWslJZHNkv4UBEFqw==", "path": "microsoft.sourcelink.common/1.1.0-beta-20206-02", "hashPath": "microsoft.sourcelink.common.1.1.0-beta-20206-02.nupkg.sha512"}, "Microsoft.SourceLink.GitHub/1.1.0-beta-20206-02": {"type": "package", "serviceable": true, "sha512": "sha512-7A7P0EwL+lypaI/CEvG4IcpAlQeAt04uPPw1SO6Q9Jwz2nE9309pQXJ4TfP/RLL8IOObACidN66+gVR+bJDZHw==", "path": "microsoft.sourcelink.github/1.1.0-beta-20206-02", "hashPath": "microsoft.sourcelink.github.1.1.0-beta-20206-02.nupkg.sha512"}, "System.Security.Cryptography.Cng/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-4WQjFuypWtxb/bl/YwEE7LYGn4fgpsikFfBU6xwEm4YBYiRAhXAEJ62lILBu2JJSFbClIAntFTGfDZafn8yZTg==", "path": "system.security.cryptography.cng/4.7.0", "hashPath": "system.security.cryptography.cng.4.7.0.nupkg.sha512"}}}