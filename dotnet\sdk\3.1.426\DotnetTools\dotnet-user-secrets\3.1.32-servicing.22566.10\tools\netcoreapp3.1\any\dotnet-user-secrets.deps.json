{"runtimeTarget": {"name": ".NETCoreApp,Version=v3.1", "signature": ""}, "compilationOptions": {}, "targets": {".NETCoreApp,Version=v3.1": {"dotnet-user-secrets/3.1.32-servicing.22566.10": {"dependencies": {"Internal.AspNetCore.BuildTasks": "3.0.0-build-20190530.3", "MicroBuild.Core": "0.3.0", "Microsoft.DotNet.GenAPI": "1.0.0-beta.22558.6", "Microsoft.Extensions.CommandLineUtils.Sources": "3.1.32-servicing.22566.2", "Microsoft.Extensions.Configuration.UserSecrets": "3.1.32", "Microsoft.Internal.Extensions.Refs": "3.1.8-servicing.20420.4", "Microsoft.Net.Compilers.Toolset": "3.4.1-beta4-20127-10", "Microsoft.SourceLink.AzureRepos.Git": "1.1.0-beta-20206-02", "Microsoft.SourceLink.GitHub": "1.1.0-beta-20206-02", "Newtonsoft.Json": "13.0.1"}, "runtime": {"dotnet-user-secrets.dll": {}}}, "Internal.AspNetCore.BuildTasks/3.0.0-build-20190530.3": {}, "MicroBuild.Core/0.3.0": {}, "Microsoft.Build.Tasks.Git/1.1.0-beta-20206-02": {}, "Microsoft.DotNet.GenAPI/1.0.0-beta.22558.6": {}, "Microsoft.Extensions.CommandLineUtils.Sources/3.1.32-servicing.22566.2": {}, "Microsoft.Extensions.Configuration/3.1.32": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "3.1.32"}, "runtime": {"lib/netcoreapp3.1/Microsoft.Extensions.Configuration.dll": {"assemblyVersion": "3.1.32.0", "fileVersion": "3.100.3222.56602"}}}, "Microsoft.Extensions.Configuration.Abstractions/3.1.32": {"dependencies": {"Microsoft.Extensions.Primitives": "3.1.32"}, "runtime": {"lib/netcoreapp3.1/Microsoft.Extensions.Configuration.Abstractions.dll": {"assemblyVersion": "3.1.32.0", "fileVersion": "3.100.3222.56602"}}}, "Microsoft.Extensions.Configuration.FileExtensions/3.1.32": {"dependencies": {"Microsoft.Extensions.Configuration": "3.1.32", "Microsoft.Extensions.FileProviders.Physical": "3.1.32"}, "runtime": {"lib/netcoreapp3.1/Microsoft.Extensions.Configuration.FileExtensions.dll": {"assemblyVersion": "3.1.32.0", "fileVersion": "3.100.3222.56602"}}}, "Microsoft.Extensions.Configuration.Json/3.1.32": {"dependencies": {"Microsoft.Extensions.Configuration": "3.1.32", "Microsoft.Extensions.Configuration.FileExtensions": "3.1.32"}, "runtime": {"lib/netcoreapp3.1/Microsoft.Extensions.Configuration.Json.dll": {"assemblyVersion": "3.1.32.0", "fileVersion": "3.100.3222.56602"}}}, "Microsoft.Extensions.Configuration.UserSecrets/3.1.32": {"dependencies": {"Microsoft.Extensions.Configuration.Json": "3.1.32"}, "runtime": {"lib/netcoreapp3.1/Microsoft.Extensions.Configuration.UserSecrets.dll": {"assemblyVersion": "3.1.32.0", "fileVersion": "3.100.3222.56602"}}}, "Microsoft.Extensions.FileProviders.Abstractions/3.1.32": {"dependencies": {"Microsoft.Extensions.Primitives": "3.1.32"}, "runtime": {"lib/netcoreapp3.1/Microsoft.Extensions.FileProviders.Abstractions.dll": {"assemblyVersion": "3.1.32.0", "fileVersion": "3.100.3222.56602"}}}, "Microsoft.Extensions.FileProviders.Physical/3.1.32": {"dependencies": {"Microsoft.Extensions.FileProviders.Abstractions": "3.1.32", "Microsoft.Extensions.FileSystemGlobbing": "3.1.32"}, "runtime": {"lib/netcoreapp3.1/Microsoft.Extensions.FileProviders.Physical.dll": {"assemblyVersion": "3.1.32.0", "fileVersion": "3.100.3222.56602"}}}, "Microsoft.Extensions.FileSystemGlobbing/3.1.32": {"runtime": {"lib/netstandard2.0/Microsoft.Extensions.FileSystemGlobbing.dll": {"assemblyVersion": "3.1.32.0", "fileVersion": "3.100.3222.56602"}}}, "Microsoft.Extensions.Primitives/3.1.32": {"runtime": {"lib/netcoreapp3.1/Microsoft.Extensions.Primitives.dll": {"assemblyVersion": "3.1.32.0", "fileVersion": "3.100.3222.56602"}}}, "Microsoft.Internal.Extensions.Refs/3.1.8-servicing.20420.4": {}, "Microsoft.Net.Compilers.Toolset/3.4.1-beta4-20127-10": {}, "Microsoft.SourceLink.AzureRepos.Git/1.1.0-beta-20206-02": {"dependencies": {"Microsoft.Build.Tasks.Git": "1.1.0-beta-20206-02", "Microsoft.SourceLink.Common": "1.1.0-beta-20206-02"}}, "Microsoft.SourceLink.Common/1.1.0-beta-20206-02": {}, "Microsoft.SourceLink.GitHub/1.1.0-beta-20206-02": {"dependencies": {"Microsoft.Build.Tasks.Git": "1.1.0-beta-20206-02", "Microsoft.SourceLink.Common": "1.1.0-beta-20206-02"}}, "Newtonsoft.Json/13.0.1": {"runtime": {"lib/netstandard2.0/Newtonsoft.Json.dll": {"assemblyVersion": "13.0.0.0", "fileVersion": "13.0.1.25517"}}}}}, "libraries": {"dotnet-user-secrets/3.1.32-servicing.22566.10": {"type": "project", "serviceable": false, "sha512": ""}, "Internal.AspNetCore.BuildTasks/3.0.0-build-20190530.3": {"type": "package", "serviceable": true, "sha512": "sha512-CU705hF0Pj7+5BWlRoRvcWm+11ATz2is8bEn4l4C70v31oV2mH+UJuPb1PGlqbYgLMhEie2JqvMhIdGYpvjaJg==", "path": "internal.aspnetcore.buildtasks/3.0.0-build-20190530.3", "hashPath": "internal.aspnetcore.buildtasks.3.0.0-build-20190530.3.nupkg.sha512"}, "MicroBuild.Core/0.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-71NxN8xP8+n08w7XuEOpbzuerL45QRodeLfrjs51qCT8LbjARyfPEcSE30YqLjFtFl3km5eH5Oaqnq2p1hQbAw==", "path": "microbuild.core/0.3.0", "hashPath": "microbuild.core.0.3.0.nupkg.sha512"}, "Microsoft.Build.Tasks.Git/1.1.0-beta-20206-02": {"type": "package", "serviceable": true, "sha512": "sha512-hZ9leS9Yd9MHpqvviMftSJFDcLYu2h1DrapW1TDm1s1fgOy71c8HvArNMd3fseVkXmp3VTfGnkgcw0FR+TI6xw==", "path": "microsoft.build.tasks.git/1.1.0-beta-20206-02", "hashPath": "microsoft.build.tasks.git.1.1.0-beta-20206-02.nupkg.sha512"}, "Microsoft.DotNet.GenAPI/1.0.0-beta.22558.6": {"type": "package", "serviceable": true, "sha512": "sha512-VHHQPMhVzZu9HDlN3lULIsaJWqyBNcUjcNxni8QYab7N3q6bR2EJOJNlkX0pot5lIu14aWjO4gCrmsIU+mUuCg==", "path": "microsoft.dotnet.genapi/1.0.0-beta.22558.6", "hashPath": "microsoft.dotnet.genapi.1.0.0-beta.22558.6.nupkg.sha512"}, "Microsoft.Extensions.CommandLineUtils.Sources/3.1.32-servicing.22566.2": {"type": "package", "serviceable": true, "sha512": "sha512-RT0N/gmPlIq6yC0LcGhhbKA5cIarvcYSLgminxcpmmSHno20dyjF7iopYAOGCPKW16nTvzpliGquQdt1Toe+BQ==", "path": "microsoft.extensions.commandlineutils.sources/3.1.32-servicing.22566.2", "hashPath": "microsoft.extensions.commandlineutils.sources.3.1.32-servicing.22566.2.nupkg.sha512"}, "Microsoft.Extensions.Configuration/3.1.32": {"type": "package", "serviceable": true, "sha512": "sha512-WuOHTU9FB1yHaIU+/Ar1s5swHshH+7YjU7eA9Lmv0kO+rta7xOrR5Xu68srdxNpE9HjqjzxGZhPJFLxpP3J1Og==", "path": "microsoft.extensions.configuration/3.1.32", "hashPath": "microsoft.extensions.configuration.3.1.32.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Abstractions/3.1.32": {"type": "package", "serviceable": true, "sha512": "sha512-w8WEwVFYbTkoDQ/eJgGUPiL4SqZOiIVBkGxbkmnJAWnFxRigFk4WZla/3MDkN9fGSis6JwJfc57YgnleTw48AA==", "path": "microsoft.extensions.configuration.abstractions/3.1.32", "hashPath": "microsoft.extensions.configuration.abstractions.3.1.32.nupkg.sha512"}, "Microsoft.Extensions.Configuration.FileExtensions/3.1.32": {"type": "package", "serviceable": true, "sha512": "sha512-ckZNYSlqB62D4m432GyLqFX+yd3RtnrSYu9S1816AXVywA5CUs8rxHyWD6wzJ+jL3/zv94F02rGA3lU+g5xfsA==", "path": "microsoft.extensions.configuration.fileextensions/3.1.32", "hashPath": "microsoft.extensions.configuration.fileextensions.3.1.32.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Json/3.1.32": {"type": "package", "serviceable": true, "sha512": "sha512-lCZbMIu6iTmLcY9gqSlmsn4wmUGE6AJZYYLlioWhGwkYF3bY92iUIg1wROoiP3vqNwSvGzofpBWh9d8y2XhgtA==", "path": "microsoft.extensions.configuration.json/3.1.32", "hashPath": "microsoft.extensions.configuration.json.3.1.32.nupkg.sha512"}, "Microsoft.Extensions.Configuration.UserSecrets/3.1.32": {"type": "package", "serviceable": true, "sha512": "sha512-7jFtz9pc1bghrxYBA9JlnVC9IoNol5zJDbuOPDaeQtrxBWyJuolwgu74DUnMfhFjZHkgQkPvSODts6h73xk8AQ==", "path": "microsoft.extensions.configuration.usersecrets/3.1.32", "hashPath": "microsoft.extensions.configuration.usersecrets.3.1.32.nupkg.sha512"}, "Microsoft.Extensions.FileProviders.Abstractions/3.1.32": {"type": "package", "serviceable": true, "sha512": "sha512-sS+U28IfgZSQUS2b3MayPdYGBJlHOWwgnfAZ77bZLkgU0z+lJz7lgzrKQUm9SgKF+OAc5B9kWJV5PB6l7mWWZA==", "path": "microsoft.extensions.fileproviders.abstractions/3.1.32", "hashPath": "microsoft.extensions.fileproviders.abstractions.3.1.32.nupkg.sha512"}, "Microsoft.Extensions.FileProviders.Physical/3.1.32": {"type": "package", "serviceable": true, "sha512": "sha512-I+N5RGDfeG+1HVF6VugJpX1uYZErvAz40EaWZ45DmkAc3qXEFTXzNXWaPmJcIrOvz4eKS1jL5VxZxVH+7re7gg==", "path": "microsoft.extensions.fileproviders.physical/3.1.32", "hashPath": "microsoft.extensions.fileproviders.physical.3.1.32.nupkg.sha512"}, "Microsoft.Extensions.FileSystemGlobbing/3.1.32": {"type": "package", "serviceable": true, "sha512": "sha512-MnccV+5jJkc69IlI/1+Hzak8w5a2wQzOgaooq0iDcd5MBA1vudkE/iHuT97+cQCyauNmXnAdPWQ7e4Y1q93sbg==", "path": "microsoft.extensions.filesystemglobbing/3.1.32", "hashPath": "microsoft.extensions.filesystemglobbing.3.1.32.nupkg.sha512"}, "Microsoft.Extensions.Primitives/3.1.32": {"type": "package", "serviceable": true, "sha512": "sha512-N8lTVwdjR+df9Sx3VdHfrecV6zl8KQoAx7kQXJ3rYwQBEw2vuZM0LKVAqjnaA/TBC8ZKnt99ptwH5iaEOxBuYQ==", "path": "microsoft.extensions.primitives/3.1.32", "hashPath": "microsoft.extensions.primitives.3.1.32.nupkg.sha512"}, "Microsoft.Internal.Extensions.Refs/3.1.8-servicing.20420.4": {"type": "package", "serviceable": true, "sha512": "sha512-WKeOFsIioIylP+1YIj9PfmOVbpauO2oDfkrAME6/GcbJiaw8pnTDKfDH7N9kLKaLkVD9KVCYEj90q6WjHaMLXQ==", "path": "microsoft.internal.extensions.refs/3.1.8-servicing.20420.4", "hashPath": "microsoft.internal.extensions.refs.3.1.8-servicing.20420.4.nupkg.sha512"}, "Microsoft.Net.Compilers.Toolset/3.4.1-beta4-20127-10": {"type": "package", "serviceable": true, "sha512": "sha512-qeWtJRbOMcHf7KGmhODZuh4B3vD0Wh3mlnQeWyqlQ7qvvP0OGhFHPQsrUb33ibJ7Xz4dYQtXyAwf8iqp3617uA==", "path": "microsoft.net.compilers.toolset/3.4.1-beta4-20127-10", "hashPath": "microsoft.net.compilers.toolset.3.4.1-beta4-20127-10.nupkg.sha512"}, "Microsoft.SourceLink.AzureRepos.Git/1.1.0-beta-20206-02": {"type": "package", "serviceable": true, "sha512": "sha512-vVYhSds9TfraTQkGHHMDMVWnr3kCkTZ7vmqUmrXQBDJFXiWTuMoP5RRa9s1M/KmgB4szi5TOb7sOaHWKDT9qDA==", "path": "microsoft.sourcelink.azurerepos.git/1.1.0-beta-20206-02", "hashPath": "microsoft.sourcelink.azurerepos.git.1.1.0-beta-20206-02.nupkg.sha512"}, "Microsoft.SourceLink.Common/1.1.0-beta-20206-02": {"type": "package", "serviceable": true, "sha512": "sha512-aek0RTQ+4Bf11WvqaXajwYoaBWkX2edBjAr5XJOvhAsHX6/9vPOb7IpHAiE/NyCse7IcpGWslJZHNkv4UBEFqw==", "path": "microsoft.sourcelink.common/1.1.0-beta-20206-02", "hashPath": "microsoft.sourcelink.common.1.1.0-beta-20206-02.nupkg.sha512"}, "Microsoft.SourceLink.GitHub/1.1.0-beta-20206-02": {"type": "package", "serviceable": true, "sha512": "sha512-7A7P0EwL+lypaI/CEvG4IcpAlQeAt04uPPw1SO6Q9Jwz2nE9309pQXJ4TfP/RLL8IOObACidN66+gVR+bJDZHw==", "path": "microsoft.sourcelink.github/1.1.0-beta-20206-02", "hashPath": "microsoft.sourcelink.github.1.1.0-beta-20206-02.nupkg.sha512"}, "Newtonsoft.Json/13.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-ppPFpBcvxdsfUonNcvITKqLl3bqxWbDCZIzDWHzjpdAHRFfZe0Dw9HmA0+za13IdyrgJwpkDTDA9fHaxOrt20A==", "path": "newtonsoft.json/13.0.1", "hashPath": "newtonsoft.json.13.0.1.nupkg.sha512"}}}