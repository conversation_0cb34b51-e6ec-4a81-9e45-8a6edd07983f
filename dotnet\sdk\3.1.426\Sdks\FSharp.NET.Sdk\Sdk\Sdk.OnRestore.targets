<Project xmlns="http://schemas.microsoft.com/developer/msbuild/2003">

  <PropertyGroup>
    <MSBuildAllProjects>$(MSBuildAllProjects);$(MSBuildThisFileFullPath)</MSBuildAllProjects>
  </PropertyGroup>

  <!-- Import design time targets for Roslyn Project System. These are only available if Visual Studio is installed. -->
  <!-- Import design time targets before the common targets, which import targets from Nuget. -->
  <PropertyGroup>
      <FSharpDesignTimeTargetsPath Condition="'$(FSharpDesignTimeTargetsPath)'==''">$(MSBuildExtensionsPath)\Microsoft\VisualStudio\Managed\Microsoft.FSharp.DesignTime.targets</FSharpDesignTimeTargetsPath>
  </PropertyGroup>
  <Import Project="$(FSharpDesignTimeTargetsPath)" Condition="'$(FSharpDesignTimeTargetsPath)' != '' and Exists('$(FSharpDesignTimeTargetsPath)')" />

  <Import Project="$(MSBuildToolsPath)\Microsoft.Common.targets" />

</Project>
