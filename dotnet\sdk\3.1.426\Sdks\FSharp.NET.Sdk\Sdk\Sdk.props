<Project xmlns="http://schemas.microsoft.com/developer/msbuild/2003">

  <PropertyGroup>
    <MSBuildAllProjects>$(MSBuildAllProjects);$(MSBuildThisFileFullPath)</MSBuildAllProjects>

    <!-- disable wilcard include of Compile items, file ordering is a feature -->
    <EnableDefaultCompileItems>false</EnableDefaultCompileItems>

    <!-- project guid used by dotnet sln add -->
    <DefaultProjectTypeGuid Condition=" '$(DefaultProjectTypeGuid)' == '' ">{6EC3EE1D-3C4E-46DD-8F32-0CC8E7565705}</DefaultProjectTypeGuid>
  </PropertyGroup>

</Project>
