<Project xmlns="http://schemas.microsoft.com/developer/msbuild/2003">

  <PropertyGroup>
    <_LinkTaskDirectoryRoot>$(MSBuildThisFileDirectory)../tools/</_LinkTaskDirectoryRoot>
    <_LinkTaskTFM Condition=" '$(MSBuildRuntimeType)' == 'Core' ">netcoreapp2.0</_LinkTaskTFM>
    <_LinkTaskTFM Condition=" '$(_LinkTaskTFM)' == '' ">net46</_LinkTaskTFM>
    <_LinkTaskDirectory>$(_LinkTaskDirectoryRoot)$(_LinkTaskTFM)/</_LinkTaskDirectory>
    <LinkTaskDllPath Condition=" '$(LinkTaskDllPath)' == '' ">$(_LinkTaskDirectory)ILLink.Tasks.dll</LinkTaskDllPath>
  </PropertyGroup>

  <PropertyGroup>
    <!-- LinkDuringPublish allows the linker to be disabled by passing
         a property on the command-line. (/p:LinkDuringPublish=false)
         -->
    <LinkDuringPublish Condition=" '$(LinkDuringPublish)' == '' ">true</LinkDuringPublish>
    <LinkDuringPublish Condition=" '$(LinkDuringPublish)' != 'true' ">false</LinkDuringPublish>
    <ShowLinkerSizeComparison Condition=" '$(ShowLinkerSizeComparison)' == '' ">false</ShowLinkerSizeComparison>
    <LinkerDumpDependencies Condition=" '$(LinkerDumpDependencies)' == '' ">false</LinkerDumpDependencies>
    <UsedApplicationAssemblyAction Condition=" '$(UsedApplicationAssemblyAction)' == '' ">Copy</UsedApplicationAssemblyAction>
    <UnusedApplicationAssemblyAction Condition=" '$(UnusedApplicationAssemblyAction)' == '' ">Delete</UnusedApplicationAssemblyAction>
    <UsedPlatformAssemblyAction Condition=" '$(UsedPlatformAssemblyAction)' == '' and '$(SelfContained)' == 'true' ">AddBypassNGen</UsedPlatformAssemblyAction>
    <UsedPlatformAssemblyAction Condition=" '$(UsedPlatformAssemblyAction)' == '' ">Skip</UsedPlatformAssemblyAction>
    <UnusedPlatformAssemblyAction Condition=" '$(UnusedPlatformAssemblyAction)' == '' and '$(SelfContained)' == 'true' ">Delete</UnusedPlatformAssemblyAction>
    <UnusedPlatformAssemblyAction Condition=" '$(UnusedPlatformAssemblyAction)' == '' ">Skip</UnusedPlatformAssemblyAction>
    <RootAllApplicationAssemblies Condition=" '$(RootAllApplicationAssemblies)' == '' ">true</RootAllApplicationAssemblies>
    <RootAllApplicationAssemblies Condition=" '$(RootAllApplicationAssemblies)' != 'true' ">false</RootAllApplicationAssemblies>
    <LinkerTrimNativeDeps Condition=" '$(LinkerTrimNativeDeps)' == '' ">true</LinkerTrimNativeDeps>
    <LinkerTrimNativeDeps Condition=" '$(LinkerTrimNativeDeps)' != 'true' ">false</LinkerTrimNativeDeps>
    <ClearInitLocals Condition=" '$(ClearInitLocals)' == '' ">false</ClearInitLocals>
  </PropertyGroup>

  <ItemGroup>
    <!-- LinkerRootDescriptors (the ItemGroup) is set to contain
         LinkerRootDescriptors (the Property), which allows adding
         linker roots on the command-line
         (/p:LinkerRootDescriptors=path/to/a.xml;path/to/b.xml;). These
         are relative paths from the project to the xml file.
         LinkerRootDescriptors (the ItemGroup) can also be set in the
         project file, and these will be included in addition to those
         specified on the command-line. -->
    <LinkerRootDescriptors Include="$(LinkerRootDescriptors)" />
  </ItemGroup>

  <!-- By default, the linker will generate roots for the intermediate
       assembly that root everything in the assembly. -->
  <PropertyGroup>
    <_IntermediateRootDescriptorName Condition=" '$(_IntermediateRootDescriptorName)' == '' ">IntermediateAssemblyRoots.xml</_IntermediateRootDescriptorName>
    <_IntermediateRootDescriptorPath Condition=" '$(_IntermediateRootDescriptorPath)' == '' ">$(IntermediateOutputPath)$(_IntermediateRootDescriptorName)</_IntermediateRootDescriptorPath>
  </PropertyGroup>

  <!-- The linker will place linked assemblies into an intermediate
       directory, and hook into the rest of the publish pipeline to
       publish the linked assemblies instead of those given as input
       to the linker. -->
  <PropertyGroup>
    <IntermediateLinkDirName Condition=" '$(IntermediateLinkDirName)' == '' ">linked</IntermediateLinkDirName>
    <IntermediateLinkDir Condition=" '$(IntermediateLinkDir)' == '' ">$(IntermediateOutputPath)$(IntermediateLinkDirName)</IntermediateLinkDir>
  </PropertyGroup>

  <!-- Used to enable incremental build for the link target. -->
  <PropertyGroup>
    <_LinkSemaphore>$(IntermediateOutputPath)Link.semaphore</_LinkSemaphore>
  </PropertyGroup>

  <!--
      This target runs the linker during the publish pipeline. The
      publish pipeline has a target called ComputeFilesToPublish,
      which computes the ItemGroup ResolvedFileToPublish. To extend
      this target, we insert a target before
      ComputeFilesToPublish. Our target rewrites the relevant inputs
      (@(IntermediateAssembly), @(ResolvedAssembliesToPublish)). This
      lets ComputeFilesToPublish be ignorant of the linker, but
      changes the meaning of IntermediateAssembly and
      ResolvedAssembliesToPublish.

      To include linked pdbs in the publish output, we also rewrite
      the ComputeFilesToPublish input
      @(_DebugSymbolsIntermediatePath). Note that this is a private
      itemgroup, so relying on this is not ideal.
  -->
  <!-- DependsOnTargets here doesn't include the targets that compute
       ResolvedAssembliesToPublish or IntermediateAssembly, because
       ComputeFilesToPublish already depends on
       these. BeforeTargets="ComputeFilesToPublish" ensures that
       ComputeLinkedFilesToPublish will run before
       ComputeFilesToPublish, but after all of its dependencies. -->
  <Target Name="ComputeLinkedFilesToPublish"
          BeforeTargets="ComputeFilesToPublish"
          DependsOnTargets="_ComputeLinkedAssemblies;_FindNativeDeps;_RemoveDuplicatesFrom_ResolvedCopyLocalPublishAssets"
          Condition=" '$(LinkDuringPublish)' == 'true' ">
    <!-- Rewrite ResolvedAssembliesToPublish, which is an input to
         ComputeFilesToPublish. -->
    <ItemGroup>
      <_ResolvedCopyLocalPublishAssets Remove="@(_ManagedAssembliesToLink)" />
      <_ResolvedCopyLocalPublishAssets Remove="@(_NativeResolvedDepsToPublish)" />
      <_ResolvedCopyLocalPublishAssets Include="@(_NativeKeptDepsToPublish)" />
      <_ResolvedCopyLocalPublishAssets Include="@(_LinkedResolvedAssemblies)" />
    </ItemGroup>

    <!-- Rewrite IntermediateAssembly, which is an input to
         ComputeFilesToPublish. -->
    <ItemGroup>
      <IntermediateAssembly Remove="@(IntermediateAssembly)" />
      <IntermediateAssembly Include="@(_LinkedIntermediateAssembly)" />
    </ItemGroup>

    <!-- Rewrite _DebugSymbolsIntermediatePath, which is an input to
         ComputeFilesToPublish. -->
    <ItemGroup>
      <_DebugSymbolsIntermediatePath Remove="@(_DebugSymbolsIntermediatePath)" Condition=" '$(_DebugSymbolsProduced)' == 'true' " />
      <_DebugSymbolsIntermediatePath Include="@(_LinkedDebugSymbols)" Condition=" '$(_DebugSymbolsProduced)' == 'true' " />
    </ItemGroup>
  </Target>

  <!-- Work around duplicate items in
       _ResolvedCopyLocalPublishAssets. See
       https://github.com/dotnet/sdk/issues/3007. -->
  <Target Name="_RemoveDuplicatesFrom_ResolvedCopyLocalPublishAssets">
    <RemoveDuplicates Inputs="@(_ResolvedCopyLocalPublishAssets)">
      <Output TaskParameter="Filtered" ItemName="_Unique_ResolvedCopyLocalPublishAssets" />
    </RemoveDuplicates>
    <ItemGroup>
      <_ResolvedCopyLocalPublishAssets Remove="@(_ResolvedCopyLocalPublishAssets)" />
      <_ResolvedCopyLocalPublishAssets Include="@(_Unique_ResolvedCopyLocalPublishAssets)" />
    </ItemGroup>
  </Target>

  <!-- The SDK has a target called ComputeRefAssembliesToPublish that
       runs after ComputeFilesToPublish and rewrites
       ResolvedFileToPublish to include any reference assemblies that
       aren't in ResolvedAssembliesToPublish. Because
       ComputeLinkedFilesToPublish changes ResolvedAssembliesToPublish
       (replacing the original assemblies with linked assemblies),
       this would change the behavior of
       ComputeRefAssembliesToPublish, resulting in extra assemblies
       being placed in the refs directory of the output. To prevent
       this, we save ResolvedAssembliesToPublish before linking, and
       restore it to its pre-link state before
       ComputeRefAssembliesToPublish. -->
  <Target Name="SaveResolvedAssembliesToPublish"
          BeforeTargets="ComputeLinkedFilesToPublish"
          DependsOnTargets="_RemoveDuplicatesFrom_ResolvedCopyLocalPublishAssets">
    <ItemGroup>
      <PreLinkResolvedAssembliesToPublish Include="@(_ResolvedCopyLocalPublishAssets)" />
    </ItemGroup>
  </Target>
  <Target Name="RestoreResolvedAssembliesToPublish"
          BeforeTargets="ComputeRefAssembliesToPublish">
    <ItemGroup>
      <_ResolvedCopyLocalPublishAssets Remove="@(_ResolvedCopyLocalPublishAssets)" />
      <_ResolvedCopyLocalPublishAssets Include="@(PreLinkResolvedAssembliesToPublish)" />
    </ItemGroup>
  </Target>

  <!-- Print out a size comparison report for the linked
       assemblies. This is disabled by default, but can be turned on
       by setting $(ShowLinkerSizeComparison) to true. This runs after
       the ComputeFilesToPublish, so it is output even during
       incremental builds and takes into account all transformations
       on the files to be published. -->
  <UsingTask TaskName="CompareAssemblySizes" AssemblyFile="$(LinkTaskDllPath)" />
  <UsingTask TaskName="FilterByMetadata" AssemblyFile="$(LinkTaskDllPath)" />
  <Target Name="_CompareLinkedAssemblySizes"
          AfterTargets="ComputeFilesToPublish"
          DependsOnTargets="_ComputeManagedAssembliesToLink;_ComputeLinkedAssemblies;_RemoveDuplicatesFrom_ResolvedCopyLocalPublishAssets"
          Condition=" '$(LinkDuringPublish)' == 'true' And '$(ShowLinkerSizeComparison)' == 'true' ">
    <FilterByMetadata Items="@(_ResolvedCopyLocalPublishAssets);@(IntermediateAssembly)"
                      MetadataName="Filename"
                      MetadataValues="@(_ManagedAssembliesToLink->'%(Filename)')">
      <Output TaskParameter="FilteredItems" ItemName="_FinalAssembliesTouchedByLinker" />
    </FilterByMetadata>
    <CompareAssemblySizes UnlinkedAssemblies="@(_ManagedAssembliesToLink)"
                          LinkedAssemblies="@(_FinalAssembliesTouchedByLinker)" />
  </Target>


  <!-- Computes _LinkedResolvedAssemblies,
       _LinkedIntermediateAssembly, and
       _LinkedDebugSymbols. _LinkedResolvedAssemblies needs to keep
       metadata from _ManagedResolvedAssembliesToPublish, since this
       is used by ComputeFilesToPublish. -->
  <Target Name="_ComputeLinkedAssemblies"
          DependsOnTargets="_ComputeManagedResolvedAssembliesToPublish;ILLink">
    <ItemGroup>
      <__LinkedResolvedAssemblies Include="@(_ManagedResolvedAssembliesToPublish->'$(IntermediateLinkDir)/%(Filename)%(Extension)')" />
      <__LinkedResolvedAssemblies Remove="@(_DuplicateManagedAssembliesToSkip->'$(IntermediateLinkDir)/%(Filename)%(Extension)')" />
      <_LinkedResolvedAssemblies Include="@(__LinkedResolvedAssemblies)" Condition="Exists('%(Identity)')" />
    </ItemGroup>

    <ItemGroup>
      <__LinkedIntermediateAssembly Include="@(IntermediateAssembly->'$(IntermediateLinkDir)/%(Filename)%(Extension)')" />
      <_LinkedIntermediateAssembly Include="@(__LinkedIntermediateAssembly)" Condition="Exists('%(Identity)')" />
    </ItemGroup>

    <ItemGroup>
      <__LinkedDebugSymbols Include="@(_DebugSymbolsIntermediatePath->'$(IntermediateLinkDir)/%(Filename)%(Extension)')"
                            Condition=" '$(_DebugSymbolsProduced)' == 'true' " />
      <_LinkedDebugSymbols Include="@(__LinkedDebugSymbols)"
                           Condition="Exists('%(Identity)') And '$(_DebugSymbolsProduced)' == 'true' " />
    </ItemGroup>

  </Target>

  <UsingTask TaskName="SetAssemblyActions" AssemblyFile="$(LinkTaskDllPath)" />
  <Target Name="_SetAssemblyActions"
          DependsOnTargets="_ComputeManagedAssembliesToLink;_ComputePlatformLibraries">

    <ItemGroup>
      <_PlatformAssembliesToLink Include="@(PlatformLibraries->'%(Filename)')" />
      <_ApplicationAssembliesToLink Include="@(_ManagedAssembliesToLink->'%(Filename)')" />
      <_ApplicationAssembliesToLink Remove="@(_PlatformAssembliesToLink)" />
    </ItemGroup>

    <SetAssemblyActions AssemblyPaths="@(_ManagedAssembliesToLink)"
                        ApplicationAssemblyNames="@(_ApplicationAssembliesToLink)"
                        PlatformAssemblyNames="@(_PlatformAssembliesToLink)"
                        UsedApplicationAssemblyAction="$(UsedApplicationAssemblyAction)"
                        UnusedApplicationAssemblyAction="$(UnusedApplicationAssemblyAction)"
                        UsedPlatformAssemblyAction="$(UsedPlatformAssemblyAction)"
                        UnusedPlatformAssemblyAction="$(UnusedPlatformAssemblyAction)">
      <Output TaskParameter="AssemblyPathsWithActions" ItemName="_ManagedAssembliesToLinkWithActions" />
    </SetAssemblyActions>

    <ItemGroup>
      <_ManagedAssembliesToLink Remove="@(_ManagedAssembliesToLink)" />
      <_ManagedAssembliesToLink Include="@(_ManagedAssembliesToLinkWithActions)" />
    </ItemGroup>

  </Target>

  <!-- This calls the linker. Inputs are the managed assemblies to
       link, and root specifications. The semaphore enables msbuild to
       skip linking during an incremental build, when the semaphore is
       up to date with respect to _ManagedAssembliesToLink. -->
  <UsingTask TaskName="ILLink" AssemblyFile="$(LinkTaskDllPath)" />
  <Target Name="ILLink"
          DependsOnTargets="_ComputeManagedAssembliesToLink;_ComputeLinkerRootAssemblies;_ComputeLinkerRootDescriptors;_SetAssemblyActions"
          Inputs="@(_ManagedAssembliesToLink);@(LinkerRootDescriptors);$(MSBuildAllProjects)"
          Outputs="$(_LinkSemaphore)">
    <!-- These extra arguments have been hard-coded for now, as this
         is what we want to use when linking a self-contained app. In
         the future we will want to generate these depending on the
         scenario in which the linker is invoked. -->
    <PropertyGroup>
      <ExtraLinkerArgs Condition=" '$(ExtraLinkerArgs)' == '' ">-t -l none -b true --skip-unresolved true --verbose</ExtraLinkerArgs>
    </PropertyGroup>
    <ILLink AssemblyPaths="@(_ManagedAssembliesToLink)"
            RootAssemblyNames="@(LinkerRootAssemblies)"
            RootDescriptorFiles="@(LinkerRootDescriptors)"
            OutputDirectory="$(IntermediateLinkDir)"
            DumpDependencies="$(LinkerDumpDependencies)"
            ClearInitLocals="$(ClearInitLocals)"
            ClearInitLocalsAssemblies="$(ClearInitLocalsAssemblies)"
            ExtraArgs="$(ExtraLinkerArgs)" />

    <Touch Files="$(_LinkSemaphore)" AlwaysCreate="true">
      <Output TaskParameter="TouchedFiles" ItemName="FileWrites" />
    </Touch>
  </Target>

  <!-- FindNativeDeps scans the managed assemblies kept by the linker
       to find references to native files. It outputs the found native
       dependencies, and these are prevented from being published. -->
  <UsingTask TaskName="FindNativeDeps" AssemblyFile="$(LinkTaskDllPath)" />
  <Target Name="_FindNativeDeps"
          DependsOnTargets="_ComputeLinkedAssemblies;_RemoveDuplicatesFrom_ResolvedCopyLocalPublishAssets"
          Condition=" '$(LinkerTrimNativeDeps)' == 'true' ">
    <ItemGroup>
      <_NativeResolvedDepsToPublish Include="@(_ResolvedCopyLocalPublishAssets)" />
      <_NativeResolvedDepsToPublish Remove="@(_ManagedResolvedAssembliesToPublish)" />
      <_NativeResolvedDepsToPublish Remove="@(_NativeResolvedDepsToPublish->WithMetadataValue('AssetType', 'resources'))" />
    </ItemGroup>

    <ItemGroup>
      <_ManagedLinkedAssemblies Include="@(_LinkedResolvedAssemblies)" />
      <_ManagedLinkedAssemblies Include="@(_LinkedIntermediateAssembly)" />
    </ItemGroup>

    <ItemGroup>
      <_NativeDepsToAlwaysKeep Include="coreclr.dll;libcoreclr.dylib;libcoreclr.so" />
      <_NativeDepsToAlwaysKeep Include="clrjit.dll;libclrjit.dylib;libclrjit.so" />
      <_NativeDepsToAlwaysKeep Include="hostfxr.dll;libhostfxr.dylib;libhostfxr.so" />
      <_NativeDepsToAlwaysKeep Include="hostpolicy.dll;libhostpolicy.dylib;libhostpolicy.so" />
    </ItemGroup>

    <FindNativeDeps ManagedAssemblyPaths="@(_ManagedLinkedAssemblies);@(_DuplicateManagedAssembliesToSkip)"
                    NativeDepsPaths="@(_NativeResolvedDepsToPublish)"
                    NativeDepsToKeep="@(_NativeDepsToAlwaysKeep)">
      <Output TaskParameter="KeptNativeDepsPaths" ItemName="_NativeKeptDepsToPublish" />
    </FindNativeDeps>

  </Target>

  <!-- Computes the managed assemblies that are input to the
       linker. Includes managed assemblies from
       ResolvedAssembliesToPublish, and IntermediateAssembly. -->
  <UsingTask TaskName="FindDuplicatesByMetadata" AssemblyFile="$(LinkTaskDllPath)" />
  <Target Name="_ComputeManagedAssembliesToLink"
          DependsOnTargets="_ComputeManagedResolvedAssembliesToPublish">
    <ItemGroup>
      <_ManagedAssembliesToLink Include="@(IntermediateAssembly)" />
      <_ManagedAssembliesToLink Include="@(_ManagedResolvedAssembliesToPublish)" />
    </ItemGroup>

    <!-- Remove duplicate implementation dlls. This can happen if a
         portable app has platform-specific managed assemblies. The
         linker isn't currently able to analyze multiple assemblies
         with the same identity, so we skip these, keeping only one
         representative assembly from each set of duplicates. -->
    <FindDuplicatesByMetadata Items="@(_ManagedAssembliesToLink)"
                              MetadataName="Filename">
      <Output TaskParameter="DuplicateItems" ItemName="_DuplicateManagedAssemblies" />
      <Output TaskParameter="DuplicateRepresentatives" ItemName="_DuplicateManagedAssembliesToLink" />
    </FindDuplicatesByMetadata>
    <ItemGroup>
      <_DuplicateManagedAssembliesToSkip Include="@(_DuplicateManagedAssemblies)" />
      <_DuplicateManagedAssembliesToSkip Remove="@(_DuplicateManagedAssembliesToLink)" />
      <_ManagedAssembliesToLink Remove="@(_DuplicateManagedAssembliesToSkip)" />
    </ItemGroup>

    <!-- Portable publish: need to supply the linker with any needed
         reference assemblies as well.

         Sometimes assemblies have separate reference and
         implementation assemblies. In these cases, prefer the
         implementation assembly, and prevent inclusion of the ref
         assembly by filtering on filename instead of the full
         path. -->
    <FilterByMetadata Items="@(ReferencePath->'%(ResolvedPath)')"
                      MetadataName="Filename"
                      MetadataValues="@(_ManagedAssembliesToLink->'%(Filename)')"
                      Condition=" '$(SelfContained)' != 'true' ">
      <Output TaskParameter="FilteredItems" ItemName="_ReferencedLibrariesToExclude" />
    </FilterByMetadata>
    <ItemGroup Condition=" '$(SelfContained)' != 'true' ">
      <_ReferencedLibraries Include="@(ReferencePath->'%(ResolvedPath)')" Exclude="@(_ReferencedLibrariesToExclude)" />
      <_ManagedAssembliesToLink Include="@(_ReferencedLibraries)" />
    </ItemGroup>

  </Target>


  <!-- Computes the managed subset of
       ResolvedAssembliesToPublish. This needs to preserve metadata,
       because it is later used to compute the linked assemblies with
       metadata that gets used by the rest of the publish
       pipeline. This needs to run even during incremental build,
       because we use the list of managed assemblies to filter the
       publish output. -->
  <UsingTask TaskName="ComputeManagedAssemblies" AssemblyFile="$(LinkTaskDllPath)" />
  <Target Name="_ComputeManagedResolvedAssembliesToPublish"
          DependsOnTargets="_RemoveDuplicatesFrom_ResolvedCopyLocalPublishAssets">
    <!-- TODO: Is there a better way to get the managed assemblies
         from ResolvedAssembliesToPublish? We may be able to check for
         AssetType="runtime" on managed assemblies - would that give
         the same set of assemblies? -->
    <ComputeManagedAssemblies Assemblies="@(_ResolvedCopyLocalPublishAssets)">
      <Output TaskParameter="ManagedAssemblies" ItemName="_ManagedResolvedAssembliesToPublish" />
    </ComputeManagedAssemblies>
    <!-- For now, hard-code System.Private.CoreLib.ni, the only .ni
         file we've been encountering. This is a special case matching
         how we compute the linker roots by default - we always root
         System.Private.CoreLib, so we want to keep it in the publish
         output and the generated deps.json file. Excluding it from
         _ManagedResolvedAssembliesToPublish will prevent it from
         getting filtered out of the publish output later.

         In the future we may want to detect ngen assemblies and
         filter them more robustly. -->
    <!-- TODO: Which .ni files do we expect to be in
         ResolvedAssembliesToPublish? -->
    <ItemGroup>
      <_ManagedResolvedAssembliesToPublish Remove="@(_ManagedResolvedAssembliesToPublish->WithMetadataValue('Filename', 'System.Private.CoreLib.ni'))" />
    </ItemGroup>
    <!-- Some of the managed dlls are satellite assemblies containing
         binary resources that we don't want to link. -->
    <ItemGroup>
      <_ManagedResolvedAssembliesToPublish Remove="@(_ManagedResolvedAssembliesToPublish->WithMetadataValue('AssetType', 'resources'))" />
    </ItemGroup>
  </Target>


  <!-- Compute the assemblies the linker should mark as roots. By
       default, these are passed as "-a", but this is an
       implementation detail of the link task and should not be relied
       upon in general. In the future we may add other inputs to the
       link task to control how these are rooted.
  -->
  <!-- We may want to add an additional mode in which we root only the
       assembly we have built, rooting all entry points visible from
       the outside of the assembly. We need to discuss whether this
       even makes sense during publish, since typically libraries get
       packaged via "dotnet pack", and the pack task takes input from
       build, not from publish. Thus linking the library itself should
       be part of the build step or pack step, OR pack could be made
       to work on the publish output. -->
  <Target Name="_ComputeLinkerRootAssemblies"
          DependsOnTargets="_ComputeManagedResolvedAssembliesToPublish;_ComputePlatformLibraries;_CheckSystemPrivateCorelibEmbeddedRoots">
    <!-- If RootAllApplicationAssemblies is true, roots are everything
         minus the framework assemblies. This doesn't include the
         intermediate assembly, because we root it separately using an
         xml file, which lets us explicitly root everything.

         If RootAllApplicationAssemblies is false, only intermediate
         assembly's Main method is rooted.

         System.Private.CoreLib is rooted unless it has an embedded
         xml descriptor file. -->

    <ItemGroup Condition=" '$(RootAllApplicationAssemblies)' == 'true' ">
      <_LinkerRootAssemblies Include="@(_ManagedResolvedAssembliesToPublish->'%(Filename)')" />
      <_LinkerRootAssemblies Remove="@(PlatformLibraries->'%(Filename)')" />
    </ItemGroup>
    <RemoveDuplicates Inputs="@(_LinkerRootAssemblies)"
                      Condition=" '$(RootAllApplicationAssemblies)' == 'true' ">
      <Output TaskParameter="Filtered" ItemName="_UniqueLinkerRootAssemblies" />
    </RemoveDuplicates>
    <ItemGroup Condition=" '$(RootAllApplicationAssemblies)' == 'true' ">
      <LinkerRootAssemblies Include="@(_UniqueLinkerRootAssemblies)" />
    </ItemGroup>

    <ItemGroup Condition=" '$(RootAllApplicationAssemblies)' == 'false' ">
      <LinkerRootAssemblies Include="@(IntermediateAssembly)" />
    </ItemGroup>

    <ItemGroup Condition=" '$(_SPCHasEmbeddedRootDescriptor)' != 'true' ">
      <LinkerRootAssemblies Include="@(_ManagedResolvedAssembliesToPublish->'%(Filename)')" Condition=" '%(Filename)' == 'System.Private.CoreLib' " />
    </ItemGroup>

  </Target>

  <UsingTask TaskName="CheckEmbeddedRootDescriptor" AssemblyFile="$(LinkTaskDllPath)" />
  <Target Name="_CheckSystemPrivateCorelibEmbeddedRoots" Condition=" '$(SelfContained)' == 'true' "
          DependsOnTargets="_ComputeManagedAssembliesToLink">
    <CheckEmbeddedRootDescriptor AssemblyPath="@(_ManagedAssembliesToLink->WithMetadataValue('Filename', 'System.Private.CoreLib'))">
      <Output TaskParameter="HasEmbeddedRootDescriptor" PropertyName="_SPCHasEmbeddedRootDescriptor" />
    </CheckEmbeddedRootDescriptor>
  </Target>

  <!-- Platform libraries are the managed runtime assets needed by the
       "platform", currently Microsoft.NETCore.App. -->
  <UsingTask TaskName="GetRuntimeLibraries" AssemblyFile="$(LinkTaskDllPath)" />
  <Target Name="_ComputePlatformLibraries"
          DependsOnTargets="_ComputeManagedAssembliesToLink;ResolveRuntimePackAssets">

    <ItemGroup>
      <PlatformLibraries Include="@(RuntimePackAssets)" />
    </ItemGroup>

    <ItemGroup>
      <PlatformLibraries Include="@(_ManagedAssembliesToLink)" Condition=" '%(Filename)' == 'System.Private.CoreLib' " />
    </ItemGroup>

    <ItemGroup Condition=" '$(SelfContained)' != 'true' ">
      <!-- Portable publish: computed referenced-not-published set in _ComputeManagedAssembliesToLink -->
      <PlatformLibraries Include="@(_ReferencedLibraries)" />
    </ItemGroup>
  </Target>


  <!-- This target prepares the LinkerRootDescriptors itemgroup for
       input to the link task. In addition to including user-specified
       LinkerRootDescriptors in the ItemGroup statically above, we
       dynamically include the generated descriptor files for the
       intermediate assembly. -->
  <Target Name="_ComputeLinkerRootDescriptors"
          DependsOnTargets="_GenerateIntermediateRootDescriptor"
          Condition=" '$(RootAllApplicationAssemblies)' == 'true' ">
    <ItemGroup>
      <LinkerRootDescriptors Include="$(_IntermediateRootDescriptorPath)" />
    </ItemGroup>
  </Target>


  <!-- Generates the xml root descriptor file that completely roots
       the intermediate assembly. -->
  <UsingTask TaskName="CreateRootDescriptorFile" AssemblyFile="$(LinkTaskDllPath)" />
  <Target Name="_GenerateIntermediateRootDescriptor"
          Inputs="@(IntermediateAssembly)"
          Outputs="$(_IntermediateRootDescriptorPath)"
          Condition=" '$(RootAllApplicationAssemblies)' == 'true' ">
    <CreateRootDescriptorFile AssemblyNames="@(IntermediateAssembly->'%(Filename)')"
                              RootDescriptorFilePath="$(_IntermediateRootDescriptorPath)" />
  </Target>

  <!-- Uses _PublishConflictPackageFiles, an input to the publish deps
       file generation target, to exclude the removed managed
       assemblies and native dependencies from the generated deps
       file. -->
  <UsingTask TaskName="ComputeRemovedAssemblies" AssemblyFile="$(LinkTaskDllPath)" />
  <Target Name="_ExcludeRemovedFilesFromDepFileGeneration"
          DependsOnTargets="_ComputeManagedResolvedAssembliesToPublish;_ComputeLinkedAssemblies;_FindNativeDeps;_HandlePackageFileConflictsForPublish"
          BeforeTargets="GeneratePublishDependencyFile"
          Condition=" '$(LinkDuringPublish)' == 'true' ">
    <ComputeRemovedAssemblies InputAssemblies="@(_ManagedAssembliesToLink)"
                              KeptAssemblies="@(_LinkedResolvedAssemblies)">
      <Output TaskParameter="RemovedAssemblies" ItemName="_RemovedManagedAssemblies" />
    </ComputeRemovedAssemblies>

    <ItemGroup>
      <_RemovedNativeDeps Include="@(_NativeResolvedDepsToPublish)" />
      <_RemovedNativeDeps Remove="@(_NativeKeptDepsToPublish)" />

      <_PublishConflictPackageFiles Include="@(_RemovedManagedAssemblies)" />
      <_PublishConflictPackageFiles Include="@(_RemovedNativeDeps)" />
    </ItemGroup>
  </Target>

</Project>
