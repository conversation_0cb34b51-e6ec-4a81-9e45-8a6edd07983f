{"runtimeTarget": {"name": ".NETCoreApp,Version=v2.0", "signature": ""}, "compilationOptions": {}, "targets": {".NETCoreApp,Version=v2.0": {"illink/0.1.6-prerelease.19380.1": {"dependencies": {"Microsoft.SourceLink.GitHub": "1.0.0-beta2-19367-01", "Microsoft.SourceLink.Vsts.Git": "1.0.0-beta2-19367-01", "Mono.Cecil": "0.1.6-prerelease.19380.1", "Mono.Cecil.Mdb": "0.1.6-prerelease.19380.1", "Mono.Cecil.Pdb": "0.1.6-prerelease.19380.1", "XliffTasks": "1.0.0-beta.19252.1"}, "runtime": {"illink.dll": {}}}, "Microsoft.Build.Tasks.Git/1.0.0-beta2-19367-01": {}, "Microsoft.SourceLink.Common/1.0.0-beta2-19367-01": {}, "Microsoft.SourceLink.GitHub/1.0.0-beta2-19367-01": {"dependencies": {"Microsoft.Build.Tasks.Git": "1.0.0-beta2-19367-01", "Microsoft.SourceLink.Common": "1.0.0-beta2-19367-01"}}, "Microsoft.SourceLink.Vsts.Git/1.0.0-beta2-19367-01": {"dependencies": {"Microsoft.Build.Tasks.Git": "1.0.0-beta2-19367-01", "Microsoft.SourceLink.Common": "1.0.0-beta2-19367-01"}}, "XliffTasks/1.0.0-beta.19252.1": {}, "Mono.Cecil/0.1.6-prerelease.19380.1": {"runtime": {"Mono.Cecil.dll": {}}}, "Mono.Cecil.Mdb/0.1.6-prerelease.19380.1": {"dependencies": {"Mono.Cecil": "0.1.6-prerelease.19380.1"}, "runtime": {"Mono.Cecil.Mdb.dll": {}}}, "Mono.Cecil.Pdb/0.1.6-prerelease.19380.1": {"dependencies": {"Mono.Cecil": "0.1.6-prerelease.19380.1"}, "runtime": {"Mono.Cecil.Pdb.dll": {}}}}}, "libraries": {"illink/0.1.6-prerelease.19380.1": {"type": "project", "serviceable": false, "sha512": ""}, "Microsoft.Build.Tasks.Git/1.0.0-beta2-19367-01": {"type": "package", "serviceable": true, "sha512": "sha512-3kbkb7aUF41YuJnQzoCJRbjb6bgYY3KHlJ9GGJZ30Y5ytdFusLAC5o3/kfE+Vm6slvu4EBgIwMUknL6U+Pu9uA==", "path": "microsoft.build.tasks.git/1.0.0-beta2-19367-01", "hashPath": "microsoft.build.tasks.git.1.0.0-beta2-19367-01.nupkg.sha512"}, "Microsoft.SourceLink.Common/1.0.0-beta2-19367-01": {"type": "package", "serviceable": true, "sha512": "sha512-T6ZEkbRgqcmDoTQDn0ES4FcXiq6uOiqPmbb+hCnKQ/i45W3WjM1+hfNGmsXvTK/e/AqEGiqtXJIi9ZtmbHnzHQ==", "path": "microsoft.sourcelink.common/1.0.0-beta2-19367-01", "hashPath": "microsoft.sourcelink.common.1.0.0-beta2-19367-01.nupkg.sha512"}, "Microsoft.SourceLink.GitHub/1.0.0-beta2-19367-01": {"type": "package", "serviceable": true, "sha512": "sha512-+Zfc8EddeIPTy9w26xrMOqIL5k5fPICfoYGPMhvlCcmENVT0pslIvrOzRaEvv1UgUL1cjbGRO8SXa1HtoVEhPA==", "path": "microsoft.sourcelink.github/1.0.0-beta2-19367-01", "hashPath": "microsoft.sourcelink.github.1.0.0-beta2-19367-01.nupkg.sha512"}, "Microsoft.SourceLink.Vsts.Git/1.0.0-beta2-19367-01": {"type": "package", "serviceable": true, "sha512": "sha512-vfYRwh2jIQ5XFmqk9BebaGnj3tL9p1hkZ270NMXutiE7jCGH1zMB+3HCPec6DpnC4V3XX1oWlwAXoxNtXB90pQ==", "path": "microsoft.sourcelink.vsts.git/1.0.0-beta2-19367-01", "hashPath": "microsoft.sourcelink.vsts.git.1.0.0-beta2-19367-01.nupkg.sha512"}, "XliffTasks/1.0.0-beta.19252.1": {"type": "package", "serviceable": true, "sha512": "sha512-pf1QwugyHdppWF5Q+qLSIqFkPkyBJOTAbvDxMO7Cv8zRkLGR9/OfOrjgSrcnniA2OTfZVo7JzTdNbDNAKSRZIA==", "path": "xlifftasks/1.0.0-beta.19252.1", "hashPath": "xlifftasks.1.0.0-beta.19252.1.nupkg.sha512"}, "Mono.Cecil/0.1.6-prerelease.19380.1": {"type": "project", "serviceable": false, "sha512": ""}, "Mono.Cecil.Mdb/0.1.6-prerelease.19380.1": {"type": "project", "serviceable": false, "sha512": ""}, "Mono.Cecil.Pdb/0.1.6-prerelease.19380.1": {"type": "project", "serviceable": false, "sha512": ""}}}