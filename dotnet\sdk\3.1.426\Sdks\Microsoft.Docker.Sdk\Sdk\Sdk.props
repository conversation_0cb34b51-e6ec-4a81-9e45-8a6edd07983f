<?xml version="1.0" encoding="utf-8"?>

<!--
***********************************************************************************************
Sdk.props

WARNING:  DO NOT MODIFY this file unless you are knowledgeable about MSBuild and have
          created a backup copy.  Incorrect changes to this file will make it
          impossible to load or build your projects from the command-line or the IDE.

Copyright (c) Microsoft Corporation. All rights reserved.
Licensed under the MIT license.
***********************************************************************************************
-->

<Project xmlns="http://schemas.microsoft.com/developer/msbuild/2003">

  <PropertyGroup>
    <MSBuildAllProjects>$(MSBuildAllProjects);$(MSBuildThisFileFullPath)</MSBuildAllProjects>
  </PropertyGroup>

  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" />

  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <OutputPath Condition=" '$(OutputPath)' == '' ">bin\$(Configuration)\</OutputPath>
    <OutputType>DockerCompose</OutputType>
    <SkipCopyBuildProduct>true</SkipCopyBuildProduct>
    <AddAdditionalExplicitAssemblyReferences>false</AddAdditionalExplicitAssemblyReferences>
    <DebugSymbols>false</DebugSymbols>
    <TargetRuntime>None</TargetRuntime>
  </PropertyGroup>

  <PropertyGroup>
    <ResolveNuGetPackages>false</ResolveNuGetPackages>
    <SkipImportNuGetProps>true</SkipImportNuGetProps>
    <SkipImportNuGetBuildTargets>true</SkipImportNuGetBuildTargets>
  </PropertyGroup>

  <Import Project="$(MSBuildExtensionsPath)\Sdks\Microsoft.Docker.Sdk\build\Microsoft.VisualStudio.Docker.Compose.props"
          Condition="Exists('$(MSBuildExtensionsPath)\Sdks\Microsoft.Docker.Sdk\build\Microsoft.VisualStudio.Docker.Compose.props')" />

</Project>
