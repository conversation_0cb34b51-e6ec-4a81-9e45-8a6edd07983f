<?xml version="1.0" encoding="utf-8"?>

<!--
***********************************************************************************************
Sdk.targets

WARNING:  DO NOT MODIFY this file unless you are knowledgeable about MSBuild and have
          created a backup copy.  Incorrect changes to this file will make it
          impossible to load or build your projects from the command-line or the IDE.

Copyright (c) Microsoft Corporation. All rights reserved.
Licensed under the MIT license.
***********************************************************************************************
-->

<Project xmlns="http://schemas.microsoft.com/developer/msbuild/2003">

  <PropertyGroup>
    <MSBuildAllProjects>$(MSBuildAllProjects);$(MSBuildThisFileFullPath)</MSBuildAllProjects>
  </PropertyGroup>

  <Import Project="$(MSBuildToolsPath)\Microsoft.Common.targets" />

  <Target Name="CoreCompile" />

  <Target Name="CreateManifestResourceNames" />

  <Import Project="$(MSBuildExtensionsPath)\Sdks\Microsoft.Docker.Sdk\build\Microsoft.VisualStudio.Docker.Compose.targets"
          Condition="Exists('$(MSBuildExtensionsPath)\Sdks\Microsoft.Docker.Sdk\build\Microsoft.VisualStudio.Docker.Compose.targets')"/>

</Project>
