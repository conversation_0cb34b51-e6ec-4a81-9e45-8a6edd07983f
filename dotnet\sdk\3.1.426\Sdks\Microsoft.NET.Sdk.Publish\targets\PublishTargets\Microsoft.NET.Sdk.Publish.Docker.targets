﻿<!--
***********************************************************************************************
Microsoft.NET.Sdk.Publish.Docker.targets

WARNING:  DO NOT MODIFY this file unless you are knowledgeable about MSBuild and have
          created a backup copy.  Incorrect changes to this file will make it
          impossible to load or build your web deploy projects from the command-line or the IDE.

This file defines the steps in the standard build process to deploy web application projects.

Copyright (C) Microsoft Corporation. All rights reserved.
***********************************************************************************************
-->

<Project xmlns="http://schemas.microsoft.com/developer/msbuild/2003">

  <PropertyGroup>
    <_DotNetPublishFiles>
      DockerPublish;
    </_DotNetPublishFiles>
  </PropertyGroup>

  <!--
  ***********************************************************************************************
  TARGET : DockerPublish
  ***********************************************************************************************
 -->
  <PropertyGroup>
    <DockerPublishDependsOn>
      $(DockerPublishDependsOn);
      _InitDockerComposeBuildSource;
      DockerComposeBuild;
    </DockerPublishDependsOn>
  </PropertyGroup>

  <Target Name="DockerPublish"
          DependsOnTargets="$(DockerPublishDependsOn)" />

  <!--
  ***********************************************************************************************
  TARGET : _InitDockerComposeBuildSource
  ***********************************************************************************************
 -->

  <Target Name="_InitDockerComposeBuildSource">
    <PropertyGroup>
      <DockerComposeBuildSource>$(PublishIntermediateOutputPath)</DockerComposeBuildSource>
    </PropertyGroup>

  </Target>

</Project>