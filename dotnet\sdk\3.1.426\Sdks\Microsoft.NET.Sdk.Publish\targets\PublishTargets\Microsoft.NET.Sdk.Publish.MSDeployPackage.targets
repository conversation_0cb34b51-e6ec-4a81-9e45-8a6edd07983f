﻿<!--
***********************************************************************************************
Microsoft.NET.Sdk.Publish.MSDeployPackage.targets

WARNING:  DO NOT MODIFY this file unless you are knowledgeable about MSBuild and have
          created a backup copy.  Incorrect changes to this file will make it
          impossible to load or build your web deploy projects from the command-line or the IDE.

This file defines the steps in the standard build process to deploy web application projects.

Copyright (C) Microsoft Corporation. All rights reserved.
***********************************************************************************************
-->

<Project xmlns="http://schemas.microsoft.com/developer/msbuild/2003">

  <!--
  ***********************************************************************************************
  Including the tasks
  ***********************************************************************************************
 -->
  <UsingTask TaskName="MSDeploy" AssemblyFile="$(_PublishTaskAssemblyFullPath)"/>
  <UsingTask TaskName="VSMSDeploy" AssemblyFile="$(_PublishTaskAssemblyFullPath)"/>
  <UsingTask TaskName="NormalizeServiceUrl" AssemblyFile="$(_PublishTaskAssemblyFullPath)"/>
  <UsingTask TaskName="ValidateParameter" AssemblyFile="$(_PublishTaskAssemblyFullPath)"/>
  <UsingTask TaskName="CreateManifestFile" AssemblyFile="$(_PublishTaskAssemblyFullPath)" />
  <UsingTask TaskName="ImportParameterFile" AssemblyFile="$(_PublishTaskAssemblyFullPath)" />
  <UsingTask TaskName="CreateParameterFile" AssemblyFile="$(_PublishTaskAssemblyFullPath)" />
  <UsingTask TaskName="CreateMSDeployScript" AssemblyFile="$(_PublishTaskAssemblyFullPath)" />

  <PropertyGroup>
    <_DotNetPublishFiles>
      MSDeployPackagePublish;
    </_DotNetPublishFiles>
  </PropertyGroup>

  <!--
  ***********************************************************************************************
  TARGET : MSDeployPackagePublish
  ***********************************************************************************************
 -->
  <PropertyGroup>
    <MSDeployPackagePublishDependsOn>
      $(MSDeployPackagePublishDependsOn);
      _InitMSDeployPackageProperties;
      _PrepareForMsDeployPublish;
      _CreateManifestFiles;
      _CreateParameterFiles;
      _CreateMSDeployScript;
    </MSDeployPackagePublishDependsOn>
  </PropertyGroup>

  <Target Name="MSDeployPackagePublish" DependsOnTargets="$(MSDeployPackagePublishDependsOn)">

    <PropertyGroup>
      <MSDeployPublishSourceType>manifest</MSDeployPublishSourceType>
      <MSDeployPublishSourceRoot>$(_MsDeploySourceManifestPath)</MSDeployPublishSourceRoot>
      <!--Destination is alway a manifest-->
      <MSDeployPublishDestinationType>Package</MSDeployPublishDestinationType>
      <MSDeployPublishDestinationRoot>$(PackageLocation)</MSDeployPublishDestinationRoot>
      <RetryAttemptsForDeployment Condition=" '$(RetryAttemptsForDeployment)' == '' ">2</RetryAttemptsForDeployment>
      <PublishEnableLinks Condition="'$(PublishEnableLinks)' == ''"></PublishEnableLinks>
      <PublishDisableLinks Condition="'$(PublishDisableLinks)' == ''">AppPoolExtension;ContentExtension;CertificateExtension</PublishDisableLinks>
      <!-- UserAgent string sent to msdeploy -->
      <_MSDeployUserAgentSource>CmdLine</_MSDeployUserAgentSource>
      <_MSDeployUserAgent>VS$(_MSDeployUserAgentSource)</_MSDeployUserAgent>
      <!-- MsDeploy Api is not supported for core yet -->
      <UseMsDeployExe Condition="'$(UseMsDeployExe)' == '' And '$(MSBuildRuntimeType)' == 'Core'">true</UseMsDeployExe>
    </PropertyGroup>

    <!--  Data Passed to MSDeploy -->
    <ItemGroup>
      <MsDeploySourceProviderSetting Remove="@(MsDeploySourceProviderSetting)" />
      <MsDeploySourceProviderSetting Include="$(MSDeployPublishSourceType)">
        <Path>$(MSDeployPublishSourceRoot)</Path>
      </MsDeploySourceProviderSetting>

      <MsDeployDestinationProviderSetting Remove="@(MsDeployDestinationProviderSetting)" />
      <MsDeployDestinationProviderSetting Include="$(MSDeployPublishDestinationType)">
        <Path>$(MSDeployPublishDestinationRoot)</Path>
      </MsDeployDestinationProviderSetting>
    </ItemGroup>

    <VSMSDeploy Condition="'$(UseMsdeployExe)' != 'true'"
       MSDeployVersionsToTry="$(_MSDeployVersionsToTry)"
       Source="@(MsDeploySourceProviderSetting)"
       Destination="@(MsDeployDestinationProviderSetting)"
       DeploymentTraceLevel="$(PackageTraceLevel)"
       DisableLink="$(PublishDisableLinks)"
       EnableLink="$(PublishEnableLinks)"
       ImportDeclareParametersItems="$(_MSDeployParametersFilePath)"
       ReplaceRuleItems="@(MsDeployReplaceRules)"
       RetryAttempts="$(RetryAttemptsForDeployment)">
      <Output TaskParameter="Result" PropertyName="PackageResult" />
    </VSMSDeploy>

    <MSdeploy
      Condition="'$(UseMsdeployExe)' == 'true'"
      Verb="sync"
      Source="@(MsDeploySourceProviderSetting)"
      Destination="@(MsDeployDestinationProviderSetting)"
      DisableLink="$(PublishDisableLinks)"
      EnableLink="$(PublishEnableLinks)"
      ImportDeclareParametersItems="$(_MSDeployParametersFilePath)"
      ReplaceRuleItems="@(MsDeployReplaceRules)"
      RetryAttempts="$(RetryAttemptsForDeployment)"
      ExePath="$(MSDeployPath)"
      />
  </Target>

  <!--
  ***********************************************************************************************
  TARGET : _InitMSDeployPackageProperties
  ***********************************************************************************************
 -->

  <Target Name="_InitMSDeployPackageProperties">
   
    <PropertyGroup>
      <DefaultPackageFileName Condition="'$(DefaultPackageFileName)'==''">$(MSBuildProjectName).zip</DefaultPackageFileName>
      <PackageLocation Condition="'$(PackageLocation)' == ''">$(DesktopBuildPackageLocation)</PackageLocation>
      <PackageLocation Condition="'$(PackageLocation)' == ''">$(OutputPath)</PackageLocation>
      <PackageLocation Condition="'$([System.IO.Path]::GetExtension($(PackageLocation)))' == '' And !HasTrailingSlash('$(PackageLocation)')">$(PackageLocation)\</PackageLocation>
      <PackageLocation Condition="'$([System.IO.Path]::GetExtension($(PackageLocation)))' == '' ">$(PackageLocation)$(DefaultPackageFileName)</PackageLocation>
      <DeployIisAppPath Condition="'$(DeployIisAppPath)' ==''">Default Web Site</DeployIisAppPath>
      <EnableOptimisticParameterDefaultValue Condition="'$(EnableOptimisticParameterDefaultValue)'==''">True</EnableOptimisticParameterDefaultValue>
    </PropertyGroup>

    <ConvertToAbsolutePath Paths="$(PackageLocation)">
      <Output TaskParameter="AbsolutePaths" PropertyName="PackageLocation"/>
    </ConvertToAbsolutePath>

  </Target>
  
  <!--
  ***********************************************************************************************
  TARGET : _PrepareForMsDeployPackagePublish
  ***********************************************************************************************
 -->

  <Target Name="_PrepareForMsDeployPublish">

    <PropertyGroup>
      <_destinationFolder Condition="'$(_destinationFolder)' == ''">$([System.IO.Path]::GetDirectoryName($(PackageLocation)))\</_destinationFolder>
      <_MsDeploySourceManifestPath Condition="'$(_MsDeploySourceManifestPath)' == ''">$(_destinationFolder)$(MSBuildProjectName).SourceManifest.xml</_MsDeploySourceManifestPath>
      <_MSDeployParametersFilePath Condition="'$(_MSDeployParametersFilePath)' == ''">$(_destinationFolder)$(MSBuildProjectName).Parameters.xml</_MSDeployParametersFilePath>
      <_MSDeploySetParametersFilePath Condition="'$(_MSDeploySetParametersFilePath)' == ''">$(_destinationFolder)$(MSBuildProjectName).SetParameters.xml</_MSDeploySetParametersFilePath>
      <_MSDeployScriptFilePath Condition="'$(_MSDeployScriptFilePath)' == ''">$(_destinationFolder)$(MSBuildProjectName).deploy.cmd</_MSDeployScriptFilePath>
      <_MSDeployReadMeFilePath Condition="'$(_MSDeployReadMeFilePath)' == ''">$(_destinationFolder)$(MSBuildProjectName).deploy-readme.txt</_MSDeployReadMeFilePath>
    </PropertyGroup>

    <ItemGroup>
      <_PublishConfigFiles Include="$(_MsDeploySourceManifestPath);$(_MSDeployParametersFilePath);$(_MSDeploySetParametersFilePath);$(_MSDeployScriptFilePath);$(_MSDeployReadMeFilePath)" />
    </ItemGroup>
    
    <MakeDir Directories="$(_destinationFolder)" Condition="!Exists('$(_destinationFolder)')" ContinueOnError="true"/>
    <Delete Files="@(_PublishConfigFiles)" ContinueOnError="true" />
    <Touch AlwaysCreate="true" Files="@(_PublishConfigFiles)" ContinueOnError="true" />
  </Target>

  <!--
  ***********************************************************************************************
  TARGET : _CreateManifestFiles
  ***********************************************************************************************
 -->

  <Target Name="_CreateManifestFiles">

    <ItemGroup>
      <MsDeploySourceManifest Remove="@(MsDeploySourceManifest)" />

      <MsDeploySourceManifest Include="IisApp" >
        <Path>$(PublishIntermediateOutputPath)</Path>
      </MsDeploySourceManifest>
    </ItemGroup>

    <ItemGroup Condition="'@(_EFSQLScripts)' != ''">
      <MsDeploySourceManifest Include="dbfullsql" >
        <Path>%(_EFSQLScripts.Identity)</Path>
      </MsDeploySourceManifest>
    </ItemGroup>

    <CreateManifestFile
      Manifests="@(MsDeploySourceManifest)"
      ManifestFile="$(_MsDeploySourceManifestPath)" />
  </Target>
  
    <!--
  ***********************************************************************************************
  TARGET : _CreateMSDeployScript
  ***********************************************************************************************
 -->

  <Target Name="_CreateMSDeployScript">

    <CreateMsDeployScript
      Condition="'$(IsCreateMSDeployScriptDisabled)' != 'true'"
      ProjectName="$(MSBuildProjectName)"
      ScriptFullPath="$(_MSDeployScriptFilePath)" 
      ReadMeFullPath="$(_MSDeployReadMeFilePath)"/>
      
  </Target>

  <!--
  ***********************************************************************************************
  TARGET : _CreateParameterFiles
  ***********************************************************************************************
 -->

  <Target Name="_CreateParameterFiles">

    <ItemGroup>
      <MsDeployDeclareParameters Remove="@(MsDeployDeclareParameters)" />

      <MsDeployDeclareParameters Include="IIS Web Application Name" >
        <Kind>ProviderPath</Kind>
        <Scope>IisApp</Scope>
        <Match>$(PublishIntermediateOutputPath)</Match>
        <Description></Description>
        <DefaultValue>$(DeployIisAppPath)</DefaultValue>
        <Value>$(DeployIisAppPath)</Value>
        <Tags>IisApp</Tags>
        <Priority></Priority>
        <ExcludeFromSetParameter>false</ExcludeFromSetParameter>
      </MsDeployDeclareParameters>
    </ItemGroup>

    <ItemGroup Condition="'@(_EFSQLScripts)' != ''">
      <MsDeployDeclareParameters Include="%(_EFSQLScripts.DBContext)">
        <Kind>ProviderPath</Kind>
        <Scope>dbfullsql</Scope>
        <Match>%(_EFSQLScripts.Identity)</Match>
        <Description></Description>
        <DefaultValue>%(_EFSQLScripts.ConnectionString)</DefaultValue>
        <Value>%(_EFSQLScripts.ConnectionString)</Value>
        <Tags>dbfullsql</Tags>
        <Priority></Priority>
        <ExcludeFromSetParameter>false</ExcludeFromSetParameter>
      </MsDeployDeclareParameters>
    </ItemGroup>

    <ItemGroup>
      <ParametersXMLFiles Include="$(MSBuildProjectDirectory)\Parameters.xml" 
                          Condition="Exists('$(MSBuildProjectDirectory)\Parameters.xml')"/>
    </ItemGroup>

    <ImportParameterFile Condition="'@(ParametersXMLFiles)' != ''" 
                         Files="@(ParametersXMLFiles)">
      <Output TaskParameter="Result"
              ItemName="_ImportedMSDeployDeclareParameters" />
    </ImportParameterFile>

    <ItemGroup>
      <MsDeployDeclareParameters Include="@(_ImportedMSDeployDeclareParameters)"
                                 Condition="'%(_ImportedMSDeployDeclareParameters.Identity)' !=''">
        <Value>%(_ImportedMSDeployDeclareParameters.DefaultValue)</Value>
        <Priority></Priority>
      </MsDeployDeclareParameters>
    </ItemGroup>
    
    <CreateParameterFile
      Parameters="@(MsDeployDeclareParameters)"
      DeclareSetParameterFile="$(_MSDeployParametersFilePath)"
      IncludeDefaultValue="True"
      OptimisticParameterDefaultValue="$(EnableOptimisticParameterDefaultValue)"
      SetParameterFile="$(_MSDeploySetParametersFilePath)"
      GenerateFileEvenIfEmpty="True" />
  </Target>

  <!--
  ***********************************************************************************************
  Item Definitions
  ***********************************************************************************************
 -->

  <ItemDefinitionGroup>
    <MsDeploySourceProviderSetting>
      <Path></Path>
      <ComputerName></ComputerName>
      <!--<Wmsvc></Wmsvc>  Not supported yet-->
      <UserName></UserName>
      <Password></Password>
      <EncryptPassword></EncryptPassword>
      <IncludeAcls></IncludeAcls>
      <authType></authType>
      <prefetchPayload></prefetchPayload>
    </MsDeploySourceProviderSetting>
  </ItemDefinitionGroup>
  
  <ItemDefinitionGroup>
    <MsDeployDestinationProviderSetting>
      <Path></Path>
      <ComputerName></ComputerName>
      <!--<Wmsvc></Wmsvc>  Not supported yet-->
      <UserName></UserName>
      <Password></Password>
      <EncryptPassword></EncryptPassword>
      <IncludeAcls></IncludeAcls>
      <authType></authType>
      <prefetchPayload></prefetchPayload>
    </MsDeployDestinationProviderSetting>
  </ItemDefinitionGroup>
  
  <!--
  DeploymentSkipRule(string skipAction, string objectName, string absolutePath, string XPath);-->
  <ItemDefinitionGroup>
    <MsDeploySkipRules>
      <SkipAction></SkipAction>
      <ObjectName></ObjectName>
      <AbsolutePath></AbsolutePath>
      <XPath></XPath>
      <KeyAttribute></KeyAttribute>
      <!--Source, Destination, Both(the default)-->
      <Apply></Apply>
    </MsDeploySkipRules>
  </ItemDefinitionGroup>

  <!-- AdditionalProviderSettings denote the additionProviderSetting need to be set in the manifest files-->
  <ItemDefinitionGroup>
    <MsDeploySourceManifest>
      <Path>Unknown</Path>
      <AdditionalProviderSettings></AdditionalProviderSettings>
    </MsDeploySourceManifest>
  </ItemDefinitionGroup>
</Project>
