﻿<!--
***********************************************************************************************
Sdk.Razor.CurrentVersion.MultiTargeting.targets

WARNING:  DO NOT MODIFY this file unless you are knowledgeable about MSBuild and have
          created a backup copy.  Incorrect changes to this file will make it
          impossible to load or build your projects from the command-line or the IDE.

Copyright (c) .NET Foundation. All rights reserved.
***********************************************************************************************
-->
<Project ToolsVersion="14.0">

  <Target Name="RazorCompile">
    <Error Text="The 'RazorCompile' target is not supported without specifying a target framework. The current project targets multiple frameworks, please specify a framework to execute this target." />
  </Target>

  <Target Name="RazorGenerate">
    <Error Text="The 'RazorGenerate' target is not supported without specifying a target framework. The current project targets multiple frameworks, please specify a framework to execute this target." />
  </Target>

</Project>
