<!--
***********************************************************************************************
Microsoft.NET.Sdk.Razor.CodeGeneration

WARNING:  DO NOT MODIFY this file unless you are knowledgeable about MSBuild and have
          created a backup copy.  Incorrect changes to this file will make it
          impossible to load or build your projects from the command-line or the IDE.

Copyright (c) .NET Foundation. All rights reserved.
***********************************************************************************************
-->

<Project ToolsVersion="14.0">
  <!--
    Targets used for Razor SDK code generation. Support for the RazorCoreGenerate target.
    This target is explicitly imported by Razor SDK.
  -->

  <UsingTask
    TaskName="Microsoft.AspNetCore.Razor.Tasks.SdkRazorGenerate"
    AssemblyFile="$(RazorSdkBuildTasksAssembly)"
    Condition="'$(RazorSdkBuildTasksAssembly)' != ''" />

  <UsingTask
    TaskName="Microsoft.AspNetCore.Razor.Tasks.SdkRazorTagHelper"
    AssemblyFile="$(RazorSdkBuildTasksAssembly)"
    Condition="'$(RazorSdkBuildTasksAssembly)' != ''" />

  <!--
    Consider these properties to be private to this targets file. The main Razor SDK should define all of the properties
    that we use to pass data back and forth.
  -->
  <PropertyGroup>
    <!-- Used for tag helper discovery -->
    <_RazorTagHelperInputCache>$(IntermediateOutputPath)$(TargetName).TagHelpers.input.cache</_RazorTagHelperInputCache>
    <_RazorTagHelperOutputCache>$(IntermediateOutputPath)$(TargetName).TagHelpers.output.cache</_RazorTagHelperOutputCache>

    <!-- Used to hash file inputs for RazorGenerate -->
    <_RazorGenerateInputsHash></_RazorGenerateInputsHash>
    <_RazorGenerateInputsHashFile>$(IntermediateOutputPath)$(MSBuildProjectName).RazorCoreGenerate.cache</_RazorGenerateInputsHashFile>
  </PropertyGroup>

  <!--
    This target will only be called when we have some .cshtml files that are going to participate in code generation.

    This is part of the chain of targets that are called once we've actually committed to generating code.
  -->
  <Target
    Name="_HashRazorGenerateInputs"
    Condition="'@(RazorGenerateWithTargetPath)' != ''">

    <Hash ItemsToHash="@(RazorGenerateWithTargetPath)">
      <Output TaskParameter="HashResult" PropertyName="_RazorGenerateInputsHash" />
    </Hash>

    <MakeDir
      Directories="$(IntermediateOutputPath)"
      Condition="!Exists('$(IntermediateOutputPath)')" />

    <WriteLinesToFile
      Lines="$(_RazorGenerateInputsHash)"
      File="$(_RazorGenerateInputsHashFile)"
      Overwrite="True"
      WriteOnlyWhenDifferent="True" />

    <ItemGroup>
      <FileWrites Include="$(_RazorGenerateInputsHashFile)" />
    </ItemGroup>
  </Target>

   <!-- Force a Compile to happen if we are not doing a components build. This keeps parity with 2.x CodeGeneration targets -->
   <Target
    Name="_RazorEnsureCompiled"
    Condition="'$(_RazorComponentDeclarationAssemblyFullPath)' == ''"
    DependsOnTargets="Compile" />

  <Target
    Name="ResolveTagHelperRazorGenerateInputs"
    Inputs="$(MSBuildAllProjects);@(RazorReferencePath)"
    DependsOnTargets="_RazorEnsureCompiled"
    Outputs="$(_RazorTagHelperInputCache)"
    Condition="'@(RazorGenerateWithTargetPath)' != ''">

    <!--
      We're manipulating our output directly here because we want to separate the actual up-to-date check
      of RazorCoreGenerate from the output of this target. Many times the set of tag helpers doesn't change
      so we don't need to regenerate the code.
      -->
    <Touch
      Files="$(_RazorTagHelperInputCache)"
      AlwaysCreate="true" />

    <ItemGroup>
      <FileWrites Include="$(_RazorTagHelperInputCache)" />
    </ItemGroup>

    <SdkRazorTagHelper
      Debug="$(_RazorDebugTagHelperTask)"
      DebugTool="$(_RazorDebugTagHelperTool)"
      ToolAssembly="$(_RazorSdkToolAssembly)"
      UseServer="$(UseRazorBuildServer)"
      ForceServer="$(_RazorForceBuildServer)"
      PipeName="$(_RazorBuildServerPipeName)"
      Version="$(RazorLangVersion)"
      Configuration="@(ResolvedRazorConfiguration)"
      Extensions="@(ResolvedRazorExtension)"
      Assemblies="@(RazorReferencePath)"
      ProjectRoot="$(MSBuildProjectDirectory)"
      TagHelperManifest="$(_RazorTagHelperOutputCache)">
      <Output
        TaskParameter="TagHelperManifest"
        ItemName="FileWrites"/>
    </SdkRazorTagHelper>
  </Target>

  <Target Name="_ResolveRazorGenerateOutputs" Condition="'@(RazorGenerateWithTargetPath)' != ''">
    <Error
      Text="RazorGenerateWithTargetPath item '%(RazorGenerateWithTargetPath.Identity)' does not specify required metadata 'GeneratedOutput'."
      Condition="'%(RazorGenerateWithTargetPath.GeneratedOutput)' == ''" />

    <ItemGroup>
      <_RazorGenerateOutput Include="%(RazorGenerateWithTargetPath.GeneratedOutput)" />
    </ItemGroup>
  </Target>

  <PropertyGroup>
    <RazorCoreGenerateDependsOn>
      _HashRazorGenerateInputs;
      _ResolveRazorGenerateOutputs;
    </RazorCoreGenerateDependsOn>
  </PropertyGroup>

  <Target
    Name="RazorCoreGenerate"
    DependsOnTargets="$(RazorCoreGenerateDependsOn)"
    Inputs="$(MSBuildAllProjects);$(_RazorGenerateInputsHashFile);$(_RazorTagHelperOutputCache);@(RazorGenerateWithTargetPath)"
    Outputs="@(_RazorGenerateOutput)"
    Condition="'@(RazorGenerateWithTargetPath)'!= ''">

    <RemoveDir
      Directories="$(RazorGenerateIntermediateOutputPath)"
      Condition = "Exists('$(RazorGenerateIntermediateOutputPath)')"/>

    <MakeDir
      Directories="%(_RazorGenerateOutput.RelativeDir)"
      Condition="!Exists('%(_RazorGenerateOutput.RelativeDir)')" />

    <SdkRazorGenerate
      Debug="$(_RazorDebugGenerateCodeTask)"
      DebugTool="$(_RazorDebugGenerateCodeTool)"
      ToolAssembly="$(_RazorSdkToolAssembly)"
      UseServer="$(UseRazorBuildServer)"
      ForceServer="$(_RazorForceBuildServer)"
      PipeName="$(_RazorBuildServerPipeName)"
      Version="$(RazorLangVersion)"
      RootNamespace="$(RootNamespace)"
      CSharpLanguageVersion="$(LangVersion)"
      Configuration="@(ResolvedRazorConfiguration)"
      Extensions="@(ResolvedRazorExtension)"
      Sources="@(RazorGenerateWithTargetPath)"
      ProjectRoot="$(MSBuildProjectDirectory)"
      TagHelperManifest="$(_RazorTagHelperOutputCache)" />

    <ItemGroup>
      <FileWrites Include="@(_RazorGenerateOutput)" />
    </ItemGroup>
  </Target>

  <PropertyGroup>
    <ResolveRazorCompileInputsDependsOn>$(ResolveRazorCompileInputsDependsOn);_ResolveGeneratedRazorCompileInputs</ResolveRazorCompileInputsDependsOn>
  </PropertyGroup>

  <Target Name="_ResolveGeneratedRazorCompileInputs">
    <ItemGroup>
      <RazorCompile
        Include="%(RazorGenerateWithTargetPath.GeneratedOutput)"
        Condition="'%(RazorGenerateWithTargetPath.DocumentKind)'=='mvc'" />
    </ItemGroup>
  </Target>

</Project>
