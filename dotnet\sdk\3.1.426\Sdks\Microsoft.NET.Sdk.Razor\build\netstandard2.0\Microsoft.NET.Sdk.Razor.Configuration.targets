<!--
***********************************************************************************************
Microsoft.Net.Sdk.Razor.Versions.targets

WARNING:  DO NOT MODIFY this file unless you are knowledgeable about MSBuild and have
          created a backup copy.  Incorrect changes to this file will make it
          impossible to load or build your projects from the command-line or the IDE.

Copyright (c) .NET Foundation. All rights reserved.
***********************************************************************************************
-->
<Project ToolsVersion="14.0">

  <!--
      Initialize properties and items inferred using the project version. This file is not imported in projects referencing
      MVC \ Razor 2.2 or earlier since values specified here are provided via targets and props imported from NuGet packages
      such as Microsoft.AspNetCore.Razor.Design.

      The properties and items here are designed to be read by CPS so they should be just simple evaluation-time values
      and should not require targets to initialize.

      Also, these values are based on whether the library is targeting netcoreapp3.0 for now. A better heuristic
      would be to detect whether the ASP.NET Core shared framework is referenced.
  -->
  <PropertyGroup>
    <!-- Continue setting this property to maintain compat with legacy Razor.Design -->
    <IsRazorCompilerReferenced Condition="'$(IsRazorCompilerReferenced)'==''">true</IsRazorCompilerReferenced>
  </PropertyGroup>

  <!--
      Resolve MVC specific configuration depending on the presence of MVC specific Razor files.
  -->
  <Target Name="_ResolveMvcAssemblyAttributes"
    DependsOnTargets="ResolveRazorGenerateInputs"
    Condition="'$(Language)' == 'C#' AND '$(AddRazorSupportForMvc)' == 'true'">

   <PropertyGroup Condition="'@(RazorGenerate->Count())' != '0'">
      <!--
        MVC uses a ProvideApplicationPartFactoryAttribute on the generated assembly to load compiled views from assembly. Set this to false, to prevent generating this attribute.
      -->
      <GenerateProvideApplicationPartFactoryAttribute Condition="'$(GenerateProvideApplicationPartFactoryAttribute)'==''">true</GenerateProvideApplicationPartFactoryAttribute>

      <!--
        Determines if the Sdk is allowed to add additional attributes to the project assembly.
        For instance, MVC will generally want to add attributes to support view discovery and runtime compilation.
      -->
      <GenerateRazorAssemblyInfo Condition="'$(GenerateRazorAssemblyInfo)'==''">true</GenerateRazorAssemblyInfo>

      <!--
        The type name of the ApplicationPartFactory applied to the generated Razor file.
      -->
      <ProvideApplicationPartFactoryAttributeTypeName Condition="'$(ProvideApplicationPartFactoryAttributeTypeName)' == ''">Microsoft.AspNetCore.Mvc.ApplicationParts.CompiledRazorAssemblyApplicationPartFactory, Microsoft.AspNetCore.Mvc.Razor</ProvideApplicationPartFactoryAttributeTypeName>
    </PropertyGroup>

    <ItemGroup Condition="'$(GenerateProvideApplicationPartFactoryAttribute)' == 'true' AND '$(ProvideApplicationPartFactoryAttributeTypeName)'!=''">
      <RazorTargetAssemblyAttribute Include="Microsoft.AspNetCore.Mvc.ApplicationParts.ProvideApplicationPartFactoryAttribute">
        <_Parameter1>$(ProvideApplicationPartFactoryAttributeTypeName)</_Parameter1>
      </RazorTargetAssemblyAttribute>
    </ItemGroup>

    <ItemGroup Condition="'$(GenerateRazorAssemblyInfo)'=='true' AND '$(ResolvedRazorCompileToolset)'=='RazorSdk' AND ('$(RazorCompileOnBuild)' == 'true' OR '$(RazorCompileOnPublish)' == 'true')">
      <_RazorAssemblyAttribute Include="Microsoft.AspNetCore.Mvc.ApplicationParts.RelatedAssemblyAttribute">
        <_Parameter1>$(RazorTargetName)</_Parameter1>
      </_RazorAssemblyAttribute>
    </ItemGroup>
  </Target>

  <!--
    Determine the default Razor configuration
  -->
  <PropertyGroup Condition="'$(RazorDefaultConfiguration)'==''">
    <RazorDefaultConfiguration Condition="'$(AddRazorSupportForMvc)'=='true'">MVC-3.0</RazorDefaultConfiguration>
    <RazorDefaultConfiguration Condition="'$(RazorDefaultConfiguration)'==''">Default</RazorDefaultConfiguration>
  </PropertyGroup>

  <ItemGroup>
    <!--
      While technically the assembly in this package can provide support for the MVC-2.X, don't declare
      it here. The IDE is hardcoded to inject 2.0 support when needed. The settings flowing through MSBuild should reflect
      the project's runtime.
    -->
    <RazorConfiguration Include="Default" />
    <RazorConfiguration Include="MVC-3.0" >
      <Extensions>MVC-3.0;$(CustomRazorExtension)</Extensions>
    </RazorConfiguration>
  </ItemGroup>

  <ItemGroup>
    <RazorExtension Include="MVC-3.0">
      <AssemblyName>Microsoft.AspNetCore.Mvc.Razor.Extensions</AssemblyName>
      <AssemblyFilePath>$(RazorSdkDirectoryRoot)extensions\mvc-3-0\Microsoft.AspNetCore.Mvc.Razor.Extensions.dll</AssemblyFilePath>
    </RazorExtension>
  </ItemGroup>

</Project>
