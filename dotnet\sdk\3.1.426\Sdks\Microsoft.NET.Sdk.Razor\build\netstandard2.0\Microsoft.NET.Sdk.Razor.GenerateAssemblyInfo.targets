<!--
***********************************************************************************************
Microsoft.NET.Sdk.Razor.GenerateAssemblyInfo.targets

WARNING:  DO NOT MODIFY this file unless you are knowledgeable about MSBuild and have
          created a backup copy.  Incorrect changes to this file will make it
          impossible to load or build your projects from the command-line or the IDE.

Copyright (c) .NET Foundation. All rights reserved.
***********************************************************************************************
-->
<Project ToolsVersion="14.0">
  <PropertyGroup>
    <!-- Determines if the generated Razor assembly includes an auto-generated assembly info. -->
    <GenerateRazorTargetAssemblyInfo Condition="'$(GenerateRazorTargetAssemblyInfo)'==''">true</GenerateRazorTargetAssemblyInfo>

    <!-- Set to true, to automatically include some AssemblyAttributes inferred from the project metadata in the generated Razor assembly -->
    <EnableDefaultRazorTargetAssemblyInfoAttributes Condition="'$(EnableDefaultRazorTargetAssemblyInfoAttributes)'==''">true</EnableDefaultRazorTargetAssemblyInfoAttributes>

    <!-- AssemblyInfo that gets added to the generated Razor dll -->
    <RazorTargetAssemblyInfo Condition="'$(RazorTargetAssemblyInfo)'==''">$(IntermediateOutputPath)$(MSBuildProjectName).RazorTargetAssemblyInfo.cs</RazorTargetAssemblyInfo>
    <_RazorTargetAssemblyInfoInputsCacheFile>$(IntermediateOutputPath)$(MSBuildProjectName).RazorTargetAssemblyInfo.cache</_RazorTargetAssemblyInfoInputsCacheFile>

    <!-- AssemblyInfo that gets added to the project being compiled -->
    <_RazorAssemblyInfo>$(IntermediateOutputPath)$(MSBuildProjectName).RazorAssemblyInfo.cs</_RazorAssemblyInfo>
    <_RazorAssemblyInfoInputsCacheFile>$(IntermediateOutputPath)$(MSBuildProjectName).RazorAssemblyInfo.cache</_RazorAssemblyInfoInputsCacheFile>
  </PropertyGroup>

  <PropertyGroup>
    <GenerateRazorTargetAssemblyInfoDependsOn>
      GetRazorTargetAssemblyAttributes;
      _CreateRazorTargetAssemblyInfoInputsCacheFile;
      CoreGenerateRazorTargetAssemblyInfo
    </GenerateRazorTargetAssemblyInfoDependsOn>

    <GenerateRazorTargetAssemblyInfoDependsOn Condition="'$(_Targeting30OrNewerRazorLangVersion)' == 'true'">
      _ResolveMvcAssemblyAttributes;
      $(GenerateRazorTargetAssemblyInfoDependsOn)
    </GenerateRazorTargetAssemblyInfoDependsOn>
  </PropertyGroup>

  <Target
    Name="GenerateRazorTargetAssemblyInfo"
    DependsOnTargets="$(GenerateRazorTargetAssemblyInfoDependsOn)">
  </Target>

  <Target
    Name="CoreGenerateRazorTargetAssemblyInfo"
    Inputs="$(_RazorTargetAssemblyInfoInputsCacheFile)"
    Outputs="$(RazorTargetAssemblyInfo)"
    Condition="'$(GenerateRazorTargetAssemblyInfo)'=='true' AND '@(RazorCompile)'!=''">

    <ItemGroup Condition="'$(GenerateRazorTargetAssemblyInfo)'=='true'">
      <!-- Ensure the generated assemblyinfo file is not already part of RazorCompile sources -->
      <RazorCompile Remove="$(RazorTargetAssemblyInfo)" />
      <RazorCompile Include="$(RazorTargetAssemblyInfo)" />
    </ItemGroup>

    <WriteCodeFragment AssemblyAttributes="@(RazorTargetAssemblyAttribute)" Language="C#" OutputFile="$(RazorTargetAssemblyInfo)" />

    <ItemGroup>
      <FileWrites Include="$(RazorTargetAssemblyInfo)" />
    </ItemGroup>

  </Target>

  <Target
    Name="GetRazorTargetAssemblyAttributes"
    DependsOnTargets="GetAssemblyVersion"
    Condition="'$(EnableDefaultRazorTargetAssemblyInfoAttributes)'=='true'">

    <PropertyGroup>
      <RazorAssemblyFileVersion Condition="'$(RazorAssemblyFileVersion)' == ''">$(FileVersion)</RazorAssemblyFileVersion>
      <RazorAssemblyInformationalVersion Condition="'$(RazorAssemblyInformationalVersion)' == ''">$(InformationalVersion)</RazorAssemblyInformationalVersion>
      <RazorAssemblyDescription Condition="'$(RazorAssemblyDescription)'==''">$(Description)</RazorAssemblyDescription>
      <RazorAssemblyTitle Condition="'$(RazorAssemblyTitle)'==''">$(RazorTargetName)</RazorAssemblyTitle>
      <RazorAssemblyVersion Condition="'$(RazorAssemblyVersion)' == ''">$(AssemblyVersion)</RazorAssemblyVersion>
    </PropertyGroup>

    <ItemGroup>
      <RazorTargetAssemblyAttribute Include="System.Reflection.AssemblyCompanyAttribute" Condition="'$(Company)' != '' and '$(GenerateAssemblyCompanyAttribute)' == 'true'">
        <_Parameter1>$(Company)</_Parameter1>
      </RazorTargetAssemblyAttribute>
      <RazorTargetAssemblyAttribute Include="System.Reflection.AssemblyConfigurationAttribute" Condition="'$(Configuration)' != '' and '$(GenerateAssemblyConfigurationAttribute)' == 'true'">
        <_Parameter1>$(Configuration)</_Parameter1>
      </RazorTargetAssemblyAttribute>
      <RazorTargetAssemblyAttribute Include="System.Reflection.AssemblyCopyrightAttribute" Condition="'$(Copyright)' != '' and '$(GenerateAssemblyCopyrightAttribute)' == 'true'">
        <_Parameter1>$(Copyright)</_Parameter1>
      </RazorTargetAssemblyAttribute>
      <RazorTargetAssemblyAttribute Include="System.Reflection.AssemblyProductAttribute" Condition="'$(Product)' != '' and '$(GenerateAssemblyProductAttribute)' == 'true'">
        <_Parameter1>$(Product)</_Parameter1>
      </RazorTargetAssemblyAttribute>
      <RazorTargetAssemblyAttribute Include="System.Resources.NeutralResourcesLanguageAttribute" Condition="'$(NeutralLanguage)' != '' and '$(GenerateNeutralResourcesLanguageAttribute)' == 'true'">
        <_Parameter1>$(NeutralLanguage)</_Parameter1>
      </RazorTargetAssemblyAttribute>

      <RazorTargetAssemblyAttribute Include="System.Reflection.AssemblyDescriptionAttribute" Condition="'$(RazorAssemblyDescription)' != '' and '$(GenerateAssemblyDescriptionAttribute)' == 'true'">
        <_Parameter1>$(RazorAssemblyDescription)</_Parameter1>
      </RazorTargetAssemblyAttribute>
      <RazorTargetAssemblyAttribute Include="System.Reflection.AssemblyFileVersionAttribute" Condition="'$(RazorAssemblyFileVersion)' != '' and '$(GenerateAssemblyFileVersionAttribute)' == 'true'">
        <_Parameter1>$(RazorAssemblyFileVersion)</_Parameter1>
      </RazorTargetAssemblyAttribute>
      <RazorTargetAssemblyAttribute Include="System.Reflection.AssemblyInformationalVersionAttribute" Condition="'$(RazorAssemblyInformationalVersion)' != '' and '$(GenerateAssemblyInformationalVersionAttribute)' == 'true'">
        <_Parameter1>$(RazorAssemblyInformationalVersion)</_Parameter1>
      </RazorTargetAssemblyAttribute>
      <RazorTargetAssemblyAttribute Include="System.Reflection.AssemblyTitleAttribute" Condition="'$(RazorAssemblyTitle)' != '' and '$(GenerateAssemblyTitleAttribute)' == 'true'">
        <_Parameter1>$(RazorAssemblyTitle)</_Parameter1>
      </RazorTargetAssemblyAttribute>
      <RazorTargetAssemblyAttribute Include="System.Reflection.AssemblyVersionAttribute" Condition="'$(RazorAssemblyVersion)' != '' and '$(GenerateAssemblyVersionAttribute)' == 'true'">
        <_Parameter1>$(RazorAssemblyVersion)</_Parameter1>
      </RazorTargetAssemblyAttribute>
    </ItemGroup>

  </Target>

  <!--
    To allow version changes to be respected on incremental builds (e.g. through CLI parameters),
    create a hash of all assembly attributes so that the cache file will change with the calculated
    assembly attribute values and msbuild will then execute CoreGenerateAssembly to generate a new file.
  -->
  <Target Name="_CreateRazorTargetAssemblyInfoInputsCacheFile" Condition="'@(RazorTargetAssemblyAttribute)' != ''">

    <!-- We only use up to _Parameter1 for most attributes, but other targets may add additional assembly attributes with multiple parameters. -->
    <Hash ItemsToHash="@(RazorTargetAssemblyAttribute->'%(Identity)%(_Parameter1)%(_Parameter2)%(_Parameter3)%(_Parameter4)%(_Parameter5)%(_Parameter6)%(_Parameter7)%(_Parameter8)')">
      <Output TaskParameter="HashResult" PropertyName="_RazorTargetAssemblyAttributesHash" />
    </Hash>

    <WriteLinesToFile
      Lines="$(_RazorTargetAssemblyAttributesHash)"
      File="$(_RazorTargetAssemblyInfoInputsCacheFile)"
      Overwrite="True"
      WriteOnlyWhenDifferent="True" />

    <ItemGroup>
      <FileWrites Include="$(_RazorTargetAssemblyInfoInputsCacheFile)" />
    </ItemGroup>

  </Target>

  <Target Name="_CreateRazorAssemblyInfoInputsCacheFile" Condition="'@(_RazorAssemblyAttribute)' != ''">

    <!-- We only use up to _Parameter1 for most attributes, but other targets may add additional assembly attributes with multiple parameters. -->
    <Hash ItemsToHash="@(_RazorAssemblyAttribute->'%(Identity)%(_Parameter1)%(_Parameter2)%(_Parameter3)%(_Parameter4)%(_Parameter5)%(_Parameter6)%(_Parameter7)%(_Parameter8)')">
      <Output TaskParameter="HashResult" PropertyName="_RazorAssemblyAttributesHash" />
    </Hash>

    <WriteLinesToFile
      Lines="$(_RazorAssemblyAttributesHash)"
      File="$(_RazorAssemblyInfoInputsCacheFile)"
      Overwrite="True"
      WriteOnlyWhenDifferent="True" />

    <ItemGroup>
      <FileWrites Include="$(_RazorAssemblyInfoInputsCacheFile)" />
    </ItemGroup>

  </Target>

  <Target
    Name="_CoreGenerateRazorAssemblyInfo"
    DependsOnTargets="_CreateRazorAssemblyInfoInputsCacheFile"
    Inputs="$(_RazorAssemblyInfoInputsCacheFile)"
    Outputs="$(_RazorAssemblyInfo)"
    Condition="'$(GenerateRazorAssemblyInfo)' == 'true' AND '@(_RazorAssemblyAttribute)' != ''">

    <ItemGroup>
      <Compile Remove="$(_RazorAssemblyInfo)" />
      <Compile Include="$(_RazorAssemblyInfo)" />
    </ItemGroup>

    <WriteCodeFragment AssemblyAttributes="@(_RazorAssemblyAttribute)" Language="$(Language)" OutputFile="$(_RazorAssemblyInfo)" />

    <ItemGroup>
      <FileWrites Include="$(_RazorAssemblyInfo)" />
    </ItemGroup>

  </Target>

  <PropertyGroup>
    <!-- Generate attributes in the main assembly if we're targeting a C# project and using the Razor Sdk. -->
    <CoreCompileDependsOn Condition="'$(ResolvedRazorCompileToolset)'=='RazorSdk' and '$(Language)' == 'C#'">
      $(CoreCompileDependsOn);
      _GenerateRazorAssemblyInfo
    </CoreCompileDependsOn>

    <_GenerateRazorAssemblyInfoDependsOn>RazorGetAssemblyAttributes;PrepareForBuild;_CoreGenerateRazorAssemblyInfo</_GenerateRazorAssemblyInfoDependsOn>

    <!-- In 3.0 or later, we need to invoke a target to determine MVC specific configuration. -->
    <_GenerateRazorAssemblyInfoDependsOn Condition="'$(_Targeting30OrNewerRazorLangVersion)' == 'true'">
      _ResolveMvcAssemblyAttributes;
      $(_GenerateRazorAssemblyInfoDependsOn);
    </_GenerateRazorAssemblyInfoDependsOn>
  </PropertyGroup>

  <Target Name="_GenerateRazorAssemblyInfo" DependsOnTargets="$(_GenerateRazorAssemblyInfoDependsOn)" />

</Project>
