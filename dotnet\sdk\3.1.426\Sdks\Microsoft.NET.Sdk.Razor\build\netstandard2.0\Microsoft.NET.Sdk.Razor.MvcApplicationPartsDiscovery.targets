<!--
***********************************************************************************************
Microsoft.NET.Sdk.Razor.ApplicationPartsDiscovery

WARNING:  DO NOT MODIFY this file unless you are knowledgeable about MSBuild and have
          created a backup copy.  Incorrect changes to this file will make it
          impossible to load or build your projects from the command-line or the IDE.

Copyright (c) .NET Foundation. All rights reserved.
***********************************************************************************************
-->

<Project ToolsVersion="14.0">
  <UsingTask
    TaskName="Microsoft.AspNetCore.Razor.Tasks.FindAssembliesWithReferencesTo"
    AssemblyFile="$(RazorSdkBuildTasksAssembly)"
    Condition="'$(RazorSdkBuildTasksAssembly)' != ''" />

  <PropertyGroup>
    <GenerateMvcApplicationPartsAssemblyAttributes Condition="'$(GenerateMvcApplicationPartsAssemblyAttributes)' == '' AND '$(OutputType)' == 'Exe'">true</GenerateMvcApplicationPartsAssemblyAttributes>

    <CoreCompileDependsOn Condition="'$(GenerateMvcApplicationPartsAssemblyAttributes)' == 'true' AND '$(DesignTimeBuild)' != 'true'">
      _DiscoverMvcApplicationParts;
      $(CoreCompileDependsOn);
    </CoreCompileDependsOn>

    <_MvcApplicationPartAttributeGeneratedFile>$(IntermediateOutputPath)$(TargetName).MvcApplicationPartsAssemblyInfo$(DefaultLanguageSourceExtension)</_MvcApplicationPartAttributeGeneratedFile>
    <_MvcApplicationPartCacheFile>$(IntermediateOutputPath)$(TargetName).MvcApplicationPartsAssemblyInfo.cache</_MvcApplicationPartCacheFile>
  </PropertyGroup>

  <Target
    Name="_DiscoverMvcApplicationParts"
    Inputs="$(ProjectAssetsFile);$(MSBuildAllProjects)"
    Outputs="$(_MvcApplicationPartCacheFile)"
    DependsOnTargets="ResolveAssemblyReferences">

    <ItemGroup>
      <_MvcAssemblyName Include="Microsoft.AspNetCore.Mvc" />
      <_MvcAssemblyName Include="Microsoft.AspNetCore.Mvc.Abstractions" />
      <_MvcAssemblyName Include="Microsoft.AspNetCore.Mvc.ApiExplorer" />
      <_MvcAssemblyName Include="Microsoft.AspNetCore.Mvc.Core" />
      <_MvcAssemblyName Include="Microsoft.AspNetCore.Mvc.Cors" />
      <_MvcAssemblyName Include="Microsoft.AspNetCore.Mvc.DataAnnotations" />
      <_MvcAssemblyName Include="Microsoft.AspNetCore.Mvc.Formatters.Json" />
      <_MvcAssemblyName Include="Microsoft.AspNetCore.Mvc.Formatters.Xml" />
      <_MvcAssemblyName Include="Microsoft.AspNetCore.Mvc.Localization" />
      <_MvcAssemblyName Include="Microsoft.AspNetCore.Mvc.NewtonsoftJson" />
      <_MvcAssemblyName Include="Microsoft.AspNetCore.Mvc.Razor" />
      <_MvcAssemblyName Include="Microsoft.AspNetCore.Mvc.RazorPages" />
      <_MvcAssemblyName Include="Microsoft.AspNetCore.Mvc.TagHelpers" />
      <_MvcAssemblyName Include="Microsoft.AspNetCore.Mvc.ViewFeatures" />
    </ItemGroup>

    <FindAssembliesWithReferencesTo Assemblies="@(ReferencePath)" TargetAssemblyNames="@(_MvcAssemblyName)">
      <Output TaskParameter="ResolvedAssemblies" ItemName="_ApplicationPartAssemblyNames"/>
    </FindAssembliesWithReferencesTo>

    <ItemGroup>
       <_MvcApplicationPartAttribute Include="Microsoft.AspNetCore.Mvc.ApplicationParts.ApplicationPartAttribute">
        <_Parameter1>%(_ApplicationPartAssemblyNames.Identity)</_Parameter1>
      </_MvcApplicationPartAttribute>
    </ItemGroup>

    <!-- If we found application part assemblies, generate attributes for it and add it to compilation list -->
    <WriteCodeFragment
      AssemblyAttributes="@(_MvcApplicationPartAttribute)"
      Language="$(Language)"
      OutputFile="$(_MvcApplicationPartAttributeGeneratedFile)"
      Condition="'@(_ApplicationPartAssemblyNames->Count())' != '0'" />

    <!--
      If the generated attribute file exists, but no assembly names were discovered,
      it suggests that the assembly references were changed to no longer point to MVC.
      In this case, delete the file so that future incremental builds can no longer pick it up.
    -->
    <Delete
      Files="$(_MvcApplicationPartAttributeGeneratedFile)"
      Condition="'@(_ApplicationPartAssemblyNames->Count())' == '0' AND Exists('$(_MvcApplicationPartAttributeGeneratedFile)')" />

    <ItemGroup Condition="Exists('$(_MvcApplicationPartAttributeGeneratedFile)')">
      <Compile Remove="$(_MvcApplicationPartAttributeGeneratedFile)" Condition="'$(Language)'!='F#'" />
      <Compile Include="$(_MvcApplicationPartAttributeGeneratedFile)"  Condition="'$(Language)'!='F#'" />

      <CompileBefore Remove="$(_MvcApplicationPartAttributeGeneratedFile)" Condition="'$(Language)'=='F#'" />
      <CompileBefore Include="$(_MvcApplicationPartAttributeGeneratedFile)"  Condition="'$(Language)'=='F#'" />

      <FileWrites Include="$(_MvcApplicationPartAttributeGeneratedFile)"/>
    </ItemGroup>

    <!--
      If we did not find any application parts, produce an empty file which is not added to compilation.
      This is required to play nicely with incremental builds.
    -->
    <Touch
      Files="$(_MvcApplicationPartCacheFile)"
      AlwaysCreate="true" />

    <ItemGroup>
      <FileWrites Include="$(_MvcApplicationPartCacheFile)" />
    </ItemGroup>
  </Target>
</Project>
