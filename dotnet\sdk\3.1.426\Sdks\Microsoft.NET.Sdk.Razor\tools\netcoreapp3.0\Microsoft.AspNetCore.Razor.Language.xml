<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Microsoft.AspNetCore.Razor.Language</name>
    </assembly>
    <members>
        <member name="F:Microsoft.AspNetCore.Razor.Language.AllowedChildTagDescriptorComparer.Default">
            <summary>
            A default instance of the <see cref="T:Microsoft.AspNetCore.Razor.Language.AllowedChildTagDescriptorComparer"/>.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.AllowedChildTagDescriptorComparer.Equals(Microsoft.AspNetCore.Razor.Language.AllowedChildTagDescriptor,Microsoft.AspNetCore.Razor.Language.AllowedChildTagDescriptor)">
            <inheritdoc />
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.AllowedChildTagDescriptorComparer.GetHashCode(Microsoft.AspNetCore.Razor.Language.AllowedChildTagDescriptor)">
            <inheritdoc />
        </member>
        <member name="T:Microsoft.AspNetCore.Razor.Language.BoundAttributeDescriptor">
            <summary>
            A metadata class describing a tag helper attribute.
            </summary>
        </member>
        <member name="F:Microsoft.AspNetCore.Razor.Language.BoundAttributeDescriptorComparer.Default">
            <summary>
            A default instance of the <see cref="T:Microsoft.AspNetCore.Razor.Language.BoundAttributeDescriptorComparer"/>.
            </summary>
        </member>
        <member name="F:Microsoft.AspNetCore.Razor.Language.BoundAttributeParameterDescriptorComparer.Default">
            <summary>
            A default instance of the <see cref="T:Microsoft.AspNetCore.Razor.Language.BoundAttributeParameterDescriptorComparer"/>.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Razor.Language.Components.ComponentDocumentClassifierPass.FallbackRootNamespace">
            <summary>
            The fallback value of the root namespace. Only used if the fallback root namespace
            was not passed in.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Razor.Language.Components.ComponentDocumentClassifierPass.MangleClassNames">
             <summary>
             Gets or sets whether to mangle class names.
             
             Set to true in the IDE so we can generated mangled class names. This is needed
             to avoid conflicts between generated design-time code and the code in the editor.
            
             A better workaround for this would be to create a singlefilegenerator that overrides
             the codegen process when a document is open, but this is more involved, so hacking
             it for now.
             </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Components.ComponentDocumentClassifierPass.OnDocumentStructureCreated(Microsoft.AspNetCore.Razor.Language.RazorCodeDocument,Microsoft.AspNetCore.Razor.Language.Intermediate.NamespaceDeclarationIntermediateNode,Microsoft.AspNetCore.Razor.Language.Intermediate.ClassDeclarationIntermediateNode,Microsoft.AspNetCore.Razor.Language.Intermediate.MethodDeclarationIntermediateNode)">
            <inheritdoc />
        </member>
        <member name="F:Microsoft.AspNetCore.Razor.Language.Components.ComponentMetadata.ChildContent.ParameterAttributeName">
            <summary>
            The name of the synthesized attribute used to set a child content parameter.
            </summary>
        </member>
        <member name="F:Microsoft.AspNetCore.Razor.Language.Components.ComponentMetadata.ChildContent.DefaultParameterName">
            <summary>
            The default name of the child content parameter (unless set by a Context attribute).
            </summary>
        </member>
        <member name="T:Microsoft.AspNetCore.Razor.Language.Components.ComponentRuntimeNodeWriter">
            <summary>
            Generates the C# code corresponding to Razor source document contents.
            </summary>
        </member>
        <member name="T:Microsoft.AspNetCore.Razor.Language.Components.ScopeStack">
            <summary>
            Keeps track of the nesting of elements/containers while writing out the C# source code
            for a component. This allows us to detect mismatched start/end tags, as well as inject
            additional C# source to capture component descendants in a lambda.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Components.TagHelperBoundAttributeDescriptorExtensions.IsEventCallbackProperty(Microsoft.AspNetCore.Razor.Language.BoundAttributeDescriptor)">
            <summary>
            Gets a value indicating whether the attribute is of type <c>EventCallback</c> or
            <c>EventCallback{T}</c>
            </summary>
            <param name="attribute">The <see cref="T:Microsoft.AspNetCore.Razor.Language.BoundAttributeDescriptor"/>.</param>
            <returns><c>true</c> if the attribute is an event callback, otherwise <c>false</c>.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Components.TagHelperBoundAttributeDescriptorExtensions.IsChildContentProperty(Microsoft.AspNetCore.Razor.Language.BoundAttributeDescriptor)">
            <summary>
            Gets a value that indicates whether the property is a child content property. Properties are
            considered child content if they have the type <c>RenderFragment</c> or <c>RenderFragment{T}</c>.
            </summary>
            <param name="attribute">The <see cref="T:Microsoft.AspNetCore.Razor.Language.BoundAttributeDescriptor"/>.</param>
            <returns>Returns <c>true</c> if the property is child content, otherwise <c>false</c>.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Components.TagHelperBoundAttributeDescriptorExtensions.IsChildContentProperty(Microsoft.AspNetCore.Razor.Language.BoundAttributeDescriptorBuilder)">
            <summary>
            Gets a value that indicates whether the property is a child content property. Properties are
            considered child content if they have the type <c>RenderFragment</c> or <c>RenderFragment{T}</c>.
            </summary>
            <param name="attribute">The <see cref="T:Microsoft.AspNetCore.Razor.Language.BoundAttributeDescriptorBuilder"/>.</param>
            <returns>Returns <c>true</c> if the property is child content, otherwise <c>false</c>.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Components.TagHelperBoundAttributeDescriptorExtensions.IsParameterizedChildContentProperty(Microsoft.AspNetCore.Razor.Language.BoundAttributeDescriptor)">
            <summary>
            Gets a value that indicates whether the property is a parameterized child content property. Properties are
            considered parameterized child content if they have the type <c>RenderFragment{T}</c> (for some T).
            </summary>
            <param name="attribute">The <see cref="T:Microsoft.AspNetCore.Razor.Language.BoundAttributeDescriptor"/>.</param>
            <returns>Returns <c>true</c> if the property is parameterized child content, otherwise <c>false</c>.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Components.TagHelperBoundAttributeDescriptorExtensions.IsParameterizedChildContentProperty(Microsoft.AspNetCore.Razor.Language.BoundAttributeDescriptorBuilder)">
            <summary>
            Gets a value that indicates whether the property is a parameterized child content property. Properties are
            considered parameterized child content if they have the type <c>RenderFragment{T}</c> (for some T).
            </summary>
            <param name="attribute">The <see cref="T:Microsoft.AspNetCore.Razor.Language.BoundAttributeDescriptor"/>.</param>
            <returns>Returns <c>true</c> if the property is parameterized child content, otherwise <c>false</c>.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Components.TagHelperBoundAttributeDescriptorExtensions.IsChildContentParameterNameProperty(Microsoft.AspNetCore.Razor.Language.BoundAttributeDescriptor)">
            <summary>
            Gets a value that indicates whether the property is used to specify the name of the parameter
            for a parameterized child content property.
            </summary>
            <param name="attribute">The <see cref="T:Microsoft.AspNetCore.Razor.Language.BoundAttributeDescriptor"/>.</param>
            <returns>
            Returns <c>true</c> if the property specifies the name of a parameter for a parameterized child content,
            otherwise <c>false</c>.
            </returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Components.TagHelperDescriptorExtensions.IsInvariantCultureBindTagHelper(Microsoft.AspNetCore.Razor.Language.TagHelperDescriptor)">
            <summary>
            Gets a value that indicates where the tag helper is a bind tag helper with a default
            culture value of <see cref="P:System.Globalization.CultureInfo.InvariantCulture"/>.
            </summary>
            <param name="tagHelper">The <see cref="T:Microsoft.AspNetCore.Razor.Language.TagHelperDescriptor"/>.</param>
            <returns>
            <c>true</c> if this tag helper is a bind tag helper and defaults in <see cref="P:System.Globalization.CultureInfo.InvariantCulture"/>
            </returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Components.TagHelperDescriptorExtensions.GetFormat(Microsoft.AspNetCore.Razor.Language.TagHelperDescriptor)">
            <summary>
            Gets the default format value for a bind tag helper.
            </summary>
            <param name="tagHelper">The <see cref="T:Microsoft.AspNetCore.Razor.Language.TagHelperDescriptor"/>.</param>
            <returns>The format, or <c>null</c>.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Components.TagHelperDescriptorExtensions.IsComponentFullyQualifiedNameMatch(Microsoft.AspNetCore.Razor.Language.TagHelperDescriptor)">
            <summary>
            Gets whether the component matches a tag with a fully qualified name.
            </summary>
            <param name="tagHelper">The <see cref="T:Microsoft.AspNetCore.Razor.Language.TagHelperDescriptor"/>.</param>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Components.TagHelperDescriptorExtensions.GetChildContentProperties(Microsoft.AspNetCore.Razor.Language.TagHelperDescriptor)">
            <summary>
            Gets the set of component attributes that can accept child content (<c>RenderFragment</c> or <c>RenderFragment{T}</c>).
            </summary>
            <param name="tagHelper">The <see cref="T:Microsoft.AspNetCore.Razor.Language.TagHelperDescriptor"/>.</param>
            <returns>The child content attributes</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Components.TagHelperDescriptorExtensions.GetTypeParameters(Microsoft.AspNetCore.Razor.Language.TagHelperDescriptor)">
            <summary>
            Gets the set of component attributes that represent generic type parameters of the component type.
            </summary>
            <param name="tagHelper">The <see cref="T:Microsoft.AspNetCore.Razor.Language.TagHelperDescriptor"/>.</param>
            <returns>The type parameter attributes</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.DefaultRazorProjectItem.#ctor(System.String,System.String,System.String,System.String,System.IO.FileInfo)">
            <summary>
            Initializes a new instance of <see cref="T:Microsoft.AspNetCore.Razor.Language.DefaultRazorProjectItem"/>.
            </summary>
            <param name="basePath">The base path.</param>
            <param name="relativePhysicalPath">The physical path of the base path.</param>
            <param name="filePath">The path.</param>
            <param name="fileKind">The file kind. If null, the document kind will be inferred from the file extension.</param>
            <param name="file">The <see cref="T:System.IO.FileInfo"/>.</param>
        </member>
        <member name="T:Microsoft.AspNetCore.Razor.Language.DirectiveDescriptor">
            <summary>
            A descriptor type for a directive that can be parsed by the Razor parser.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Razor.Language.DirectiveDescriptor.Description">
            <summary>
            Gets the description of the directive.
            </summary>
            <remarks>
            The description is used for information purposes, and has no effect on parsing.
            </remarks>
        </member>
        <member name="P:Microsoft.AspNetCore.Razor.Language.DirectiveDescriptor.Directive">
            <summary>
            Gets the directive keyword without the leading <c>@</c> token.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Razor.Language.DirectiveDescriptor.DisplayName">
            <summary>
            Gets the display name of the directive.
            </summary>
            <remarks>
            The display name is used for information purposes, and has no effect on parsing.
            </remarks>
        </member>
        <member name="P:Microsoft.AspNetCore.Razor.Language.DirectiveDescriptor.Kind">
            <summary>
            Gets the kind of the directive. The kind determines whether or not a directive has an associated block.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Razor.Language.DirectiveDescriptor.Usage">
            <summary>
            Gets the way a directive can be used. The usage determines how many, and where directives can exist per document.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Razor.Language.DirectiveDescriptor.Tokens">
            <summary>
            Gets the list of directive tokens that can follow the directive keyword.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.DirectiveDescriptor.CreateDirective(System.String,Microsoft.AspNetCore.Razor.Language.DirectiveKind)">
            <summary>
            Creates a new <see cref="T:Microsoft.AspNetCore.Razor.Language.DirectiveDescriptor"/>.
            </summary>
            <param name="directive">The directive keyword.</param>
            <param name="kind">The directive kind.</param>
            <returns>A <see cref="T:Microsoft.AspNetCore.Razor.Language.DirectiveDescriptor"/> for the created directive.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.DirectiveDescriptor.CreateDirective(System.String,Microsoft.AspNetCore.Razor.Language.DirectiveKind,System.Action{Microsoft.AspNetCore.Razor.Language.IDirectiveDescriptorBuilder})">
            <summary>
            Creates a new <see cref="T:Microsoft.AspNetCore.Razor.Language.DirectiveDescriptor"/>.
            </summary>
            <param name="directive">The directive keyword.</param>
            <param name="kind">The directive kind.</param>
            <param name="configure">A configuration delegate for the directive.</param>
            <returns>A <see cref="T:Microsoft.AspNetCore.Razor.Language.DirectiveDescriptor"/> for the created directive.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.DirectiveDescriptor.CreateSingleLineDirective(System.String)">
            <summary>
            Creates a new <see cref="T:Microsoft.AspNetCore.Razor.Language.DirectiveDescriptor"/> with <see cref="P:Microsoft.AspNetCore.Razor.Language.DirectiveDescriptor.Kind"/> set to <see cref="F:Microsoft.AspNetCore.Razor.Language.DirectiveKind.SingleLine"/>
            </summary>
            <param name="directive">The directive keyword.</param>
            <returns>A <see cref="T:Microsoft.AspNetCore.Razor.Language.DirectiveDescriptor"/> for the created directive.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.DirectiveDescriptor.CreateSingleLineDirective(System.String,System.Action{Microsoft.AspNetCore.Razor.Language.IDirectiveDescriptorBuilder})">
            <summary>
            Creates a new <see cref="T:Microsoft.AspNetCore.Razor.Language.DirectiveDescriptor"/> with <see cref="P:Microsoft.AspNetCore.Razor.Language.DirectiveDescriptor.Kind"/> set to <see cref="F:Microsoft.AspNetCore.Razor.Language.DirectiveKind.SingleLine"/>
            </summary>
            <param name="directive">The directive keyword.</param>
            <param name="configure">A configuration delegate for the directive.</param>
            <returns>A <see cref="T:Microsoft.AspNetCore.Razor.Language.DirectiveDescriptor"/> for the created directive.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.DirectiveDescriptor.CreateRazorBlockDirective(System.String)">
            <summary>
            Creates a new <see cref="T:Microsoft.AspNetCore.Razor.Language.DirectiveDescriptor"/> with <see cref="P:Microsoft.AspNetCore.Razor.Language.DirectiveDescriptor.Kind"/> set to <see cref="F:Microsoft.AspNetCore.Razor.Language.DirectiveKind.RazorBlock"/>
            </summary>
            <param name="directive">The directive keyword.</param>
            <returns>A <see cref="T:Microsoft.AspNetCore.Razor.Language.DirectiveDescriptor"/> for the created directive.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.DirectiveDescriptor.CreateRazorBlockDirective(System.String,System.Action{Microsoft.AspNetCore.Razor.Language.IDirectiveDescriptorBuilder})">
            <summary>
            Creates a new <see cref="T:Microsoft.AspNetCore.Razor.Language.DirectiveDescriptor"/> with <see cref="P:Microsoft.AspNetCore.Razor.Language.DirectiveDescriptor.Kind"/> set to <see cref="F:Microsoft.AspNetCore.Razor.Language.DirectiveKind.RazorBlock"/>
            </summary>
            <param name="directive">The directive keyword.</param>
            <param name="configure">A configuration delegate for the directive.</param>
            <returns>A <see cref="T:Microsoft.AspNetCore.Razor.Language.DirectiveDescriptor"/> for the created directive.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.DirectiveDescriptor.CreateCodeBlockDirective(System.String)">
            <summary>
            Creates a new <see cref="T:Microsoft.AspNetCore.Razor.Language.DirectiveDescriptor"/> with <see cref="P:Microsoft.AspNetCore.Razor.Language.DirectiveDescriptor.Kind"/> set to <see cref="F:Microsoft.AspNetCore.Razor.Language.DirectiveKind.CodeBlock"/>
            </summary>
            <param name="directive">The directive keyword.</param>
            <returns>A <see cref="T:Microsoft.AspNetCore.Razor.Language.DirectiveDescriptor"/> for the created directive.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.DirectiveDescriptor.CreateCodeBlockDirective(System.String,System.Action{Microsoft.AspNetCore.Razor.Language.IDirectiveDescriptorBuilder})">
            <summary>
            Creates a new <see cref="T:Microsoft.AspNetCore.Razor.Language.DirectiveDescriptor"/> with <see cref="P:Microsoft.AspNetCore.Razor.Language.DirectiveDescriptor.Kind"/> set to <see cref="F:Microsoft.AspNetCore.Razor.Language.DirectiveKind.CodeBlock"/>
            </summary>
            <param name="directive">The directive keyword.</param>
            <param name="configure">A configuration delegate for the directive.</param>
            <returns>A <see cref="T:Microsoft.AspNetCore.Razor.Language.DirectiveDescriptor"/> for the created directive.</returns>
        </member>
        <member name="T:Microsoft.AspNetCore.Razor.Language.DirectiveUsage">
            <summary>
            The way a directive can be used. The usage determines how many, and where directives can exist per file.
            </summary>
        </member>
        <member name="F:Microsoft.AspNetCore.Razor.Language.DirectiveUsage.Unrestricted">
            <summary>
            Directive can exist anywhere in the file.
            </summary>
        </member>
        <member name="F:Microsoft.AspNetCore.Razor.Language.DirectiveUsage.FileScopedSinglyOccurring">
            <summary>
            Directive must exist prior to any HTML or code and have no duplicates. When importing the directive, if it is 
            <see cref="F:Microsoft.AspNetCore.Razor.Language.DirectiveKind.SingleLine"/>, the last occurrence of the directive is imported.
            </summary>
        </member>
        <member name="F:Microsoft.AspNetCore.Razor.Language.DirectiveUsage.FileScopedMultipleOccurring">
            <summary>
            Directive must exist prior to any HTML or code.
            </summary>
        </member>
        <member name="T:Microsoft.AspNetCore.Razor.Language.Extensions.RazorCompiledItemMetadataAttributeIntermediateNode">
            <summary>
            An <see cref="T:Microsoft.AspNetCore.Razor.Language.Intermediate.ExtensionIntermediateNode"/> that generates code for <c>RazorCompiledItemMetadataAttribute</c>.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Razor.Language.Extensions.RazorCompiledItemMetadataAttributeIntermediateNode.Key">
            <summary>
            Gets or sets the attribute key.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Razor.Language.Extensions.RazorCompiledItemMetadataAttributeIntermediateNode.Value">
            <summary>
            Gets or sets the attribute value.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.HtmlConventions.ToHtmlCase(System.String)">
            <summary>
            Converts from pascal/camel case to lower kebab-case.
            </summary>
            <example>
            SomeThing => some-thing
            capsONInside => caps-on-inside
            CAPSOnOUTSIDE => caps-on-outside
            ALLCAPS => allcaps
            One1Two2Three3 => one1-two2-three3
            ONE1TWO2THREE3 => one1two2three3
            First_Second_ThirdHi => first_second_third-hi
            </example>
        </member>
        <member name="T:Microsoft.AspNetCore.Razor.Language.IDirectiveDescriptorBuilder">
            <summary>
            A builder interface for configuring a <see cref="T:Microsoft.AspNetCore.Razor.Language.DirectiveDescriptor"/>.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Razor.Language.IDirectiveDescriptorBuilder.Description">
            <summary>
            Gets or sets the description of the directive.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Razor.Language.IDirectiveDescriptorBuilder.Directive">
            <summary>
            Gets the directive keyword.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Razor.Language.IDirectiveDescriptorBuilder.DisplayName">
            <summary>
            Gets or sets the display name of the directive.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Razor.Language.IDirectiveDescriptorBuilder.Kind">
            <summary>
            Gets the directive kind.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Razor.Language.IDirectiveDescriptorBuilder.Usage">
            <summary>
            Gets or sets the directive usage. The usage determines how many, and where directives can exist per document.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Razor.Language.IDirectiveDescriptorBuilder.Tokens">
            <summary>
            Gets a list of the directive tokens.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.IDirectiveDescriptorBuilder.Build">
            <summary>
            Creates a <see cref="T:Microsoft.AspNetCore.Razor.Language.DirectiveDescriptor"/> based on the current property values of the builder.
            </summary>
            <returns>The created <see cref="T:Microsoft.AspNetCore.Razor.Language.DirectiveDescriptor" />.</returns>
        </member>
        <member name="F:Microsoft.AspNetCore.Razor.Language.IntermediateNodePassBase.DefaultFeatureOrder">
            <summary>
            The default implementation of the <see cref="T:Microsoft.AspNetCore.Razor.Language.IRazorEngineFeature"/>s that run in a
            <see cref="T:Microsoft.AspNetCore.Razor.Language.IRazorEnginePhase"/> will use this value for its Order property.
            </summary>
            <remarks>
            This value is chosen in such a way that the default implementation runs after the other
            custom <see cref="T:Microsoft.AspNetCore.Razor.Language.IRazorEngineFeature"/> implementations for a particular <see cref="T:Microsoft.AspNetCore.Razor.Language.IRazorEnginePhase"/>.
            </remarks>
        </member>
        <member name="P:Microsoft.AspNetCore.Razor.Language.Intermediate.ComponentIntermediateNode.ChildContentParameterName">
            <summary>
            Gets the child content parameter name (null if unset) that was applied at the component level.
            </summary>
        </member>
        <member name="T:Microsoft.AspNetCore.Razor.Language.Intermediate.ComponentTypeInferenceMethodIntermediateNode">
            <summary>
            Represents a type-inference thunk that is used by the generated component code.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Razor.Language.Intermediate.ComponentTypeInferenceMethodIntermediateNode.Component">
            <summary>
            Gets the component usage linked to this type inference method.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Razor.Language.Intermediate.ComponentTypeInferenceMethodIntermediateNode.FullTypeName">
            <summary>
            Gets the full type name of the generated class containing this method.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Razor.Language.Intermediate.ComponentTypeInferenceMethodIntermediateNode.MethodName">
            <summary>
            Gets the name of the generated method.
            </summary>
        </member>
        <member name="T:Microsoft.AspNetCore.Razor.Language.IRazorCSharpLoweringPhase">
            <summary>
            Generates C# code using the intermediate node document.
            </summary>
            <remarks>
            After IR processing, the <see cref="T:Microsoft.AspNetCore.Razor.Language.IRazorCSharpLoweringPhase"/> transforms the intermediate node document into
            generated C# code. At this time any directives or other constructs that cannot be understood by code generation
            will result in an error.
            </remarks>
        </member>
        <member name="T:Microsoft.AspNetCore.Razor.Language.IRazorDirectiveClassifierPhase">
            <summary>
            Understands directive nodes and performs the necessary modifications to the document.
            </summary>
            <remarks>
            <para>
            The second phase of intermediate node processing is directive classification. Passes in this phase should interpret
            directives and processing them accordingly by transforming nodes or adding diagnostics. At this time the document 
            kind has been identified, so any directive that can't be applied should trigger
            errors. If implementing a document kind that diverges from the standard structure of Razor documents
            it may be necessary to reimplement processing of default directives.
            </para>
            <para>
            <see cref="T:Microsoft.AspNetCore.Razor.Language.IRazorDirectiveClassifierPass"/> objects are executed according to an ascending ordering of the
            <see cref="P:Microsoft.AspNetCore.Razor.Language.IRazorDirectiveClassifierPass.Order"/> property.
            </para>
            </remarks>
        </member>
        <member name="T:Microsoft.AspNetCore.Razor.Language.IRazorDocumentClassifierPhase">
            <summary>
            Modifies the intermediate node document to a desired structure.
            </summary>
            <remarks>
            <para>
            The first phase of intermediate node procesing is document classification. Passes in this phase should classify the
            document according to any relevant criteria (project configuration, file extension, directive) and modify
            the intermediate node document to suit the desired document shape. Document classifiers should also set
            <see cref="P:Microsoft.AspNetCore.Razor.Language.Intermediate.DocumentIntermediateNode.DocumentKind"/> to prevent other classifiers from running. If no classifier 
            matches the document, then it will be classified as &quot;generic&quot; and processed according to set 
            of reasonable defaults.
            </para>
            <para>
            <see cref="T:Microsoft.AspNetCore.Razor.Language.IRazorDocumentClassifierPass"/> objects are executed according to an ascending ordering of the
            <see cref="P:Microsoft.AspNetCore.Razor.Language.IRazorDocumentClassifierPass.Order"/> property.
            </para>
            </remarks>
        </member>
        <member name="T:Microsoft.AspNetCore.Razor.Language.IRazorIntermediateNodeLoweringPhase">
            <summary>
            Generates the intermediate node document from <see cref="T:Microsoft.AspNetCore.Razor.Language.RazorSyntaxTree"/>.
            </summary>
            <remarks>
            The document is first produced by <see cref="T:Microsoft.AspNetCore.Razor.Language.IRazorIntermediateNodeLoweringPhase"/>. At this point no intermediate node
            passes have been executed. The default <see cref="T:Microsoft.AspNetCore.Razor.Language.IRazorIntermediateNodeLoweringPhase"/> will perform a mechanical
            transformation of the syntax tree to intermediate nodes, resulting in a mostly flat structure. It is up to later phases 
            to give the document structure and semantics according to a document kind. The default 
            <see cref="T:Microsoft.AspNetCore.Razor.Language.IRazorIntermediateNodeLoweringPhase"/> is also responsible for merging nodes from imported documents.
            </remarks>
        </member>
        <member name="T:Microsoft.AspNetCore.Razor.Language.IRazorOptimizationPhase">
            <summary>
            Performs necessary modifications to the <see cref="T:Microsoft.AspNetCore.Razor.Language.Intermediate.DocumentIntermediateNode"/> to complete and
            optimize code generation.
            </summary>
            <remarks>
            <para>
            The last phase of intermediate node document processing is optimization. Passes in this phase perform some 
            kind of transformation on the intermediate node document that optimizes the generated code. The key distinction 
            here is that information may be discarded during this phase.
            </para>
            <para>
            <see cref="T:Microsoft.AspNetCore.Razor.Language.IRazorOptimizationPass"/> objects are executed according to an ascending ordering of the
            <see cref="P:Microsoft.AspNetCore.Razor.Language.IRazorOptimizationPass.Order"/> property.
            </para>
            </remarks>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Legacy.AddTagHelperChunkGenerator.Equals(System.Object)">
            <inheritdoc />
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Legacy.AddTagHelperChunkGenerator.GetHashCode">
            <inheritdoc />
        </member>
        <member name="T:Microsoft.AspNetCore.Razor.Language.Legacy.ErrorSink">
            <summary>
            Used to manage <see cref="T:Microsoft.AspNetCore.Razor.Language.RazorDiagnostic"/>s encountered during the Razor parsing phase.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Legacy.ErrorSink.#ctor">
            <summary>
            Instantiates a new instance of <see cref="T:Microsoft.AspNetCore.Razor.Language.Legacy.ErrorSink"/>.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Razor.Language.Legacy.ErrorSink.Errors">
            <summary>
            <see cref="T:Microsoft.AspNetCore.Razor.Language.RazorDiagnostic"/>s collected.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Legacy.ErrorSink.OnError(Microsoft.AspNetCore.Razor.Language.RazorDiagnostic)">
            <summary>
            Tracks the given <paramref name="error"/>.
            </summary>
            <param name="error">The <see cref="T:Microsoft.AspNetCore.Razor.Language.RazorDiagnostic"/> to track.</param>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Legacy.HtmlMarkupParser.IsCommentContentEndingInvalid(System.Collections.Generic.IEnumerable{Microsoft.AspNetCore.Razor.Language.Syntax.InternalSyntax.SyntaxToken})">
            <summary>
            Verifies, that the sequence doesn't end with the "&lt;!-" HtmlTokens. Note, the first token is an opening bracket token
            </summary>
        </member>
        <member name="T:Microsoft.AspNetCore.Razor.Language.Legacy.PartialParseResultInternal">
            <summary>
            The result of attempting an incremental parse
            </summary>
            <remarks>
            Either the Accepted or Rejected flag is ALWAYS set.
            Additionally, Provisional may be set with Accepted and SpanContextChanged may be set with Rejected.
            Provisional may NOT be set with Rejected and SpanContextChanged may NOT be set with Accepted.
            </remarks>
        </member>
        <member name="F:Microsoft.AspNetCore.Razor.Language.Legacy.PartialParseResultInternal.Rejected">
            <summary>
            Indicates that the edit could not be accepted and that a reparse is underway.
            </summary>
        </member>
        <member name="F:Microsoft.AspNetCore.Razor.Language.Legacy.PartialParseResultInternal.Accepted">
            <summary>
            Indicates that the edit was accepted and has been added to the parse tree
            </summary>
        </member>
        <member name="F:Microsoft.AspNetCore.Razor.Language.Legacy.PartialParseResultInternal.Provisional">
            <summary>
            Indicates that the edit was accepted, but that a reparse should be forced when idle time is available
            since the edit may be misclassified
            </summary>
            <remarks>
            This generally occurs when a "." is typed in an Implicit Expression, since editors require that this
            be assigned to Code in order to properly support features like IntelliSense.  However, if no further edits
            occur following the ".", it should be treated as Markup.
            </remarks>
        </member>
        <member name="F:Microsoft.AspNetCore.Razor.Language.Legacy.PartialParseResultInternal.SpanContextChanged">
            <summary>
            Indicates that the edit caused a change in the span's context and that if any statement completions were active prior to starting this
            partial parse, they should be reinitialized.
            </summary>
        </member>
        <member name="F:Microsoft.AspNetCore.Razor.Language.Legacy.PartialParseResultInternal.AutoCompleteBlock">
            <summary>
            Indicates that the edit requires an auto completion to occur
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Legacy.RemoveTagHelperChunkGenerator.Equals(System.Object)">
            <inheritdoc />
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Legacy.RemoveTagHelperChunkGenerator.GetHashCode">
            <inheritdoc />
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Legacy.SpanEditHandler.IsEndDeletion(Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxNode,Microsoft.AspNetCore.Razor.Language.SourceChange)">
            <summary>
            Returns true if the specified change is an insertion of text at the end of this span.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Legacy.SpanEditHandler.IsEndReplace(Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxNode,Microsoft.AspNetCore.Razor.Language.SourceChange)">
            <summary>
            Returns true if the specified change is a replacement of text at the end of this span.
            </summary>
        </member>
        <member name="T:Microsoft.AspNetCore.Razor.Language.Legacy.TagHelperDirectiveType">
            <summary>
            The type of tag helper directive.
            </summary>
        </member>
        <member name="F:Microsoft.AspNetCore.Razor.Language.Legacy.TagHelperDirectiveType.AddTagHelper">
            <summary>
            An <c>@addTagHelper</c> directive.
            </summary>
        </member>
        <member name="F:Microsoft.AspNetCore.Razor.Language.Legacy.TagHelperDirectiveType.RemoveTagHelper">
            <summary>
            A <c>@removeTagHelper</c> directive.
            </summary>
        </member>
        <member name="F:Microsoft.AspNetCore.Razor.Language.Legacy.TagHelperDirectiveType.TagHelperPrefix">
            <summary>
            A <c>@tagHelperPrefix</c> directive.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Legacy.TagHelperPrefixDirectiveChunkGenerator.Equals(System.Object)">
            <inheritdoc />
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Legacy.TagHelperPrefixDirectiveChunkGenerator.GetHashCode">
            <inheritdoc />
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Legacy.Tokenizer.Stop">
            <summary>
            Returns a result indicating that the machine should stop executing and return null output.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Legacy.Tokenizer.Transition(System.Int32)">
            <summary>
            Returns a result indicating that this state has no output and the machine should immediately invoke the specified state
            </summary>
            <remarks>
            By returning no output, the state machine will invoke the next state immediately, before returning
            controller to the caller of <see cref="M:Microsoft.AspNetCore.Razor.Language.Legacy.Tokenizer.Turn"/>
            </remarks>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Legacy.Tokenizer.Transition(System.Int32,Microsoft.AspNetCore.Razor.Language.Syntax.InternalSyntax.SyntaxToken)">
            <summary>
            Returns a result containing the specified output and indicating that the next call to
            <see cref="M:Microsoft.AspNetCore.Razor.Language.Legacy.Tokenizer.Turn"/> should invoke the provided state.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Legacy.Tokenizer.Stay">
            <summary>
            Returns a result indicating that this state has no output and the machine should remain in this state
            </summary>
            <remarks>
            By returning no output, the state machine will re-invoke the current state again before returning
            controller to the caller of <see cref="M:Microsoft.AspNetCore.Razor.Language.Legacy.Tokenizer.Turn"/>
            </remarks>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Legacy.Tokenizer.Stay(Microsoft.AspNetCore.Razor.Language.Syntax.InternalSyntax.SyntaxToken)">
            <summary>
            Returns a result containing the specified output and indicating that the next call to
            <see cref="M:Microsoft.AspNetCore.Razor.Language.Legacy.Tokenizer.Turn"/> should re-invoke the current state.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Legacy.Tokenizer.Lookahead(System.String,System.Boolean,System.Boolean)">
            <summary>
            Internal for unit testing
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Legacy.TokenizerBackedParser`1.LookaheadUntil(System.Func{Microsoft.AspNetCore.Razor.Language.Syntax.InternalSyntax.SyntaxToken,System.Collections.Generic.IEnumerable{Microsoft.AspNetCore.Razor.Language.Syntax.InternalSyntax.SyntaxToken},System.Boolean})">
            <summary>
            Looks forward until the specified condition is met.
            </summary>
            <param name="condition">A predicate accepting the token being evaluated and the list of tokens which have been looped through.</param>
            <returns>true, if the condition was met. false - if the condition wasn't met and the last token has already been processed.</returns>
            <remarks>The list of previous tokens is passed in the reverse order. So the last processed element will be the first one in the list.</remarks>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Legacy.TokenizerBackedParser`1.PutBack(System.Collections.Generic.IEnumerable{Microsoft.AspNetCore.Razor.Language.Syntax.InternalSyntax.SyntaxToken})">
            <summary>
            Put the specified tokens back in the input stream. The provided list MUST be in the ORDER THE TOKENS WERE READ. The
            list WILL be reversed and the Putback(SyntaxToken) will be called on each item.
            </summary>
            <remarks>
            If a document contains tokens: a, b, c, d, e, f
            and AcceptWhile or AcceptUntil is used to collect until d
            the list returned by AcceptWhile/Until will contain: a, b, c IN THAT ORDER
            that is the correct format for providing to this method. The caller of this method would,
            in that case, want to put c, b and a back into the stream, so "a, b, c" is the CORRECT order
            </remarks>
        </member>
        <member name="T:Microsoft.AspNetCore.Razor.Language.NotFoundProjectItem">
            <summary>
            A <see cref="T:Microsoft.AspNetCore.Razor.Language.RazorProjectItem"/> that does not exist.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.NotFoundProjectItem.#ctor(System.String,System.String,System.String)">
            <summary>
            Initializes a new instance of <see cref="T:Microsoft.AspNetCore.Razor.Language.NotFoundProjectItem"/>.
            </summary>
            <param name="basePath">The base path.</param>
            <param name="path">The path.</param>
            <param name="fileKind">The file kind</param>
        </member>
        <member name="P:Microsoft.AspNetCore.Razor.Language.NotFoundProjectItem.BasePath">
            <inheritdoc />
        </member>
        <member name="P:Microsoft.AspNetCore.Razor.Language.NotFoundProjectItem.FilePath">
            <inheritdoc />
        </member>
        <member name="P:Microsoft.AspNetCore.Razor.Language.NotFoundProjectItem.FileKind">
            <inheritdoc />
        </member>
        <member name="P:Microsoft.AspNetCore.Razor.Language.NotFoundProjectItem.Exists">
            <inheritdoc />
        </member>
        <member name="P:Microsoft.AspNetCore.Razor.Language.NotFoundProjectItem.PhysicalPath">
            <inheritdoc />
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.NotFoundProjectItem.Read">
            <inheritdoc />
        </member>
        <member name="P:Microsoft.AspNetCore.Razor.Language.RazorCodeGenerationOptions.RootNamespace">
            <summary>
            Gets the root namespace for the generated code.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Razor.Language.RazorCodeGenerationOptions.SuppressChecksum">
            <summary>
            Gets a value that indicates whether to suppress the default <c>#pragma checksum</c> directive in the 
            generated C# code. If <c>false</c> the checkum directive will be included, otherwise it will not be
            generated. Defaults to <c>false</c>, meaning that the checksum will be included.
            </summary>
            <remarks>
            The <c>#pragma checksum</c> is required to enable debugging and should only be supressed for testing
            purposes.
            </remarks>
        </member>
        <member name="P:Microsoft.AspNetCore.Razor.Language.RazorCodeGenerationOptions.SuppressMetadataAttributes">
            <summary>
            Gets a value that indicates whether to suppress the default metadata attributes in the generated 
            C# code. If <c>false</c> the default attributes will be included, otherwise they will not be generated.
            Defaults to <c>false</c> at run time, meaning that the attributes will be included. Defaults to
            <c>true</c> at design time, meaning that the attributes will not be included.
            </summary>
            <remarks>
            <para>
            The <c>Microsoft.AspNetCore.Razor.Runtime</c> package includes a default set of attributes intended
            for runtimes to discover metadata about the compiled code.
            </para>
            <para>
            The default metadata attributes should be suppressed if code generation targets a runtime without
            a reference to <c>Microsoft.AspNetCore.Razor.Runtime</c>, or for testing purposes.
            </para>
            </remarks>
        </member>
        <member name="P:Microsoft.AspNetCore.Razor.Language.RazorCodeGenerationOptions.SuppressPrimaryMethodBody">
            <summary>
            Gets or sets a value that determines if an empty body is generated for the primary method.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Razor.Language.RazorCodeGenerationOptions.SuppressNullabilityEnforcement">
            <summary>
            Gets a value that determines if nullability type enforcement should be suppressed for user code.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Razor.Language.RazorCodeGenerationOptionsBuilder.RootNamespace">
            <summary>
            Gets or sets the root namespace of the generated code.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Razor.Language.RazorCodeGenerationOptionsBuilder.SuppressChecksum">
            <summary>
            Gets or sets a value that indicates whether to suppress the default <c>#pragma checksum</c> directive in the 
            generated C# code. If <c>false</c> the checkum directive will be included, otherwise it will not be
            generated. Defaults to <c>false</c>, meaning that the checksum will be included.
            </summary>
            <remarks>
            The <c>#pragma checksum</c> is required to enable debugging and should only be supressed for testing
            purposes.
            </remarks>
        </member>
        <member name="P:Microsoft.AspNetCore.Razor.Language.RazorCodeGenerationOptionsBuilder.SuppressMetadataAttributes">
            <summary>
            Gets or setsa value that indicates whether to suppress the default metadata attributes in the generated 
            C# code. If <c>false</c> the default attributes will be included, otherwise they will not be generated.
            Defaults to <c>false</c> at run time, meaning that the attributes will be included. Defaults to
            <c>true</c> at design time, meaning that the attributes will not be included.
            </summary>
            <remarks>
            <para>
            The <c>Microsoft.AspNetCore.Razor.Runtime</c> package includes a default set of attributes intended
            for runtimes to discover metadata about the compiled code.
            </para>
            <para>
            The default metadata attributes should be suppressed if code generation targets a runtime without
            a reference to <c>Microsoft.AspNetCore.Razor.Runtime</c>, or for testing purposes.
            </para>
            </remarks>
        </member>
        <member name="P:Microsoft.AspNetCore.Razor.Language.RazorCodeGenerationOptionsBuilder.SuppressPrimaryMethodBody">
            <summary>
            Gets or sets a value that determines if an empty body is generated for the primary method.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Razor.Language.RazorCodeGenerationOptionsBuilder.SuppressNullabilityEnforcement">
            <summary>
            Gets or sets a value that determines if nullability type enforcement should be suppressed for user code.
            </summary>
        </member>
        <member name="T:Microsoft.AspNetCore.Razor.Language.RazorEngineBuilderExtensions">
            <summary>
            Extension methods to <see cref="T:Microsoft.AspNetCore.Razor.Language.IRazorEngineBuilder" />.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.RazorEngineBuilderExtensions.AddDirective(Microsoft.AspNetCore.Razor.Language.IRazorEngineBuilder,Microsoft.AspNetCore.Razor.Language.DirectiveDescriptor)">
            <summary>
            Adds the specified <see cref="T:Microsoft.AspNetCore.Razor.Language.DirectiveDescriptor"/>.
            </summary>
            <param name="builder">The <see cref="T:Microsoft.AspNetCore.Razor.Language.IRazorEngineBuilder"/>.</param>
            <param name="directive">The <see cref="T:Microsoft.AspNetCore.Razor.Language.DirectiveDescriptor"/> to add.</param>
            <returns>The <see cref="T:Microsoft.AspNetCore.Razor.Language.IRazorEngineBuilder"/>.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.RazorEngineBuilderExtensions.AddTargetExtension(Microsoft.AspNetCore.Razor.Language.IRazorEngineBuilder,Microsoft.AspNetCore.Razor.Language.CodeGeneration.ICodeTargetExtension)">
            <summary>
            Adds the specified <see cref="T:Microsoft.AspNetCore.Razor.Language.CodeGeneration.ICodeTargetExtension"/>.
            </summary>
            <param name="builder">The <see cref="T:Microsoft.AspNetCore.Razor.Language.IRazorEngineBuilder"/>.</param>
            <param name="extension">The <see cref="T:Microsoft.AspNetCore.Razor.Language.CodeGeneration.ICodeTargetExtension"/> to add.</param>
            <returns>The <see cref="T:Microsoft.AspNetCore.Razor.Language.IRazorEngineBuilder"/>.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.RazorEngineBuilderExtensions.SetBaseType(Microsoft.AspNetCore.Razor.Language.IRazorEngineBuilder,System.String)">
            <summary>
            Sets the base type for generated types.
            </summary>
            <param name="builder">The <see cref="T:Microsoft.AspNetCore.Razor.Language.IRazorEngineBuilder"/>.</param>
            <param name="baseType">The name of the base type.</param>
            <returns>The <see cref="T:Microsoft.AspNetCore.Razor.Language.IRazorEngineBuilder"/>.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.RazorEngineBuilderExtensions.ConfigureClass(Microsoft.AspNetCore.Razor.Language.IRazorEngineBuilder,System.Action{Microsoft.AspNetCore.Razor.Language.RazorCodeDocument,Microsoft.AspNetCore.Razor.Language.Intermediate.ClassDeclarationIntermediateNode})">
            <summary>
            Registers a class configuration delegate that gets invoked during code generation.
            </summary>
            <param name="builder">The <see cref="T:Microsoft.AspNetCore.Razor.Language.IRazorEngineBuilder"/>.</param>
            <param name="configureClass"><see cref="T:System.Action"/> invoked to configure 
            <see cref="T:Microsoft.AspNetCore.Razor.Language.Intermediate.ClassDeclarationIntermediateNode"/> during code generation.</param>
            <returns>The <see cref="T:Microsoft.AspNetCore.Razor.Language.IRazorEngineBuilder"/>.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.RazorEngineBuilderExtensions.SetNamespace(Microsoft.AspNetCore.Razor.Language.IRazorEngineBuilder,System.String)">
            <summary>
            Sets the namespace for generated types.
            </summary>
            <param name="builder">The <see cref="T:Microsoft.AspNetCore.Razor.Language.IRazorEngineBuilder"/>.</param>
            <param name="namespaceName">The name of the namespace.</param>
            <returns>The <see cref="T:Microsoft.AspNetCore.Razor.Language.IRazorEngineBuilder"/>.</returns>
        </member>
        <member name="P:Microsoft.AspNetCore.Razor.Language.RazorParserOptions.ParseLeadingDirectives">
            <summary>
            Gets a value which indicates whether the parser will parse only the leading directives. If <c>true</c>
            the parser will halt at the first HTML content or C# code block. If <c>false</c> the whole document is parsed.
            </summary>
            <remarks>
            Currently setting this option to <c>true</c> will result in only the first line of directives being parsed.
            In a future release this may be updated to include all leading directive content.
            </remarks>
        </member>
        <member name="T:Microsoft.AspNetCore.Razor.Language.RazorProject">
            <summary>
            An abstraction for working with a project containing Razor files.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.RazorProject.EnumerateItems(System.String)">
            <summary>
            Gets a sequence of <see cref="T:Microsoft.AspNetCore.Razor.Language.RazorProjectItem"/> under the specific path in the project.
            </summary>
            <param name="basePath">The base path.</param>
            <returns>The sequence of <see cref="T:Microsoft.AspNetCore.Razor.Language.RazorProjectItem"/>.</returns>
            <remarks>
            Project items returned by this method have inferred FileKinds from their corresponding file paths.
            </remarks>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.RazorProject.GetItem(System.String)">
            <summary>
            Gets a <see cref="T:Microsoft.AspNetCore.Razor.Language.RazorProjectItem"/> for the specified path.
            </summary>
            <param name="path">The path.</param>
            <returns>The <see cref="T:Microsoft.AspNetCore.Razor.Language.RazorProjectItem"/>.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.RazorProject.GetItem(System.String,System.String)">
            <summary>
            Gets a <see cref="T:Microsoft.AspNetCore.Razor.Language.RazorProjectItem"/> for the specified path.
            </summary>
            <param name="path">The path.</param>
            <param name="fileKind">The file kind</param>
            <returns>The <see cref="T:Microsoft.AspNetCore.Razor.Language.RazorProjectItem"/>.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.RazorProject.FindHierarchicalItems(System.String,System.String)">
            <summary>
            Gets the sequence of files named <paramref name="fileName"/> that are applicable to the specified path.
            </summary>
            <param name="path">The path of a project item.</param>
            <param name="fileName">The file name to seek.</param>
            <returns>A sequence of applicable <see cref="T:Microsoft.AspNetCore.Razor.Language.RazorProjectItem"/> instances.</returns>
            <remarks>
            This method returns paths starting from the directory of <paramref name="path"/> and
            traverses to the project root.
            e.g.
            /Views/Home/View.cshtml -> [ /Views/Home/FileName.cshtml, /Views/FileName.cshtml, /FileName.cshtml ]
            
            Project items returned by this method have inferred FileKinds from their corresponding file paths.
            </remarks>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.RazorProject.FindHierarchicalItems(System.String,System.String,System.String)">
            <summary>
            Gets the sequence of files named <paramref name="fileName"/> that are applicable to the specified path.
            </summary>
            <param name="basePath">The base path.</param>
            <param name="path">The path of a project item.</param>
            <param name="fileName">The file name to seek.</param>
            <returns>A sequence of applicable <see cref="T:Microsoft.AspNetCore.Razor.Language.RazorProjectItem"/> instances.</returns>
            <remarks>
            This method returns paths starting from the directory of <paramref name="path"/> and
            traverses to the <paramref name="basePath"/>.
            e.g.
            (/Views, /Views/Home/View.cshtml) -> [ /Views/Home/FileName.cshtml, /Views/FileName.cshtml ]
            
            Project items returned by this method have inferred FileKinds from their corresponding file paths.
            </remarks>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.RazorProject.NormalizeAndEnsureValidPath(System.String)">
            <summary>
            Performs validation for paths passed to methods of <see cref="T:Microsoft.AspNetCore.Razor.Language.RazorProject"/>.
            </summary>
            <param name="path">The path to validate.</param>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.RazorProjectEngineBuilderExtensions.ConfigureClass(Microsoft.AspNetCore.Razor.Language.RazorProjectEngineBuilder,System.Action{Microsoft.AspNetCore.Razor.Language.RazorCodeDocument,Microsoft.AspNetCore.Razor.Language.Intermediate.ClassDeclarationIntermediateNode})">
            <summary>
            Registers a class configuration delegate that gets invoked during code generation.
            </summary>
            <param name="builder">The <see cref="T:Microsoft.AspNetCore.Razor.Language.RazorProjectEngineBuilder"/>.</param>
            <param name="configureClass"><see cref="T:System.Action"/> invoked to configure 
            <see cref="T:Microsoft.AspNetCore.Razor.Language.Intermediate.ClassDeclarationIntermediateNode"/> during code generation.</param>
            <returns>The <see cref="T:Microsoft.AspNetCore.Razor.Language.RazorProjectEngineBuilder"/>.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.RazorProjectEngineBuilderExtensions.SetBaseType(Microsoft.AspNetCore.Razor.Language.RazorProjectEngineBuilder,System.String)">
            <summary>
            Sets the base type for generated types.
            </summary>
            <param name="builder">The <see cref="T:Microsoft.AspNetCore.Razor.Language.RazorProjectEngineBuilder"/>.</param>
            <param name="baseType">The name of the base type.</param>
            <returns>The <see cref="T:Microsoft.AspNetCore.Razor.Language.RazorProjectEngineBuilder"/>.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.RazorProjectEngineBuilderExtensions.SetNamespace(Microsoft.AspNetCore.Razor.Language.RazorProjectEngineBuilder,System.String)">
            <summary>
            Sets the namespace for generated types.
            </summary>
            <param name="builder">The <see cref="T:Microsoft.AspNetCore.Razor.Language.RazorProjectEngineBuilder"/>.</param>
            <param name="namespaceName">The name of the namespace.</param>
            <returns>The <see cref="T:Microsoft.AspNetCore.Razor.Language.RazorProjectEngineBuilder"/>.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.RazorProjectEngineBuilderExtensions.SetRootNamespace(Microsoft.AspNetCore.Razor.Language.RazorProjectEngineBuilder,System.String)">
            <summary>
            Sets the root namespace for the generated code.
            </summary>
            <param name="builder">The <see cref="T:Microsoft.AspNetCore.Razor.Language.RazorProjectEngineBuilder"/>.</param>
            <param name="rootNamespace">The root namespace.</param>
            <returns>The <see cref="T:Microsoft.AspNetCore.Razor.Language.RazorProjectEngineBuilder"/>.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.RazorProjectEngineBuilderExtensions.AddTargetExtension(Microsoft.AspNetCore.Razor.Language.RazorProjectEngineBuilder,Microsoft.AspNetCore.Razor.Language.CodeGeneration.ICodeTargetExtension)">
            <summary>
            Adds the specified <see cref="T:Microsoft.AspNetCore.Razor.Language.CodeGeneration.ICodeTargetExtension"/>.
            </summary>
            <param name="builder">The <see cref="T:Microsoft.AspNetCore.Razor.Language.RazorProjectEngineBuilder"/>.</param>
            <param name="extension">The <see cref="T:Microsoft.AspNetCore.Razor.Language.CodeGeneration.ICodeTargetExtension"/> to add.</param>
            <returns>The <see cref="T:Microsoft.AspNetCore.Razor.Language.RazorProjectEngineBuilder"/>.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.RazorProjectEngineBuilderExtensions.AddDirective(Microsoft.AspNetCore.Razor.Language.RazorProjectEngineBuilder,Microsoft.AspNetCore.Razor.Language.DirectiveDescriptor)">
            <summary>
            Adds the specified <see cref="T:Microsoft.AspNetCore.Razor.Language.DirectiveDescriptor"/>.
            </summary>
            <param name="builder">The <see cref="T:Microsoft.AspNetCore.Razor.Language.RazorProjectEngineBuilder"/>.</param>
            <param name="directive">The <see cref="T:Microsoft.AspNetCore.Razor.Language.DirectiveDescriptor"/> to add.</param>
            <returns>The <see cref="T:Microsoft.AspNetCore.Razor.Language.RazorProjectEngineBuilder"/>.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.RazorProjectEngineBuilderExtensions.AddDirective(Microsoft.AspNetCore.Razor.Language.RazorProjectEngineBuilder,Microsoft.AspNetCore.Razor.Language.DirectiveDescriptor,System.String[])">
            <summary>
            Adds the specified <see cref="T:Microsoft.AspNetCore.Razor.Language.DirectiveDescriptor"/> for the provided file kind.
            </summary>
            <param name="builder">The <see cref="T:Microsoft.AspNetCore.Razor.Language.RazorProjectEngineBuilder"/>.</param>
            <param name="directive">The <see cref="T:Microsoft.AspNetCore.Razor.Language.DirectiveDescriptor"/> to add.</param>
            <param name="fileKinds">The file kinds, for which to register the directive. See <see cref="T:Microsoft.AspNetCore.Razor.Language.FileKinds"/>.</param>
            <returns>The <see cref="T:Microsoft.AspNetCore.Razor.Language.RazorProjectEngineBuilder"/>.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.RazorProjectEngineBuilderExtensions.AddDefaultImports(Microsoft.AspNetCore.Razor.Language.RazorProjectEngineBuilder,System.String[])">
            <summary>
            Adds the provided <see cref="T:Microsoft.AspNetCore.Razor.Language.RazorProjectItem" />s as imports to all project items processed
            by the <see cref="T:Microsoft.AspNetCore.Razor.Language.RazorProjectEngine"/>.
            </summary>
            <param name="builder">The <see cref="T:Microsoft.AspNetCore.Razor.Language.RazorProjectEngineBuilder"/>.</param>
            <param name="imports">The collection of imports.</param>
            <returns>The <see cref="T:Microsoft.AspNetCore.Razor.Language.RazorProjectEngineBuilder"/>.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.RazorProjectFileSystem.Create(System.String)">
            <summary>
            Create a Razor project file system based off of a root directory.
            </summary>
            <param name="rootDirectoryPath">The directory to root the file system at.</param>
            <returns>A <see cref="T:Microsoft.AspNetCore.Razor.Language.RazorProjectFileSystem"/></returns>
        </member>
        <member name="T:Microsoft.AspNetCore.Razor.Language.RazorProjectItem">
            <summary>
            An item in a <see cref="T:Microsoft.AspNetCore.Razor.Language.RazorProjectFileSystem"/>.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Razor.Language.RazorProjectItem.BasePath">
            <summary>
            Path specified in <see cref="M:Microsoft.AspNetCore.Razor.Language.RazorProject.EnumerateItems(System.String)"/>.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Razor.Language.RazorProjectItem.FilePath">
            <summary>
            File path relative to <see cref="P:Microsoft.AspNetCore.Razor.Language.RazorProjectItem.BasePath"/>. This property uses the project path syntax,
            using <c>/</c> as a path separator and does not follow the operating system's file system
            conventions.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Razor.Language.RazorProjectItem.PhysicalPath">
            <summary>
            The absolute physical (file system) path to the file, including the file name.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Razor.Language.RazorProjectItem.RelativePhysicalPath">
            <summary>
            The relative physical (file system) path to the file, including the file name. Relative to the
            physical path of the <see cref="P:Microsoft.AspNetCore.Razor.Language.RazorProjectItem.BasePath"/>.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Razor.Language.RazorProjectItem.FileKind">
            <summary>
            Gets the document kind that should be used for the generated document. If possible this will be inferred from the file path. May be null.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.RazorProjectItem.Read">
            <summary>
            Gets the file contents as readonly <see cref="T:System.IO.Stream"/>.
            </summary>
            <returns>The <see cref="T:System.IO.Stream"/>.</returns>
        </member>
        <member name="P:Microsoft.AspNetCore.Razor.Language.RazorProjectItem.Exists">
            <summary>
            Gets a value that determines if the file exists.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Razor.Language.RazorProjectItem.CombinedPath">
            <summary>
            The root relative path of the item.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Razor.Language.RazorProjectItem.Extension">
            <summary>
            The extension of the file.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Razor.Language.RazorProjectItem.FileName">
            <summary>
            The name of the file including the extension.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Razor.Language.RazorProjectItem.FilePathWithoutExtension">
            <summary>
            File path relative to <see cref="P:Microsoft.AspNetCore.Razor.Language.RazorProjectItem.BasePath"/> without the extension.
            </summary>
        </member>
        <member name="T:Microsoft.AspNetCore.Razor.Language.RazorSourceDocument">
            <summary>
            The Razor template source.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Razor.Language.RazorSourceDocument.Encoding">
            <summary>
            Gets the encoding of the text in the original source document.
            </summary>
            <remarks>
            Depending on the method used to create a <see cref="T:Microsoft.AspNetCore.Razor.Language.RazorSourceDocument"/> the encoding may be used to
            read the file contents, or it may be solely informational. Refer to the documentation on the method
            used to create the <see cref="T:Microsoft.AspNetCore.Razor.Language.RazorSourceDocument"/> for details.
            </remarks>
        </member>
        <member name="P:Microsoft.AspNetCore.Razor.Language.RazorSourceDocument.FilePath">
            <summary>
            Gets the file path of the orginal source document.
            </summary>
            <remarks>
            The file path may be either an absolute path or project-relative path. An absolute path is required
            to generate debuggable assemblies.
            </remarks>
        </member>
        <member name="P:Microsoft.AspNetCore.Razor.Language.RazorSourceDocument.RelativePath">
            <summary>
            Gets the project-relative path to the source file. May be <c>null</c>.
            </summary>
            <remarks>
            The relative path (if provided) is used for display (error messages). The project-relative path may also
            be used to embed checksums of the original source documents to support runtime recompilation of Razor code.
            </remarks>
        </member>
        <member name="P:Microsoft.AspNetCore.Razor.Language.RazorSourceDocument.Item(System.Int32)">
            <summary>
            Gets a character at given position.
            </summary>
            <param name="position">The position to get the character from.</param>
        </member>
        <member name="P:Microsoft.AspNetCore.Razor.Language.RazorSourceDocument.Length">
            <summary>
            Gets the length of the text in characters.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Razor.Language.RazorSourceDocument.Lines">
            <summary>
            Gets the <see cref="T:Microsoft.AspNetCore.Razor.Language.RazorSourceLineCollection"/>.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.RazorSourceDocument.CopyTo(System.Int32,System.Char[],System.Int32,System.Int32)">
            <summary>
            Copies a range of characters from the <see cref="T:Microsoft.AspNetCore.Razor.Language.RazorSourceDocument"/> to the specified <paramref name="destination"/>.
            </summary>
            <param name="sourceIndex">The index of the first character in this instance to copy.</param>
            <param name="destination">The destination buffer.</param>
            <param name="destinationIndex">The index in destination at which the copy operation begins.</param>
            <param name="count">The number of characters in this instance to copy to destination.</param>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.RazorSourceDocument.GetChecksum">
            <summary>
            Calculates the checksum for the <see cref="T:Microsoft.AspNetCore.Razor.Language.RazorSourceDocument"/>.
            </summary>
            <returns>The checksum.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.RazorSourceDocument.GetChecksumAlgorithm">
            <summary>
            Gets the name of the algorithm used to compute the checksum returned by <see cref="M:Microsoft.AspNetCore.Razor.Language.RazorSourceDocument.GetChecksum"/>.
            </summary>
            <remarks>
            This member did not exist in the 2.0 release, so it is possible for an implementation to return
            the wrong value (or <c>null</c>). Implementations of <see cref="T:Microsoft.AspNetCore.Razor.Language.RazorSourceDocument"/> should
            override this member and specify their choice of hash algorithm even if it is the same as the
            default (<c>SHA1</c>).
            </remarks>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.RazorSourceDocument.GetFilePathForDisplay">
            <summary>
            Gets the file path in a format that should be used for display.
            </summary>
            <returns>The <see cref="P:Microsoft.AspNetCore.Razor.Language.RazorSourceDocument.RelativePath"/> if set, or the <see cref="P:Microsoft.AspNetCore.Razor.Language.RazorSourceDocument.FilePath"/>.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.RazorSourceDocument.ReadFrom(System.IO.Stream,System.String)">
            <summary>
            Reads the <see cref="T:Microsoft.AspNetCore.Razor.Language.RazorSourceDocument"/> from the specified <paramref name="stream"/>.
            </summary>
            <param name="stream">The <see cref="T:System.IO.Stream"/> to read from.</param>
            <param name="fileName">The file name of the template.</param>
            <returns>The <see cref="T:Microsoft.AspNetCore.Razor.Language.RazorSourceDocument"/>.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.RazorSourceDocument.ReadFrom(System.IO.Stream,System.String,System.Text.Encoding)">
            <summary>
            Reads the <see cref="T:Microsoft.AspNetCore.Razor.Language.RazorSourceDocument"/> from the specified <paramref name="stream"/>.
            </summary>
            <param name="stream">The <see cref="T:System.IO.Stream"/> to read from.</param>
            <param name="fileName">The file name of the template.</param>
            <param name="encoding">The <see cref="T:System.Text.Encoding"/> to use to read the <paramref name="stream"/>.</param>
            <returns>The <see cref="T:Microsoft.AspNetCore.Razor.Language.RazorSourceDocument"/>.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.RazorSourceDocument.ReadFrom(System.IO.Stream,System.Text.Encoding,Microsoft.AspNetCore.Razor.Language.RazorSourceDocumentProperties)">
            <summary>
            Reads the <see cref="T:Microsoft.AspNetCore.Razor.Language.RazorSourceDocument"/> from the specified <paramref name="stream"/>.
            </summary>
            <param name="stream">The <see cref="T:System.IO.Stream"/> to read from.</param>
            <param name="encoding">The <see cref="T:System.Text.Encoding"/> to use to read the <paramref name="stream"/>.</param>
            <param name="properties">Properties to configure the <see cref="T:Microsoft.AspNetCore.Razor.Language.RazorSourceDocument"/>.</param>
            <returns>The <see cref="T:Microsoft.AspNetCore.Razor.Language.RazorSourceDocument"/>.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.RazorSourceDocument.ReadFrom(Microsoft.AspNetCore.Razor.Language.RazorProjectItem)">
            <summary>
            Reads the <see cref="T:Microsoft.AspNetCore.Razor.Language.RazorSourceDocument"/> from the specified <paramref name="projectItem"/>.
            </summary>
            <param name="projectItem">The <see cref="T:Microsoft.AspNetCore.Razor.Language.RazorProjectItem"/> to read from.</param>
            <returns>The <see cref="T:Microsoft.AspNetCore.Razor.Language.RazorSourceDocument"/>.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.RazorSourceDocument.Create(System.String,System.String)">
            <summary>
            Creates a <see cref="T:Microsoft.AspNetCore.Razor.Language.RazorSourceDocument"/> from the specified <paramref name="content"/>.
            </summary>
            <param name="content">The source document content.</param>
            <param name="fileName">The file name of the <see cref="T:Microsoft.AspNetCore.Razor.Language.RazorSourceDocument"/>.</param>
            <returns>The <see cref="T:Microsoft.AspNetCore.Razor.Language.RazorSourceDocument"/>.</returns>
            <remarks>Uses <see cref="P:System.Text.Encoding.UTF8" /></remarks>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.RazorSourceDocument.Create(System.String,Microsoft.AspNetCore.Razor.Language.RazorSourceDocumentProperties)">
            <summary>
            Creates a <see cref="T:Microsoft.AspNetCore.Razor.Language.RazorSourceDocument"/> from the specified <paramref name="content"/>.
            </summary>
            <param name="content">The source document content.</param>
            <param name="properties">Properties to configure the <see cref="T:Microsoft.AspNetCore.Razor.Language.RazorSourceDocument"/>.</param>
            <returns>The <see cref="T:Microsoft.AspNetCore.Razor.Language.RazorSourceDocument"/>.</returns>
            <remarks>Uses <see cref="P:System.Text.Encoding.UTF8" /></remarks>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.RazorSourceDocument.Create(System.String,System.String,System.Text.Encoding)">
            <summary>
            Creates a <see cref="T:Microsoft.AspNetCore.Razor.Language.RazorSourceDocument"/> from the specified <paramref name="content"/>.
            </summary>
            <param name="content">The source document content.</param>
            <param name="fileName">The file name of the <see cref="T:Microsoft.AspNetCore.Razor.Language.RazorSourceDocument"/>.</param>
            <param name="encoding">The <see cref="T:System.Text.Encoding"/> of the file <paramref name="content"/> was read from.</param>
            <returns>The <see cref="T:Microsoft.AspNetCore.Razor.Language.RazorSourceDocument"/>.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.RazorSourceDocument.Create(System.String,System.Text.Encoding,Microsoft.AspNetCore.Razor.Language.RazorSourceDocumentProperties)">
            <summary>
            Creates a <see cref="T:Microsoft.AspNetCore.Razor.Language.RazorSourceDocument"/> from the specified <paramref name="content"/>.
            </summary>
            <param name="content">The source document content.</param>
            <param name="encoding">The encoding of the source document.</param>
            <param name="properties">Properties to configure the <see cref="T:Microsoft.AspNetCore.Razor.Language.RazorSourceDocument"/>.</param>
            <returns>The <see cref="T:Microsoft.AspNetCore.Razor.Language.RazorSourceDocument"/>.</returns>
        </member>
        <member name="T:Microsoft.AspNetCore.Razor.Language.RazorSourceDocumentProperties">
            <summary>
            Use to configure optional properties for creating a <see cref="T:Microsoft.AspNetCore.Razor.Language.RazorSourceDocument"/>.
            </summary>
        </member>
        <member name="F:Microsoft.AspNetCore.Razor.Language.RazorSourceDocumentProperties.Default">
            <summary>
            A <see cref="T:Microsoft.AspNetCore.Razor.Language.RazorSourceDocumentProperties"/> with default values.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.RazorSourceDocumentProperties.#ctor">
            <summary>
            Creates a new <see cref="T:Microsoft.AspNetCore.Razor.Language.RazorSourceDocumentProperties"/>.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.RazorSourceDocumentProperties.#ctor(System.String,System.String)">
            <summary>
            Creates a new <see cref="T:Microsoft.AspNetCore.Razor.Language.RazorSourceDocumentProperties"/>.
            </summary>
            <param name="filePath">
            The path to the source file. Provide an rooted path if possible. May be <c>null</c>.
            </param>
            <param name="relativePath">
            The project-relative path to the source file. May be <c>null</c>. Must be a non-rooted path.
            </param>
        </member>
        <member name="P:Microsoft.AspNetCore.Razor.Language.RazorSourceDocumentProperties.FilePath">
            <summary>
            Gets the path to the source file. May be an absolute or project-relative path. May be <c>null</c>.
            </summary>
            <remarks>
            An absolute path must be provided to generate debuggable assemblies.
            </remarks>
        </member>
        <member name="P:Microsoft.AspNetCore.Razor.Language.RazorSourceDocumentProperties.RelativePath">
            <summary>
            Gets the project-relative path to the source file. May be <c>null</c>.
            </summary>
            <remarks>
            The relative path (if provided) is used for display (error messages). The project-relative path may also
            be used to embed checksums of the original source documents to support runtime recompilation of Razor code.
            </remarks>
        </member>
        <member name="T:Microsoft.AspNetCore.Razor.Language.RequiredAttributeDescriptor.NameComparisonMode">
            <summary>
            Acceptable <see cref="P:Microsoft.AspNetCore.Razor.Language.RequiredAttributeDescriptor.Name"/> comparison modes.
            </summary>
        </member>
        <member name="F:Microsoft.AspNetCore.Razor.Language.RequiredAttributeDescriptor.NameComparisonMode.FullMatch">
            <summary>
            HTML attribute name case insensitively matches <see cref="P:Microsoft.AspNetCore.Razor.Language.RequiredAttributeDescriptor.Name"/>.
            </summary>
        </member>
        <member name="F:Microsoft.AspNetCore.Razor.Language.RequiredAttributeDescriptor.NameComparisonMode.PrefixMatch">
            <summary>
            HTML attribute name case insensitively starts with <see cref="P:Microsoft.AspNetCore.Razor.Language.RequiredAttributeDescriptor.Name"/>.
            </summary>
        </member>
        <member name="T:Microsoft.AspNetCore.Razor.Language.RequiredAttributeDescriptor.ValueComparisonMode">
            <summary>
            Acceptable <see cref="P:Microsoft.AspNetCore.Razor.Language.RequiredAttributeDescriptor.Value"/> comparison modes.
            </summary>
        </member>
        <member name="F:Microsoft.AspNetCore.Razor.Language.RequiredAttributeDescriptor.ValueComparisonMode.None">
            <summary>
            HTML attribute value always matches <see cref="P:Microsoft.AspNetCore.Razor.Language.RequiredAttributeDescriptor.Value"/>.
            </summary>
        </member>
        <member name="F:Microsoft.AspNetCore.Razor.Language.RequiredAttributeDescriptor.ValueComparisonMode.FullMatch">
            <summary>
            HTML attribute value case sensitively matches <see cref="P:Microsoft.AspNetCore.Razor.Language.RequiredAttributeDescriptor.Value"/>.
            </summary>
        </member>
        <member name="F:Microsoft.AspNetCore.Razor.Language.RequiredAttributeDescriptor.ValueComparisonMode.PrefixMatch">
            <summary>
            HTML attribute value case sensitively starts with <see cref="P:Microsoft.AspNetCore.Razor.Language.RequiredAttributeDescriptor.Value"/>.
            </summary>
        </member>
        <member name="F:Microsoft.AspNetCore.Razor.Language.RequiredAttributeDescriptor.ValueComparisonMode.SuffixMatch">
            <summary>
            HTML attribute value case sensitively ends with <see cref="P:Microsoft.AspNetCore.Razor.Language.RequiredAttributeDescriptor.Value"/>.
            </summary>
        </member>
        <member name="T:Microsoft.AspNetCore.Razor.Language.RequiredAttributeDescriptorComparer">
            <summary>
            An <see cref="T:System.Collections.Generic.IEqualityComparer`1"/> used to check equality between
            two <see cref="T:Microsoft.AspNetCore.Razor.Language.RequiredAttributeDescriptor"/>s.
            </summary>
        </member>
        <member name="F:Microsoft.AspNetCore.Razor.Language.RequiredAttributeDescriptorComparer.Default">
            <summary>
            A default instance of the <see cref="T:Microsoft.AspNetCore.Razor.Language.RequiredAttributeDescriptorComparer"/>.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.RequiredAttributeDescriptorComparer.Equals(Microsoft.AspNetCore.Razor.Language.RequiredAttributeDescriptor,Microsoft.AspNetCore.Razor.Language.RequiredAttributeDescriptor)">
            <inheritdoc />
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.RequiredAttributeDescriptorComparer.GetHashCode(Microsoft.AspNetCore.Razor.Language.RequiredAttributeDescriptor)">
            <inheritdoc />
        </member>
        <member name="T:Microsoft.AspNetCore.Razor.Language.SourceLocation">
            <summary>
            A location in a Razor file.
            </summary>
        </member>
        <member name="F:Microsoft.AspNetCore.Razor.Language.SourceLocation.Undefined">
            <summary>
            An undefined <see cref="T:Microsoft.AspNetCore.Razor.Language.SourceLocation"/>.
            </summary>
        </member>
        <member name="F:Microsoft.AspNetCore.Razor.Language.SourceLocation.Zero">
            <summary>
            A <see cref="T:Microsoft.AspNetCore.Razor.Language.SourceLocation"/> with <see cref="P:Microsoft.AspNetCore.Razor.Language.SourceLocation.AbsoluteIndex"/>, <see cref="P:Microsoft.AspNetCore.Razor.Language.SourceLocation.LineIndex"/>, and
            <see cref="P:Microsoft.AspNetCore.Razor.Language.SourceLocation.CharacterIndex"/> initialized to 0.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.SourceLocation.#ctor(System.Int32,System.Int32,System.Int32)">
            <summary>
            Initializes a new instance of <see cref="T:Microsoft.AspNetCore.Razor.Language.SourceLocation"/>.
            </summary>
            <param name="absoluteIndex">The absolute index.</param>
            <param name="lineIndex">The line index.</param>
            <param name="characterIndex">The character index.</param>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.SourceLocation.#ctor(System.String,System.Int32,System.Int32,System.Int32)">
            <summary>
            Initializes a new instance of <see cref="T:Microsoft.AspNetCore.Razor.Language.SourceLocation"/>.
            </summary>
            <param name="filePath">The file path.</param>
            <param name="absoluteIndex">The absolute index.</param>
            <param name="lineIndex">The line index.</param>
            <param name="characterIndex">The character index.</param>
        </member>
        <member name="P:Microsoft.AspNetCore.Razor.Language.SourceLocation.FilePath">
            <summary>
            Path of the file.
            </summary>
            <remarks>
            <para>
            When <c>null</c>, the parser assumes the location is in the file currently being processed.
            </para>
            <para>Set property is only accessible for deserialization purposes.</para>
            </remarks>
        </member>
        <member name="P:Microsoft.AspNetCore.Razor.Language.SourceLocation.AbsoluteIndex">
            <remarks>Set property is only accessible for deserialization purposes.</remarks>
        </member>
        <member name="P:Microsoft.AspNetCore.Razor.Language.SourceLocation.LineIndex">
            <remarks>Set property is only accessible for deserialization purposes.</remarks>
        </member>
        <member name="P:Microsoft.AspNetCore.Razor.Language.SourceLocation.CharacterIndex">
            <remarks>Set property is only accessible for deserialization purposes.</remarks>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.SourceLocation.FromSpan(System.Nullable{Microsoft.AspNetCore.Razor.Language.SourceSpan})">
            <summary>
            Creates a new instance of <see cref="T:Microsoft.AspNetCore.Razor.Language.SourceLocation"/> from the provided span.
            </summary>
            <param name="span">
            The souce span. If <c>null</c>, <see cref="F:Microsoft.AspNetCore.Razor.Language.SourceLocation.Undefined"/> will be returned.
            </param>
            <remarks>A <see cref="T:Microsoft.AspNetCore.Razor.Language.SourceLocation"/> that corresponds to the beginning of the span.</remarks>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.SourceLocation.ToString">
            <inheritdoc />
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.SourceLocation.Equals(System.Object)">
            <inheritdoc />
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.SourceLocation.GetHashCode">
            <inheritdoc />
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.SourceLocation.Equals(Microsoft.AspNetCore.Razor.Language.SourceLocation)">
            <inheritdoc />
        </member>
        <member name="P:Microsoft.AspNetCore.Razor.Language.Syntax.ChildSyntaxList.Count">
            <summary>
            Gets the number of children contained in the <see cref="T:Microsoft.AspNetCore.Razor.Language.Syntax.ChildSyntaxList"/>.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Razor.Language.Syntax.ChildSyntaxList.Item(System.Int32)">
            <summary>Gets the child at the specified index.</summary>
            <param name="index">The zero-based index of the child to get.</param>
            <exception cref="T:System.ArgumentOutOfRangeException">
              <paramref name="index"/> is less than 0.-or-<paramref name="index" /> is equal to or greater than <see cref="P:Microsoft.AspNetCore.Razor.Language.Syntax.ChildSyntaxList.Count"/>. </exception>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Syntax.ChildSyntaxList.ItemInternal(Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxNode,System.Int32)">
            <summary>
            internal indexer that does not verify index.
            Used when caller has already ensured that index is within bounds.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Syntax.ChildSyntaxList.ChildThatContainsPosition(Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxNode,System.Int32)">
            <summary>
            Locate the node that is a child of the given <see cref="T:Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxNode"/> and contains the given position.
            </summary>
            <param name="node">The <see cref="T:Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxNode"/> to search.</param>
            <param name="targetPosition">The position.</param>
            <returns>The node that spans the given position.</returns>
            <remarks>
            Assumes that <paramref name="targetPosition"/> is within the span of <paramref name="node"/>.
            </remarks>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Syntax.ChildSyntaxList.ItemInternalAsNode(Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxNode,System.Int32)">
            <summary>
            internal indexer that does not verify index.
            Used when caller has already ensured that index is within bounds.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Syntax.ChildSyntaxList.First">
            <summary>
            Returns the first child in the list.
            </summary>
            <returns>The first child in the list.</returns>
            <exception cref="T:System.InvalidOperationException">The list is empty.</exception>    
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Syntax.ChildSyntaxList.Last">
            <summary>
            Returns the last child in the list.
            </summary>
            <returns>The last child in the list.</returns>
            <exception cref="T:System.InvalidOperationException">The list is empty.</exception>    
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Syntax.ChildSyntaxList.Reverse">
            <summary>
            Returns a list which contains all children of <see cref="T:Microsoft.AspNetCore.Razor.Language.Syntax.ChildSyntaxList"/> in reversed order.
            </summary>
            <returns><see cref="T:Microsoft.AspNetCore.Razor.Language.Syntax.ChildSyntaxList.Reversed"/> which contains all children of <see cref="T:Microsoft.AspNetCore.Razor.Language.Syntax.ChildSyntaxList"/> in reversed order</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Syntax.ChildSyntaxList.GetEnumerator">
            <summary>Returns an enumerator that iterates through the <see cref="T:Microsoft.AspNetCore.Razor.Language.Syntax.ChildSyntaxList"/>.</summary>
            <returns>A <see cref="T:Microsoft.AspNetCore.Razor.Language.Syntax.ChildSyntaxList.Enumerator"/> for the <see cref="T:Microsoft.AspNetCore.Razor.Language.Syntax.ChildSyntaxList"/>.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Syntax.ChildSyntaxList.Equals(System.Object)">
            <summary>Determines whether the specified object is equal to the current instance.</summary>
            <returns>true if the specified object is a <see cref="T:Microsoft.AspNetCore.Razor.Language.Syntax.ChildSyntaxList" /> structure and is equal to the current instance; otherwise, false.</returns>
            <param name="obj">The object to be compared with the current instance.</param>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Syntax.ChildSyntaxList.Equals(Microsoft.AspNetCore.Razor.Language.Syntax.ChildSyntaxList)">
            <summary>Determines whether the specified <see cref="T:Microsoft.AspNetCore.Razor.Language.Syntax.ChildSyntaxList" /> structure is equal to the current instance.</summary>
            <returns>true if the specified <see cref="T:Microsoft.AspNetCore.Razor.Language.Syntax.ChildSyntaxList" /> structure is equal to the current instance; otherwise, false.</returns>
            <param name="other">The <see cref="T:Microsoft.AspNetCore.Razor.Language.Syntax.ChildSyntaxList" /> structure to be compared with the current instance.</param>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Syntax.ChildSyntaxList.GetHashCode">
            <summary>Returns the hash code for the current instance.</summary>
            <returns>A 32-bit signed integer hash code.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Syntax.ChildSyntaxList.op_Equality(Microsoft.AspNetCore.Razor.Language.Syntax.ChildSyntaxList,Microsoft.AspNetCore.Razor.Language.Syntax.ChildSyntaxList)">
            <summary>Indicates whether two <see cref="T:Microsoft.AspNetCore.Razor.Language.Syntax.ChildSyntaxList" /> structures are equal.</summary>
            <returns>true if <paramref name="list1" /> is equal to <paramref name="list2" />; otherwise, false.</returns>
            <param name="list1">The <see cref="T:Microsoft.AspNetCore.Razor.Language.Syntax.ChildSyntaxList" /> structure on the left side of the equality operator.</param>
            <param name="list2">The <see cref="T:Microsoft.AspNetCore.Razor.Language.Syntax.ChildSyntaxList" /> structure on the right side of the equality operator.</param>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Syntax.ChildSyntaxList.op_Inequality(Microsoft.AspNetCore.Razor.Language.Syntax.ChildSyntaxList,Microsoft.AspNetCore.Razor.Language.Syntax.ChildSyntaxList)">
            <summary>Indicates whether two <see cref="T:Microsoft.AspNetCore.Razor.Language.Syntax.ChildSyntaxList" /> structures are unequal.</summary>
            <returns>true if <paramref name="list1" /> is equal to <paramref name="list2" />; otherwise, false.</returns>
            <param name="list1">The <see cref="T:Microsoft.AspNetCore.Razor.Language.Syntax.ChildSyntaxList" /> structure on the left side of the inequality operator.</param>
            <param name="list2">The <see cref="T:Microsoft.AspNetCore.Razor.Language.Syntax.ChildSyntaxList" /> structure on the right side of the inequality operator.</param>
        </member>
        <member name="T:Microsoft.AspNetCore.Razor.Language.Syntax.ChildSyntaxList.Enumerator">
            <summary>Enumerates the elements of a <see cref="T:Microsoft.AspNetCore.Razor.Language.Syntax.ChildSyntaxList" />.</summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Syntax.ChildSyntaxList.Enumerator.MoveNext">
            <summary>Advances the enumerator to the next element of the <see cref="T:Microsoft.AspNetCore.Razor.Language.Syntax.ChildSyntaxList" />.</summary>
            <returns>true if the enumerator was successfully advanced to the next element; false if the enumerator has passed the end of the collection.</returns>
        </member>
        <member name="P:Microsoft.AspNetCore.Razor.Language.Syntax.ChildSyntaxList.Enumerator.Current">
            <summary>Gets the element at the current position of the enumerator.</summary>
            <returns>The element in the <see cref="T:Microsoft.AspNetCore.Razor.Language.Syntax.ChildSyntaxList" /> at the current position of the enumerator.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Syntax.ChildSyntaxList.Enumerator.Reset">
            <summary>Sets the enumerator to its initial position, which is before the first element in the collection.</summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Razor.Language.Syntax.ChildSyntaxList.EnumeratorImpl.Current">
            <summary>
            Gets the element in the collection at the current position of the enumerator.
            </summary>
            <returns>
            The element in the collection at the current position of the enumerator.
              </returns>
        </member>
        <member name="P:Microsoft.AspNetCore.Razor.Language.Syntax.ChildSyntaxList.EnumeratorImpl.System#Collections#IEnumerator#Current">
            <summary>
            Gets the element in the collection at the current position of the enumerator.
            </summary>
            <returns>
            The element in the collection at the current position of the enumerator.
              </returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Syntax.ChildSyntaxList.EnumeratorImpl.MoveNext">
            <summary>
            Advances the enumerator to the next element of the collection.
            </summary>
            <returns>
            true if the enumerator was successfully advanced to the next element; false if the enumerator has passed the end of the collection.
            </returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Syntax.ChildSyntaxList.EnumeratorImpl.Reset">
            <summary>
            Sets the enumerator to its initial position, which is before the first element in the collection.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Syntax.ChildSyntaxList.EnumeratorImpl.Dispose">
            <summary>
            Performs application-defined tasks associated with freeing, releasing, or resetting unmanaged resources.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Razor.Language.Syntax.ChildSyntaxList.Reversed.EnumeratorImpl.Current">
            <summary>
            Gets the element in the collection at the current position of the enumerator.
            </summary>
            <returns>
            The element in the collection at the current position of the enumerator.
              </returns>
        </member>
        <member name="P:Microsoft.AspNetCore.Razor.Language.Syntax.ChildSyntaxList.Reversed.EnumeratorImpl.System#Collections#IEnumerator#Current">
            <summary>
            Gets the element in the collection at the current position of the enumerator.
            </summary>
            <returns>
            The element in the collection at the current position of the enumerator.
              </returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Syntax.ChildSyntaxList.Reversed.EnumeratorImpl.MoveNext">
            <summary>
            Advances the enumerator to the next element of the collection.
            </summary>
            <returns>
            true if the enumerator was successfully advanced to the next element; false if the enumerator has passed the end of the collection.
            </returns>
            <exception cref="T:System.InvalidOperationException">The collection was modified after the enumerator was created. </exception>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Syntax.ChildSyntaxList.Reversed.EnumeratorImpl.Reset">
            <summary>
            Sets the enumerator to its initial position, which is before the first element in the collection.
            </summary>
            <exception cref="T:System.InvalidOperationException">The collection was modified after the enumerator was created. </exception>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Syntax.ChildSyntaxList.Reversed.EnumeratorImpl.Dispose">
            <summary>
            Performs application-defined tasks associated with freeing, releasing, or resetting unmanaged resources.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Syntax.InternalSyntax.SyntaxList.WithLotsOfChildren.FindSlotIndexContainingOffset(System.Int32)">
            <summary>
            Find the slot that contains the given offset.
            </summary>
            <param name="offset">The target offset. Must be between 0 and <see cref="P:Microsoft.AspNetCore.Razor.Language.Syntax.GreenNode.FullWidth"/>.</param>
            <returns>The slot index of the slot containing the given offset.</returns>
            <remarks>
            This implementation uses a binary search to find the first slot that contains
            the given offset.
            </remarks>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Syntax.InternalSyntax.SyntaxList.WithLotsOfChildren.BinarySearchUpperBound(System.Int32[],System.Int32)">
            <summary>
            Search a sorted integer array for the target value in O(log N) time.
            </summary>
            <param name="array">The array of integers which must be sorted in ascending order.</param>
            <param name="value">The target value.</param>
            <returns>An index in the array pointing to the position where <paramref name="value"/> should be
            inserted in order to maintain the sorted order. All values to the right of this position will be
            strictly greater than <paramref name="value"/>. Note that this may return a position off the end
            of the array if all elements are less than or equal to <paramref name="value"/>.</returns>
        </member>
        <member name="T:Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxVisitor`1">
            <summary>
            Represents a <see cref="T:Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxNode"/> visitor that visits only the single SyntaxNode
            passed into its Visit method and produces 
            a value of the type specified by the <typeparamref name="TResult"/> parameter.
            </summary>
            <typeparam name="TResult">
            The type of the return value this visitor's Visit method.
            </typeparam>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxVisitor`1.VisitRazorDocument(Microsoft.AspNetCore.Razor.Language.Syntax.RazorDocumentSyntax)">
            <summary>Called when the visitor visits a RazorDocumentSyntax node.</summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxVisitor`1.VisitRazorCommentBlock(Microsoft.AspNetCore.Razor.Language.Syntax.RazorCommentBlockSyntax)">
            <summary>Called when the visitor visits a RazorCommentBlockSyntax node.</summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxVisitor`1.VisitRazorMetaCode(Microsoft.AspNetCore.Razor.Language.Syntax.RazorMetaCodeSyntax)">
            <summary>Called when the visitor visits a RazorMetaCodeSyntax node.</summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxVisitor`1.VisitGenericBlock(Microsoft.AspNetCore.Razor.Language.Syntax.GenericBlockSyntax)">
            <summary>Called when the visitor visits a GenericBlockSyntax node.</summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxVisitor`1.VisitUnclassifiedTextLiteral(Microsoft.AspNetCore.Razor.Language.Syntax.UnclassifiedTextLiteralSyntax)">
            <summary>Called when the visitor visits a UnclassifiedTextLiteralSyntax node.</summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxVisitor`1.VisitMarkupBlock(Microsoft.AspNetCore.Razor.Language.Syntax.MarkupBlockSyntax)">
            <summary>Called when the visitor visits a MarkupBlockSyntax node.</summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxVisitor`1.VisitMarkupTransition(Microsoft.AspNetCore.Razor.Language.Syntax.MarkupTransitionSyntax)">
            <summary>Called when the visitor visits a MarkupTransitionSyntax node.</summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxVisitor`1.VisitMarkupTextLiteral(Microsoft.AspNetCore.Razor.Language.Syntax.MarkupTextLiteralSyntax)">
            <summary>Called when the visitor visits a MarkupTextLiteralSyntax node.</summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxVisitor`1.VisitMarkupEphemeralTextLiteral(Microsoft.AspNetCore.Razor.Language.Syntax.MarkupEphemeralTextLiteralSyntax)">
            <summary>Called when the visitor visits a MarkupEphemeralTextLiteralSyntax node.</summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxVisitor`1.VisitMarkupCommentBlock(Microsoft.AspNetCore.Razor.Language.Syntax.MarkupCommentBlockSyntax)">
            <summary>Called when the visitor visits a MarkupCommentBlockSyntax node.</summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxVisitor`1.VisitMarkupMinimizedAttributeBlock(Microsoft.AspNetCore.Razor.Language.Syntax.MarkupMinimizedAttributeBlockSyntax)">
            <summary>Called when the visitor visits a MarkupMinimizedAttributeBlockSyntax node.</summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxVisitor`1.VisitMarkupAttributeBlock(Microsoft.AspNetCore.Razor.Language.Syntax.MarkupAttributeBlockSyntax)">
            <summary>Called when the visitor visits a MarkupAttributeBlockSyntax node.</summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxVisitor`1.VisitMarkupMiscAttributeContent(Microsoft.AspNetCore.Razor.Language.Syntax.MarkupMiscAttributeContentSyntax)">
            <summary>Called when the visitor visits a MarkupMiscAttributeContentSyntax node.</summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxVisitor`1.VisitMarkupLiteralAttributeValue(Microsoft.AspNetCore.Razor.Language.Syntax.MarkupLiteralAttributeValueSyntax)">
            <summary>Called when the visitor visits a MarkupLiteralAttributeValueSyntax node.</summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxVisitor`1.VisitMarkupDynamicAttributeValue(Microsoft.AspNetCore.Razor.Language.Syntax.MarkupDynamicAttributeValueSyntax)">
            <summary>Called when the visitor visits a MarkupDynamicAttributeValueSyntax node.</summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxVisitor`1.VisitMarkupElement(Microsoft.AspNetCore.Razor.Language.Syntax.MarkupElementSyntax)">
            <summary>Called when the visitor visits a MarkupElementSyntax node.</summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxVisitor`1.VisitMarkupStartTag(Microsoft.AspNetCore.Razor.Language.Syntax.MarkupStartTagSyntax)">
            <summary>Called when the visitor visits a MarkupStartTagSyntax node.</summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxVisitor`1.VisitMarkupEndTag(Microsoft.AspNetCore.Razor.Language.Syntax.MarkupEndTagSyntax)">
            <summary>Called when the visitor visits a MarkupEndTagSyntax node.</summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxVisitor`1.VisitMarkupTagHelperElement(Microsoft.AspNetCore.Razor.Language.Syntax.MarkupTagHelperElementSyntax)">
            <summary>Called when the visitor visits a MarkupTagHelperElementSyntax node.</summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxVisitor`1.VisitMarkupTagHelperStartTag(Microsoft.AspNetCore.Razor.Language.Syntax.MarkupTagHelperStartTagSyntax)">
            <summary>Called when the visitor visits a MarkupTagHelperStartTagSyntax node.</summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxVisitor`1.VisitMarkupTagHelperEndTag(Microsoft.AspNetCore.Razor.Language.Syntax.MarkupTagHelperEndTagSyntax)">
            <summary>Called when the visitor visits a MarkupTagHelperEndTagSyntax node.</summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxVisitor`1.VisitMarkupTagHelperAttribute(Microsoft.AspNetCore.Razor.Language.Syntax.MarkupTagHelperAttributeSyntax)">
            <summary>Called when the visitor visits a MarkupTagHelperAttributeSyntax node.</summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxVisitor`1.VisitMarkupMinimizedTagHelperAttribute(Microsoft.AspNetCore.Razor.Language.Syntax.MarkupMinimizedTagHelperAttributeSyntax)">
            <summary>Called when the visitor visits a MarkupMinimizedTagHelperAttributeSyntax node.</summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxVisitor`1.VisitMarkupTagHelperAttributeValue(Microsoft.AspNetCore.Razor.Language.Syntax.MarkupTagHelperAttributeValueSyntax)">
            <summary>Called when the visitor visits a MarkupTagHelperAttributeValueSyntax node.</summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxVisitor`1.VisitMarkupTagHelperDirectiveAttribute(Microsoft.AspNetCore.Razor.Language.Syntax.MarkupTagHelperDirectiveAttributeSyntax)">
            <summary>Called when the visitor visits a MarkupTagHelperDirectiveAttributeSyntax node.</summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxVisitor`1.VisitMarkupMinimizedTagHelperDirectiveAttribute(Microsoft.AspNetCore.Razor.Language.Syntax.MarkupMinimizedTagHelperDirectiveAttributeSyntax)">
            <summary>Called when the visitor visits a MarkupMinimizedTagHelperDirectiveAttributeSyntax node.</summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxVisitor`1.VisitCSharpCodeBlock(Microsoft.AspNetCore.Razor.Language.Syntax.CSharpCodeBlockSyntax)">
            <summary>Called when the visitor visits a CSharpCodeBlockSyntax node.</summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxVisitor`1.VisitCSharpTransition(Microsoft.AspNetCore.Razor.Language.Syntax.CSharpTransitionSyntax)">
            <summary>Called when the visitor visits a CSharpTransitionSyntax node.</summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxVisitor`1.VisitCSharpStatementLiteral(Microsoft.AspNetCore.Razor.Language.Syntax.CSharpStatementLiteralSyntax)">
            <summary>Called when the visitor visits a CSharpStatementLiteralSyntax node.</summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxVisitor`1.VisitCSharpExpressionLiteral(Microsoft.AspNetCore.Razor.Language.Syntax.CSharpExpressionLiteralSyntax)">
            <summary>Called when the visitor visits a CSharpExpressionLiteralSyntax node.</summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxVisitor`1.VisitCSharpEphemeralTextLiteral(Microsoft.AspNetCore.Razor.Language.Syntax.CSharpEphemeralTextLiteralSyntax)">
            <summary>Called when the visitor visits a CSharpEphemeralTextLiteralSyntax node.</summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxVisitor`1.VisitCSharpTemplateBlock(Microsoft.AspNetCore.Razor.Language.Syntax.CSharpTemplateBlockSyntax)">
            <summary>Called when the visitor visits a CSharpTemplateBlockSyntax node.</summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxVisitor`1.VisitCSharpStatement(Microsoft.AspNetCore.Razor.Language.Syntax.CSharpStatementSyntax)">
            <summary>Called when the visitor visits a CSharpStatementSyntax node.</summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxVisitor`1.VisitCSharpStatementBody(Microsoft.AspNetCore.Razor.Language.Syntax.CSharpStatementBodySyntax)">
            <summary>Called when the visitor visits a CSharpStatementBodySyntax node.</summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxVisitor`1.VisitCSharpExplicitExpression(Microsoft.AspNetCore.Razor.Language.Syntax.CSharpExplicitExpressionSyntax)">
            <summary>Called when the visitor visits a CSharpExplicitExpressionSyntax node.</summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxVisitor`1.VisitCSharpExplicitExpressionBody(Microsoft.AspNetCore.Razor.Language.Syntax.CSharpExplicitExpressionBodySyntax)">
            <summary>Called when the visitor visits a CSharpExplicitExpressionBodySyntax node.</summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxVisitor`1.VisitCSharpImplicitExpression(Microsoft.AspNetCore.Razor.Language.Syntax.CSharpImplicitExpressionSyntax)">
            <summary>Called when the visitor visits a CSharpImplicitExpressionSyntax node.</summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxVisitor`1.VisitCSharpImplicitExpressionBody(Microsoft.AspNetCore.Razor.Language.Syntax.CSharpImplicitExpressionBodySyntax)">
            <summary>Called when the visitor visits a CSharpImplicitExpressionBodySyntax node.</summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxVisitor`1.VisitRazorDirective(Microsoft.AspNetCore.Razor.Language.Syntax.RazorDirectiveSyntax)">
            <summary>Called when the visitor visits a RazorDirectiveSyntax node.</summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxVisitor`1.VisitRazorDirectiveBody(Microsoft.AspNetCore.Razor.Language.Syntax.RazorDirectiveBodySyntax)">
            <summary>Called when the visitor visits a RazorDirectiveBodySyntax node.</summary>
        </member>
        <member name="T:Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxVisitor">
            <summary>
            Represents a <see cref="T:Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxNode"/> visitor that visits only the single SyntaxNode
            passed into its Visit method.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxVisitor.VisitRazorDocument(Microsoft.AspNetCore.Razor.Language.Syntax.RazorDocumentSyntax)">
            <summary>Called when the visitor visits a RazorDocumentSyntax node.</summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxVisitor.VisitRazorCommentBlock(Microsoft.AspNetCore.Razor.Language.Syntax.RazorCommentBlockSyntax)">
            <summary>Called when the visitor visits a RazorCommentBlockSyntax node.</summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxVisitor.VisitRazorMetaCode(Microsoft.AspNetCore.Razor.Language.Syntax.RazorMetaCodeSyntax)">
            <summary>Called when the visitor visits a RazorMetaCodeSyntax node.</summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxVisitor.VisitGenericBlock(Microsoft.AspNetCore.Razor.Language.Syntax.GenericBlockSyntax)">
            <summary>Called when the visitor visits a GenericBlockSyntax node.</summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxVisitor.VisitUnclassifiedTextLiteral(Microsoft.AspNetCore.Razor.Language.Syntax.UnclassifiedTextLiteralSyntax)">
            <summary>Called when the visitor visits a UnclassifiedTextLiteralSyntax node.</summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxVisitor.VisitMarkupBlock(Microsoft.AspNetCore.Razor.Language.Syntax.MarkupBlockSyntax)">
            <summary>Called when the visitor visits a MarkupBlockSyntax node.</summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxVisitor.VisitMarkupTransition(Microsoft.AspNetCore.Razor.Language.Syntax.MarkupTransitionSyntax)">
            <summary>Called when the visitor visits a MarkupTransitionSyntax node.</summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxVisitor.VisitMarkupTextLiteral(Microsoft.AspNetCore.Razor.Language.Syntax.MarkupTextLiteralSyntax)">
            <summary>Called when the visitor visits a MarkupTextLiteralSyntax node.</summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxVisitor.VisitMarkupEphemeralTextLiteral(Microsoft.AspNetCore.Razor.Language.Syntax.MarkupEphemeralTextLiteralSyntax)">
            <summary>Called when the visitor visits a MarkupEphemeralTextLiteralSyntax node.</summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxVisitor.VisitMarkupCommentBlock(Microsoft.AspNetCore.Razor.Language.Syntax.MarkupCommentBlockSyntax)">
            <summary>Called when the visitor visits a MarkupCommentBlockSyntax node.</summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxVisitor.VisitMarkupMinimizedAttributeBlock(Microsoft.AspNetCore.Razor.Language.Syntax.MarkupMinimizedAttributeBlockSyntax)">
            <summary>Called when the visitor visits a MarkupMinimizedAttributeBlockSyntax node.</summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxVisitor.VisitMarkupAttributeBlock(Microsoft.AspNetCore.Razor.Language.Syntax.MarkupAttributeBlockSyntax)">
            <summary>Called when the visitor visits a MarkupAttributeBlockSyntax node.</summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxVisitor.VisitMarkupMiscAttributeContent(Microsoft.AspNetCore.Razor.Language.Syntax.MarkupMiscAttributeContentSyntax)">
            <summary>Called when the visitor visits a MarkupMiscAttributeContentSyntax node.</summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxVisitor.VisitMarkupLiteralAttributeValue(Microsoft.AspNetCore.Razor.Language.Syntax.MarkupLiteralAttributeValueSyntax)">
            <summary>Called when the visitor visits a MarkupLiteralAttributeValueSyntax node.</summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxVisitor.VisitMarkupDynamicAttributeValue(Microsoft.AspNetCore.Razor.Language.Syntax.MarkupDynamicAttributeValueSyntax)">
            <summary>Called when the visitor visits a MarkupDynamicAttributeValueSyntax node.</summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxVisitor.VisitMarkupElement(Microsoft.AspNetCore.Razor.Language.Syntax.MarkupElementSyntax)">
            <summary>Called when the visitor visits a MarkupElementSyntax node.</summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxVisitor.VisitMarkupStartTag(Microsoft.AspNetCore.Razor.Language.Syntax.MarkupStartTagSyntax)">
            <summary>Called when the visitor visits a MarkupStartTagSyntax node.</summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxVisitor.VisitMarkupEndTag(Microsoft.AspNetCore.Razor.Language.Syntax.MarkupEndTagSyntax)">
            <summary>Called when the visitor visits a MarkupEndTagSyntax node.</summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxVisitor.VisitMarkupTagHelperElement(Microsoft.AspNetCore.Razor.Language.Syntax.MarkupTagHelperElementSyntax)">
            <summary>Called when the visitor visits a MarkupTagHelperElementSyntax node.</summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxVisitor.VisitMarkupTagHelperStartTag(Microsoft.AspNetCore.Razor.Language.Syntax.MarkupTagHelperStartTagSyntax)">
            <summary>Called when the visitor visits a MarkupTagHelperStartTagSyntax node.</summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxVisitor.VisitMarkupTagHelperEndTag(Microsoft.AspNetCore.Razor.Language.Syntax.MarkupTagHelperEndTagSyntax)">
            <summary>Called when the visitor visits a MarkupTagHelperEndTagSyntax node.</summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxVisitor.VisitMarkupTagHelperAttribute(Microsoft.AspNetCore.Razor.Language.Syntax.MarkupTagHelperAttributeSyntax)">
            <summary>Called when the visitor visits a MarkupTagHelperAttributeSyntax node.</summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxVisitor.VisitMarkupMinimizedTagHelperAttribute(Microsoft.AspNetCore.Razor.Language.Syntax.MarkupMinimizedTagHelperAttributeSyntax)">
            <summary>Called when the visitor visits a MarkupMinimizedTagHelperAttributeSyntax node.</summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxVisitor.VisitMarkupTagHelperAttributeValue(Microsoft.AspNetCore.Razor.Language.Syntax.MarkupTagHelperAttributeValueSyntax)">
            <summary>Called when the visitor visits a MarkupTagHelperAttributeValueSyntax node.</summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxVisitor.VisitMarkupTagHelperDirectiveAttribute(Microsoft.AspNetCore.Razor.Language.Syntax.MarkupTagHelperDirectiveAttributeSyntax)">
            <summary>Called when the visitor visits a MarkupTagHelperDirectiveAttributeSyntax node.</summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxVisitor.VisitMarkupMinimizedTagHelperDirectiveAttribute(Microsoft.AspNetCore.Razor.Language.Syntax.MarkupMinimizedTagHelperDirectiveAttributeSyntax)">
            <summary>Called when the visitor visits a MarkupMinimizedTagHelperDirectiveAttributeSyntax node.</summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxVisitor.VisitCSharpCodeBlock(Microsoft.AspNetCore.Razor.Language.Syntax.CSharpCodeBlockSyntax)">
            <summary>Called when the visitor visits a CSharpCodeBlockSyntax node.</summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxVisitor.VisitCSharpTransition(Microsoft.AspNetCore.Razor.Language.Syntax.CSharpTransitionSyntax)">
            <summary>Called when the visitor visits a CSharpTransitionSyntax node.</summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxVisitor.VisitCSharpStatementLiteral(Microsoft.AspNetCore.Razor.Language.Syntax.CSharpStatementLiteralSyntax)">
            <summary>Called when the visitor visits a CSharpStatementLiteralSyntax node.</summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxVisitor.VisitCSharpExpressionLiteral(Microsoft.AspNetCore.Razor.Language.Syntax.CSharpExpressionLiteralSyntax)">
            <summary>Called when the visitor visits a CSharpExpressionLiteralSyntax node.</summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxVisitor.VisitCSharpEphemeralTextLiteral(Microsoft.AspNetCore.Razor.Language.Syntax.CSharpEphemeralTextLiteralSyntax)">
            <summary>Called when the visitor visits a CSharpEphemeralTextLiteralSyntax node.</summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxVisitor.VisitCSharpTemplateBlock(Microsoft.AspNetCore.Razor.Language.Syntax.CSharpTemplateBlockSyntax)">
            <summary>Called when the visitor visits a CSharpTemplateBlockSyntax node.</summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxVisitor.VisitCSharpStatement(Microsoft.AspNetCore.Razor.Language.Syntax.CSharpStatementSyntax)">
            <summary>Called when the visitor visits a CSharpStatementSyntax node.</summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxVisitor.VisitCSharpStatementBody(Microsoft.AspNetCore.Razor.Language.Syntax.CSharpStatementBodySyntax)">
            <summary>Called when the visitor visits a CSharpStatementBodySyntax node.</summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxVisitor.VisitCSharpExplicitExpression(Microsoft.AspNetCore.Razor.Language.Syntax.CSharpExplicitExpressionSyntax)">
            <summary>Called when the visitor visits a CSharpExplicitExpressionSyntax node.</summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxVisitor.VisitCSharpExplicitExpressionBody(Microsoft.AspNetCore.Razor.Language.Syntax.CSharpExplicitExpressionBodySyntax)">
            <summary>Called when the visitor visits a CSharpExplicitExpressionBodySyntax node.</summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxVisitor.VisitCSharpImplicitExpression(Microsoft.AspNetCore.Razor.Language.Syntax.CSharpImplicitExpressionSyntax)">
            <summary>Called when the visitor visits a CSharpImplicitExpressionSyntax node.</summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxVisitor.VisitCSharpImplicitExpressionBody(Microsoft.AspNetCore.Razor.Language.Syntax.CSharpImplicitExpressionBodySyntax)">
            <summary>Called when the visitor visits a CSharpImplicitExpressionBodySyntax node.</summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxVisitor.VisitRazorDirective(Microsoft.AspNetCore.Razor.Language.Syntax.RazorDirectiveSyntax)">
            <summary>Called when the visitor visits a RazorDirectiveSyntax node.</summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxVisitor.VisitRazorDirectiveBody(Microsoft.AspNetCore.Razor.Language.Syntax.RazorDirectiveBodySyntax)">
            <summary>Called when the visitor visits a RazorDirectiveBodySyntax node.</summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxFactory.RazorDocument(Microsoft.AspNetCore.Razor.Language.Syntax.RazorBlockSyntax)">
            <summary>Creates a new RazorDocumentSyntax instance.</summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxFactory.RazorCommentBlock(Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxToken,Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxToken,Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxToken,Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxToken,Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxToken)">
            <summary>Creates a new RazorCommentBlockSyntax instance.</summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxFactory.RazorCommentBlock">
            <summary>Creates a new RazorCommentBlockSyntax instance.</summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxFactory.RazorMetaCode(Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxList{Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxToken})">
            <summary>Creates a new RazorMetaCodeSyntax instance.</summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxFactory.RazorMetaCode">
            <summary>Creates a new RazorMetaCodeSyntax instance.</summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxFactory.GenericBlock(Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxList{Microsoft.AspNetCore.Razor.Language.Syntax.RazorSyntaxNode})">
            <summary>Creates a new GenericBlockSyntax instance.</summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxFactory.GenericBlock">
            <summary>Creates a new GenericBlockSyntax instance.</summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxFactory.UnclassifiedTextLiteral(Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxList{Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxToken})">
            <summary>Creates a new UnclassifiedTextLiteralSyntax instance.</summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxFactory.UnclassifiedTextLiteral">
            <summary>Creates a new UnclassifiedTextLiteralSyntax instance.</summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxFactory.MarkupBlock(Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxList{Microsoft.AspNetCore.Razor.Language.Syntax.RazorSyntaxNode})">
            <summary>Creates a new MarkupBlockSyntax instance.</summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxFactory.MarkupBlock">
            <summary>Creates a new MarkupBlockSyntax instance.</summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxFactory.MarkupTransition(Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxList{Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxToken})">
            <summary>Creates a new MarkupTransitionSyntax instance.</summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxFactory.MarkupTransition">
            <summary>Creates a new MarkupTransitionSyntax instance.</summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxFactory.MarkupTextLiteral(Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxList{Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxToken})">
            <summary>Creates a new MarkupTextLiteralSyntax instance.</summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxFactory.MarkupTextLiteral">
            <summary>Creates a new MarkupTextLiteralSyntax instance.</summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxFactory.MarkupEphemeralTextLiteral(Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxList{Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxToken})">
            <summary>Creates a new MarkupEphemeralTextLiteralSyntax instance.</summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxFactory.MarkupEphemeralTextLiteral">
            <summary>Creates a new MarkupEphemeralTextLiteralSyntax instance.</summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxFactory.MarkupCommentBlock(Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxList{Microsoft.AspNetCore.Razor.Language.Syntax.RazorSyntaxNode})">
            <summary>Creates a new MarkupCommentBlockSyntax instance.</summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxFactory.MarkupCommentBlock">
            <summary>Creates a new MarkupCommentBlockSyntax instance.</summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxFactory.MarkupMinimizedAttributeBlock(Microsoft.AspNetCore.Razor.Language.Syntax.MarkupTextLiteralSyntax,Microsoft.AspNetCore.Razor.Language.Syntax.MarkupTextLiteralSyntax)">
            <summary>Creates a new MarkupMinimizedAttributeBlockSyntax instance.</summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxFactory.MarkupMinimizedAttributeBlock">
            <summary>Creates a new MarkupMinimizedAttributeBlockSyntax instance.</summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxFactory.MarkupAttributeBlock(Microsoft.AspNetCore.Razor.Language.Syntax.MarkupTextLiteralSyntax,Microsoft.AspNetCore.Razor.Language.Syntax.MarkupTextLiteralSyntax,Microsoft.AspNetCore.Razor.Language.Syntax.MarkupTextLiteralSyntax,Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxToken,Microsoft.AspNetCore.Razor.Language.Syntax.MarkupTextLiteralSyntax,Microsoft.AspNetCore.Razor.Language.Syntax.RazorBlockSyntax,Microsoft.AspNetCore.Razor.Language.Syntax.MarkupTextLiteralSyntax)">
            <summary>Creates a new MarkupAttributeBlockSyntax instance.</summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxFactory.MarkupAttributeBlock(Microsoft.AspNetCore.Razor.Language.Syntax.MarkupTextLiteralSyntax,Microsoft.AspNetCore.Razor.Language.Syntax.MarkupTextLiteralSyntax,Microsoft.AspNetCore.Razor.Language.Syntax.MarkupTextLiteralSyntax,Microsoft.AspNetCore.Razor.Language.Syntax.MarkupTextLiteralSyntax,Microsoft.AspNetCore.Razor.Language.Syntax.RazorBlockSyntax,Microsoft.AspNetCore.Razor.Language.Syntax.MarkupTextLiteralSyntax)">
            <summary>Creates a new MarkupAttributeBlockSyntax instance.</summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxFactory.MarkupAttributeBlock">
            <summary>Creates a new MarkupAttributeBlockSyntax instance.</summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxFactory.MarkupMiscAttributeContent(Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxList{Microsoft.AspNetCore.Razor.Language.Syntax.RazorSyntaxNode})">
            <summary>Creates a new MarkupMiscAttributeContentSyntax instance.</summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxFactory.MarkupMiscAttributeContent">
            <summary>Creates a new MarkupMiscAttributeContentSyntax instance.</summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxFactory.MarkupLiteralAttributeValue(Microsoft.AspNetCore.Razor.Language.Syntax.MarkupTextLiteralSyntax,Microsoft.AspNetCore.Razor.Language.Syntax.MarkupTextLiteralSyntax)">
            <summary>Creates a new MarkupLiteralAttributeValueSyntax instance.</summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxFactory.MarkupLiteralAttributeValue">
            <summary>Creates a new MarkupLiteralAttributeValueSyntax instance.</summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxFactory.MarkupDynamicAttributeValue(Microsoft.AspNetCore.Razor.Language.Syntax.MarkupTextLiteralSyntax,Microsoft.AspNetCore.Razor.Language.Syntax.RazorBlockSyntax)">
            <summary>Creates a new MarkupDynamicAttributeValueSyntax instance.</summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxFactory.MarkupDynamicAttributeValue(Microsoft.AspNetCore.Razor.Language.Syntax.RazorBlockSyntax)">
            <summary>Creates a new MarkupDynamicAttributeValueSyntax instance.</summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxFactory.MarkupElement(Microsoft.AspNetCore.Razor.Language.Syntax.MarkupStartTagSyntax,Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxList{Microsoft.AspNetCore.Razor.Language.Syntax.RazorSyntaxNode},Microsoft.AspNetCore.Razor.Language.Syntax.MarkupEndTagSyntax)">
            <summary>Creates a new MarkupElementSyntax instance.</summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxFactory.MarkupElement(Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxList{Microsoft.AspNetCore.Razor.Language.Syntax.RazorSyntaxNode})">
            <summary>Creates a new MarkupElementSyntax instance.</summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxFactory.MarkupStartTag(Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxToken,Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxToken,Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxToken,Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxList{Microsoft.AspNetCore.Razor.Language.Syntax.RazorSyntaxNode},Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxToken,Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxToken)">
            <summary>Creates a new MarkupStartTagSyntax instance.</summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxFactory.MarkupStartTag(Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxList{Microsoft.AspNetCore.Razor.Language.Syntax.RazorSyntaxNode})">
            <summary>Creates a new MarkupStartTagSyntax instance.</summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxFactory.MarkupEndTag(Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxToken,Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxToken,Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxToken,Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxToken,Microsoft.AspNetCore.Razor.Language.Syntax.MarkupMiscAttributeContentSyntax,Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxToken)">
            <summary>Creates a new MarkupEndTagSyntax instance.</summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxFactory.MarkupEndTag(Microsoft.AspNetCore.Razor.Language.Syntax.MarkupMiscAttributeContentSyntax)">
            <summary>Creates a new MarkupEndTagSyntax instance.</summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxFactory.MarkupTagHelperElement(Microsoft.AspNetCore.Razor.Language.Syntax.MarkupTagHelperStartTagSyntax,Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxList{Microsoft.AspNetCore.Razor.Language.Syntax.RazorSyntaxNode},Microsoft.AspNetCore.Razor.Language.Syntax.MarkupTagHelperEndTagSyntax)">
            <summary>Creates a new MarkupTagHelperElementSyntax instance.</summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxFactory.MarkupTagHelperElement(Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxList{Microsoft.AspNetCore.Razor.Language.Syntax.RazorSyntaxNode})">
            <summary>Creates a new MarkupTagHelperElementSyntax instance.</summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxFactory.MarkupTagHelperStartTag(Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxToken,Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxToken,Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxToken,Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxList{Microsoft.AspNetCore.Razor.Language.Syntax.RazorSyntaxNode},Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxToken,Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxToken)">
            <summary>Creates a new MarkupTagHelperStartTagSyntax instance.</summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxFactory.MarkupTagHelperStartTag(Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxList{Microsoft.AspNetCore.Razor.Language.Syntax.RazorSyntaxNode})">
            <summary>Creates a new MarkupTagHelperStartTagSyntax instance.</summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxFactory.MarkupTagHelperEndTag(Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxToken,Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxToken,Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxToken,Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxToken,Microsoft.AspNetCore.Razor.Language.Syntax.MarkupMiscAttributeContentSyntax,Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxToken)">
            <summary>Creates a new MarkupTagHelperEndTagSyntax instance.</summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxFactory.MarkupTagHelperEndTag(Microsoft.AspNetCore.Razor.Language.Syntax.MarkupMiscAttributeContentSyntax)">
            <summary>Creates a new MarkupTagHelperEndTagSyntax instance.</summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxFactory.MarkupTagHelperAttribute(Microsoft.AspNetCore.Razor.Language.Syntax.MarkupTextLiteralSyntax,Microsoft.AspNetCore.Razor.Language.Syntax.MarkupTextLiteralSyntax,Microsoft.AspNetCore.Razor.Language.Syntax.MarkupTextLiteralSyntax,Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxToken,Microsoft.AspNetCore.Razor.Language.Syntax.MarkupTextLiteralSyntax,Microsoft.AspNetCore.Razor.Language.Syntax.MarkupTagHelperAttributeValueSyntax,Microsoft.AspNetCore.Razor.Language.Syntax.MarkupTextLiteralSyntax)">
            <summary>Creates a new MarkupTagHelperAttributeSyntax instance.</summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxFactory.MarkupTagHelperAttribute(Microsoft.AspNetCore.Razor.Language.Syntax.MarkupTextLiteralSyntax,Microsoft.AspNetCore.Razor.Language.Syntax.MarkupTextLiteralSyntax,Microsoft.AspNetCore.Razor.Language.Syntax.MarkupTextLiteralSyntax,Microsoft.AspNetCore.Razor.Language.Syntax.MarkupTextLiteralSyntax,Microsoft.AspNetCore.Razor.Language.Syntax.MarkupTagHelperAttributeValueSyntax,Microsoft.AspNetCore.Razor.Language.Syntax.MarkupTextLiteralSyntax)">
            <summary>Creates a new MarkupTagHelperAttributeSyntax instance.</summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxFactory.MarkupTagHelperAttribute">
            <summary>Creates a new MarkupTagHelperAttributeSyntax instance.</summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxFactory.MarkupMinimizedTagHelperAttribute(Microsoft.AspNetCore.Razor.Language.Syntax.MarkupTextLiteralSyntax,Microsoft.AspNetCore.Razor.Language.Syntax.MarkupTextLiteralSyntax)">
            <summary>Creates a new MarkupMinimizedTagHelperAttributeSyntax instance.</summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxFactory.MarkupMinimizedTagHelperAttribute">
            <summary>Creates a new MarkupMinimizedTagHelperAttributeSyntax instance.</summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxFactory.MarkupTagHelperAttributeValue(Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxList{Microsoft.AspNetCore.Razor.Language.Syntax.RazorSyntaxNode})">
            <summary>Creates a new MarkupTagHelperAttributeValueSyntax instance.</summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxFactory.MarkupTagHelperAttributeValue">
            <summary>Creates a new MarkupTagHelperAttributeValueSyntax instance.</summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxFactory.MarkupTagHelperDirectiveAttribute(Microsoft.AspNetCore.Razor.Language.Syntax.MarkupTextLiteralSyntax,Microsoft.AspNetCore.Razor.Language.Syntax.RazorMetaCodeSyntax,Microsoft.AspNetCore.Razor.Language.Syntax.MarkupTextLiteralSyntax,Microsoft.AspNetCore.Razor.Language.Syntax.RazorMetaCodeSyntax,Microsoft.AspNetCore.Razor.Language.Syntax.MarkupTextLiteralSyntax,Microsoft.AspNetCore.Razor.Language.Syntax.MarkupTextLiteralSyntax,Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxToken,Microsoft.AspNetCore.Razor.Language.Syntax.MarkupTextLiteralSyntax,Microsoft.AspNetCore.Razor.Language.Syntax.MarkupTagHelperAttributeValueSyntax,Microsoft.AspNetCore.Razor.Language.Syntax.MarkupTextLiteralSyntax)">
            <summary>Creates a new MarkupTagHelperDirectiveAttributeSyntax instance.</summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxFactory.MarkupTagHelperDirectiveAttribute(Microsoft.AspNetCore.Razor.Language.Syntax.MarkupTextLiteralSyntax,Microsoft.AspNetCore.Razor.Language.Syntax.RazorMetaCodeSyntax,Microsoft.AspNetCore.Razor.Language.Syntax.MarkupTextLiteralSyntax,Microsoft.AspNetCore.Razor.Language.Syntax.RazorMetaCodeSyntax,Microsoft.AspNetCore.Razor.Language.Syntax.MarkupTextLiteralSyntax,Microsoft.AspNetCore.Razor.Language.Syntax.MarkupTextLiteralSyntax,Microsoft.AspNetCore.Razor.Language.Syntax.MarkupTextLiteralSyntax,Microsoft.AspNetCore.Razor.Language.Syntax.MarkupTagHelperAttributeValueSyntax,Microsoft.AspNetCore.Razor.Language.Syntax.MarkupTextLiteralSyntax)">
            <summary>Creates a new MarkupTagHelperDirectiveAttributeSyntax instance.</summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxFactory.MarkupTagHelperDirectiveAttribute">
            <summary>Creates a new MarkupTagHelperDirectiveAttributeSyntax instance.</summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxFactory.MarkupMinimizedTagHelperDirectiveAttribute(Microsoft.AspNetCore.Razor.Language.Syntax.MarkupTextLiteralSyntax,Microsoft.AspNetCore.Razor.Language.Syntax.RazorMetaCodeSyntax,Microsoft.AspNetCore.Razor.Language.Syntax.MarkupTextLiteralSyntax,Microsoft.AspNetCore.Razor.Language.Syntax.RazorMetaCodeSyntax,Microsoft.AspNetCore.Razor.Language.Syntax.MarkupTextLiteralSyntax)">
            <summary>Creates a new MarkupMinimizedTagHelperDirectiveAttributeSyntax instance.</summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxFactory.MarkupMinimizedTagHelperDirectiveAttribute">
            <summary>Creates a new MarkupMinimizedTagHelperDirectiveAttributeSyntax instance.</summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxFactory.CSharpCodeBlock(Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxList{Microsoft.AspNetCore.Razor.Language.Syntax.RazorSyntaxNode})">
            <summary>Creates a new CSharpCodeBlockSyntax instance.</summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxFactory.CSharpCodeBlock">
            <summary>Creates a new CSharpCodeBlockSyntax instance.</summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxFactory.CSharpTransition(Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxToken)">
            <summary>Creates a new CSharpTransitionSyntax instance.</summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxFactory.CSharpTransition">
            <summary>Creates a new CSharpTransitionSyntax instance.</summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxFactory.CSharpStatementLiteral(Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxList{Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxToken})">
            <summary>Creates a new CSharpStatementLiteralSyntax instance.</summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxFactory.CSharpStatementLiteral">
            <summary>Creates a new CSharpStatementLiteralSyntax instance.</summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxFactory.CSharpExpressionLiteral(Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxList{Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxToken})">
            <summary>Creates a new CSharpExpressionLiteralSyntax instance.</summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxFactory.CSharpExpressionLiteral">
            <summary>Creates a new CSharpExpressionLiteralSyntax instance.</summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxFactory.CSharpEphemeralTextLiteral(Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxList{Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxToken})">
            <summary>Creates a new CSharpEphemeralTextLiteralSyntax instance.</summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxFactory.CSharpEphemeralTextLiteral">
            <summary>Creates a new CSharpEphemeralTextLiteralSyntax instance.</summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxFactory.CSharpTemplateBlock(Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxList{Microsoft.AspNetCore.Razor.Language.Syntax.RazorSyntaxNode})">
            <summary>Creates a new CSharpTemplateBlockSyntax instance.</summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxFactory.CSharpTemplateBlock">
            <summary>Creates a new CSharpTemplateBlockSyntax instance.</summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxFactory.CSharpStatement(Microsoft.AspNetCore.Razor.Language.Syntax.CSharpTransitionSyntax,Microsoft.AspNetCore.Razor.Language.Syntax.CSharpSyntaxNode)">
            <summary>Creates a new CSharpStatementSyntax instance.</summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxFactory.CSharpStatement(Microsoft.AspNetCore.Razor.Language.Syntax.CSharpSyntaxNode)">
            <summary>Creates a new CSharpStatementSyntax instance.</summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxFactory.CSharpStatementBody(Microsoft.AspNetCore.Razor.Language.Syntax.RazorMetaCodeSyntax,Microsoft.AspNetCore.Razor.Language.Syntax.CSharpCodeBlockSyntax,Microsoft.AspNetCore.Razor.Language.Syntax.RazorMetaCodeSyntax)">
            <summary>Creates a new CSharpStatementBodySyntax instance.</summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxFactory.CSharpStatementBody">
            <summary>Creates a new CSharpStatementBodySyntax instance.</summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxFactory.CSharpExplicitExpression(Microsoft.AspNetCore.Razor.Language.Syntax.CSharpTransitionSyntax,Microsoft.AspNetCore.Razor.Language.Syntax.CSharpSyntaxNode)">
            <summary>Creates a new CSharpExplicitExpressionSyntax instance.</summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxFactory.CSharpExplicitExpression(Microsoft.AspNetCore.Razor.Language.Syntax.CSharpSyntaxNode)">
            <summary>Creates a new CSharpExplicitExpressionSyntax instance.</summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxFactory.CSharpExplicitExpressionBody(Microsoft.AspNetCore.Razor.Language.Syntax.RazorMetaCodeSyntax,Microsoft.AspNetCore.Razor.Language.Syntax.CSharpCodeBlockSyntax,Microsoft.AspNetCore.Razor.Language.Syntax.RazorMetaCodeSyntax)">
            <summary>Creates a new CSharpExplicitExpressionBodySyntax instance.</summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxFactory.CSharpExplicitExpressionBody">
            <summary>Creates a new CSharpExplicitExpressionBodySyntax instance.</summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxFactory.CSharpImplicitExpression(Microsoft.AspNetCore.Razor.Language.Syntax.CSharpTransitionSyntax,Microsoft.AspNetCore.Razor.Language.Syntax.CSharpSyntaxNode)">
            <summary>Creates a new CSharpImplicitExpressionSyntax instance.</summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxFactory.CSharpImplicitExpression(Microsoft.AspNetCore.Razor.Language.Syntax.CSharpSyntaxNode)">
            <summary>Creates a new CSharpImplicitExpressionSyntax instance.</summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxFactory.CSharpImplicitExpressionBody(Microsoft.AspNetCore.Razor.Language.Syntax.CSharpCodeBlockSyntax)">
            <summary>Creates a new CSharpImplicitExpressionBodySyntax instance.</summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxFactory.CSharpImplicitExpressionBody">
            <summary>Creates a new CSharpImplicitExpressionBodySyntax instance.</summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxFactory.RazorDirective(Microsoft.AspNetCore.Razor.Language.Syntax.CSharpTransitionSyntax,Microsoft.AspNetCore.Razor.Language.Syntax.CSharpSyntaxNode)">
            <summary>Creates a new RazorDirectiveSyntax instance.</summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxFactory.RazorDirective(Microsoft.AspNetCore.Razor.Language.Syntax.CSharpSyntaxNode)">
            <summary>Creates a new RazorDirectiveSyntax instance.</summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxFactory.RazorDirectiveBody(Microsoft.AspNetCore.Razor.Language.Syntax.RazorSyntaxNode,Microsoft.AspNetCore.Razor.Language.Syntax.CSharpCodeBlockSyntax)">
            <summary>Creates a new RazorDirectiveBodySyntax instance.</summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxFactory.RazorDirectiveBody(Microsoft.AspNetCore.Razor.Language.Syntax.RazorSyntaxNode)">
            <summary>Creates a new RazorDirectiveBodySyntax instance.</summary>
        </member>
        <member name="T:Microsoft.AspNetCore.Razor.Language.Syntax.ObjectPool`1">
             <summary>
             Generic implementation of object pooling pattern with predefined pool size limit. The main
             purpose is that limited number of frequently used objects can be kept in the pool for
             further recycling.
            
             Notes:
             1) it is not the goal to keep all returned objects. Pool is not meant for storage. If there
                is no space in the pool, extra returned objects will be dropped.
            
             2) it is implied that if object was obtained from a pool, the caller will return it back in
                a relatively short time. Keeping checked out objects for long durations is ok, but
                reduces usefulness of pooling. Just new up your own.
            
             Not returning objects to the pool in not detrimental to the pool's work, but is a bad practice.
             Rationale:
                If there is no intent for reusing the object, do not use pool - just use "new".
             </summary>
        </member>
        <member name="T:Microsoft.AspNetCore.Razor.Language.Syntax.ObjectPool`1.Factory">
            <remarks>
            Not using <see cref="T:System.Func{T}"/> because this file is linked into the (debugger) Formatter,
            which does not have that type (since it compiles against .NET 2.0).
            </remarks>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Syntax.ObjectPool`1.Allocate">
            <summary>
            Produces an instance.
            </summary>
            <remarks>
            Search strategy is a simple linear probing which is chosen for it cache-friendliness.
            Note that Free will try to store recycled objects close to the start thus statistically
            reducing how far we will typically search.
            </remarks>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Syntax.ObjectPool`1.Free(`0)">
            <summary>
            Returns objects to the pool.
            </summary>
            <remarks>
            Search strategy is a simple linear probing which is chosen for it cache-friendliness.
            Note that Free will try to store recycled objects close to the start thus statistically
            reducing how far we will typically search in Allocate.
            </remarks>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Syntax.ObjectPool`1.ForgetTrackedObject(`0,`0)">
             <summary>
             Removes an object from leak tracking.
            
             This is called when an object is returned to the pool.  It may also be explicitly
             called if an object allocated from the pool is intentionally not being returned
             to the pool.  This can be of use with pooled arrays if the consumer wants to
             return a larger array to the pool than was originally allocated.
             </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Syntax.StackGuard.EnsureSufficientExecutionStack(System.Int32)">
            <summary>
                Ensures that the remaining stack space is large enough to execute
                the average function.
            </summary>
            <param name="recursionDepth">how many times the calling function has recursed</param>
            <exception cref="T:System.InsufficientExecutionStackException">
                The available stack space is insufficient to execute
                the average function.
            </exception>
        </member>
        <member name="T:Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxAnnotation">
            <summary>
            A SyntaxAnnotation is used to annotate syntax elements with additional information. 
            
            Since syntax elements are immutable, annotating them requires creating new instances of them
            with the annotations attached.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxList`1.#ctor(`0)">
            <summary>
            Creates a singleton list of syntax nodes.
            </summary>
            <param name="node">The single element node.</param>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxList`1.#ctor(System.Collections.Generic.IEnumerable{`0})">
            <summary>
            Creates a list of syntax nodes.
            </summary>
            <param name="nodes">A sequence of element nodes.</param>
        </member>
        <member name="P:Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxList`1.Count">
            <summary>
            The number of nodes in the list.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxList`1.Item(System.Int32)">
            <summary>
            Gets the node at the specified index.
            </summary>
            <param name="index">The zero-based index of the node to get or set.</param>
            <returns>The node at the specified index.</returns>
        </member>
        <member name="P:Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxList`1.FullSpan">
            <summary>
            The absolute span of the list elements in characters, including the leading and trailing trivia of the first and last elements.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxList`1.Span">
            <summary>
            The absolute span of the list elements in characters, not including the leading and trailing trivia of the first and last elements.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxList`1.ToString">
            <summary>
            Returns the string representation of the nodes in this list, not including
            the first node's leading trivia and the last node's trailing trivia.
            </summary>
            <returns>
            The string representation of the nodes in this list, not including
            the first node's leading trivia and the last node's trailing trivia.
            </returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxList`1.ToFullString">
            <summary>
            Returns the full string representation of the nodes in this list including
            the first node's leading trivia and the last node's trailing trivia.
            </summary>
            <returns>
            The full string representation of the nodes in this list including
            the first node's leading trivia and the last node's trailing trivia.
            </returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxList`1.Add(`0)">
            <summary>
            Creates a new list with the specified node added at the end.
            </summary>
            <param name="node">The node to add.</param>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxList`1.AddRange(System.Collections.Generic.IEnumerable{`0})">
            <summary>
            Creates a new list with the specified nodes added at the end.
            </summary>
            <param name="nodes">The nodes to add.</param>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxList`1.Insert(System.Int32,`0)">
            <summary>
            Creates a new list with the specified node inserted at the index.
            </summary>
            <param name="index">The index to insert at.</param>
            <param name="node">The node to insert.</param>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxList`1.InsertRange(System.Int32,System.Collections.Generic.IEnumerable{`0})">
            <summary>
            Creates a new list with the specified nodes inserted at the index.
            </summary>
            <param name="index">The index to insert at.</param>
            <param name="nodes">The nodes to insert.</param>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxList`1.RemoveAt(System.Int32)">
            <summary>
            Creates a new list with the element at specified index removed.
            </summary>
            <param name="index">The index of the element to remove.</param>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxList`1.Remove(`0)">
            <summary>
            Creates a new list with the element removed.
            </summary>
            <param name="node">The element to remove.</param>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxList`1.Replace(`0,`0)">
            <summary>
            Creates a new list with the specified element replaced with the new node.
            </summary>
            <param name="nodeInList">The element to replace.</param>
            <param name="newNode">The new node.</param>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxList`1.ReplaceRange(`0,System.Collections.Generic.IEnumerable{`0})">
            <summary>
            Creates a new list with the specified element replaced with new nodes.
            </summary>
            <param name="nodeInList">The element to replace.</param>
            <param name="newNodes">The new nodes.</param>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxList`1.First">
            <summary>
            The first node in the list.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxList`1.FirstOrDefault">
            <summary>
            The first node in the list or default if the list is empty.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxList`1.Last">
            <summary>
            The last node in the list.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxList`1.LastOrDefault">
            <summary>
            The last node in the list or default if the list is empty.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxList`1.Any">
            <summary>
            True if the list has at least one node.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxList`1.GetEnumerator">
            <summary>
            Get's the enumerator for this list.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxList`1.IndexOf(`0)">
            <summary>
            The index of the node in this list, or -1 if the node is not in the list.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxNode.ChildNodes">
            <summary>
            The list of child nodes of this node, where each element is a SyntaxNode instance.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxNode.Ancestors">
            <summary>
            Gets a list of ancestor nodes
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxNode.AncestorsAndSelf">
            <summary>
            Gets a list of ancestor nodes (including this node) 
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxNode.FirstAncestorOrSelf``1(System.Func{``0,System.Boolean})">
            <summary>
            Gets the first node of type TNode that matches the predicate.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxNode.DescendantNodes(System.Func{Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxNode,System.Boolean})">
            <summary>
            Gets a list of descendant nodes in prefix document order.
            </summary>
            <param name="descendIntoChildren">An optional function that determines if the search descends into the argument node's children.</param>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxNode.DescendantNodesAndSelf(System.Func{Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxNode,System.Boolean})">
            <summary>
            Gets a list of descendant nodes (including this node) in prefix document order.
            </summary>
            <param name="descendIntoChildren">An optional function that determines if the search descends into the argument node's children.</param>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxNodeExtensions.GetAllDiagnostics``1(``0)">
            <summary>
            Gets top-level and nested diagnostics from the <paramref name="node"/>.
            </summary>
            <typeparam name="TNode">The type of syntax node.</typeparam>
            <param name="node">The syntax node.</param>
            <returns>The list of <see cref="T:Microsoft.AspNetCore.Razor.Language.RazorDiagnostic"/>s.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxNodeExtensions.ReplaceSyntax``1(``0,System.Collections.Generic.IEnumerable{Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxNode},System.Func{Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxNode,Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxNode,Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxNode})">
            <summary>
            Creates a new tree of nodes with the specified nodes, tokens and trivia replaced.
            </summary>
            <typeparam name="TRoot">The type of the root node.</typeparam>
            <param name="root">The root node of the tree of nodes.</param>
            <param name="nodes">The nodes to be replaced.</param>
            <param name="computeReplacementNode">A function that computes a replacement node for the
            argument nodes. The first argument is the original node. The second argument is the same
            node potentially rewritten with replaced descendants.</param>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxNodeExtensions.ReplaceNodes``2(``0,System.Collections.Generic.IEnumerable{``1},System.Func{``1,``1,Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxNode})">
            <summary>
            Creates a new tree of nodes with the specified old node replaced with a new node.
            </summary>
            <typeparam name="TRoot">The type of the root node.</typeparam>
            <typeparam name="TNode">The type of the nodes being replaced.</typeparam>
            <param name="root">The root node of the tree of nodes.</param>
            <param name="nodes">The nodes to be replaced; descendants of the root node.</param>
            <param name="computeReplacementNode">A function that computes a replacement node for the
            argument nodes. The first argument is the original node. The second argument is the same
            node potentially rewritten with replaced descendants.</param>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxNodeExtensions.ReplaceNode``1(``0,Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxNode,Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxNode)">
            <summary>
            Creates a new tree of nodes with the specified old node replaced with a new node.
            </summary>
            <typeparam name="TRoot">The type of the root node.</typeparam>
            <param name="root">The root node of the tree of nodes.</param>
            <param name="oldNode">The node to be replaced; a descendant of the root node.</param>
            <param name="newNode">The new node to use in the new tree in place of the old node.</param>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxNodeExtensions.ReplaceNode``1(``0,Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxNode,System.Collections.Generic.IEnumerable{Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxNode})">
            <summary>
            Creates a new tree of nodes with specified old node replaced with a new nodes.
            </summary>
            <typeparam name="TRoot">The type of the root node.</typeparam>
            <param name="root">The root of the tree of nodes.</param>
            <param name="oldNode">The node to be replaced; a descendant of the root node and an element of a list member.</param>
            <param name="newNodes">A sequence of nodes to use in the tree in place of the old node.</param>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxNodeExtensions.InsertNodesBefore``1(``0,Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxNode,System.Collections.Generic.IEnumerable{Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxNode})">
            <summary>
            Creates a new tree of nodes with new nodes inserted before the specified node.
            </summary>
            <typeparam name="TRoot">The type of the root node.</typeparam>
            <param name="root">The root of the tree of nodes.</param>
            <param name="nodeInList">The node to insert before; a descendant of the root node an element of a list member.</param>
            <param name="newNodes">A sequence of nodes to insert into the tree immediately before the specified node.</param>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxNodeExtensions.InsertNodesAfter``1(``0,Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxNode,System.Collections.Generic.IEnumerable{Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxNode})">
            <summary>
            Creates a new tree of nodes with new nodes inserted after the specified node.
            </summary>
            <typeparam name="TRoot">The type of the root node.</typeparam>
            <param name="root">The root of the tree of nodes.</param>
            <param name="nodeInList">The node to insert after; a descendant of the root node an element of a list member.</param>
            <param name="newNodes">A sequence of nodes to insert into the tree immediately after the specified node.</param>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxTriviaList.#ctor(Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxTrivia[])">
            <summary>
            Creates a list of trivia.
            </summary>
            <param name="trivias">An array of trivia.</param>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxTriviaList.#ctor(System.Collections.Generic.IEnumerable{Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxTrivia})">
            <summary>
            Creates a list of trivia.
            </summary>
            <param name="trivias">A sequence of trivia.</param>
        </member>
        <member name="P:Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxTriviaList.Item(System.Int32)">
            <summary>
            Gets the trivia at the specified index.
            </summary>
            <param name="index">The zero-based index of the trivia to get.</param>
            <returns>The token at the specified index.</returns>
            <exception cref="T:System.ArgumentOutOfRangeException">
            <paramref name="index" /> is less than 0.-or-<paramref name="index" /> is equal to or greater than <see cref="P:Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxTriviaList.Count" />. </exception>
        </member>
        <member name="P:Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxTriviaList.FullSpan">
            <summary>
            The absolute span of the list elements in characters, including the leading and trailing trivia of the first and last elements.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxTriviaList.Span">
            <summary>
            The absolute span of the list elements in characters, not including the leading and trailing trivia of the first and last elements.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxTriviaList.First">
            <summary>
            Returns the first trivia in the list.
            </summary>
            <returns>The first trivia in the list.</returns>
            <exception cref="T:System.InvalidOperationException">The list is empty.</exception>        
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxTriviaList.Last">
            <summary>
            Returns the last trivia in the list.
            </summary>
            <returns>The last trivia in the list.</returns>
            <exception cref="T:System.InvalidOperationException">The list is empty.</exception>        
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxTriviaList.Any">
            <summary>
            Does this list have any items.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxTriviaList.Reverse">
            <summary>
            Returns a list which contains all elements of <see cref="T:Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxTriviaList"/> in reversed order.
            </summary>
            <returns><see cref="T:Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxTriviaList.Reversed"/> which contains all elements of <see cref="T:Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxTriviaList"/> in reversed order</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxTriviaList.Add(Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxTrivia)">
            <summary>
            Creates a new <see cref="T:Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxTriviaList"/> with the specified trivia added to the end.
            </summary>
            <param name="trivia">The trivia to add.</param>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxTriviaList.AddRange(System.Collections.Generic.IEnumerable{Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxTrivia})">
            <summary>
            Creates a new <see cref="T:Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxTriviaList"/> with the specified trivia added to the end.
            </summary>
            <param name="trivia">The trivia to add.</param>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxTriviaList.Insert(System.Int32,Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxTrivia)">
            <summary>
            Creates a new <see cref="T:Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxTriviaList"/> with the specified trivia inserted at the index.
            </summary>
            <param name="index">The index in the list to insert the trivia at.</param>
            <param name="trivia">The trivia to insert.</param>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxTriviaList.InsertRange(System.Int32,System.Collections.Generic.IEnumerable{Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxTrivia})">
            <summary>
            Creates a new <see cref="T:Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxTriviaList"/> with the specified trivia inserted at the index.
            </summary>
            <param name="index">The index in the list to insert the trivia at.</param>
            <param name="trivia">The trivia to insert.</param>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxTriviaList.RemoveAt(System.Int32)">
            <summary>
            Creates a new <see cref="T:Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxTriviaList"/> with the element at the specified index removed.
            </summary>
            <param name="index">The index identifying the element to remove.</param>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxTriviaList.Remove(Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxTrivia)">
            <summary>
            Creates a new <see cref="T:Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxTriviaList"/> with the specified element removed.
            </summary>
            <param name="triviaInList">The trivia element to remove.</param>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxTriviaList.Replace(Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxTrivia,Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxTrivia)">
            <summary>
            Creates a new <see cref="T:Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxTriviaList"/> with the specified element replaced with new trivia.
            </summary>
            <param name="triviaInList">The trivia element to replace.</param>
            <param name="newTrivia">The trivia to replace the element with.</param>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxTriviaList.ReplaceRange(Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxTrivia,System.Collections.Generic.IEnumerable{Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxTrivia})">
            <summary>
            Creates a new <see cref="T:Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxTriviaList"/> with the specified element replaced with new trivia.
            </summary>
            <param name="triviaInList">The trivia element to replace.</param>
            <param name="newTrivia">The trivia to replace the element with.</param>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxTriviaList.GetNodeAt(System.Int32)">
            <summary>
            get the green node at the specific slot
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxTriviaList.CopyTo(System.Int32,Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxTrivia[],System.Int32,System.Int32)">
            <summary>
            Copy <paramref name="count"/> number of items starting at <paramref name="offset"/> from this list into <paramref name="array"/> starting at <paramref name="arrayOffset"/>.
            </summary>
        </member>
        <member name="T:Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxTriviaList.Reversed">
            <summary>
            Reversed enumerable.
            </summary>
        </member>
        <member name="T:Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxWalker">
            <summary>
            Represents a <see cref="T:Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxVisitor"/> that descends an entire <see cref="T:Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxNode"/> graph
            visiting each SyntaxNode and its child SyntaxNodes and <see cref="T:Microsoft.AspNetCore.Razor.Language.Syntax.SyntaxToken"/>s in depth-first order.
            </summary>
        </member>
        <member name="T:Microsoft.AspNetCore.Razor.Language.Syntax.TextSpan">
            <summary>
            Immutable abstract representation of a span of text.  For example, in an error diagnostic that reports a
            location, it could come from a parsed string, text from a tool editor buffer, etc.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Syntax.TextSpan.#ctor(System.Int32,System.Int32)">
            <summary>
            Creates a TextSpan instance beginning with the position Start and having the Length
            specified with <paramref name="length" />.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Razor.Language.Syntax.TextSpan.Start">
            <summary>
            Start point of the span.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Razor.Language.Syntax.TextSpan.End">
            <summary>
            End of the span.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Razor.Language.Syntax.TextSpan.Length">
            <summary>
            Length of the span.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Razor.Language.Syntax.TextSpan.IsEmpty">
            <summary>
            Determines whether or not the span is empty.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Syntax.TextSpan.Contains(System.Int32)">
            <summary>
            Determines whether the position lies within the span.
            </summary>
            <param name="position">
            The position to check.
            </param>
            <returns>
            <c>true</c> if the position is greater than or equal to Start and strictly less
            than End, otherwise <c>false</c>.
            </returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Syntax.TextSpan.Contains(Microsoft.AspNetCore.Razor.Language.Syntax.TextSpan)">
            <summary>
            Determines whether <paramref name="span"/> falls completely within this span.
            </summary>
            <param name="span">
            The span to check.
            </param>
            <returns>
            <c>true</c> if the specified span falls completely within this span, otherwise <c>false</c>.
            </returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Syntax.TextSpan.OverlapsWith(Microsoft.AspNetCore.Razor.Language.Syntax.TextSpan)">
            <summary>
            Determines whether <paramref name="span"/> overlaps this span. Two spans are considered to overlap
            if they have positions in common and neither is empty. Empty spans do not overlap with any
            other span.
            </summary>
            <param name="span">
            The span to check.
            </param>
            <returns>
            <c>true</c> if the spans overlap, otherwise <c>false</c>.
            </returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Syntax.TextSpan.Overlap(Microsoft.AspNetCore.Razor.Language.Syntax.TextSpan)">
            <summary>
            Returns the overlap with the given span, or null if there is no overlap.
            </summary>
            <param name="span">
            The span to check.
            </param>
            <returns>
            The overlap of the spans, or null if the overlap is empty.
            </returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Syntax.TextSpan.IntersectsWith(Microsoft.AspNetCore.Razor.Language.Syntax.TextSpan)">
            <summary>
            Determines whether <paramref name="span"/> intersects this span. Two spans are considered to
            intersect if they have positions in common or the end of one span
            coincides with the start of the other span.
            </summary>
            <param name="span">
            The span to check.
            </param>
            <returns>
            <c>true</c> if the spans intersect, otherwise <c>false</c>.
            </returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Syntax.TextSpan.IntersectsWith(System.Int32)">
            <summary>
            Determines whether <paramref name="position"/> intersects this span.
            A position is considered to intersect if it is between the start and
            end positions (inclusive) of this span.
            </summary>
            <param name="position">
            The position to check.
            </param>
            <returns>
            <c>true</c> if the position intersects, otherwise <c>false</c>.
            </returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Syntax.TextSpan.Intersection(Microsoft.AspNetCore.Razor.Language.Syntax.TextSpan)">
            <summary>
            Returns the intersection with the given span, or null if there is no intersection.
            </summary>
            <param name="span">
            The span to check.
            </param>
            <returns>
            The intersection of the spans, or null if the intersection is empty.
            </returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Syntax.TextSpan.FromBounds(System.Int32,System.Int32)">
             <summary>
             Creates a new <see cref="T:Microsoft.AspNetCore.Razor.Language.Syntax.TextSpan"/> from <paramref name="start" /> and <paramref
             name="end"/> positions as opposed to a position and length.
            
             The returned TextSpan contains the range with <paramref name="start"/> inclusive,
             and <paramref name="end"/> exclusive.
             </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Syntax.TextSpan.op_Equality(Microsoft.AspNetCore.Razor.Language.Syntax.TextSpan,Microsoft.AspNetCore.Razor.Language.Syntax.TextSpan)">
            <summary>
            Determines if two instances of <see cref="T:Microsoft.AspNetCore.Razor.Language.Syntax.TextSpan"/> are the same.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Syntax.TextSpan.op_Inequality(Microsoft.AspNetCore.Razor.Language.Syntax.TextSpan,Microsoft.AspNetCore.Razor.Language.Syntax.TextSpan)">
            <summary>
            Determines if two instances of <see cref="T:Microsoft.AspNetCore.Razor.Language.Syntax.TextSpan"/> are different.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Syntax.TextSpan.Equals(Microsoft.AspNetCore.Razor.Language.Syntax.TextSpan)">
            <summary>
            Determines if current instance of <see cref="T:Microsoft.AspNetCore.Razor.Language.Syntax.TextSpan"/> is equal to another.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Syntax.TextSpan.Equals(System.Object)">
            <summary>
            Determines if current instance of <see cref="T:Microsoft.AspNetCore.Razor.Language.Syntax.TextSpan"/> is equal to another.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Syntax.TextSpan.GetHashCode">
            <summary>
            Produces a hash code for <see cref="T:Microsoft.AspNetCore.Razor.Language.Syntax.TextSpan"/>.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Syntax.TextSpan.ToString">
            <summary>
            Provides a string representation for <see cref="T:Microsoft.AspNetCore.Razor.Language.Syntax.TextSpan"/>.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Syntax.TextSpan.CompareTo(Microsoft.AspNetCore.Razor.Language.Syntax.TextSpan)">
            <summary>
            Compares current instance of <see cref="T:Microsoft.AspNetCore.Razor.Language.Syntax.TextSpan"/> with another.
            </summary>
        </member>
        <member name="T:Microsoft.AspNetCore.Razor.Language.TagHelperBinder">
            <summary>
            Enables retrieval of <see cref="T:Microsoft.AspNetCore.Razor.Language.TagHelperBinding"/>'s.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.TagHelperBinder.#ctor(System.String,System.Collections.Generic.IEnumerable{Microsoft.AspNetCore.Razor.Language.TagHelperDescriptor})">
            <summary>
            Instantiates a new instance of the <see cref="T:Microsoft.AspNetCore.Razor.Language.TagHelperBinder"/>.
            </summary>
            <param name="tagHelperPrefix">The tag helper prefix being used by the document.</param>
            <param name="descriptors">The descriptors that the <see cref="T:Microsoft.AspNetCore.Razor.Language.TagHelperBinder"/> will pull from.</param>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.TagHelperBinder.GetBinding(System.String,System.Collections.Generic.IReadOnlyList{System.Collections.Generic.KeyValuePair{System.String,System.String}},System.String,System.Boolean)">
            <summary>
            Gets all tag helpers that match the given HTML tag criteria.
            </summary>
            <param name="tagName">The name of the HTML tag to match. Providing a '*' tag name
            retrieves catch-all <see cref="T:Microsoft.AspNetCore.Razor.Language.TagHelperDescriptor"/>s (descriptors that target every tag).</param>
            <param name="attributes">Attributes on the HTML tag.</param>
            <param name="parentTagName">The parent tag name of the given <paramref name="tagName"/> tag.</param>
            <param name="parentIsTagHelper">Is the parent tag of the given <paramref name="tagName"/> tag a tag helper.</param>
            <returns><see cref="T:Microsoft.AspNetCore.Razor.Language.TagHelperDescriptor"/>s that apply to the given HTML tag criteria.
            Will return <c>null</c> if no <see cref="T:Microsoft.AspNetCore.Razor.Language.TagHelperDescriptor"/>s are a match.</returns>
        </member>
        <member name="P:Microsoft.AspNetCore.Razor.Language.TagHelperBinding.IsAttributeMatch">
            <summary>
            Gets a value that indicates whether the the binding matched on attributes only.
            </summary>
            <returns><c>false</c> if the entire element should be classified as a tag helper.</returns>
            <remarks>
            If this returns <c>true</c>, use <c>TagHelperFactsService.GetBoundTagHelperAttributes</c> to find the 
            set of attributes that should be considered part of the match.
            </remarks>
        </member>
        <member name="F:Microsoft.AspNetCore.Razor.Language.TagHelperDescriptorComparer.Default">
            <summary>
            A default instance of the <see cref="T:Microsoft.AspNetCore.Razor.Language.TagHelperDescriptorComparer"/>.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.TagHelperDescriptorComparer.GetHashCode(Microsoft.AspNetCore.Razor.Language.TagHelperDescriptor)">
            <inheritdoc />
        </member>
        <member name="T:Microsoft.AspNetCore.Razor.Language.TagHelperDocumentContext">
            <summary>
            The binding information for Tag Helpers resulted to a <see cref="T:Microsoft.AspNetCore.Razor.Language.RazorCodeDocument"/>. Represents the
            Tag Helper information after processing by directives.
            </summary>
        </member>
        <member name="F:Microsoft.AspNetCore.Razor.Language.TagMatchingRuleDescriptorComparer.Default">
            <summary>
            A default instance of the <see cref="T:Microsoft.AspNetCore.Razor.Language.TagMatchingRuleDescriptorComparer"/>.
            </summary>
        </member>
        <member name="T:Microsoft.AspNetCore.Razor.Language.TagMode">
            <summary>
            The mode in which an element should render.
            </summary>
        </member>
        <member name="F:Microsoft.AspNetCore.Razor.Language.TagMode.StartTagAndEndTag">
            <summary>
            Include both start and end tags.
            </summary>
        </member>
        <member name="F:Microsoft.AspNetCore.Razor.Language.TagMode.SelfClosing">
            <summary>
            A self-closed tag.
            </summary>
        </member>
        <member name="F:Microsoft.AspNetCore.Razor.Language.TagMode.StartTagOnly">
            <summary>
            Only a start tag.
            </summary>
        </member>
        <member name="T:Microsoft.AspNetCore.Razor.Language.TagStructure">
            <summary>
            The structure the element should be written in.
            </summary>
        </member>
        <member name="F:Microsoft.AspNetCore.Razor.Language.TagStructure.Unspecified">
            <summary>
            If no other tag helper applies to the same element and specifies a <see cref="T:Microsoft.AspNetCore.Razor.Language.TagStructure"/>,
            <see cref="F:Microsoft.AspNetCore.Razor.Language.TagStructure.NormalOrSelfClosing"/> will be used.
            </summary>
        </member>
        <member name="F:Microsoft.AspNetCore.Razor.Language.TagStructure.NormalOrSelfClosing">
            <summary>
            Element can be written as &lt;my-tag-helper&gt;&lt;/my-tag-helper&gt; or &lt;my-tag-helper /&gt;.
            </summary>
        </member>
        <member name="F:Microsoft.AspNetCore.Razor.Language.TagStructure.WithoutEndTag">
            <summary>
            Element can be written as &lt;my-tag-helper&gt; or &lt;my-tag-helper /&gt;.
            </summary>
            <remarks>Elements with a <see cref="F:Microsoft.AspNetCore.Razor.Language.TagStructure.WithoutEndTag"/> structure will never have any content.</remarks>
        </member>
        <member name="P:Microsoft.AspNetCore.Razor.Language.ComponentResources.AttributeDirective_AttributeToken_Description">
            <summary>The C# attribute that will be applied to the current class.</summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Razor.Language.ComponentResources.AttributeDirective_AttributeToken_Name">
            <summary>Attribute</summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Razor.Language.ComponentResources.AttributeDirective_Description">
            <summary>Specifies the C# attribute that will be applied to the current class.</summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Razor.Language.ComponentResources.BindTagHelper_Component_Documentation">
            <summary>Binds the provided expression to the '{0}' property and a change event delegate to the '{1}' property of the component.</summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.ComponentResources.FormatBindTagHelper_Component_Documentation(System.Object,System.Object)">
            <summary>Binds the provided expression to the '{0}' property and a change event delegate to the '{1}' property of the component.</summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Razor.Language.ComponentResources.BindTagHelper_Element_Culture_Documentation">
            <summary>Specifies the culture to use for conversions.</summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Razor.Language.ComponentResources.BindTagHelper_Element_Documentation">
            <summary>Binds the provided expression to the '{0}' attribute and a change event delegate to the '{1}' attribute.</summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.ComponentResources.FormatBindTagHelper_Element_Documentation(System.Object,System.Object)">
            <summary>Binds the provided expression to the '{0}' attribute and a change event delegate to the '{1}' attribute.</summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Razor.Language.ComponentResources.BindTagHelper_Element_Event_Documentation">
            <summary>Specifies the event handler name to attach for change notifications for the value provided by the '{0}' attribute.</summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.ComponentResources.FormatBindTagHelper_Element_Event_Documentation(System.Object)">
            <summary>Specifies the event handler name to attach for change notifications for the value provided by the '{0}' attribute.</summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Razor.Language.ComponentResources.BindTagHelper_Element_Format_Documentation">
            <summary>Specifies a format to convert the value specified by the '{0}' attribute. The format string can currently only be used with expressions of type &lt;code&gt;DateTime&lt;/code&gt;.</summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.ComponentResources.FormatBindTagHelper_Element_Format_Documentation(System.Object)">
            <summary>Specifies a format to convert the value specified by the '{0}' attribute. The format string can currently only be used with expressions of type &lt;code&gt;DateTime&lt;/code&gt;.</summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Razor.Language.ComponentResources.BindTagHelper_Fallback_Documentation">
            <summary>Binds the provided expression to an attribute and a change event, based on the naming of the bind attribute. For example: &lt;code&gt;@bind-value="..."&lt;/code&gt; and &lt;code&gt;@bind-value:event="onchange"&lt;/code&gt; will assign the current value of the expression to the 'v ...</summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Razor.Language.ComponentResources.BindTagHelper_Fallback_Event_Documentation">
            <summary>Specifies the event handler name to attach for change notifications for the value provided by the '{0}' attribute.</summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.ComponentResources.FormatBindTagHelper_Fallback_Event_Documentation(System.Object)">
            <summary>Specifies the event handler name to attach for change notifications for the value provided by the '{0}' attribute.</summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Razor.Language.ComponentResources.BindTagHelper_Fallback_Format_Documentation">
            <summary>Specifies a format to convert the value specified by the corresponding bind attribute. For example: &lt;code&gt;@bind-value:format="..."&lt;/code&gt; will apply a format string to the value specified in &lt;code&gt;@bind-value="..."&lt;/code&gt;. The format string can currently o ...</summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Razor.Language.ComponentResources.ChildContentParameterName_Documentation">
            <summary>Specifies the parameter name for the '{0}' child content expression.</summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.ComponentResources.FormatChildContentParameterName_Documentation(System.Object)">
            <summary>Specifies the parameter name for the '{0}' child content expression.</summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Razor.Language.ComponentResources.ChildContentParameterName_TopLevelDocumentation">
            <summary>Specifies the parameter name for all child content expressions.</summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Razor.Language.ComponentResources.ComponentTypeParameter_Documentation">
            <summary>Specifies the type of the type parameter {0} for the {1} component.</summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.ComponentResources.FormatComponentTypeParameter_Documentation(System.Object,System.Object)">
            <summary>Specifies the type of the type parameter {0} for the {1} component.</summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Razor.Language.ComponentResources.EventHandlerTagHelper_Documentation">
            <summary>Sets the '{0}' attribute to the provided string or delegate value. A delegate value should be of type '{1}'.</summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.ComponentResources.FormatEventHandlerTagHelper_Documentation(System.Object,System.Object)">
            <summary>Sets the '{0}' attribute to the provided string or delegate value. A delegate value should be of type '{1}'.</summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Razor.Language.ComponentResources.EventHandlerTagHelper_PreventDefault_Documentation">
            <summary>Specifies whether to cancel (if cancelable) the default action that belongs to the '{0}' event.</summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.ComponentResources.FormatEventHandlerTagHelper_PreventDefault_Documentation(System.Object)">
            <summary>Specifies whether to cancel (if cancelable) the default action that belongs to the '{0}' event.</summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Razor.Language.ComponentResources.EventHandlerTagHelper_StopPropagation_Documentation">
            <summary>Specifies whether to prevent further propagation of the '{0}' event in the capturing and bubbling phases.</summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.ComponentResources.FormatEventHandlerTagHelper_StopPropagation_Documentation(System.Object)">
            <summary>Specifies whether to prevent further propagation of the '{0}' event in the capturing and bubbling phases.</summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Razor.Language.ComponentResources.ImplementsDirective_Description">
            <summary>Declares an interface implementation for the current class.</summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Razor.Language.ComponentResources.ImplementsDirective_TypeToken_Description">
            <summary>The interface type implemented by the current class.</summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Razor.Language.ComponentResources.ImplementsDirective_TypeToken_Name">
            <summary>TypeName</summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Razor.Language.ComponentResources.KeyTagHelper_Documentation">
            <summary>Ensures that the component or element will be preserved across renders if (and only if) the supplied key value matches.</summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Razor.Language.ComponentResources.LayoutDirective_Description">
            <summary>Declares a layout type for the current document.</summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Razor.Language.ComponentResources.LayoutDirective_TypeToken_Description">
            <summary>The interface type implemented by the current document.</summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Razor.Language.ComponentResources.LayoutDirective_TypeToken_Name">
            <summary>TypeName</summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Razor.Language.ComponentResources.PageDirectiveCannotBeImported">
            <summary>The '@{0}' directive specified in {1} file will not be imported. The directive must appear at the top of each Razor file</summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.ComponentResources.FormatPageDirectiveCannotBeImported(System.Object,System.Object)">
            <summary>The '@{0}' directive specified in {1} file will not be imported. The directive must appear at the top of each Razor file</summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Razor.Language.ComponentResources.PageDirective_Description">
            <summary>Mark the page as a routable component.</summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Razor.Language.ComponentResources.PageDirective_RouteToken_Description">
            <summary>An optional route template for the component.</summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Razor.Language.ComponentResources.PageDirective_RouteToken_Name">
            <summary>route template</summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Razor.Language.ComponentResources.RefTagHelper_Documentation">
            <summary>Populates the specified field or property with a reference to the element or component.</summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Razor.Language.ComponentResources.SplatTagHelper_Documentation">
            <summary>Merges a collection of attributes into the current element or component.</summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Razor.Language.ComponentResources.TypeParamDirective_Description">
            <summary>Declares a generic type parameter for the generated component class.</summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Razor.Language.ComponentResources.TypeParamDirective_Token_Description">
            <summary>The name of the type parameter.</summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Razor.Language.ComponentResources.TypeParamDirective_Token_Name">
            <summary>type parameter</summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Razor.Language.Resources.AddTagHelperDirective_Description">
            <summary>Register Tag Helpers for use in the current document.</summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Razor.Language.Resources.AddTagHelperDirective_StringToken_Description">
            <summary>Add tag helpers from the specified type name and assembly name. Specify '*' for the type name to include all tag helper types from the specified assembly.</summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Razor.Language.Resources.AddTagHelperDirective_StringToken_Name">
            <summary>TypeName, AssemblyName</summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Razor.Language.Resources.ArgumentCannotBeNullOrEmpty">
            <summary>Value cannot be null or an empty string.</summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Razor.Language.Resources.Block_Type_Not_Specified">
            <summary>Block cannot be built because a Type has not been specified in the BlockBuilder</summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Razor.Language.Resources.BlockDirectiveCannotBeImported">
            <summary>Block directive '{0}' cannot be imported.</summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Resources.FormatBlockDirectiveCannotBeImported(System.Object)">
            <summary>Block directive '{0}' cannot be imported.</summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Razor.Language.Resources.BlockName_Code">
            <summary>code</summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Razor.Language.Resources.BlockName_ExplicitExpression">
            <summary>explicit expression</summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Razor.Language.Resources.CodeWriter_InvalidNewLine">
            <summary>Invalid newline sequence '{0}'. Support newline sequences are '\r\n' and '\n'.</summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Resources.FormatCodeWriter_InvalidNewLine(System.Object)">
            <summary>Invalid newline sequence '{0}'. Support newline sequences are '\r\n' and '\n'.</summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Razor.Language.Resources.CSharpToken_CharacterLiteral">
            <summary>&lt;&lt;character literal&gt;&gt;</summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Razor.Language.Resources.CSharpToken_Comment">
            <summary>&lt;&lt;comment&gt;&gt;</summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Razor.Language.Resources.CSharpToken_Identifier">
            <summary>&lt;&lt;identifier&gt;&gt;</summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Razor.Language.Resources.CSharpToken_IntegerLiteral">
            <summary>&lt;&lt;integer literal&gt;&gt;</summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Razor.Language.Resources.CSharpToken_Keyword">
            <summary>&lt;&lt;keyword&gt;&gt;</summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Razor.Language.Resources.CSharpToken_Newline">
            <summary>&lt;&lt;newline sequence&gt;&gt;</summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Razor.Language.Resources.CSharpToken_RealLiteral">
            <summary>&lt;&lt;real literal&gt;&gt;</summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Razor.Language.Resources.CSharpToken_StringLiteral">
            <summary>&lt;&lt;string literal&gt;&gt;</summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Razor.Language.Resources.CSharpToken_Whitespace">
            <summary>&lt;&lt;white space&gt;&gt;</summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Razor.Language.Resources.Diagnostic_CodeTarget_UnsupportedExtension">
            <summary>The document type '{0}' does not support the extension '{1}'.</summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Resources.FormatDiagnostic_CodeTarget_UnsupportedExtension(System.Object,System.Object)">
            <summary>The document type '{0}' does not support the extension '{1}'.</summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Razor.Language.Resources.DirectiveDescriptor_InvalidDirectiveKeyword">
            <summary>Invalid directive keyword '{0}'. Directives must have a non-empty keyword that consists only of letters.</summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Resources.FormatDirectiveDescriptor_InvalidDirectiveKeyword(System.Object)">
            <summary>Invalid directive keyword '{0}'. Directives must have a non-empty keyword that consists only of letters.</summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Razor.Language.Resources.DirectiveDescriptor_InvalidNonOptionalToken">
            <summary>A non-optional directive token cannot follow an optional directive token.</summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Razor.Language.Resources.DirectiveExpectsIdentifier">
            <summary>The '{0}' directive expects an identifier.</summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Resources.FormatDirectiveExpectsIdentifier(System.Object)">
            <summary>The '{0}' directive expects an identifier.</summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Razor.Language.Resources.DirectiveExpectsNamespace">
            <summary>The '{0}' directive expects a namespace name.</summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Resources.FormatDirectiveExpectsNamespace(System.Object)">
            <summary>The '{0}' directive expects a namespace name.</summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Razor.Language.Resources.DirectiveExpectsQuotedStringLiteral">
            <summary>The '{0}' directive expects a string surrounded by double quotes.</summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Resources.FormatDirectiveExpectsQuotedStringLiteral(System.Object)">
            <summary>The '{0}' directive expects a string surrounded by double quotes.</summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Razor.Language.Resources.DirectiveExpectsTypeName">
            <summary>The '{0}' directive expects a type name.</summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Resources.FormatDirectiveExpectsTypeName(System.Object)">
            <summary>The '{0}' directive expects a type name.</summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Razor.Language.Resources.DirectiveMustAppearAtStartOfLine">
            <summary>The '{0}` directive must appear at the start of the line.</summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Resources.FormatDirectiveMustAppearAtStartOfLine(System.Object)">
            <summary>The '{0}` directive must appear at the start of the line.</summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Razor.Language.Resources.DirectiveTokensMustBeSeparatedByWhitespace">
            <summary>The '{0}' directives value(s) must be separated by whitespace.</summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Resources.FormatDirectiveTokensMustBeSeparatedByWhitespace(System.Object)">
            <summary>The '{0}' directives value(s) must be separated by whitespace.</summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Razor.Language.Resources.DocumentMissingTarget">
            <summary>The document of kind '{0}' does not have a '{1}'. The document classifier must set a value for '{2}'.</summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Resources.FormatDocumentMissingTarget(System.Object,System.Object,System.Object)">
            <summary>The document of kind '{0}' does not have a '{1}'. The document classifier must set a value for '{2}'.</summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Razor.Language.Resources.DuplicateDirective">
            <summary>The '{0}' directive may only occur once per document.</summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Resources.FormatDuplicateDirective(System.Object)">
            <summary>The '{0}' directive may only occur once per document.</summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Razor.Language.Resources.EndBlock_Called_Without_Matching_StartBlock">
            <summary>"EndBlock" was called without a matching call to "StartBlock".</summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Razor.Language.Resources.ErrorComponent_Newline">
            <summary>line break</summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Razor.Language.Resources.FeatureDependencyMissing">
            <summary>The '{0}' feature requires a '{1}' provided by the '{2}'.</summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Resources.FormatFeatureDependencyMissing(System.Object,System.Object,System.Object)">
            <summary>The '{0}' feature requires a '{1}' provided by the '{2}'.</summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Razor.Language.Resources.FeatureMustBeInitialized">
            <summary>The feature must be initialized by setting the '{0}' property.</summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Resources.FormatFeatureMustBeInitialized(System.Object)">
            <summary>The feature must be initialized by setting the '{0}' property.</summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Razor.Language.Resources.FunctionsDirective_Description">
            <summary>Specify a C# code block.</summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Razor.Language.Resources.HtmlToken_NewLine">
            <summary>&lt;&lt;newline sequence&gt;&gt;</summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Razor.Language.Resources.HtmlToken_RazorComment">
            <summary>&lt;&lt;razor comment&gt;&gt;</summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Razor.Language.Resources.HtmlToken_Text">
            <summary>&lt;&lt;text&gt;&gt;</summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Razor.Language.Resources.HtmlToken_WhiteSpace">
            <summary>&lt;&lt;white space&gt;&gt;</summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Razor.Language.Resources.InheritsDirective_Description">
            <summary>Specify the base class for the current document.</summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Razor.Language.Resources.InheritsDirective_TypeToken_Description">
            <summary>The base type that the current page inherits.</summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Razor.Language.Resources.InheritsDirective_TypeToken_Name">
            <summary>TypeName</summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Razor.Language.Resources.IntermediateNodeBuilder_PopInvalid">
            <summary>The '{0}' operation is not valid when the builder is empty.</summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Resources.FormatIntermediateNodeBuilder_PopInvalid(System.Object)">
            <summary>The '{0}' operation is not valid when the builder is empty.</summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Razor.Language.Resources.IntermediateNodeReference_CollectionIsReadOnly">
            <summary>The node '{0}' has a read-only child collection and cannot be modified.</summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Resources.FormatIntermediateNodeReference_CollectionIsReadOnly(System.Object)">
            <summary>The node '{0}' has a read-only child collection and cannot be modified.</summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Razor.Language.Resources.IntermediateNodeReference_NodeNotFound">
            <summary>The reference is invalid. The node '{0}' could not be found as a child of '{1}'.</summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Resources.FormatIntermediateNodeReference_NodeNotFound(System.Object,System.Object)">
            <summary>The reference is invalid. The node '{0}' could not be found as a child of '{1}'.</summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Razor.Language.Resources.IntermediateNodeReference_NotInitialized">
            <summary>The reference is invalid. References initialized with the default constructor cannot modify nodes.</summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Razor.Language.Resources.IntermediateNodes_InvalidParentNode">
            <summary>The '{0}' node type can only be used as a direct child of a '{1}' node.</summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Resources.FormatIntermediateNodes_InvalidParentNode(System.Object,System.Object)">
            <summary>The '{0}' node type can only be used as a direct child of a '{1}' node.</summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Razor.Language.Resources.InvalidOperation_SpanIsNotChangeOwner">
            <summary>The node '{0}' is not the owner of change '{1}'.</summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Resources.FormatInvalidOperation_SpanIsNotChangeOwner(System.Object,System.Object)">
            <summary>The node '{0}' is not the owner of change '{1}'.</summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Razor.Language.Resources.InvalidTagHelperLookupText">
            <summary>Invalid tag helper directive look up text '{0}'. The correct look up text format is: "name, assemblyName".</summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Resources.FormatInvalidTagHelperLookupText(System.Object)">
            <summary>Invalid tag helper directive look up text '{0}'. The correct look up text format is: "name, assemblyName".</summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Razor.Language.Resources.InvalidTagHelperPrefixValue">
            <summary>Invalid tag helper directive '{0}' value. '{1}' is not allowed in prefix '{2}'.</summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Resources.FormatInvalidTagHelperPrefixValue(System.Object,System.Object,System.Object)">
            <summary>Invalid tag helper directive '{0}' value. '{1}' is not allowed in prefix '{2}'.</summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Razor.Language.Resources.KeyMustNotBeNull">
            <summary>The key must not be null.</summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Razor.Language.Resources.Language_Does_Not_Support_RazorComment">
            <summary>Cannot use built-in RazorComment handler, language characteristics does not define the CommentStart, CommentStar and CommentBody known token types or parser does not override TokenizerBackedParser.OutputSpanBeforeRazorComment</summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Razor.Language.Resources.MismatchedContentEncoding">
            <summary>The specified encoding '{0}' does not match the content's encoding '{1}'.</summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Resources.FormatMismatchedContentEncoding(System.Object,System.Object)">
            <summary>The specified encoding '{0}' does not match the content's encoding '{1}'.</summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Razor.Language.Resources.ParseError_AtInCode_Must_Be_Followed_By_Colon_Paren_Or_Identifier_Start">
            <summary>The "@" character must be followed by a ":", "(", or a C# identifier.  If you intended to switch to markup, use an HTML start tag, for example:
            
            @if(isLoggedIn) {{
                &lt;p&gt;Hello, @user!&lt;/p&gt;
            }}</summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Razor.Language.Resources.ParseError_BlockComment_Not_Terminated">
            <summary>End of file was reached before the end of the block comment.  All comments started with "/*" sequence must be terminated with a matching "*/" sequence.</summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Razor.Language.Resources.ParseError_DirectiveMustHaveValue">
            <summary>Directive '{0}' must have a value.</summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Resources.FormatParseError_DirectiveMustHaveValue(System.Object)">
            <summary>Directive '{0}' must have a value.</summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Razor.Language.Resources.ParseError_Expected_CloseBracket_Before_EOF">
            <summary>An opening "{0}" is missing the corresponding closing "{1}".</summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Resources.FormatParseError_Expected_CloseBracket_Before_EOF(System.Object,System.Object)">
            <summary>An opening "{0}" is missing the corresponding closing "{1}".</summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Razor.Language.Resources.ParseError_Expected_EndOfBlock_Before_EOF">
            <summary>The {0} block is missing a closing "{1}" character.  Make sure you have a matching "{1}" character for all the "{2}" characters within this block, and that none of the "{1}" characters are being interpreted as markup.</summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Resources.FormatParseError_Expected_EndOfBlock_Before_EOF(System.Object,System.Object,System.Object)">
            <summary>The {0} block is missing a closing "{1}" character.  Make sure you have a matching "{1}" character for all the "{2}" characters within this block, and that none of the "{1}" characters are being interpreted as markup.</summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Razor.Language.Resources.ParseError_HelperDirectiveNotAvailable">
            <summary>The {0} directive is not supported.</summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Resources.FormatParseError_HelperDirectiveNotAvailable(System.Object)">
            <summary>The {0} directive is not supported.</summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Razor.Language.Resources.ParseError_IncompleteQuotesAroundDirective">
            <summary>Optional quote around the directive '{0}' is missing the corresponding opening or closing quote.</summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Resources.FormatParseError_IncompleteQuotesAroundDirective(System.Object)">
            <summary>Optional quote around the directive '{0}' is missing the corresponding opening or closing quote.</summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Razor.Language.Resources.ParseError_InlineMarkup_Blocks_Cannot_Be_Nested">
            <summary>Inline markup blocks (@&lt;p&gt;Content&lt;/p&gt;) cannot be nested.  Only one level of inline markup is allowed.</summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Razor.Language.Resources.ParseError_MarkupBlock_Must_Start_With_Tag">
            <summary>Markup in a code block must start with a tag and all start tags must be matched with end tags.  Do not use unclosed tags like "&lt;br&gt;".  Instead use self-closing tags like "&lt;br/&gt;".</summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Razor.Language.Resources.ParseError_MissingEndTag">
            <summary>The "{0}" element was not closed.  All elements must be either self-closing or have a matching end tag.</summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Resources.FormatParseError_MissingEndTag(System.Object)">
            <summary>The "{0}" element was not closed.  All elements must be either self-closing or have a matching end tag.</summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Razor.Language.Resources.ParseError_NamespaceImportAndTypeAlias_Cannot_Exist_Within_CodeBlock">
            <summary>Namespace imports and type aliases cannot be placed within code blocks.  They must immediately follow an "@" character in markup.  It is recommended that you put them at the top of the page, as in the following example:
            
            @using System.Drawing;
            @{{
                // O ...</summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Razor.Language.Resources.ParseError_OuterTagMissingName">
            <summary>Outer tag is missing a name. The first character of a markup block must be an HTML tag with a valid name.</summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Razor.Language.Resources.ParseError_RazorComment_Not_Terminated">
            <summary>End of file was reached before the end of the block comment.  All comments that start with the "@*" sequence must be terminated with a matching "*@" sequence.</summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Razor.Language.Resources.ParseError_ReservedWord">
            <summary>"{0}" is a reserved word and cannot be used in implicit expressions.  An explicit expression ("@()") must be used.</summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Resources.FormatParseError_ReservedWord(System.Object)">
            <summary>"{0}" is a reserved word and cannot be used in implicit expressions.  An explicit expression ("@()") must be used.</summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Razor.Language.Resources.ParseError_Sections_Cannot_Be_Nested">
            <summary>Section blocks ("{0}") cannot be nested.  Only one level of section blocks are allowed.</summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Resources.FormatParseError_Sections_Cannot_Be_Nested(System.Object)">
            <summary>Section blocks ("{0}") cannot be nested.  Only one level of section blocks are allowed.</summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Razor.Language.Resources.ParseError_SingleLine_ControlFlowStatements_CannotContainMarkup">
            <summary>Single-statement control-flow statements in Razor documents statements cannot contain markup. Markup should be enclosed in "{{" and "}}".</summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Razor.Language.Resources.ParseError_TextTagCannotContainAttributes">
            <summary>"&lt;text&gt;" and "&lt;/text&gt;" tags cannot contain attributes.</summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Razor.Language.Resources.ParseError_Unexpected_Character_At_Start_Of_CodeBlock">
            <summary>"{0}" is not valid at the start of a code block.  Only identifiers, keywords, comments, "(" and "{{" are valid.</summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Resources.FormatParseError_Unexpected_Character_At_Start_Of_CodeBlock(System.Object)">
            <summary>"{0}" is not valid at the start of a code block.  Only identifiers, keywords, comments, "(" and "{{" are valid.</summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Razor.Language.Resources.ParseError_Unexpected_EndOfFile_At_Start_Of_CodeBlock">
            <summary>End-of-file was found after the "@" character.  "@" must be followed by a valid code block.  If you want to output an "@", escape it using the sequence: "@@"</summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Razor.Language.Resources.ParseError_Unexpected_Nested_CodeBlock">
            <summary>Unexpected "{{" after "@" character. Once inside the body of a code block (@if {{}}, @{{}}, etc.) you do not need to use "@{{" to switch to code.</summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Razor.Language.Resources.ParseError_Unexpected_WhiteSpace_At_Start_Of_CodeBlock">
            <summary>A space or line break was encountered after the "@" character.  Only valid identifiers, keywords, comments, "(" and "{{" are valid at the start of a code block and they must occur immediately following "@" with no space in between.</summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Razor.Language.Resources.ParseError_UnexpectedEndTag">
            <summary>Encountered end tag "{0}" with no matching start tag.  Are your start/end tags properly balanced?</summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Resources.FormatParseError_UnexpectedEndTag(System.Object)">
            <summary>Encountered end tag "{0}" with no matching start tag.  Are your start/end tags properly balanced?</summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Razor.Language.Resources.ParseError_UnfinishedTag">
            <summary>End of file or an unexpected character was reached before the "{0}" tag could be parsed.  Elements inside markup blocks must be complete. They must either be self-closing ("&lt;br /&gt;") or have matching end tags ("&lt;p&gt;Hello&lt;/p&gt;").  If you intended to display a  ...</summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Resources.FormatParseError_UnfinishedTag(System.Object)">
            <summary>End of file or an unexpected character was reached before the "{0}" tag could be parsed.  Elements inside markup blocks must be complete. They must either be self-closing ("&lt;br /&gt;") or have matching end tags ("&lt;p&gt;Hello&lt;/p&gt;").  If you intended to display a  ...</summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Razor.Language.Resources.ParseError_Unterminated_String_Literal">
            <summary>Unterminated string literal.  Strings that start with a quotation mark (") must be terminated before the end of the line.  However, strings that start with @ and a quotation mark (@") can span multiple lines.</summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Razor.Language.Resources.Parser_Context_Not_Set">
            <summary>Parser was started with a null Context property.  The Context property must be set BEFORE calling any methods on the parser.</summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Razor.Language.Resources.ParserContext_CannotCompleteTree_NoRootBlock">
            <summary>Cannot complete the tree, StartBlock must be called at least once.</summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Razor.Language.Resources.ParserContext_CannotCompleteTree_OutstandingBlocks">
            <summary>Cannot complete the tree, there are still open blocks.</summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Razor.Language.Resources.ParserContext_NoCurrentBlock">
            <summary>Cannot finish span, there is no current block. Call StartBlock at least once before finishing a span</summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Razor.Language.Resources.PhaseDependencyMissing">
            <summary>The '{0}' phase requires a '{1}' provided by the '{2}'.</summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Resources.FormatPhaseDependencyMissing(System.Object,System.Object,System.Object)">
            <summary>The '{0}' phase requires a '{1}' provided by the '{2}'.</summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Razor.Language.Resources.PhaseMustBeInitialized">
            <summary>The phase must be initialized by setting the '{0}' property.</summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Resources.FormatPhaseMustBeInitialized(System.Object)">
            <summary>The phase must be initialized by setting the '{0}' property.</summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Razor.Language.Resources.RazorProject_PathMustStartWithForwardSlash">
            <summary>Path must begin with a forward slash '/'.</summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Razor.Language.Resources.RemoveTagHelperDirective_Description">
            <summary>Remove Tag Helpers for use in the current document.</summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Razor.Language.Resources.RemoveTagHelperDirective_StringToken_Description">
            <summary>Remove tag helpers from the specified type name and assembly name. Specify '*' for the type name to remove all tag helper types from the specified assembly.</summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Razor.Language.Resources.RemoveTagHelperDirective_StringToken_Name">
            <summary>TypeName, AssemblyName</summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Razor.Language.Resources.RenderingContextRequiresDelegate">
            <summary>The '{0}' requires a '{1}' delegate to be set.</summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Resources.FormatRenderingContextRequiresDelegate(System.Object,System.Object)">
            <summary>The '{0}' requires a '{1}' delegate to be set.</summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Razor.Language.Resources.RewriterError_EmptyTagHelperBoundAttribute">
            <summary>Attribute '{0}' on tag helper element '{1}' requires a value. Tag helper bound attributes of type '{2}' cannot be empty or contain only whitespace.</summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Resources.FormatRewriterError_EmptyTagHelperBoundAttribute(System.Object,System.Object,System.Object)">
            <summary>Attribute '{0}' on tag helper element '{1}' requires a value. Tag helper bound attributes of type '{2}' cannot be empty or contain only whitespace.</summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Razor.Language.Resources.SectionDirective_Description">
            <summary>Define a section to be rendered in the configured layout page.</summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Razor.Language.Resources.SectionDirective_NameToken_Description">
            <summary>The name of the section.</summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Razor.Language.Resources.SectionDirective_NameToken_Name">
            <summary>SectionName</summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Razor.Language.Resources.SectionExample">
            <summary>@section Header { ... }</summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Razor.Language.Resources.Token_Unknown">
            <summary>&lt;&lt;unknown&gt;&gt;</summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Razor.Language.Resources.TagHelper_InvalidBoundAttributeName">
            <summary>Invalid tag helper bound property '{1}' on tag helper '{0}'. Tag helpers cannot bind to HTML attributes with name '{2}' because the name contains a '{3}' character.</summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Resources.FormatTagHelper_InvalidBoundAttributeName(System.Object,System.Object,System.Object,System.Object)">
            <summary>Invalid tag helper bound property '{1}' on tag helper '{0}'. Tag helpers cannot bind to HTML attributes with name '{2}' because the name contains a '{3}' character.</summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Razor.Language.Resources.TagHelper_InvalidBoundAttributeNameStartsWith">
            <summary>Invalid tag helper bound property '{1}' on tag helper '{0}'. Tag helpers cannot bind to HTML attributes with name '{2}' because the name starts with '{3}'.</summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Resources.FormatTagHelper_InvalidBoundAttributeNameStartsWith(System.Object,System.Object,System.Object,System.Object)">
            <summary>Invalid tag helper bound property '{1}' on tag helper '{0}'. Tag helpers cannot bind to HTML attributes with name '{2}' because the name starts with '{3}'.</summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Razor.Language.Resources.TagHelper_InvalidBoundAttributeNullOrWhitespace">
            <summary>Invalid tag helper bound property '{1}' on tag helper '{0}'. Tag helpers cannot bind to HTML attributes with a null or empty name.</summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Resources.FormatTagHelper_InvalidBoundAttributeNullOrWhitespace(System.Object,System.Object)">
            <summary>Invalid tag helper bound property '{1}' on tag helper '{0}'. Tag helpers cannot bind to HTML attributes with a null or empty name.</summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Razor.Language.Resources.TagHelper_InvalidBoundAttributePrefix">
            <summary>Invalid tag helper bound property '{1}' on tag helper '{0}'. Tag helpers cannot bind to HTML attributes with prefix '{2}' because the prefix contains a '{3}' character.</summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Resources.FormatTagHelper_InvalidBoundAttributePrefix(System.Object,System.Object,System.Object,System.Object)">
            <summary>Invalid tag helper bound property '{1}' on tag helper '{0}'. Tag helpers cannot bind to HTML attributes with prefix '{2}' because the prefix contains a '{3}' character.</summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Razor.Language.Resources.TagHelper_InvalidBoundAttributePrefixStartsWith">
            <summary>Invalid tag helper bound property '{1}' on tag helper '{0}'. Tag helpers cannot bind to HTML attributes with prefix '{2}' because the prefix starts with '{3}'.</summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Resources.FormatTagHelper_InvalidBoundAttributePrefixStartsWith(System.Object,System.Object,System.Object,System.Object)">
            <summary>Invalid tag helper bound property '{1}' on tag helper '{0}'. Tag helpers cannot bind to HTML attributes with prefix '{2}' because the prefix starts with '{3}'.</summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Razor.Language.Resources.TagHelper_InvalidRestrictedChild">
            <summary>Invalid restricted child '{1}' for tag helper '{0}'. Tag helpers cannot restrict child elements that contain a '{2}' character.</summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Resources.FormatTagHelper_InvalidRestrictedChild(System.Object,System.Object,System.Object)">
            <summary>Invalid restricted child '{1}' for tag helper '{0}'. Tag helpers cannot restrict child elements that contain a '{2}' character.</summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Razor.Language.Resources.TagHelper_InvalidRestrictedChildNullOrWhitespace">
            <summary>Invalid restricted child for tag helper '{0}'. Name cannot be null or whitespace.</summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Resources.FormatTagHelper_InvalidRestrictedChildNullOrWhitespace(System.Object)">
            <summary>Invalid restricted child for tag helper '{0}'. Name cannot be null or whitespace.</summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Razor.Language.Resources.TagHelper_InvalidTargetedAttributeName">
            <summary>Tag helpers cannot target attribute name '{0}' because it contains a '{1}' character.</summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Resources.FormatTagHelper_InvalidTargetedAttributeName(System.Object,System.Object)">
            <summary>Tag helpers cannot target attribute name '{0}' because it contains a '{1}' character.</summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Razor.Language.Resources.TagHelper_InvalidTargetedAttributeNameNullOrWhitespace">
            <summary>Targeted attribute name cannot be null or whitespace.</summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Razor.Language.Resources.TagHelper_InvalidTargetedParentTagName">
            <summary>Tag helpers cannot target parent tag name '{0}' because it contains a '{1}' character.</summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Resources.FormatTagHelper_InvalidTargetedParentTagName(System.Object,System.Object)">
            <summary>Tag helpers cannot target parent tag name '{0}' because it contains a '{1}' character.</summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Razor.Language.Resources.TagHelper_InvalidTargetedParentTagNameNullOrWhitespace">
            <summary>Targeted parent tag name cannot be null or whitespace.</summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Razor.Language.Resources.TagHelper_InvalidTargetedTagName">
            <summary>Tag helpers cannot target tag name '{0}' because it contains a '{1}' character.</summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Resources.FormatTagHelper_InvalidTargetedTagName(System.Object,System.Object)">
            <summary>Tag helpers cannot target tag name '{0}' because it contains a '{1}' character.</summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Razor.Language.Resources.TagHelper_InvalidTargetedTagNameNullOrWhitespace">
            <summary>Targeted tag name cannot be null or whitespace.</summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Razor.Language.Resources.TagHelperAssemblyNameCannotBeEmptyOrNull">
            <summary>Tag helper directive assembly name cannot be null or empty.</summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Razor.Language.Resources.TagHelperBlockRewriter_IndexerAttributeNameMustIncludeKey">
            <summary>The tag helper attribute '{0}' in element '{1}' is missing a key. The syntax is '&lt;{1} {0}{{ key }}="value"&gt;'.</summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Resources.FormatTagHelperBlockRewriter_IndexerAttributeNameMustIncludeKey(System.Object,System.Object)">
            <summary>The tag helper attribute '{0}' in element '{1}' is missing a key. The syntax is '&lt;{1} {0}{{ key }}="value"&gt;'.</summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Razor.Language.Resources.TagHelperBlockRewriter_TagHelperAttributeListMustBeWellFormed">
            <summary>TagHelper attributes must be well-formed.</summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Razor.Language.Resources.TagHelperParseTreeRewriter_CannotHaveNonTagContent">
            <summary>The parent &lt;{0}&gt; tag helper does not allow non-tag content. Only child tag helper(s) targeting tag name(s) '{1}' are allowed.</summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Resources.FormatTagHelperParseTreeRewriter_CannotHaveNonTagContent(System.Object,System.Object)">
            <summary>The parent &lt;{0}&gt; tag helper does not allow non-tag content. Only child tag helper(s) targeting tag name(s) '{1}' are allowed.</summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Razor.Language.Resources.TagHelperParseTreeRewriter_EndTagTagHelperMustNotHaveAnEndTag">
            <summary>Found an end tag (&lt;/{0}&gt;) for tag helper '{1}' with tag structure that disallows an end tag ('{2}').</summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Resources.FormatTagHelperParseTreeRewriter_EndTagTagHelperMustNotHaveAnEndTag(System.Object,System.Object,System.Object)">
            <summary>Found an end tag (&lt;/{0}&gt;) for tag helper '{1}' with tag structure that disallows an end tag ('{2}').</summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Razor.Language.Resources.TagHelperParseTreeRewriter_InconsistentTagStructure">
            <summary>Tag helpers '{0}' and '{1}' targeting element '{2}' must not expect different {3} values.</summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Resources.FormatTagHelperParseTreeRewriter_InconsistentTagStructure(System.Object,System.Object,System.Object,System.Object)">
            <summary>Tag helpers '{0}' and '{1}' targeting element '{2}' must not expect different {3} values.</summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Razor.Language.Resources.TagHelperParseTreeRewriter_InvalidNestedTag">
            <summary>The &lt;{0}&gt; tag is not allowed by parent &lt;{1}&gt; tag helper. Only child tags with name(s) '{2}' are allowed.</summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Resources.FormatTagHelperParseTreeRewriter_InvalidNestedTag(System.Object,System.Object,System.Object)">
            <summary>The &lt;{0}&gt; tag is not allowed by parent &lt;{1}&gt; tag helper. Only child tags with name(s) '{2}' are allowed.</summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Razor.Language.Resources.TagHelperPrefixDirective_Description">
            <summary>Specify a prefix that is required in an element name for it to be included in Tag Helper processing.</summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Razor.Language.Resources.TagHelperPrefixDirective_PrefixToken_Description">
            <summary>The tag prefix to apply to tag helpers.</summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Razor.Language.Resources.TagHelperPrefixDirective_PrefixToken_Name">
            <summary>Prefix</summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Razor.Language.Resources.TagHelpers_AttributesMustHaveAName">
            <summary>Tag Helper '{0}'s attributes must have names.</summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Resources.FormatTagHelpers_AttributesMustHaveAName(System.Object)">
            <summary>Tag Helper '{0}'s attributes must have names.</summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Razor.Language.Resources.TagHelpers_CannotHaveCSharpInTagDeclaration">
            <summary>The tag helper '{0}' must not have C# in the element's attribute declaration area.</summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Resources.FormatTagHelpers_CannotHaveCSharpInTagDeclaration(System.Object)">
            <summary>The tag helper '{0}' must not have C# in the element's attribute declaration area.</summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Razor.Language.Resources.TagHelpers_CodeBlocks_NotSupported_InAttributes">
            <summary>Code blocks (e.g. @{{var variable = 23;}}) must not appear in non-string tag helper attribute values.
             Already in an expression (code) context. If necessary an explicit expression (e.g. @(@readonly)) may be used.</summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Razor.Language.Resources.TagHelpers_InlineMarkupBlocks_NotSupported_InAttributes">
            <summary>Inline markup blocks (e.g. @&lt;p&gt;content&lt;/p&gt;) must not appear in non-string tag helper attribute values.
             Expected a '{0}' attribute value, not a string.</summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Resources.FormatTagHelpers_InlineMarkupBlocks_NotSupported_InAttributes(System.Object)">
            <summary>Inline markup blocks (e.g. @&lt;p&gt;content&lt;/p&gt;) must not appear in non-string tag helper attribute values.
             Expected a '{0}' attribute value, not a string.</summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Razor.Language.Resources.TagHelpersParseTreeRewriter_FoundMalformedTagHelper">
            <summary>Found a malformed '{0}' tag helper. Tag helpers must have a start and end tag or be self closing.</summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Resources.FormatTagHelpersParseTreeRewriter_FoundMalformedTagHelper(System.Object)">
            <summary>Found a malformed '{0}' tag helper. Tag helpers must have a start and end tag or be self closing.</summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Razor.Language.Resources.TagHelpersParseTreeRewriter_MissingCloseAngle">
            <summary>Missing close angle for tag helper '{0}'.</summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Resources.FormatTagHelpersParseTreeRewriter_MissingCloseAngle(System.Object)">
            <summary>Missing close angle for tag helper '{0}'.</summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Razor.Language.Resources.UnexpectedDirectiveKind">
            <summary>Unreachable code. This can happen when a new {0} is introduced.</summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Resources.FormatUnexpectedDirectiveKind(System.Object)">
            <summary>Unreachable code. This can happen when a new {0} is introduced.</summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Razor.Language.Resources.UnexpectedDirectiveLiteral">
            <summary>Unexpected literal following the '{0}' directive. Expected '{1}'.</summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Resources.FormatUnexpectedDirectiveLiteral(System.Object,System.Object)">
            <summary>Unexpected literal following the '{0}' directive. Expected '{1}'.</summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Razor.Language.Resources.UnexpectedEOFAfterDirective">
            <summary>Unexpected end of file following the '{0}' directive. Expected '{1}'.</summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Resources.FormatUnexpectedEOFAfterDirective(System.Object,System.Object)">
            <summary>Unexpected end of file following the '{0}' directive. Expected '{1}'.</summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Razor.Language.Resources.UnsupportedChecksumAlgorithm">
            <summary>The hash algorithm '{0}' is not supported for checksum generation. Supported algorithms are: '{1}'. Set '{2}' to '{3}' to suppress automatic checksum generation.</summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Resources.FormatUnsupportedChecksumAlgorithm(System.Object,System.Object,System.Object,System.Object)">
            <summary>The hash algorithm '{0}' is not supported for checksum generation. Supported algorithms are: '{1}'. Set '{2}' to '{3}' to suppress automatic checksum generation.</summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Razor.Language.Resources.PropertyMustNotBeNull">
            <summary>The '{0}.{1}' property must not be null.</summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Resources.FormatPropertyMustNotBeNull(System.Object,System.Object)">
            <summary>The '{0}.{1}' property must not be null.</summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Razor.Language.Resources.RazorProjectEngineMissingFeatureDependency">
            <summary>The '{0}' is missing feature '{1}'.</summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Resources.FormatRazorProjectEngineMissingFeatureDependency(System.Object,System.Object)">
            <summary>The '{0}' is missing feature '{1}'.</summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Razor.Language.Resources.RazorLanguageVersion_InvalidVersion">
            <summary>The Razor language version '{0}' is unrecognized or not supported by this version of Razor.</summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Resources.FormatRazorLanguageVersion_InvalidVersion(System.Object)">
            <summary>The Razor language version '{0}' is unrecognized or not supported by this version of Razor.</summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Razor.Language.Resources.VirtualFileSystem_FileDoesNotBelongToDirectory">
            <summary>File path '{0}' does not belong to the directory '{1}'.</summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Resources.FormatVirtualFileSystem_FileDoesNotBelongToDirectory(System.Object,System.Object)">
            <summary>File path '{0}' does not belong to the directory '{1}'.</summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Razor.Language.Resources.VirtualFileSystem_InvalidRelativePath">
            <summary>The file path '{0}' is invalid. File path is the root relative path of the file starting with '/' and should not contain any '\' characters.</summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Resources.FormatVirtualFileSystem_InvalidRelativePath(System.Object)">
            <summary>The file path '{0}' is invalid. File path is the root relative path of the file starting with '/' and should not contain any '\' characters.</summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Razor.Language.Resources.Rewriter_InsufficientStack">
            <summary>Not enough stack space to continue parsing this document. Razor doesn't support deeply nested elements.</summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Razor.Language.Resources.NamespaceDirective_Description">
            <summary>Specify the base namespace for the document.</summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Razor.Language.Resources.NamespaceDirective_NamespaceToken_Description">
            <summary>The namespace for the document.</summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Razor.Language.Resources.NamespaceDirective_NamespaceToken_Name">
            <summary>Namespace</summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Razor.Language.Resources.TagHelper_InvalidBoundAttributeParameterName">
            <summary>Invalid tag helper bound attribute parameter '{1}' on bound attribute '{0}'. Tag helpers cannot bind to HTML attribute parameters with name '{1}' because the name contains a '{3}' character.</summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Resources.FormatTagHelper_InvalidBoundAttributeParameterName(System.Object,System.Object,System.Object)">
            <summary>Invalid tag helper bound attribute parameter '{1}' on bound attribute '{0}'. Tag helpers cannot bind to HTML attribute parameters with name '{1}' because the name contains a '{3}' character.</summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Razor.Language.Resources.TagHelper_InvalidBoundAttributeParameterNullOrWhitespace">
            <summary>Invalid tag helper bound attribute parameter '{0}'. Tag helpers cannot bind to HTML attribute parameters with a null or empty name.</summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Resources.FormatTagHelper_InvalidBoundAttributeParameterNullOrWhitespace(System.Object)">
            <summary>Invalid tag helper bound attribute parameter '{0}'. Tag helpers cannot bind to HTML attribute parameters with a null or empty name.</summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Razor.Language.Resources.DirectiveExpectsCSharpAttribute">
            <summary>The '{0}' directive expects a C# attribute.</summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Resources.FormatDirectiveExpectsCSharpAttribute(System.Object)">
            <summary>The '{0}' directive expects a C# attribute.</summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Razor.Language.Resources.TagHelper_InvalidBoundDirectiveAttributeName">
            <summary>Invalid tag helper bound directive attribute '{1}' on tag helper '{0}'. The directive attribute '{2}' should start with a '@' character.</summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Resources.FormatTagHelper_InvalidBoundDirectiveAttributeName(System.Object,System.Object,System.Object)">
            <summary>Invalid tag helper bound directive attribute '{1}' on tag helper '{0}'. The directive attribute '{2}' should start with a '@' character.</summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Razor.Language.Resources.TagHelper_InvalidBoundDirectiveAttributePrefix">
            <summary>Invalid tag helper bound directive attribute '{1}' on tag helper '{0}'. Tag helpers cannot bind to directive attributes with prefix '{2}' because the prefix doesn't start with a '@' character.</summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Resources.FormatTagHelper_InvalidBoundDirectiveAttributePrefix(System.Object,System.Object,System.Object)">
            <summary>Invalid tag helper bound directive attribute '{1}' on tag helper '{0}'. Tag helpers cannot bind to directive attributes with prefix '{2}' because the prefix doesn't start with a '@' character.</summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Razor.Language.Resources.TagHelper_InvalidRequiredDirectiveAttributeName">
            <summary>Invalid tag helper required directive attribute '{0}'. The directive attribute '{1}' should start with a '@' character.</summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Razor.Language.Resources.FormatTagHelper_InvalidRequiredDirectiveAttributeName(System.Object,System.Object)">
            <summary>Invalid tag helper required directive attribute '{0}'. The directive attribute '{1}' should start with a '@' character.</summary>
        </member>
    </members>
</doc>
