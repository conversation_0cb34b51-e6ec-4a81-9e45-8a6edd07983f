{"runtimeTarget": {"name": ".NETCoreApp,Version=v3.0", "signature": ""}, "compilationOptions": {}, "targets": {".NETCoreApp,Version=v3.0": {"rzc/3.1.32-servicing.22566.3": {"dependencies": {"Microsoft.CodeAnalysis.Razor": "3.1.32", "Microsoft.Extensions.CommandLineUtils.Sources": "3.1.32-servicing.22566.2", "Microsoft.NETFramework.ReferenceAssemblies": "1.0.0-preview.2", "Microsoft.Net.Compilers.Toolset": "3.4.1-beta4-20127-10", "Microsoft.SourceLink.AzureRepos.Git": "1.1.0-beta-20206-02", "Microsoft.SourceLink.GitHub": "1.1.0-beta-20206-02", "Newtonsoft.Json": "13.0.1"}, "runtime": {"rzc.dll": {}}}, "Microsoft.Build.Tasks.Git/1.1.0-beta-20206-02": {}, "Microsoft.CodeAnalysis.Analyzers/2.9.3": {}, "Microsoft.CodeAnalysis.Common/3.3.0": {"dependencies": {"Microsoft.CodeAnalysis.Analyzers": "2.9.3", "System.Collections.Immutable": "1.5.0", "System.Memory": "4.5.3", "System.Reflection.Metadata": "1.6.0", "System.Runtime.CompilerServices.Unsafe": "4.5.2", "System.Text.Encoding.CodePages": "4.5.1", "System.Threading.Tasks.Extensions": "4.5.3"}, "runtime": {"lib/netstandard2.0/Microsoft.CodeAnalysis.dll": {"assemblyVersion": "3.3.0.0", "fileVersion": "3.300.19.41501"}}, "resources": {"lib/netstandard2.0/cs/Microsoft.CodeAnalysis.resources.dll": {"locale": "cs"}, "lib/netstandard2.0/de/Microsoft.CodeAnalysis.resources.dll": {"locale": "de"}, "lib/netstandard2.0/es/Microsoft.CodeAnalysis.resources.dll": {"locale": "es"}, "lib/netstandard2.0/fr/Microsoft.CodeAnalysis.resources.dll": {"locale": "fr"}, "lib/netstandard2.0/it/Microsoft.CodeAnalysis.resources.dll": {"locale": "it"}, "lib/netstandard2.0/ja/Microsoft.CodeAnalysis.resources.dll": {"locale": "ja"}, "lib/netstandard2.0/ko/Microsoft.CodeAnalysis.resources.dll": {"locale": "ko"}, "lib/netstandard2.0/pl/Microsoft.CodeAnalysis.resources.dll": {"locale": "pl"}, "lib/netstandard2.0/pt-BR/Microsoft.CodeAnalysis.resources.dll": {"locale": "pt-BR"}, "lib/netstandard2.0/ru/Microsoft.CodeAnalysis.resources.dll": {"locale": "ru"}, "lib/netstandard2.0/tr/Microsoft.CodeAnalysis.resources.dll": {"locale": "tr"}, "lib/netstandard2.0/zh-Hans/Microsoft.CodeAnalysis.resources.dll": {"locale": "zh-Hans"}, "lib/netstandard2.0/zh-Hant/Microsoft.CodeAnalysis.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "Microsoft.CodeAnalysis.CSharp/3.3.0": {"dependencies": {"Microsoft.CodeAnalysis.Common": "3.3.0"}, "runtime": {"lib/netstandard2.0/Microsoft.CodeAnalysis.CSharp.dll": {"assemblyVersion": "3.3.0.0", "fileVersion": "3.300.19.41501"}}, "resources": {"lib/netstandard2.0/cs/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "cs"}, "lib/netstandard2.0/de/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "de"}, "lib/netstandard2.0/es/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "es"}, "lib/netstandard2.0/fr/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "fr"}, "lib/netstandard2.0/it/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "it"}, "lib/netstandard2.0/ja/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "ja"}, "lib/netstandard2.0/ko/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "ko"}, "lib/netstandard2.0/pl/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "pl"}, "lib/netstandard2.0/pt-BR/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "pt-BR"}, "lib/netstandard2.0/ru/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "ru"}, "lib/netstandard2.0/tr/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "tr"}, "lib/netstandard2.0/zh-Hans/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "zh-Hans"}, "lib/netstandard2.0/zh-Hant/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "Microsoft.Extensions.CommandLineUtils.Sources/3.1.32-servicing.22566.2": {}, "Microsoft.Net.Compilers.Toolset/3.4.1-beta4-20127-10": {}, "Microsoft.NETCore.Platforms/2.1.2": {}, "Microsoft.NETFramework.ReferenceAssemblies/1.0.0-preview.2": {}, "Microsoft.SourceLink.AzureRepos.Git/1.1.0-beta-20206-02": {"dependencies": {"Microsoft.Build.Tasks.Git": "1.1.0-beta-20206-02", "Microsoft.SourceLink.Common": "1.1.0-beta-20206-02"}}, "Microsoft.SourceLink.Common/1.1.0-beta-20206-02": {}, "Microsoft.SourceLink.GitHub/1.1.0-beta-20206-02": {"dependencies": {"Microsoft.Build.Tasks.Git": "1.1.0-beta-20206-02", "Microsoft.SourceLink.Common": "1.1.0-beta-20206-02"}}, "Newtonsoft.Json/13.0.1": {"runtime": {"lib/netstandard2.0/Newtonsoft.Json.dll": {"assemblyVersion": "13.0.0.0", "fileVersion": "13.0.1.25517"}}}, "System.Collections.Immutable/1.5.0": {}, "System.Memory/4.5.3": {}, "System.Reflection.Metadata/1.6.0": {}, "System.Runtime.CompilerServices.Unsafe/4.5.2": {}, "System.Text.Encoding.CodePages/4.5.1": {"dependencies": {"Microsoft.NETCore.Platforms": "2.1.2", "System.Runtime.CompilerServices.Unsafe": "4.5.2"}}, "System.Threading.Tasks.Extensions/4.5.3": {}, "Microsoft.AspNetCore.Razor.Language/3.1.32": {"runtime": {"Microsoft.AspNetCore.Razor.Language.dll": {}}}, "Microsoft.CodeAnalysis.Razor/3.1.32": {"dependencies": {"Microsoft.AspNetCore.Razor.Language": "3.1.32", "Microsoft.CodeAnalysis.CSharp": "3.3.0", "Microsoft.CodeAnalysis.Common": "3.3.0"}, "runtime": {"Microsoft.CodeAnalysis.Razor.dll": {}}}}}, "libraries": {"rzc/3.1.32-servicing.22566.3": {"type": "project", "serviceable": false, "sha512": ""}, "Microsoft.Build.Tasks.Git/1.1.0-beta-20206-02": {"type": "package", "serviceable": true, "sha512": "sha512-hZ9leS9Yd9MHpqvviMftSJFDcLYu2h1DrapW1TDm1s1fgOy71c8HvArNMd3fseVkXmp3VTfGnkgcw0FR+TI6xw==", "path": "microsoft.build.tasks.git/1.1.0-beta-20206-02", "hashPath": "microsoft.build.tasks.git.1.1.0-beta-20206-02.nupkg.sha512"}, "Microsoft.CodeAnalysis.Analyzers/2.9.3": {"type": "package", "serviceable": true, "sha512": "sha512-cDKEoHdg3icjLrn9EJ4KIkJG+ahU1YouhB0J8EARhj6fTqA/3oUneak0hWbToLOBLOTePu/Oc1QEA4mwmMEYVA==", "path": "microsoft.codeanalysis.analyzers/2.9.3", "hashPath": "microsoft.codeanalysis.analyzers.2.9.3.nupkg.sha512"}, "Microsoft.CodeAnalysis.Common/3.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-hK0BGWD2ZlTCZ/KD84LQrD95lwJuRg8++uVGeiLq+klmcsNf3g7j/QvXowZX5Wi+oL9egCFxI7d3A4GtpQAl0A==", "path": "microsoft.codeanalysis.common/3.3.0", "hashPath": "microsoft.codeanalysis.common.3.3.0.nupkg.sha512"}, "Microsoft.CodeAnalysis.CSharp/3.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-C0acBZTXLgi72UpV+E2zT+joclyPxNQ+zuHnvE0bS6hJA4i7kBBdW7qaLEwVP6a9MrrtL869StzvXqMc/0PvTg==", "path": "microsoft.codeanalysis.csharp/3.3.0", "hashPath": "microsoft.codeanalysis.csharp.3.3.0.nupkg.sha512"}, "Microsoft.Extensions.CommandLineUtils.Sources/3.1.32-servicing.22566.2": {"type": "package", "serviceable": true, "sha512": "sha512-RT0N/gmPlIq6yC0LcGhhbKA5cIarvcYSLgminxcpmmSHno20dyjF7iopYAOGCPKW16nTvzpliGquQdt1Toe+BQ==", "path": "microsoft.extensions.commandlineutils.sources/3.1.32-servicing.22566.2", "hashPath": "microsoft.extensions.commandlineutils.sources.3.1.32-servicing.22566.2.nupkg.sha512"}, "Microsoft.Net.Compilers.Toolset/3.4.1-beta4-20127-10": {"type": "package", "serviceable": true, "sha512": "sha512-qeWtJRbOMcHf7KGmhODZuh4B3vD0Wh3mlnQeWyqlQ7qvvP0OGhFHPQsrUb33ibJ7Xz4dYQtXyAwf8iqp3617uA==", "path": "microsoft.net.compilers.toolset/3.4.1-beta4-20127-10", "hashPath": "microsoft.net.compilers.toolset.3.4.1-beta4-20127-10.nupkg.sha512"}, "Microsoft.NETCore.Platforms/2.1.2": {"type": "package", "serviceable": true, "sha512": "sha512-mOJy3M0UN+LUG21dLGMxaWZEP6xYpQEpLuvuEQBaownaX4YuhH6NmNUlN9si+vNkAS6dwJ//N1O4DmLf2CikVg==", "path": "microsoft.netcore.platforms/2.1.2", "hashPath": "microsoft.netcore.platforms.2.1.2.nupkg.sha512"}, "Microsoft.NETFramework.ReferenceAssemblies/1.0.0-preview.2": {"type": "package", "serviceable": true, "sha512": "sha512-m+pJPEO7HyXvrOna5Sr3s77ewXonjYWJTNL6drh8xACnMNxnlqUDKx9HfGeSE9wmfY0lQwppaeZpFTPGaH7kZg==", "path": "microsoft.netframework.referenceassemblies/1.0.0-preview.2", "hashPath": "microsoft.netframework.referenceassemblies.1.0.0-preview.2.nupkg.sha512"}, "Microsoft.SourceLink.AzureRepos.Git/1.1.0-beta-20206-02": {"type": "package", "serviceable": true, "sha512": "sha512-vVYhSds9TfraTQkGHHMDMVWnr3kCkTZ7vmqUmrXQBDJFXiWTuMoP5RRa9s1M/KmgB4szi5TOb7sOaHWKDT9qDA==", "path": "microsoft.sourcelink.azurerepos.git/1.1.0-beta-20206-02", "hashPath": "microsoft.sourcelink.azurerepos.git.1.1.0-beta-20206-02.nupkg.sha512"}, "Microsoft.SourceLink.Common/1.1.0-beta-20206-02": {"type": "package", "serviceable": true, "sha512": "sha512-aek0RTQ+4Bf11WvqaXajwYoaBWkX2edBjAr5XJOvhAsHX6/9vPOb7IpHAiE/NyCse7IcpGWslJZHNkv4UBEFqw==", "path": "microsoft.sourcelink.common/1.1.0-beta-20206-02", "hashPath": "microsoft.sourcelink.common.1.1.0-beta-20206-02.nupkg.sha512"}, "Microsoft.SourceLink.GitHub/1.1.0-beta-20206-02": {"type": "package", "serviceable": true, "sha512": "sha512-7A7P0EwL+lypaI/CEvG4IcpAlQeAt04uPPw1SO6Q9Jwz2nE9309pQXJ4TfP/RLL8IOObACidN66+gVR+bJDZHw==", "path": "microsoft.sourcelink.github/1.1.0-beta-20206-02", "hashPath": "microsoft.sourcelink.github.1.1.0-beta-20206-02.nupkg.sha512"}, "Newtonsoft.Json/13.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-ppPFpBcvxdsfUonNcvITKqLl3bqxWbDCZIzDWHzjpdAHRFfZe0Dw9HmA0+za13IdyrgJwpkDTDA9fHaxOrt20A==", "path": "newtonsoft.json/13.0.1", "hashPath": "newtonsoft.json.13.0.1.nupkg.sha512"}, "System.Collections.Immutable/1.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-EXKiDFsChZW0RjrZ4FYHu9aW6+P4MCgEDCklsVseRfhoO0F+dXeMSsMRAlVXIo06kGJ/zv+2w1a2uc2+kxxSaQ==", "path": "system.collections.immutable/1.5.0", "hashPath": "system.collections.immutable.1.5.0.nupkg.sha512"}, "System.Memory/4.5.3": {"type": "package", "serviceable": true, "sha512": "sha512-3oDzvc/zzetpTKWMShs1AADwZjQ/36HnsufHRPcOjyRAAMLDlu2iD33MBI2opxnezcVUtXyqDXXjoFMOU9c7SA==", "path": "system.memory/4.5.3", "hashPath": "system.memory.4.5.3.nupkg.sha512"}, "System.Reflection.Metadata/1.6.0": {"type": "package", "serviceable": true, "sha512": "sha512-COC1aiAJjCoA5GBF+QKL2uLqEBew4JsCkQmoHKbN3TlOZKa2fKLz5CpiRQKDz0RsAOEGsVKqOD5bomsXq/4STQ==", "path": "system.reflection.metadata/1.6.0", "hashPath": "system.reflection.metadata.1.6.0.nupkg.sha512"}, "System.Runtime.CompilerServices.Unsafe/4.5.2": {"type": "package", "serviceable": true, "sha512": "sha512-wprSFgext8cwqymChhrBLu62LMg/1u92bU+VOwyfBimSPVFXtsNqEWC92Pf9ofzJFlk4IHmJA75EDJn1b2goAQ==", "path": "system.runtime.compilerservices.unsafe/4.5.2", "hashPath": "system.runtime.compilerservices.unsafe.4.5.2.nupkg.sha512"}, "System.Text.Encoding.CodePages/4.5.1": {"type": "package", "serviceable": true, "sha512": "sha512-4J2JQXbftjPMppIHJ7IC+VXQ9XfEagN92vZZNoG12i+zReYlim5dMoXFC1Zzg7tsnKDM7JPo5bYfFK4Jheq44w==", "path": "system.text.encoding.codepages/4.5.1", "hashPath": "system.text.encoding.codepages.4.5.1.nupkg.sha512"}, "System.Threading.Tasks.Extensions/4.5.3": {"type": "package", "serviceable": true, "sha512": "sha512-+MvhNtcvIbqmhANyKu91jQnvIRVSTiaOiFNfKWwXGHG48YAb4I/TyH8spsySiPYla7gKal5ZnF3teJqZAximyQ==", "path": "system.threading.tasks.extensions/4.5.3", "hashPath": "system.threading.tasks.extensions.4.5.3.nupkg.sha512"}, "Microsoft.AspNetCore.Razor.Language/3.1.32": {"type": "project", "serviceable": false, "sha512": ""}, "Microsoft.CodeAnalysis.Razor/3.1.32": {"type": "project", "serviceable": false, "sha512": ""}}}