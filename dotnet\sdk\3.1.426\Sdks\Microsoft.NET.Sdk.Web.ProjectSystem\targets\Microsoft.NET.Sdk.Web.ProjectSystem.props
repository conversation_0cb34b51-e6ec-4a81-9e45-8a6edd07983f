<!--
***********************************************************************************************
Microsoft.NET.Sdk.Web.ProjectSystem.props

WARNING:  DO NOT MODIFY this file unless you are knowledgeable about MSBuild and have
          created a backup copy.  Incorrect changes to this file will make it
          impossible to load or build your projects from the command-line or the IDE.

Copyright (c) .NET Foundation. All rights reserved.
***********************************************************************************************
-->

<Project xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>

    <OutputType>Exe</OutputType>
    <DebugSymbols Condition="'$(DebugSymbols)' == ''">true</DebugSymbols>
    <DebugType Condition="'$(DebugType)' == ''">pdbonly</DebugType>
    <GenerateDependencyFile Condition="'$(GenerateDependencyFile)' == ''">true</GenerateDependencyFile>
    <ServerGarbageCollection>true</ServerGarbageCollection>
    <IsPackable Condition="'$(IsPackable)' == ''">false</IsPackable>
    <WarnOnPackingNonPackableProject Condition="'$(WarnOnPackingNonPackableProject)' == '' and '$(IsPackable)' == 'false'">true</WarnOnPackingNonPackableProject>
  </PropertyGroup>

  <Import Project="$(MSBuildExtensionsPath)\Microsoft\VisualStudio\v$(VisualStudioVersion)\TypeScript\Microsoft.TypeScript.Default.props"
      Condition="Exists('$(MSBuildExtensionsPath)\Microsoft\VisualStudio\v$(VisualStudioVersion)\TypeScript\Microsoft.TypeScript.Default.props')"/>

</Project>

