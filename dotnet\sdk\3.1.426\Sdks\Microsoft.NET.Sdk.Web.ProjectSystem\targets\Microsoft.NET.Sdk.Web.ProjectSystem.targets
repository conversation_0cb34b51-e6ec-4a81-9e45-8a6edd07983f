﻿<!--
***********************************************************************************************
Microsoft.NET.Sdk.Web.ProjectSystem.targets

WARNING:  DO NOT MODIFY this file unless you are knowledgeable about MSBuild and have
          created a backup copy.  Incorrect changes to this file will make it
          impossible to load or build your projects from the command-line or the IDE.

Copyright (c) .NET Foundation. All rights reserved.
***********************************************************************************************
-->
<Project xmlns="http://schemas.microsoft.com/developer/msbuild/2003">

  <Import Project="$(MSBuildExtensionsPath)\Microsoft\VisualStudio\v$(VisualStudioVersion)\TypeScript\Microsoft.TypeScript.targets"
          Condition="Exists('$(MSBuildExtensionsPath)\Microsoft\VisualStudio\v$(VisualStudioVersion)\TypeScript\Microsoft.TypeScript.targets') AND '$(EnableTypeScriptNuGetTarget)' != 'true'"/>

  <Import Project="$(MSBuildExtensionsPath)\Microsoft\VisualStudio\v$(VisualStudioVersion)\TypeScript\Microsoft.TypeScript.DotNetCore.targets"
          Condition="Exists('$(MSBuildExtensionsPath)\Microsoft\VisualStudio\v$(VisualStudioVersion)\TypeScript\Microsoft.TypeScript.DotNetCore.targets') AND '$(EnableTypeScriptNuGetTarget)' != 'true'"/>

  <PropertyGroup>
    <RunWorkingDirectory Condition=" '$(RunWorkingDirectory)' == '' and '$(EnableDefaultRunWorkingDirectory)' != 'false' ">$(MSBuildProjectDirectory)</RunWorkingDirectory>
    <MSBuildWebTargetsPath>$(MSBuildExtensionsPath)\Microsoft\VisualStudio\Managed.Web\</MSBuildWebTargetsPath>
    <AspNetCoreHostingModel Condition="'$(AspNetCoreHostingModel)' == '' And '$(TargetFrameworkIdentifier)' == '.NETCoreApp' And '$(_TargetFrameworkVersionWithoutV)' != '' And '$(_TargetFrameworkVersionWithoutV)' &gt;= '3.0' ">inprocess</AspNetCoreHostingModel>
    <AspNetCoreModuleName Condition="'$(AspNetCoreModuleName)' == '' And '$(TargetFrameworkIdentifier)' == '.NETCoreApp' And '$(_TargetFrameworkVersionWithoutV)' != '' And '$(_TargetFrameworkVersionWithoutV)' &gt;= '3.0' ">AspNetCoreModuleV2</AspNetCoreModuleName>
  </PropertyGroup>

  <ItemGroup>
    <ProjectCapability Include="DotNetCoreWeb" />
    <ProjectCapability Include="AspNetCore" />
    <ProjectCapability Include="Web" />
    <ProjectCapability Include="AppServicePublish" />
  </ItemGroup>

  <ItemGroup Condition=" '$(TargetFrameworkIdentifier)' == '.NETCoreApp' And '$(_TargetFrameworkVersionWithoutV)' != '' And '$(_TargetFrameworkVersionWithoutV)' &gt;= '3.0' ">
    <ProjectCapability Include="AspNetCoreInProcessHosting" />
  </ItemGroup>

  <!--
    Newer versions of Visual Studio ship the designtime related properties in a targets file and all future design time only elements should be added there. If that file does not
    exist, it falls back to the default set of values defined here.
  -->
    <Import Project="$(MSBuildWebTargetsPath)Microsoft.Web.Designtime.targets" Condition="Exists('$(MSBuildWebTargetsPath)Microsoft.Web.Designtime.targets')" />

    <Choose>
      <When Condition="!Exists('$(MSBuildWebTargetsPath)Microsoft.Web.Designtime.targets')" >
        <PropertyGroup>
          <ActualLangName Condition="Exists('$(MSBuildWebTargetsPath)$(LangName)')">$(LangName)</ActualLangName>
          <ActualLangName Condition="'$(ActualLangName)' == ''">en-us</ActualLangName>
        </PropertyGroup>

        <!-- Web project capabilities that enables web features in VS for .NET Core projects -->
        <ItemGroup>
          <ProjectCapability Include="SupportHierarchyContextSvc" />
          <ProjectCapability Include="DynamicDependentFile" />
          <ProjectCapability Include="DynamicFileNesting" />

          <!--
            Enables UI for managing secret values when Microsoft.Extensions.Configuration.UserSecrets 1.x is referenced.
            Newer versions of this package include a MSBuild file to set this ProjectCapability, but older versions did not include this.
            See https://github.com/aspnet/Configuration/blob/9135af4b4e95c080ca4a9f0e91ba5a0b8a561c96/src/Microsoft.Extensions.Configuration.UserSecrets/build/netstandard1.0/Microsoft.Extensions.Configuration.UserSecrets.targets#L10
          -->
          <ProjectCapability Include="LocalUserSecrets" Condition=" '$(GenerateUserSecretsAttribute)' == 'true' " />

        </ItemGroup>

        <!-- Web specific properties -->
        <ItemGroup>
          <PropertyPageSchema Include="$(MSBuildWebTargetsPath)$(ActualLangName)\GeneralBrowseObject.xaml"
                              Condition="Exists('$(MSBuildWebTargetsPath)$(ActualLangName)\GeneralBrowseObject.xaml')">
            <Context>BrowseObject</Context>
          </PropertyPageSchema>
        </ItemGroup>
    </When>
  </Choose>

</Project>
