<!--
***********************************************************************************************
Sdk.targets

WARNING:  DO NOT MODIFY this file unless you are knowledgeable about MSBuild and have
          created a backup copy.  Incorrect changes to this file will make it
          impossible to load or build your projects from the command-line or the IDE.

Copyright (c) .NET Foundation. All rights reserved.
***********************************************************************************************
-->
<Project ToolsVersion="14.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">

  <!-- We can't add it here because the OutputPath and other msbuild properties are not evaluated.-->
  <!--<Import Sdk="Microsoft.NET.Sdk.Publish" Project="ImportPublishProfile.targets" />-->

  <Import Sdk="Microsoft.NET.Sdk" Project="Sdk.targets" />

  <PropertyGroup>
    <!--
      Configure the Razor SDK to add support for the MVC configuration unless the project configured it or the user opted out of the implicit reference to
      Microsoft.AspNetCore.App. This needs to happen after the .NET SDK has evaluated TFMs.
     -->
    <AddRazorSupportForMvc Condition="'$(DisableImplicitFrameworkReferences)' != 'true' And '$(AddRazorSupportForMvc)' == '' And '$(TargetFrameworkIdentifier)' == '.NETCoreApp' And '$(_TargetFrameworkVersionWithoutV)' >= '3.0'">true</AddRazorSupportForMvc>
  </PropertyGroup>

  <Import Sdk="Microsoft.NET.Sdk.Razor" Project="Sdk.targets" />

  <Import Sdk="Microsoft.NET.Sdk.Web.ProjectSystem" Project="Sdk.targets" />

  <Import Sdk="Microsoft.NET.Sdk.Publish" Project="Sdk.targets" />

</Project>
