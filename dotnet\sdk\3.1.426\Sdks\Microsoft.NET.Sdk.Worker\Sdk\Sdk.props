<!--
***********************************************************************************************
Sdk.props

WARNING:  DO NOT MODIFY this file unless you are knowledgeable about MSBuild and have
          created a backup copy.  Incorrect changes to this file will make it
          impossible to load or build your projects from the command-line or the IDE.

Copyright (c) .NET Foundation. All rights reserved.
***********************************************************************************************
-->
<Project ToolsVersion="14.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
    <!--
      Indicate to other targets that Microsoft.NET.Sdk.Worker is being used.
    -->
  <PropertyGroup>
    <UsingMicrosoftNETSdkWorker>true</UsingMicrosoftNETSdkWorker>
  </PropertyGroup>

  <!-- Default properties that shouldn't be replaced by the microsoft.net.sdk.props -->
  <PropertyGroup>
    <DebugSymbols Condition="'$(DebugSymbols)' == ''">true</DebugSymbols>
  </PropertyGroup>

  <Import Sdk="Microsoft.NET.Sdk" Project="Sdk.props" />

  <Import Project="$(MSBuildThisFileDirectory)..\targets\Microsoft.NET.Sdk.Worker.props"
		Condition="Exists('$(MSBuildThisFileDirectory)..\targets\Microsoft.NET.Sdk.Worker.props')" />

</Project>
