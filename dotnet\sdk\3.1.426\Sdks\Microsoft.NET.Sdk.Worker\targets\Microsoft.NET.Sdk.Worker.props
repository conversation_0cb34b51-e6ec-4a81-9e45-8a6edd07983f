<!--
***********************************************************************************************
Microsoft.NET.Sdk.Worker.props

WARNING:  DO NOT MODIFY this file unless you are knowledgeable about MSBuild and have
          created a backup copy.  Incorrect changes to this file will make it
          impossible to load or build your projects from the command-line or the IDE.

Copyright (c) .NET Foundation. All rights reserved.
***********************************************************************************************
-->

<Project xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <OutputType>Exe</OutputType>
    <DebugSymbols Condition="'$(DebugSymbols)' == ''">true</DebugSymbols>
    <DebugType Condition="'$(DebugType)' == ''">pdbonly</DebugType>
    <GenerateDependencyFile Condition="'$(GenerateDependencyFile)' == ''">true</GenerateDependencyFile>
    <IsPackable Condition="'$(IsPackable)' == ''">false</IsPackable>
  </PropertyGroup>

  <ItemGroup Condition=" '$(EnableDefaultItems)' == 'true' And '$(EnableDefaultContentItems)' == 'true' ">
    <!-- Publish all JSON and config files -->
    <Content Include="**\*.config" CopyToOutputDirectory="PreserveNewest" CopyToPublishDirectory="PreserveNewest" Exclude="$(DefaultItemExcludes);$(DefaultExcludesInProjectFolder)" />
    <Content Include="**\*.json" CopyToOutputDirectory="PreserveNewest" CopyToPublishDirectory="PreserveNewest" Exclude="$(DefaultItemExcludes);$(DefaultExcludesInProjectFolder)" />

    <!-- Set CopyToOutputDirectory and CopyToPublishDirectory to Never for items under AppDesignerFolder ("Properties", by default) to avoid publishing launchSettings.json -->
    <Content Update="$(AppDesignerFolder)\**" CopyToOutputDirectory="Never" CopyToPublishDirectory="Never" Condition="'$(AppDesignerFolder)' != ''"/>

    <!-- Remove Content items from other item types -->
    <None Remove="**\*.json;**\*.config" />

    <!-- Keep track of the default content items for later to distinguish them from newly generated content items -->
    <_ContentIncludedByDefault Include="@(Content)" />

  </ItemGroup>

</Project>

