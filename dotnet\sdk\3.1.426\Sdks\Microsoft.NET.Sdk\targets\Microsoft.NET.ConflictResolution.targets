<!--
***********************************************************************************************
Microsoft.NET.ConflictResolution.targets

WARNING:  DO NOT MODIFY this file unless you are knowledgeable about MSBuild and have
          created a backup copy.  Incorrect changes to this file will make it
          impossible to load or build your projects from the command-line or the IDE.

Copyright (c) .NET Foundation. All rights reserved. 
***********************************************************************************************
-->

<Project xmlns="http://schemas.microsoft.com/developer/msbuild/2003">

  <Import Project="Microsoft.NET.DefaultPackageConflictOverrides.targets" />

  <UsingTask TaskName="ResolvePackageFileConflicts" AssemblyFile="$(MicrosoftNETBuildTasksAssembly)" />

  <!--
    _HandlePackageFileConflicts
    Handles package file conflict resolution for build.
    This will differ from the conflict resolution at publish time if the publish assets differ from build.
  -->
  <Target Name="_HandlePackageFileConflicts" DependsOnTargets="GetFrameworkPaths;GetReferenceAssemblyPaths;ResolveTargetingPackAssets">

    <ItemGroup>
      <!--
        All runtime assets for conflict resolution.
        Exclude the copy-local items since those are passed in separately.
      -->
      <_RuntimeAssetsForConflictResolution
        Include="@(RuntimeCopyLocalItems);
                 @(NativeCopyLocalItems);
                 @(ResourceCopyLocalItems);
                 @(RuntimeTargetsCopyLocalItems)"
        Exclude="@(ReferenceCopyLocalPaths)" />
    </ItemGroup>

    <ResolvePackageFileConflicts References="@(Reference)"
                                 ReferenceCopyLocalPaths="@(ReferenceCopyLocalPaths)"
                                 OtherRuntimeItems="@(_RuntimeAssetsForConflictResolution)"
                                 PlatformManifests="@(PackageConflictPlatformManifests)"
                                 TargetFrameworkDirectories="$(TargetFrameworkDirectory)"
                                 PackageOverrides="@(PackageConflictOverrides)"
                                 PreferredPackages="$(PackageConflictPreferredPackages)">
      <Output TaskParameter="ReferencesWithoutConflicts" ItemName="_ReferencesWithoutConflicts" />
      <Output TaskParameter="ReferenceCopyLocalPathsWithoutConflicts" ItemName="_ReferenceCopyLocalPathsWithoutConflicts" />
      <Output TaskParameter="Conflicts" ItemName="_ConflictPackageFiles" />
    </ResolvePackageFileConflicts>

    <!-- Replace Reference / ReferenceCopyLocalPaths with the filtered lists.
         We must remove all and include rather than just remove since removal is based
         only on ItemSpec and duplicate ItemSpecs may exist with different metadata 
         (eg: HintPath) -->
    <ItemGroup>
      <Reference Remove="@(Reference)" />
      <Reference Include="@(_ReferencesWithoutConflicts)" />
      <ReferenceCopyLocalPaths Remove="@(ReferenceCopyLocalPaths)" />
      <ReferenceCopyLocalPaths Include="@(_ReferenceCopyLocalPathsWithoutConflicts)" />
    </ItemGroup>

  </Target>

  <!--
    _HandlePackageFileConflictsForPublish
    Handles package file conflict resolution for publish.
    Currently, publish assets may differ due to the following reasons:
      * A package was marked as excluded from publishing (including PrivateAssets="all").
      * There are runtime store packages to publish against.
      * If we're preserving store layout, which alters the destination paths of files.
    When none of these things are true, then we can rely on the conflict resolution from build.
  -->
  <Target Name="_HandlePackageFileConflictsForPublish"
          AfterTargets="_ResolveCopyLocalAssetsForPublish;
                        _FilterSatelliteResourcesForPublish">

    <ResolvePackageFileConflicts ReferenceCopyLocalPaths="@(_ResolvedCopyLocalPublishAssets)"
                                 PlatformManifests="@(PackageConflictPlatformManifests)"
                                 TargetFrameworkDirectories="$(TargetFrameworkDirectory)"
                                 PreferredPackages="$(PackageConflictPreferredPackages)">
      <Output TaskParameter="ReferenceCopyLocalPathsWithoutConflicts" ItemName="_ResolvedCopyLocalPublishAssetsWithoutConflicts" />
      <Output TaskParameter="Conflicts" ItemName="_PublishConflictPackageFiles" />
    </ResolvePackageFileConflicts>

    <ItemGroup>
      <_ResolvedCopyLocalPublishAssets Remove="@(_ResolvedCopyLocalPublishAssets)" />
      <_ResolvedCopyLocalPublishAssets Include="@(_ResolvedCopyLocalPublishAssetsWithoutConflicts)" />
    </ItemGroup>

  </Target>

</Project>
