<!--
***********************************************************************************************
Microsoft.NET.ILLink.targets

WARNING:  DO NOT MODIFY this file unless you are knowledgeable about MSBuild and have
          created a backup copy.  Incorrect changes to this file will make it
          impossible to load or build your projects from the command-line or the IDE.

Copyright (c) .NET Foundation. All rights reserved.
***********************************************************************************************
-->
<Project xmlns="http://schemas.microsoft.com/developer/msbuild/2003">

  <Import Project="Sdk.props" Sdk="ILLink.Tasks" />

  <PropertyGroup Condition=" '$(PublishTrimmed)' == 'true' ">
    <IntermediateLinkDir Condition=" '$(IntermediateLinkDir)' == '' ">$(IntermediateOutputPath)linked\</IntermediateLinkDir>
    <IntermediateLinkDir Condition=" !HasTrailingSlash('$(IntermediateLinkDir)') ">$(IntermediateLinkDir)\</IntermediateLinkDir>
    <!-- Used to enable incremental build for the linker target. -->
    <_LinkSemaphore>$(IntermediateOutputPath)Link.semaphore</_LinkSemaphore>
  </PropertyGroup>

  <!--
    ============================================================
                     ILLink

    Replace the files to be published with versions that have been
    passed through the linker. Also prevent removed files from being
    included in the generated deps.json.
    ============================================================
    -->
  <Target Name="ILLink"
          Condition=" '$(PublishTrimmed)' == 'true' And
                      '$(_TargetFrameworkVersionWithoutV)' >= '3.0' And '$(TargetFrameworkIdentifier)' == '.NETCoreApp' "
          DependsOnTargets="_RunILLink">

    <NETSdkInformation ResourceName="ILLink_Info" />

    <ItemGroup>
      <_LinkedResolvedFileToPublish Include="@(_LinkedResolvedFileToPublishCandidates)" Condition="Exists('%(Identity)')" />
      <ResolvedFileToPublish Remove="@(_ManagedAssembliesToLink)" />
      <ResolvedFileToPublish Include="@(_LinkedResolvedFileToPublish)" />
    </ItemGroup>

    <!-- Remove assemblies from inputs to GenerateDepsFile. See
         https://github.com/dotnet/sdk/pull/3086. -->
    <ItemGroup>
      <_RemovedManagedAssemblies Include="@(_ManagedAssembliesToLink)" Condition="!Exists('$(IntermediateLinkDir)%(Filename)%(Extension)')" />

      <ResolvedCompileFileDefinitions Remove="@(_RemovedManagedAssemblies)" />
      <RuntimeCopyLocalItems Remove="@(_RemovedManagedAssemblies)" />
      <RuntimeTargetsCopyLocalItems Remove="@(_RemovedManagedAssemblies)" />
      <UserRuntimeAssembly Remove="@(_RemovedManagedAssemblies)" />
      <RuntimePackAsset Remove="@(_RemovedManagedAssemblies)" />
    </ItemGroup>

  </Target>


  <!--
    ============================================================
                     _RunILLink

    Execute the linker. This target runs incrementally, only executing
    if the output semaphore file is out of date with respect to the inputs.
    ============================================================
    -->
  <UsingTask TaskName="ILLink" AssemblyFile="$(ILLinkTasksAssembly)" />
  <Target Name="_RunILLink"
          DependsOnTargets="_ComputeManagedAssembliesToLink;_SetILLinkDefaults"
          Inputs="$(MSBuildAllProjects);@(_ManagedAssembliesToLink);@(TrimmerRootDescriptor);@(ReferencePath)"
          Outputs="$(_LinkSemaphore)">

    <!-- When running from Desktop MSBuild, DOTNET_HOST_PATH is not set.
         In this case, explicitly specify the path to the dotnet host. -->
    <PropertyGroup Condition=" '$(DOTNET_HOST_PATH)' == '' ">
      <_DotNetHostDirectory>$(NetCoreRoot)</_DotNetHostDirectory>
      <_DotNetHostFileName>dotnet</_DotNetHostFileName>
      <_DotNetHostFileName Condition=" '$(OS)' == 'Windows_NT' ">dotnet.exe</_DotNetHostFileName>
    </PropertyGroup>

    <Delete Files="@(_LinkedResolvedFileToPublishCandidates)" />
    <ILLink AssemblyPaths="@(_ManagedAssembliesToLink)"
            ReferenceAssemblyPaths="@(ReferencePath)"
            RootAssemblyNames="@(TrimmerRootAssembly)"
            RootDescriptorFiles="@(TrimmerRootDescriptor)"
            OutputDirectory="$(IntermediateLinkDir)"
            ExtraArgs="$(_ExtraTrimmerArgs)"
            ToolExe="$(_DotNetHostFileName)"
            ToolPath="$(_DotNetHostDirectory)" />

     <Touch Files="$(_LinkSemaphore)" AlwaysCreate="true" />

  </Target>

  <!--
    ============================================================
                     _SetILLinkDefaults

    Set up the default options and inputs to ILLink.
   -->
   <Target Name="_SetILLinkDefaults"
           DependsOnTargets="_ComputeManagedAssembliesToLink">

    <!-- The defaults currently root non-framework assemblies, which
         is a no-op for portable apps. If we later support more ways
         to customize the behavior we can allow linking portable apps
         in some cases. -->
    <NETSdkError Condition="'$(SelfContained)' != 'true'" ResourceName="ILLinkNotSupportedError" />

    <PropertyGroup>
      <_ExtraTrimmerArgs>-l none --skip-unresolved true $(_ExtraTrimmerArgs)</_ExtraTrimmerArgs>
      <_ExtraTrimmerArgs>-c copyused -u copyused $(_ExtraTrimmerArgs)</_ExtraTrimmerArgs>
    </PropertyGroup>
    <ItemGroup>
      <TrimmerRootAssembly Include="@(_ManagedAssembliesToLink)" Condition=" '%(_ManagedAssembliesToLink.IsTrimmable)' != 'true' " />
      <_ManagedAssembliesToLink Update="@(_ManagedAssembliesToLink)" Condition=" '%(_ManagedAssembliesToLink.IsTrimmable)' != 'true' ">
        <action>copy</action>
      </_ManagedAssembliesToLink>
    </ItemGroup>

   </Target>

  <!--
    ============================================================
                     _ComputeManagedAssembliesToLink

    Compute the set of inputs to the linker.
    ============================================================
    -->
  <UsingTask TaskName="ComputeManagedAssemblies" AssemblyFile="$(ILLinkTasksAssembly)" />
  <Target Name="_ComputeManagedAssembliesToLink" DependsOnTargets="_ComputeAssembliesToPostprocessOnPublish">

    <!-- NB: There should not be non-managed assemblies in this list, but we still give the linker a chance to
         further refine this list. It currently drops C++/CLI assemblies in ComputeManageAssemblies. -->
    <ComputeManagedAssemblies Assemblies="@(ResolvedFileToPublish->WithMetadataValue('PostprocessAssembly', 'true'))">
      <Output TaskParameter="ManagedAssemblies" ItemName="_ManagedAssembliesToLink" />
    </ComputeManagedAssemblies>

    <ItemGroup>
      <_LinkedResolvedFileToPublishCandidates Include="@(_ManagedAssembliesToLink->'$(IntermediateLinkDir)%(Filename)%(Extension)')" />
    </ItemGroup>

  </Target>


</Project>
