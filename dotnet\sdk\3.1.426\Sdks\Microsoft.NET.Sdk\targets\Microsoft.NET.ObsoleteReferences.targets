<!--
***********************************************************************************************
Microsoft.NET.ObsoleteReferences.targets

WARNING:  DO NOT MODIFY this file unless you are knowledgeable about MSBuild and have
          created a backup copy.  Incorrect changes to this file will make it
          impossible to load or build your projects from the command-line or the IDE.

Copyright (c) .NET Foundation. All rights reserved.
***********************************************************************************************
-->
<Project ToolsVersion="14.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">

  <PropertyGroup Condition="'$(NETCoreSdkBundledCliToolsProps)' == ''">
    <NETCoreSdkBundledCliToolsProps>$(MSBuildThisFileDirectory)..\..\..\Microsoft.NETCoreSdk.BundledCliTools.props</NETCoreSdkBundledCliToolsProps>
  </PropertyGroup>

  <Import Project="$(NETCoreSdkBundledCliToolsProps)" Condition="Exists('$(NETCoreSdkBundledCliToolsProps)')" />

  <ItemGroup>
    <_ReferenceToObsoleteDotNetCliTool Include="@(DotNetCliToolReference)" />
    <DotNetCliToolReference Remove="@(BundledDotNetCliToolReference)" />
    <_ReferenceToObsoleteDotNetCliTool Remove="@(DotNetCliToolReference)" />
  </ItemGroup>

  <Target Name="_CheckForObsoleteDotNetCliToolReferences"
          BeforeTargets="CollectPackageReferences"
          Condition=" '$(SuppressObsoleteDotNetCliToolWarning)' != 'true' ">
    <NETSdkWarning Condition=" '%(_ReferenceToObsoleteDotNetCliTool.Identity)' != '' "
                   ResourceName="ProjectContainsObsoleteDotNetCliTool"
                   FormatArguments="%(_ReferenceToObsoleteDotNetCliTool.Identity)" />
  </Target>

</Project>
