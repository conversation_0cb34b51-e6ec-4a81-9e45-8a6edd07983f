<!--
***********************************************************************************************
Microsoft.NET.Publish.targets

WARNING:  DO NOT MODIFY this file unless you are knowledgeable about MSBuild and have
          created a backup copy.  Incorrect changes to this file will make it
          impossible to load or build your projects from the command-line or the IDE.

Copyright (c) .NET Foundation. All rights reserved. 
***********************************************************************************************
-->
<Project xmlns="http://schemas.microsoft.com/developer/msbuild/2003">

  <PropertyGroup>
    <DefaultCopyToPublishDirectoryMetadata Condition="'$(DefaultCopyToPublishDirectoryMetadata)' == ''">true</DefaultCopyToPublishDirectoryMetadata>
    <_GetChildProjectCopyToPublishDirectoryItems Condition="'$(_GetChildProjectCopyToPublishDirectoryItems)' == ''">true</_GetChildProjectCopyToPublishDirectoryItems>

    <IsPublishable Condition="'$(IsPublishable)'==''">true</IsPublishable>
  </PropertyGroup>

  <ItemDefinitionGroup>
    <ResolvedFileToPublish>
      <CopyToPublishDirectory>Always</CopyToPublishDirectory>
    </ResolvedFileToPublish>
  </ItemDefinitionGroup>

  <!--
    ============================================================
                                        Publish
 
    The main publish entry point.
    ============================================================
    -->

  <PropertyGroup>
    <!-- We still need to resolve references even if we are not building during publish. -->
    <!-- BuildOnlySettings are required for RAR to find satellites and dependencies -->
    <_BeforePublishNoBuildTargets>
      BuildOnlySettings;
      _PreventProjectReferencesFromBuilding;
      ResolveReferences;
      PrepareResourceNames;
      ComputeIntermediateSatelliteAssemblies;
      ComputeEmbeddedApphostPaths;
    </_BeforePublishNoBuildTargets>

    <_CorePublishTargets>
      PrepareForPublish;
      ComputeAndCopyFilesToPublishDirectory;
      GeneratePublishDependencyFile;
      BundlePublishDirectory;
    </_CorePublishTargets>

    <_PublishNoBuildAlternativeDependsOn>$(_BeforePublishNoBuildTargets);$(_CorePublishTargets)</_PublishNoBuildAlternativeDependsOn>
  </PropertyGroup>

  <Target Name="_PublishBuildAlternative"
          Condition="'$(NoBuild)' != 'true'"
          DependsOnTargets="Build;$(_CorePublishTargets)" />

  <Target Name="_PublishNoBuildAlternative"
          Condition="'$(NoBuild)' == 'true'"
          DependsOnTargets="$(_PublishNoBuildAlternativeDependsOn)" />

  <Target Name="Publish"
          Condition="$(IsPublishable) == 'true'"
          DependsOnTargets="_PublishBuildAlternative;_PublishNoBuildAlternative" >

    <!-- Ensure there is minimal verbosity output pointing to the publish directory and not just the
         build step's minimal output. Otherwise there is no indication at minimal verbosity of where
         the published assets were copied. -->
    <Message Importance="High" Text="$(MSBuildProjectName) -> $([System.IO.Path]::GetFullPath('$(PublishDir)'))" />
  </Target>

  <!-- Don't let project reference resolution build project references in NoBuild case. -->
  <Target Name="_PreventProjectReferencesFromBuilding">
    <PropertyGroup>
      <BuildProjectReferences>false</BuildProjectReferences>
    </PropertyGroup>
  </Target>

  <!--
    ============================================================
                                        PrepareForPublish

    Prepare the prerequisites for publishing.
    ============================================================
    -->
  <Target Name="PrepareForPublish">

    <NETSdkError Condition="'$(PublishSingleFile)' == 'true' And '$(_IsExecutable)' != 'true'"
                 ResourceName="CannotHaveSingleFileWithoutExecutable" />
    <NETSdkError Condition="'$(PublishSingleFile)' == 'true' And '$(_IsExecutable)' == 'true' And '$(TargetFrameworkIdentifier)' != '.NETCoreApp'"
                 ResourceName="CanOnlyHaveSingleFileWithNetCoreApp" />

    <PropertyGroup>
      <!-- Ensure any PublishDir has a trailing slash, so it can be concatenated -->
      <PublishDir Condition="!HasTrailingSlash('$(PublishDir)')">$(PublishDir)\</PublishDir>
    </PropertyGroup>

    <MakeDir Directories="$(PublishDir)" />

  </Target>

  <!--
    ============================================================
                                        ComputeAndCopyFilesToPublishDirectory

    Computes the list of all files to copy to the publish directory and then publishes them.
    ============================================================
    -->
  <Target Name="ComputeAndCopyFilesToPublishDirectory"
          DependsOnTargets="ComputeFilesToPublish;
                            CopyFilesToPublishDirectory" />

  <!--
    ============================================================
                                        CopyFilesToPublishDirectory

    Copy all build outputs, satellites and other necessary files to the publish directory.
    When publishing to a single file, only those files that are not bundled are copied.
    The remaining files are directly written to the bundle file.
    ============================================================
    -->
  <Target Name="CopyFilesToPublishDirectory"
          DependsOnTargets="_CopyResolvedFilesToPublishPreserveNewest;
                            _CopyResolvedFilesToPublishAlways" />

  <!--
    ============================================================
                                        _CopyResolvedFilesToPublishPreserveNewest

    Copy _ResolvedFileToPublishPreserveNewest items to the publish directory 
    (except files that will be bundled, during PublishSingleFile).
    ============================================================
    -->
  <Target Name="_CopyResolvedFilesToPublishPreserveNewest"
          DependsOnTargets="_ComputeResolvedFilesToPublishTypes"
          Inputs="@(_ResolvedFileToPublishPreserveNewest)"
          Outputs="@(_ResolvedFileToPublishPreserveNewest->'$(PublishDir)%(RelativePath)')">

    <!--
      PreserveNewest means that we will only copy the source to the destination if the source is newer.
      SkipUnchangedFiles is not used for that purpose because it will copy if the source and destination
      differ by size too.  Instead, this target uses inputs and outputs to only copy when the source is newer.
      -->
    <Copy SourceFiles = "@(_ResolvedUnbundledFileToPublishPreserveNewest)"
          DestinationFiles="@(_ResolvedUnbundledFileToPublishPreserveNewest->'$(PublishDir)%(RelativePath)')"
          OverwriteReadOnlyFiles="$(OverwriteReadOnlyFiles)"
          Retries="$(CopyRetryCount)"
          RetryDelayMilliseconds="$(CopyRetryDelayMilliseconds)"
          UseHardlinksIfPossible="$(CreateHardLinksForPublishFilesIfPossible)"
          UseSymboliclinksIfPossible="$(CreateSymbolicLinksForPublishFilesIfPossible)">

      <Output TaskParameter="DestinationFiles" ItemName="FileWrites"/>

    </Copy>

  </Target>

  <!--
    ============================================================
                                        _CopyResolvedFilesToPublishAlways

    Copy _ResolvedFileToPublishAlways items to the publish directory
    (except files that will be bundled, during PublishSingleFile).    
    ============================================================
    -->
  <Target Name="_CopyResolvedFilesToPublishAlways"
          DependsOnTargets="_ComputeResolvedFilesToPublishTypes">

    <!--
      Use SkipUnchangedFiles to prevent unnecessary file copies. The copy will occur if the
      destination doesn't exist, the source is newer than the destination, or if the source and
      destination differ by file size.
      -->
    <Copy SourceFiles = "@(_ResolvedUnbundledFileToPublishAlways)"
          DestinationFiles="@(_ResolvedUnbundledFileToPublishAlways->'$(PublishDir)%(RelativePath)')"
          SkipUnchangedFiles="$(SkipCopyUnchangedFiles)"
          OverwriteReadOnlyFiles="$(OverwriteReadOnlyFiles)"
          Retries="$(CopyRetryCount)"
          RetryDelayMilliseconds="$(CopyRetryDelayMilliseconds)"
          UseHardlinksIfPossible="$(CreateHardLinksForPublishFilesIfPossible)"
          UseSymboliclinksIfPossible="$(CreateSymbolicLinksForPublishFilesIfPossible)">

      <Output TaskParameter="DestinationFiles" ItemName="FileWrites"/>

    </Copy>
  </Target>

  <!--
    ============================================================
                                        CreateReadyToRunImages

    Create ReadyToRun images for managed assemblies in _ResolvedFileToPublishAlways and _ResolvedFileToPublishPreserveNewest.
    ============================================================
    -->
  <Target Name="CreateReadyToRunImages"
          Condition="'$(_TargetFrameworkVersionWithoutV)' >= '3.0' And '$(PublishReadyToRun)' == 'true' And '$(TargetFrameworkIdentifier)' == '.NETCoreApp'"
          DependsOnTargets="_PrepareForReadyToRunCompilation;
                            _CreateR2RImages;
                            _CreateR2RSymbols">

    <NETSdkError Condition="'@(_ReadyToRunCompilationFailures)' != ''" ResourceName="ReadyToRunCompilationFailed" />

    <NETSdkInformation Condition="'$(_ReadyToRunCompilerHasWarnings)' != ''" ResourceName="ReadyToRunCompilationHasWarnings_Info" />

    <ItemGroup>
      <!-- 
      Note: we only remove the entries for the IL images and replace them with the entries for the R2R images.
      We do not do the same for PDBs, because the native PDBs created by the R2R compiler complement the IL PDBs
      and do not replace them. IL PDBs are still required for debugging. Native PDBs emitted by the R2R compiler are
      only used for profiling purposes.
      -->
      <ResolvedFileToPublish Remove="@(_ReadyToRunCompileList)" />
      <ResolvedFileToPublish Include="@(_ReadyToRunFilesToPublish)" />
    </ItemGroup>

  </Target>

  <!--
    ============================================================
                                        _PrepareForReadyToRunCompilation

    Prepare build for ReadyToRun compilations. Builds list of assemblies to compile, and computes paths to ReadyToRun compiler bits
    ============================================================
    -->
  <UsingTask TaskName="PrepareForReadyToRunCompilation" AssemblyFile="$(MicrosoftNETBuildTasksAssembly)" />
  <Target Name="_PrepareForReadyToRunCompilation" DependsOnTargets="_ComputeManagedRuntimePackAssemblies;_ComputeAssembliesToPostprocessOnPublish">

    <PropertyGroup>
      <_ReadyToRunOutputPath>$(IntermediateOutputPath)R2R</_ReadyToRunOutputPath>
    </PropertyGroup>

    <MakeDir Directories="$(_ReadyToRunOutputPath)" />

    <ItemGroup>
      <_ReadyToRunImplementationAssemblies Include="@(ResolvedFileToPublish->WithMetadataValue('PostprocessAssembly', 'true'))" />
    </ItemGroup>

    <!-- Even if app is not self-contained, crossgen requires closure of implementation assemblies. Resolve conflicts
         of the runtime pack assets as though we were copying them locally, and add them to the R2R implementation
         assembly list. -->
     <ItemGroup Condition="'$(SelfContained)' != 'true'">
       <_ReadyToRunImplementationAssemblies Include="@(_ManagedRuntimePackAssembly)" ReferenceOnly="true" />
     </ItemGroup>

     <ResolvePackageFileConflicts Condition="'$(SelfContained)' != 'true'"
                                 ReferenceCopyLocalPaths="@(_ReadyToRunImplementationAssemblies)">
      <Output TaskParameter="ReferenceCopyLocalPathsWithoutConflicts" ItemName="_ReadyToRunImplementationAssembliesWithoutConflicts" />
    </ResolvePackageFileConflicts>

    <ItemGroup Condition="'$(SelfContained)' != 'true'">
      <_ReadyToRunImplementationAssemblies Remove="@(_ReadyToRunImplementationAssemblies)" />
      <_ReadyToRunImplementationAssemblies Include="@(_ReadyToRunImplementationAssembliesWithoutConflicts)" />
    </ItemGroup>

    <PrepareForReadyToRunCompilation RuntimePacks="@(ResolvedRuntimePack)" 
                                     TargetingPacks="@(ResolvedTargetingPack)"
                                     RuntimeGraphPath="$(BundledRuntimeIdentifierGraphFile)"
                                     NETCoreSdkRuntimeIdentifier="$(NETCoreSdkRuntimeIdentifier)"
                                     OutputPath="$(_ReadyToRunOutputPath)"
                                     Assemblies="@(_ReadyToRunImplementationAssemblies)"
                                     ExcludeList="@(PublishReadyToRunExclude)"
                                     EmitSymbols="$(PublishReadyToRunEmitSymbols)"
                                     IncludeSymbolsInSingleFile="$(IncludeSymbolsInSingleFile)">

      <Output TaskParameter="CrossgenTool" ItemName="_CrossgenTool" />

      <Output TaskParameter="ReadyToRunCompileList" ItemName="_ReadyToRunCompileList" />
      <Output TaskParameter="ReadyToRunSymbolsCompileList" ItemName="_ReadyToRunSymbolsCompileList" />

      <Output TaskParameter="ReadyToRunFilesToPublish" ItemName="_ReadyToRunFilesToPublish" />
      <Output TaskParameter="ReadyToRunAssembliesToReference" ItemName="_ReadyToRunAssembliesToReference" />

    </PrepareForReadyToRunCompilation>
  </Target>

  <!--
    ============================================================
                                        _CreateR2RImages

    Compiles assemblies in the _ReadyToRunCompileList list into ReadyToRun images
    ============================================================
    -->
  <UsingTask TaskName="RunReadyToRunCompiler" AssemblyFile="$(MicrosoftNETBuildTasksAssembly)" />
  <Target Name="_CreateR2RImages"
          Inputs="@(_ReadyToRunCompileList)"
          Outputs="%(_ReadyToRunCompileList.OutputR2RImage)">

    <RunReadyToRunCompiler CrossgenTool="@(_CrossgenTool)"
                           ImplementationAssemblyReferences="@(_ReadyToRunAssembliesToReference)"
                           ShowCompilerWarnings="$(PublishReadyToRunShowWarnings)"
                           CompilationEntry="@(_ReadyToRunCompileList)"
                           ContinueOnError="ErrorAndContinue">
      <Output TaskParameter="ExitCode" PropertyName="_ReadyToRunCompilerExitCode" />
      <Output TaskParameter="WarningsDetected" PropertyName="_ReadyToRunWarningsDetected" />
    </RunReadyToRunCompiler>

    <PropertyGroup>
      <!-- Use distinct property here as any of the invocations can set it -->
      <_ReadyToRunCompilerHasWarnings Condition="'$(_ReadyToRunWarningsDetected)' == 'true'">true</_ReadyToRunCompilerHasWarnings>
    </PropertyGroup>

    <ItemGroup>
      <_ReadyToRunCompilationFailures Condition="'$(_ReadyToRunCompilerExitCode)' != '' And $(_ReadyToRunCompilerExitCode) != 0"
                                      Include="@(_ReadyToRunCompileList)" />
    </ItemGroup>
  </Target>

  <!--
    ============================================================
                                        _CreateR2RSymbols

    Emit native symbols for ReadyToRun images in the _ReadyToRunSymbolsCompileList list
    ============================================================
    -->
  <Target Name="_CreateR2RSymbols"
          Inputs="@(_ReadyToRunSymbolsCompileList)"
          Outputs="%(_ReadyToRunSymbolsCompileList.OutputPDBImage)">

    <RunReadyToRunCompiler CrossgenTool="@(_CrossgenTool)"
                           ImplementationAssemblyReferences="@(_ReadyToRunAssembliesToReference)"
                           ShowCompilerWarnings="$(PublishReadyToRunShowWarnings)"
                           CompilationEntry="@(_ReadyToRunSymbolsCompileList)"
                           ContinueOnError="ErrorAndContinue">
      <Output TaskParameter="ExitCode" PropertyName="_ReadyToRunCompilerExitCode" />
      <Output TaskParameter="WarningsDetected" PropertyName="_ReadyToRunWarningsDetected" />
    </RunReadyToRunCompiler>

    <PropertyGroup>
      <!-- Use distinct property here as any of the invocations can set it -->
      <_ReadyToRunCompilerHasWarnings Condition="'$(_ReadyToRunWarningsDetected)' == 'true'">true</_ReadyToRunCompilerHasWarnings>
    </PropertyGroup>

    <ItemGroup>
      <_ReadyToRunCompilationFailures Condition="'$(_ReadyToRunCompilerExitCode)' != '' And $(_ReadyToRunCompilerExitCode) != 0"
                                      Include="@(_ReadyToRunSymbolsCompileList)" />
    </ItemGroup>
  </Target>

  <!--
    ============================================================
                                        _ComputeResolvedFilesToPublishTypes

    Splits ResolvedFileToPublish items into 'PreserveNewest' and 'Always' buckets.
    Then further splits those into 'Unbundled' buckets based on the single file setting.
    ============================================================
    -->
  <Target Name="_ComputeResolvedFilesToPublishTypes">
    <ItemGroup>

      <_ResolvedFileToPublishPreserveNewest Include="@(ResolvedFileToPublish)"
                                            Condition="'%(ResolvedFileToPublish.CopyToPublishDirectory)'=='PreserveNewest'" />

      <_ResolvedFileToPublishAlways Include="@(ResolvedFileToPublish)"
                                    Condition="'%(ResolvedFileToPublish.CopyToPublishDirectory)'=='Always'" />
    </ItemGroup>

    <ItemGroup>

      <_ResolvedUnbundledFileToPublishPreserveNewest
                    Include="@(_ResolvedFileToPublishPreserveNewest)"
                    Condition="'$(PublishSingleFile)' != 'true' or 
                               '%(_ResolvedFileToPublishPreserveNewest.ExcludeFromSingleFile)'=='true'" />

      <_ResolvedUnbundledFileToPublishAlways
              Include="@(_ResolvedFileToPublishAlways)"
              Condition="'$(PublishSingleFile)' != 'true' or 
                         '%(_ResolvedFileToPublishAlways.ExcludeFromSingleFile)'=='true'" />
    </ItemGroup>
  </Target>

  <!--
    ============================================================
                                        ComputeFilesToPublish

    Gathers all the files that need to be copied to the publish directory, including R2R and ILLinker transformations
    ============================================================
    -->
  <Target Name="ComputeFilesToPublish"
          DependsOnTargets="ComputeResolvedFilesToPublishList;
                            ILLink;
                            CreateReadyToRunImages">
  </Target>

  <PropertyGroup>
    <CopyBuildOutputToPublishDirectory Condition="'$(CopyBuildOutputToPublishDirectory)'==''">true</CopyBuildOutputToPublishDirectory>
    <CopyOutputSymbolsToPublishDirectory Condition="'$(CopyOutputSymbolsToPublishDirectory)'==''">true</CopyOutputSymbolsToPublishDirectory>
    <IncludeSymbolsInSingleFile Condition="'$(IncludeSymbolsInSingleFile)' == ''">false</IncludeSymbolsInSingleFile>
  </PropertyGroup>

  <!--
    ============================================================
                                        ComputeResolvedFilesToPublishList

    Gathers all the files that need to be copied to the publish directory.
    ============================================================
    -->
  <Target Name="ComputeResolvedFilesToPublishList"
          DependsOnTargets="_ComputeResolvedCopyLocalPublishAssets;
                            _ComputeCopyToPublishDirectoryItems;
                            ComputeRefAssembliesToPublish">
    <ItemGroup>
      <!-- Copy the build product (.dll or .exe). -->
      <ResolvedFileToPublish Include="@(IntermediateAssembly)"
                             Condition="'$(CopyBuildOutputToPublishDirectory)' == 'true'">
        <RelativePath>@(IntermediateAssembly->'%(Filename)%(Extension)')</RelativePath>
        <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      </ResolvedFileToPublish>

      <!-- Copy the deps file if using the build deps file. -->
      <ResolvedFileToPublish Include="$(ProjectDepsFilePath)"
                             Condition="'$(GenerateDependencyFile)' == 'true' and '$(_UseBuildDependencyFile)' == 'true'">
        <RelativePath>$(ProjectDepsFileName)</RelativePath>
        <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      </ResolvedFileToPublish>

      <!-- Copy the runtime config file. -->
      <ResolvedFileToPublish Include="$(ProjectRuntimeConfigFilePath)"
                             Condition="'$(GenerateRuntimeConfigurationFiles)' == 'true'">
        <RelativePath>$(ProjectRuntimeConfigFileName)</RelativePath>
        <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      </ResolvedFileToPublish>
      
      <!-- Copy the app.config (if any) -->
      <ResolvedFileToPublish Include="@(AppConfigWithTargetPath)"
                             Condition="'$(CopyBuildOutputToPublishDirectory)' == 'true'">
        <RelativePath>@(AppConfigWithTargetPath->'%(TargetPath)')</RelativePath>
        <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      </ResolvedFileToPublish>

      <!-- Copy the debug information file (.pdb), if any -->
      <ResolvedFileToPublish Include="@(_DebugSymbolsIntermediatePath)"
                             Condition="'$(_DebugSymbolsProduced)'=='true' and '$(CopyOutputSymbolsToPublishDirectory)'=='true'">
        <RelativePath>@(_DebugSymbolsIntermediatePath->'%(Filename)%(Extension)')</RelativePath>
        <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
        <ExcludeFromSingleFile Condition="'$(IncludeSymbolsInSingleFile)'!='true'">true</ExcludeFromSingleFile>
      </ResolvedFileToPublish>

      <!-- Copy satellite assemblies. -->
      <ResolvedFileToPublish Include="@(IntermediateSatelliteAssembliesWithTargetPath)">
        <RelativePath>%(IntermediateSatelliteAssembliesWithTargetPath.Culture)\%(Filename)%(Extension)</RelativePath>
        <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      </ResolvedFileToPublish>

      <!-- Copy generated COM References. -->
      <ResolvedFileToPublish Include="@(ReferenceComWrappersToCopyLocal)">
        <RelativePath>%(Filename)%(Extension)</RelativePath>
        <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      </ResolvedFileToPublish>

      <!-- Copy the resolved copy local publish assets. -->
      <ResolvedFileToPublish Include="@(_ResolvedCopyLocalPublishAssets)">
        <RelativePath>%(_ResolvedCopyLocalPublishAssets.DestinationSubDirectory)%(Filename)%(Extension)</RelativePath>
        <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      </ResolvedFileToPublish>

      <!-- Copy the xml documentation (if enabled) -->
      <ResolvedFileToPublish Include="@(FinalDocFile)"
                              Condition="'$(PublishDocumentationFile)' == 'true'">
        <RelativePath>@(FinalDocFile->'%(Filename)%(Extension)')</RelativePath>
        <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      </ResolvedFileToPublish>

      <!-- Copy all PackAsTool shims (if any) -->
      <ResolvedFileToPublish Include="@(_EmbeddedApphostPaths)">
        <RelativePath>shims/%(_EmbeddedApphostPaths.ShimRuntimeIdentifier)/%(_EmbeddedApphostPaths.Filename)%(_EmbeddedApphostPaths.Extension)</RelativePath>
        <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      </ResolvedFileToPublish>
    </ItemGroup>

  </Target>

  <!--
    ============================================================
    _ResolveCopyLocalAssetsForPublish
    Resolves the assets from packages to copy locally for publish.
    We can just use the build's copy local assets if we can reuse the build deps file.
    ============================================================
  -->
  <UsingTask TaskName="ResolveCopyLocalAssets" AssemblyFile="$(MicrosoftNETBuildTasksAssembly)" />
  <Target Name="_ResolveCopyLocalAssetsForPublish"
          DependsOnTargets="ResolveLockFileCopyLocalFiles;
                            _ComputeUseBuildDependencyFile;
                            _DefaultMicrosoftNETPlatformLibrary;
                            ResolveRuntimePackAssets;
                            _ComputePackageReferencePublish">

      <!-- For future: Delete ResolveCopyLocalAssets task.  Need to figure out how to get correct DestinationSubPath for
           PreserveStoreLayout without this task, and how to handle RuntimeStorePackages. -->
      <ResolveCopyLocalAssets AssetsFilePath="$(ProjectAssetsFile)"
                              TargetFramework="$(TargetFrameworkMoniker)"
                              RuntimeIdentifier="$(RuntimeIdentifier)"
                              PlatformLibraryName="$(MicrosoftNETPlatformLibrary)"
                              RuntimeFrameworks="@(RuntimeFramework)"
                              ExcludedPackageReferences="@(_ExcludeFromPublishPackageReference)"
                              RuntimeStorePackages="@(RuntimeStorePackages)"
                              PreserveStoreLayout="$(PreserveStoreLayout)"
                              ResolveRuntimeTargets="$(CopyLocalRuntimeTargetAssets)"
                              IsSelfContained="$(SelfContained)"
                              Condition="'$(PreserveStoreLayout)' == 'true' Or '@(RuntimeStorePackages)' != ''">
        <Output TaskParameter="ResolvedAssets" ItemName="_ResolvedCopyLocalPublishAssets" />
      </ResolveCopyLocalAssets>
    
    
      <ItemGroup>
        <_ResolvedCopyLocalPublishAssets Include="@(RuntimePackAsset)" Condition="'$(SelfContained)' == 'true'" />
      </ItemGroup>

      <ItemGroup Condition="'$(_UseBuildDependencyFile)' != 'true'">
        <!-- Remove the apphost executable from publish copy local assets; we will copy the generated apphost instead -->
        <_ResolvedCopyLocalPublishAssets Remove="@(_NativeRestoredAppHostNETCore)" />
      </ItemGroup>

      <ItemGroup Condition="'$(PreserveStoreLayout)' != 'true' And '@(RuntimeStorePackages)' == ''">
        <_ResolvedCopyLocalPublishAssets Include="@(_ResolvedCopyLocalBuildAssets)" 
                                         Condition="'%(_ResolvedCopyLocalBuildAssets.CopyToPublishDirectory)' != 'false' "/>
      </ItemGroup>

  </Target>

  <!--
    ============================================================
    _ParseTargetManifestFiles
    Parses the $(TargetManifestFiles) which contains a list of files into @(RuntimeStorePackages) items
    which describes which packages should be excluded from publish since they are contained in the runtime store.
    ============================================================
    -->
  <UsingTask TaskName="ParseTargetManifests" AssemblyFile="$(MicrosoftNETBuildTasksAssembly)" />
    <Target Name="_ParseTargetManifestFiles"
            Condition="'$(TargetManifestFiles)' != ''"
            Returns="@(RuntimeStorePackages)">

      <ParseTargetManifests TargetManifestFiles="$(TargetManifestFiles)">
        <Output TaskParameter="RuntimeStorePackages" ItemName="RuntimeStorePackages"/>
      </ParseTargetManifests>

  </Target>

  <!--
    ============================================================
    _FilterSatelliteResourcesForPublish
    Filters the resolved resource assets for build to the given resource languages.
    ============================================================
  -->
  <Target Name="_FilterSatelliteResourcesForPublish"
          Condition="'$(SatelliteResourceLanguages)' != ''">

    <ItemGroup>
      <_PublishSatelliteResources Include="@(_ResolvedCopyLocalPublishAssets)"
                                  Condition="'%(_ResolvedCopyLocalPublishAssets.AssetType)' == 'resources'" />
    </ItemGroup>

    <JoinItems Left="@(_PublishSatelliteResources)" LeftKey="Culture" LeftMetadata="*"
               Right="$(SatelliteResourceLanguages)" RightKey="" RightMetadata=""
               ItemSpecToUse="Left">
      <Output TaskParameter="JoinResult" ItemName="_FilteredPublishSatelliteResources" />
    </JoinItems>

    <ItemGroup Condition="'@(_PublishSatelliteResources)' != ''">
      <_ResolvedCopyLocalPublishAssets Remove="@(_PublishSatelliteResources)" />
      <_ResolvedCopyLocalPublishAssets Include="@(_FilteredPublishSatelliteResources)" />
    </ItemGroup>

  </Target>

  <!--
    ============================================================
    _ComputeResolvedCopyLocalPublishAssets
    Computes the files from both project and package references.
    ============================================================
  -->
  <Target Name="_ComputeResolvedCopyLocalPublishAssets"
          DependsOnTargets="_ResolveCopyLocalAssetsForPublish;
                            _FilterSatelliteResourcesForPublish">

    <ItemGroup>
      <_ResolvedCopyLocalPublishAssets Include="@(ReferenceCopyLocalPaths)"
                                       Exclude="@(_ResolvedCopyLocalBuildAssets);@(RuntimePackAsset)"
                                       Condition="'$(PublishReferencesDocumentationFiles)' == 'true' or '%(ReferenceCopyLocalPaths.Extension)' != '.xml'">
        <DestinationSubPath>%(ReferenceCopyLocalPaths.DestinationSubDirectory)%(ReferenceCopyLocalPaths.Filename)%(ReferenceCopyLocalPaths.Extension)</DestinationSubPath>
      </_ResolvedCopyLocalPublishAssets>
    </ItemGroup>

  </Target>

  <!--
    ============================================================
                                        _ComputeCopyToPublishDirectoryItems
    ============================================================
    -->
  <Target Name="_ComputeCopyToPublishDirectoryItems"
          DependsOnTargets="GetCopyToPublishDirectoryItems">

    <ItemGroup>
      <ResolvedFileToPublish Include="@(_SourceItemsToCopyToPublishDirectoryAlways)">
        <RelativePath>%(_SourceItemsToCopyToPublishDirectoryAlways.TargetPath)</RelativePath>
        <CopyToPublishDirectory>Always</CopyToPublishDirectory>
        <IsKeyOutput Condition="'%(_SourceItemsToCopyToPublishDirectoryAlways.FullPath)' == '$(AppHostIntermediatePath)'">True</IsKeyOutput>
      </ResolvedFileToPublish>

      <ResolvedFileToPublish Include="@(_SourceItemsToCopyToPublishDirectory)">
        <RelativePath>%(_SourceItemsToCopyToPublishDirectory.TargetPath)</RelativePath>
        <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
        <IsKeyOutput Condition="'%(_SourceItemsToCopyToPublishDirectory.FullPath)' == '$(AppHostIntermediatePath)'">True</IsKeyOutput>
      </ResolvedFileToPublish>
    </ItemGroup>

  </Target>

  <!--
    ============================================================
                                        GetCopyToPublishDirectoryItems

    Get all project items that may need to be transferred to the publish directory.
    This includes baggage items from transitively referenced projects. It would appear
    that this target computes full transitive closure of content items for all referenced
    projects; however that is not the case. It only collects the content items from its
    immediate children and not children of children. 
    
    See comment on GetCopyToOutputDirectoryItems, from which this logic was taken.
    ============================================================
    -->
  <Target Name="GetCopyToPublishDirectoryItems"
          Returns="@(AllPublishItemsFullPathWithTargetPath)"
          KeepDuplicateOutputs=" '$(MSBuildDisableGetCopyToPublishDirectoryItemsOptimization)' == '' "
          DependsOnTargets="AssignTargetPaths;
                            DefaultCopyToPublishDirectoryMetadata;
                            _SplitProjectReferencesByFileExistence;
                            _GetProjectReferenceTargetFrameworkProperties">


    <!-- In the general case, clients need very little of the metadata which is generated by invoking this target on this project and its children.  For those
         cases, we can immediately discard the unwanted metadata, reducing memory usage, particularly in very large and interconnected systems of projects.
         However, if some client does require the original functionality, it is sufficient to set MSBuildDisableGetCopyToPublishDirectoryItemsOptimization to
         a non-empty value and the original behavior will be restored. -->
    <PropertyGroup Condition=" '$(MSBuildDisableGetCopyToPublishDirectoryItemsOptimization)' == '' ">
      <_GCTPDIKeepDuplicates>false</_GCTPDIKeepDuplicates>
      <_GCTPDIKeepMetadata>CopyToPublishDirectory;ExcludeFromSingleFile;TargetPath</_GCTPDIKeepMetadata>
    </PropertyGroup>

    <!-- Get items from child projects first. -->
    <MSBuild Projects="@(_MSBuildProjectReferenceExistent)"
             Targets="GetCopyToPublishDirectoryItems"
             BuildInParallel="$(BuildInParallel)"
             Properties="%(_MSBuildProjectReferenceExistent.SetConfiguration); %(_MSBuildProjectReferenceExistent.SetPlatform); %(_MSBuildProjectReferenceExistent.SetTargetFramework)"
             Condition="'@(_MSBuildProjectReferenceExistent)' != '' and '$(_GetChildProjectCopyToPublishDirectoryItems)' == 'true' and '%(_MSBuildProjectReferenceExistent.Private)' != 'false'"
             ContinueOnError="$(ContinueOnError)"
             RemoveProperties="%(_MSBuildProjectReferenceExistent.GlobalPropertiesToRemove)">

      <Output TaskParameter="TargetOutputs" ItemName="_AllChildProjectPublishItemsWithTargetPath"/>

    </MSBuild>

    <!-- Target outputs must be full paths because they will be consumed by a different project. -->
    <ItemGroup>
      <_SourceItemsToCopyToPublishDirectoryAlways KeepDuplicates=" '$(_GCTPDIKeepDuplicates)' != 'false' "
                                                  KeepMetadata="$(_GCTPDIKeepMetadata)"
                                                  Include="@(_AllChildProjectPublishItemsWithTargetPath->'%(FullPath)')"
                                                  Condition="'%(_AllChildProjectPublishItemsWithTargetPath.CopyToPublishDirectory)'=='Always'"/>

      <_SourceItemsToCopyToPublishDirectory KeepDuplicates=" '$(_GCTPDIKeepDuplicates)' != 'false' "
                                            KeepMetadata="$(_GCTPDIKeepMetadata)"
                                            Include="@(_AllChildProjectPublishItemsWithTargetPath->'%(FullPath)')"
                                            Condition="'%(_AllChildProjectPublishItemsWithTargetPath.CopyToPublishDirectory)'=='PreserveNewest'"/>
    </ItemGroup>

    <!-- Remove items which we will never again use - they just sit around taking up memory otherwise -->
    <ItemGroup>
      <_AllChildProjectPublishItemsWithTargetPath Remove="@(_AllChildProjectPublishItemsWithTargetPath)"/>
    </ItemGroup>

    <!-- Get items from this project last so that they will be copied last. -->
    <ItemGroup>
      <_SourceItemsToCopyToPublishDirectoryAlways KeepMetadata="$(_GCTPDIKeepMetadata)"
                                                  Include="@(ContentWithTargetPath->'%(FullPath)')"
                                                  Condition="'%(ContentWithTargetPath.CopyToPublishDirectory)'=='Always'"/>
      <_SourceItemsToCopyToPublishDirectory KeepMetadata="$(_GCTPDIKeepMetadata)"
                                            Include="@(ContentWithTargetPath->'%(FullPath)')"
                                            Condition="'%(ContentWithTargetPath.CopyToPublishDirectory)'=='PreserveNewest'"/>
    </ItemGroup>

    <ItemGroup>
      <_SourceItemsToCopyToPublishDirectoryAlways KeepMetadata="$(_GCTPDIKeepMetadata)"
                                                  Include="@(EmbeddedResource->'%(FullPath)')"
                                                  Condition="'%(EmbeddedResource.CopyToPublishDirectory)'=='Always'"/>
      <_SourceItemsToCopyToPublishDirectory KeepMetadata="$(_GCTPDIKeepMetadata)"
                                            Include="@(EmbeddedResource->'%(FullPath)')"
                                            Condition="'%(EmbeddedResource.CopyToPublishDirectory)'=='PreserveNewest'"/>
    </ItemGroup>

    <ItemGroup>
      <_CompileItemsToPublish Include="@(Compile->'%(FullPath)')"
                              Condition="'%(Compile.CopyToPublishDirectory)'=='Always' or '%(Compile.CopyToPublishDirectory)'=='PreserveNewest'"/>
    </ItemGroup>

    <AssignTargetPath Files="@(_CompileItemsToPublish)" RootFolder="$(MSBuildProjectDirectory)">
      <Output TaskParameter="AssignedFiles" ItemName="_CompileItemsToPublishWithTargetPath" />
    </AssignTargetPath>

    <ItemGroup>
      <_SourceItemsToCopyToPublishDirectoryAlways KeepMetadata="$(_GCTPDIKeepMetadata)"
                                                  Include="@(_CompileItemsToPublishWithTargetPath)"
                                                  Condition="'%(_CompileItemsToPublishWithTargetPath.CopyToPublishDirectory)'=='Always'"/>
      <_SourceItemsToCopyToPublishDirectory KeepMetadata="$(_GCTPDIKeepMetadata)"
                                           Include="@(_CompileItemsToPublishWithTargetPath)"
                                           Condition="'%(_CompileItemsToPublishWithTargetPath.CopyToPublishDirectory)'=='PreserveNewest'"/>
    </ItemGroup>

    <ItemGroup>
      <_SourceItemsToCopyToPublishDirectoryAlways KeepMetadata="$(_GCTPDIKeepMetadata)"
                                                  Include="@(_NoneWithTargetPath->'%(FullPath)')"
                                                  Condition="'%(_NoneWithTargetPath.CopyToPublishDirectory)'=='Always'"/>
      <_SourceItemsToCopyToPublishDirectory KeepMetadata="$(_GCTPDIKeepMetadata)"
                                            Include="@(_NoneWithTargetPath->'%(FullPath)')"
                                            Condition="'%(_NoneWithTargetPath.CopyToPublishDirectory)'=='PreserveNewest'"/>
    </ItemGroup>

    <ItemGroup>
      <AllPublishItemsFullPathWithTargetPath Include="@(_SourceItemsToCopyToPublishDirectoryAlways->'%(FullPath)');@(_SourceItemsToCopyToPublishDirectory->'%(FullPath)')"/>
    </ItemGroup>

  </Target>

  <!--
    ============================================================
                                        DefaultCopyToPublishDirectoryMetadata

    If CopyToPublishDirectory isn't set on these items, the value should be taken from CopyToOutputDirectory.
    This way, projects can just set "CopyToOutputDirectory = Always/PreserveNewest" and by default the item will be copied
    to both the build output and publish directories.
    ============================================================
    -->
  <Target Name="DefaultCopyToPublishDirectoryMetadata"
          DependsOnTargets="AssignTargetPaths"
          Condition=" '$(DefaultCopyToPublishDirectoryMetadata)' == 'true' ">

    <ItemGroup>
      <ContentWithTargetPath Condition="'%(ContentWithTargetPath.CopyToOutputDirectory)'=='Always' and '%(ContentWithTargetPath.CopyToPublishDirectory)' == ''">
        <CopyToPublishDirectory>Always</CopyToPublishDirectory>
      </ContentWithTargetPath>
      <ContentWithTargetPath Condition="'%(ContentWithTargetPath.CopyToOutputDirectory)'=='PreserveNewest' and '%(ContentWithTargetPath.CopyToPublishDirectory)' == ''">
        <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      </ContentWithTargetPath>

      <EmbeddedResource Condition="'%(EmbeddedResource.CopyToOutputDirectory)'=='Always' and '%(EmbeddedResource.CopyToPublishDirectory)' == ''">
        <CopyToPublishDirectory>Always</CopyToPublishDirectory>
      </EmbeddedResource>
      <EmbeddedResource Condition="'%(EmbeddedResource.CopyToOutputDirectory)'=='PreserveNewest' and '%(EmbeddedResource.CopyToPublishDirectory)' == ''">
        <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      </EmbeddedResource>

      <Compile Condition="'%(Compile.CopyToOutputDirectory)'=='Always' and '%(Compile.CopyToPublishDirectory)' == ''">
        <CopyToPublishDirectory>Always</CopyToPublishDirectory>
      </Compile>
      <Compile Condition="'%(Compile.CopyToOutputDirectory)'=='PreserveNewest' and '%(Compile.CopyToPublishDirectory)' == ''">
        <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      </Compile>

      <_NoneWithTargetPath Condition="'%(_NoneWithTargetPath.CopyToOutputDirectory)'=='Always' and '%(_NoneWithTargetPath.CopyToPublishDirectory)' == ''">
        <CopyToPublishDirectory>Always</CopyToPublishDirectory>
      </_NoneWithTargetPath>
      <_NoneWithTargetPath Condition="'%(_NoneWithTargetPath.CopyToOutputDirectory)'=='PreserveNewest' and '%(_NoneWithTargetPath.CopyToPublishDirectory)' == ''">
        <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      </_NoneWithTargetPath>

    </ItemGroup>
  </Target>

  <PropertyGroup Condition="'$(SelfContained)' == 'true'">
    <_ComputeManagedRuntimePackAssembliesIfSelfContained>_ComputeManagedRuntimePackAssemblies</_ComputeManagedRuntimePackAssembliesIfSelfContained>
  </PropertyGroup>

  <!-- Determine the managed assembly subset of ResolvedFileToPublish that should be post-processed by linker, and ready to run compilation -->
  <Target Name="_ComputeAssembliesToPostprocessOnPublish" 
          DependsOnTargets="_ComputeUserRuntimeAssemblies;$(_ComputeManagedRuntimePackAssembliesIfSelfContained)">
    
    <!-- 
      Default set of files to post-process correspond to the items that would be designated
      as managed runtime assemblies in .deps.json, and published to root of the application.
      RuntimeTargets and satellite assemblies are excluded. Currently, both linker and ready
      to run require a RID, so there will not be RuntimeTargets. Linker could conceptually 
      operate without a RID, but would not know how to handle multiple assemblies with same
      identity.
    -->
    <ItemGroup>
      <!-- Assemblies from packages -->
      <_ManagedRuntimeAssembly Include="@(RuntimeCopyLocalItems)" />
      
      <!-- Assemblies from other references -->
      <_ManagedRuntimeAssembly Include="@(UserRuntimeAssembly)" />
      
      <!-- Assembly produced by this project -->
      <_ManagedRuntimeAssembly Include="@(IntermediateAssembly)" />
    </ItemGroup>

    <!-- Assemblies from runtime packs for self-contained apps -->
    <ItemGroup Condition="'$(SelfContained)' == 'true'">
      <_ManagedRuntimeAssembly Include="@(_ManagedRuntimePackAssembly)" />
    </ItemGroup>
    
    <!-- 
      Match above with ResolvedFileToPublish. Some of above would have been excluded from publish in
      various ways and should be excluded from the list of files to postprocess as well. Furthermore,
      the metadata must match ResolvedFileToPublish as the tools modify or remove these items in that
      list to implement their post-processing.
    -->
    <JoinItems Left="@(_ManagedRuntimeAssembly)" Right="@(ResolvedFileToPublish)" RightMetadata="*">
      <Output TaskParameter="JoinResult" ItemName="_AssemblyToPostprocessOnPublish" />
    </JoinItems>

    <!--
      Set PostprocessAssembly=true metadata on ResolvedFileToPublish, which will be honored by linker
      and crossgen.
      
      Assemblies injected into ResolvedFileToPublish outside the set above (such as razor views) are
      responsible for setting this metadata to opt in to post-processing.
    -->
    <ItemGroup>
      <ResolvedFileToPublish Remove="@(_AssemblyToPostprocessOnPublish)" />
      <ResolvedFileToPublish Include="@(_AssemblyToPostprocessOnPublish)" PostprocessAssembly="true" />
    </ItemGroup>
  </Target>

  <Target Name="_ComputeManagedRuntimePackAssemblies" Returns="@(_ManagedRuntimePackAssembly)">
    <ItemGroup>
      <!-- Special case for System.Private.Corelib due to https://github.com/dotnet/core-setup/issues/7728 -->
      <_ManagedRuntimePackAssembly Include="@(RuntimePackAsset)"
                                   Condition="'%(RuntimePackAsset.AssetType)' == 'runtime' 
                                                or '%(RuntimePackAsset.Filename)' == 'System.Private.Corelib'" />
    </ItemGroup>
  </Target>

  <Target Name="_ComputeUseBuildDependencyFile"
          DependsOnTargets="_ComputePackageReferencePublish;
                            _ParseTargetManifestFiles">
    <!-- Check to see whether we can re-use the .deps.json file from the build for publish, or whether we have to
         generate a different one. -->
    <PropertyGroup>
      <_UseBuildDependencyFile Condition="'@(_ExcludeFromPublishPackageReference)' == '' and
                                          '@(RuntimeStorePackages)' == '' and
                                          '$(PreserveStoreLayout)' != 'true' and
                                          '$(PublishTrimmed)' != 'true'">true</_UseBuildDependencyFile>
    </PropertyGroup>

  </Target>

  <!--
    ============================================================
                                        BundlePublishDirectory

    Bundle the _ResolvedFileToPublish* items one file in PublishDir
    (except those marked ExcludeFromSingleFile)
    ============================================================
    -->
  <Target Name="_ComputeFilesToBundle"
        Condition="'$(PublishSingleFile)' == 'true'">

    <ItemGroup>
      <_FilesToBundle Include="@(_ResolvedFileToPublishPreserveNewest)" 
                      Condition="'%(_ResolvedFileToPublishPreserveNewest.ExcludeFromSingleFile)' != 'true'"/>
      <_FilesToBundle Include="@(_ResolvedFileToPublishAlways)" 
                      Condition="'%(_ResolvedFileToPublishAlways.ExcludeFromSingleFile)' != 'true'"/>
    </ItemGroup>

    <PropertyGroup>
      <PublishedSingleFileName>$(AssemblyName)$(_NativeExecutableExtension)</PublishedSingleFileName>
      <PublishedSingleFilePath>$(PublishDir)$(PublishedSingleFileName)</PublishedSingleFilePath>
    </PropertyGroup>

  </Target>

  <UsingTask TaskName="GenerateBundle" AssemblyFile="$(MicrosoftNETBuildTasksAssembly)" />
  <Target Name="BundlePublishDirectory" 
          Condition="'$(PublishSingleFile)' == 'true'"
          DependsOnTargets="_ComputeFilesToBundle" 
          Inputs="@(_FilesToBundle)"
          Outputs="$(PublishedSingleFilePath)">

    <GenerateBundle FilesToBundle="@(_FilesToBundle)"
                    AppHostName="$(PublishedSingleFileName)"
                    IncludeSymbols="$(IncludeSymbolsInSingleFile)"
                    OutputDir="$(PublishDir)"
                    ShowDiagnosticOutput="false"/>

  </Target>

  <!--
    ============================================================
    _GeneratePublishDependencyFile
    Generates the $(project).deps.json file for a published app
    ============================================================
    -->
  <Target Name="GeneratePublishDependencyFile"
          DependsOnTargets="_ComputeUseBuildDependencyFile;
                            _DefaultMicrosoftNETPlatformLibrary;
                            _HandlePackageFileConflicts;
                            _HandlePackageFileConflictsForPublish;
                            _ComputeReferenceAssemblies;
                            _ComputeUserRuntimeAssemblies;
                            ResolveRuntimePackAssets;
                            _ComputePackageReferencePublish"
          Condition="'$(GenerateDependencyFile)' == 'true' and '$(_UseBuildDependencyFile)' != 'true'">

    <PropertyGroup>
      <PublishDepsFilePath Condition=" '$(PublishDepsFilePath)' == '' And '$(PublishSingleFile)' != 'true'">$(PublishDir)$(ProjectDepsFileName)</PublishDepsFilePath>
      <PublishDepsFilePath Condition=" '$(PublishDepsFilePath)' == '' And '$(PublishSingleFile)' == 'true'">$(IntermediateOutputPath)$(ProjectDepsFileName)</PublishDepsFilePath>
    </PropertyGroup>

    <ItemGroup>
      <ResolvedCompileFileDefinitions Remove="@(_PublishConflictPackageFiles)" Condition="'%(_PublishConflictPackageFiles.ConflictItemType)' == 'Reference'" />
      <RuntimeTargetsCopyLocalItems Remove="@(_PublishConflictPackageFiles)" Condition="'%(_PublishConflictPackageFiles.ConflictItemType)' != 'Reference'" />
      <RuntimePackAsset Remove="@(_PublishConflictPackageFiles)" Condition="'%(_PublishConflictPackageFiles.ConflictItemType)' != 'Reference'" />

      <_ResolvedNuGetFilesForPublish Include="@(NativeCopyLocalItems)" Condition="'%(NativeCopyLocalItems.CopyToPublishDirectory)' != 'false'" />
      <_ResolvedNuGetFilesForPublish Include="@(ResourceCopyLocalItems)" Condition="'%(ResourceCopyLocalItems.CopyToPublishDirectory)' != 'false'" />
      <_ResolvedNuGetFilesForPublish Include="@(RuntimeCopyLocalItems)" Condition="'%(RuntimeCopyLocalItems.CopyToPublishDirectory)' != 'false'" />
      <_ResolvedNuGetFilesForPublish Remove="@(_PublishConflictPackageFiles)" Condition="'%(_PublishConflictPackageFiles.ConflictItemType)' != 'Reference'" />
    </ItemGroup>

    <GenerateDepsFile ProjectPath="$(MSBuildProjectFullPath)"
                      AssetsFilePath="$(ProjectAssetsFile)"
                      DepsFilePath="$(PublishDepsFilePath)"
                      TargetFramework="$(TargetFrameworkMoniker)"
                      AssemblyName="$(AssemblyName)"
                      AssemblyExtension="$(TargetExt)"
                      AssemblyVersion="$(Version)"
                      AssemblySatelliteAssemblies="@(IntermediateSatelliteAssembliesWithTargetPath)"
                      ReferencePaths="@(ReferencePath)"
                      ReferenceDependencyPaths="@(ReferenceDependencyPaths)"
                      ReferenceSatellitePaths="@(ReferenceSatellitePaths)"
                      ReferenceAssemblies="@(_ReferenceAssemblies)"
                      RuntimePackAssets="@(RuntimePackAsset)"
                      IncludeMainProject="$(IncludeMainProjectInDepsFile)"
                      RuntimeIdentifier="$(RuntimeIdentifier)"
                      PlatformLibraryName="$(MicrosoftNETPlatformLibrary)"
                      RuntimeFrameworks="@(RuntimeFramework)"
                      CompilerOptions="@(DependencyFileCompilerOptions)"
                      RuntimeStorePackages="@(RuntimeStorePackages)"
                      CompileReferences="@(ResolvedCompileFileDefinitions)"
                      ResolvedNuGetFiles="@(_ResolvedNuGetFilesForPublish)"
                      ResolvedRuntimeTargetsFiles="@(RuntimeTargetsCopyLocalItems)"
                      UserRuntimeAssemblies="@(UserRuntimeAssembly)"
                      IsSelfContained="$(SelfContained)"
                      IncludeRuntimeFileVersions="$(IncludeFileVersionsInDependencyFile)"
                      RuntimeGraphPath="$(BundledRuntimeIdentifierGraphFile)"/>

    <ItemGroup>
      <_FilesToBundle Include="$(PublishDepsFilePath)" Condition="'$(PublishSingleFile)' == 'true'">
        <RelativePath>$(ProjectDepsFileName)</RelativePath>
      </_FilesToBundle>
    </ItemGroup>
    
  </Target>

  <!--
    ============================================================
                                        ComputeEmbeddedApphostPaths

    When no build flag is set, EmbeddedApphostPaths is not available. Compute EmbeddedApphostPaths is required to find build asset.
    ============================================================
    -->
  <UsingTask TaskName="Microsoft.NET.Build.Tasks.GetEmbeddedApphostPaths"
          AssemblyFile="$(MicrosoftNETBuildTasksAssembly)" />
  <Target Name="ComputeEmbeddedApphostPaths">

    <ItemGroup>
      <_PackAsToolShimRuntimeIdentifiers Condition="@(_PackAsToolShimRuntimeIdentifiers) ==''" Include="$(PackAsToolShimRuntimeIdentifiers)"/>
    </ItemGroup>

    <GetEmbeddedApphostPaths
      PackagedShimOutputDirectory="$(PackagedShimOutputRootDirectory)/shims/$(TargetFramework)"
      ShimRuntimeIdentifiers="@(_PackAsToolShimRuntimeIdentifiers)"
      ToolCommandName="$(ToolCommandName)"
      >

      <Output TaskParameter="EmbeddedApphostPaths" ItemName="_EmbeddedApphostPaths" />
    </GetEmbeddedApphostPaths>

  </Target>

  <!--
    ============================================================
                                        ComputeFilesCopiedToPublishDir

    Gathers all the files that will be copied to the publish directory.
    ============================================================
    -->
  <Target Name="ComputeFilesCopiedToPublishDir"
          DependsOnTargets="_ComputeResolvedFilesToPublishTypes;
                            _ComputeFilesToBundle">

    <ItemGroup>
      <FilesCopiedToPublishDir Include="@(_ResolvedUnbundledFileToPublishPreserveNewest)"/>
      <FilesCopiedToPublishDir Include="@(_ResolvedUnbundledFileToPublishAlways)"/>
      <FilesCopiedToPublishDir Include="$(PublishedSingleFilePath)" RelativePath="$(PublishedSingleFileName)" IsKeyOutput="true" Condition="'$(PublishSingleFile)' == 'true'"/>
    </ItemGroup>

  </Target>

  <!--
    ============================================================
                                            PublishItemsOutputGroup

    Emit an output group containing all files that get published.  This will be consumed by VS installer projects.
    ============================================================
    -->
  <PropertyGroup>
    <PublishItemsOutputGroupDependsOn>
      $(PublishItemsOutputGroupDependsOn);
      ResolveReferences;
      ComputeFilesToPublish;
      ComputeFilesCopiedToPublishDir;
    </PublishItemsOutputGroupDependsOn>
  </PropertyGroup>

  <Target Name="PublishItemsOutputGroup" DependsOnTargets="$(PublishItemsOutputGroupDependsOn)" Returns="@(PublishItemsOutputGroupOutputs)">
    <ItemGroup>
      <PublishItemsOutputGroupOutputs Condition="'$(GenerateDependencyFile)' == 'true' and '$(_UseBuildDependencyFile)' != 'true' and '$(PublishSingleFile)' != 'true'"
                                      Include="$(PublishDepsFilePath)"
                                      TargetPath="$(ProjectDepsFileName)" />
      <PublishItemsOutputGroupOutputs Include="@(FilesCopiedToPublishDir->'%(FullPath)')"
                                      TargetPath="%(FilesCopiedToPublishDir.RelativePath)"
                                      IsKeyOutput="%(FilesCopiedToPublishDir.IsKeyOutput)"
                                      OutputGroup="PublishItemsOutputGroup" />
    </ItemGroup>
  </Target>

  <!--
    This target exists for back-compat with Azure Functions SDK: https://github.com/dotnet/cli/issues/10363
    Because build copy-local now behaves the same as publish with respect to package dependency resolution,
    the Azure Functions SDK doesn't need to resolve publish assets for build.
    TODO: Remove this target when no longer needed as a workaround.
    -->
  <Target Name="RunResolvePublishAssemblies" />

  <!--
    ============================================================
    _CheckForLanguageAndPublishFeatureCombinationSupport
    
    Block unsupported language and feature combination.
    ============================================================
    -->
  <Target Name="_CheckForLanguageAndPublishFeatureCombinationSupport"
        BeforeTargets="Publish;PrepareForPublish">

    <NETSdkError Condition="'$(Language)' == 'C++' and '$(TargetFrameworkIdentifier)' == '.NETCoreApp'"
                 ResourceName="NoSupportCppPublishDotnetCore" />

  </Target>
</Project>
