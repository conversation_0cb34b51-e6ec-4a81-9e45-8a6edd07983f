<!--
***********************************************************************************************
Microsoft.NET.Sdk.BeforeCommon.targets

WARNING:  DO NOT MODIFY this file unless you are knowledgeable about MSBuild and have
          created a backup copy.  Incorrect changes to this file will make it
          impossible to load or build your projects from the command-line or the IDE.

Copyright (c) .NET Foundation. All rights reserved. 
***********************************************************************************************
-->
<Project ToolsVersion="14.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">

  <!-- For projects that aren't using Microsoft.NET.Sdk, these props files won't have been imported yet.
       So import them here. -->
  <ImportGroup Condition="'$(UsingNETSdkDefaults)' != 'true'">
    <Import Project="Microsoft.NET.Sdk.DefaultItems.props" />
    <Import Project="Microsoft.NET.SupportedTargetFrameworks.props" />
  </ImportGroup>
  
  <PropertyGroup>
    <_IsExecutable Condition="'$(OutputType)' == 'Exe' or '$(OutputType)'=='WinExe'">true</_IsExecutable>
  </PropertyGroup>

  <PropertyGroup Condition="'$(HasRuntimeOutput)' == ''">
    <HasRuntimeOutput>$(_IsExecutable)</HasRuntimeOutput>
    <_UsingDefaultForHasRuntimeOutput>true</_UsingDefaultForHasRuntimeOutput>
  </PropertyGroup>

  <Import Project="$(MSBuildThisFileDirectory)Microsoft.NET.DefaultAssemblyInfo.targets" Condition="'$(UsingNETSdkDefaults)' == 'true'"/>

  <!-- Set default intermediate and output paths -->
  <Import Project="$(MSBuildThisFileDirectory)Microsoft.NET.DefaultOutputPaths.targets" Condition="'$(UsingNETSdkDefaults)' == 'true'"/>
  
  <!-- Before any additional SDK targets are imported, import the publish profile.
       This allows the publish profile to set properties like RuntimeIdentifier and them be
       respected by the SDK. -->
  <Import Project="$(MSBuildThisFileDirectory)Microsoft.NET.Sdk.ImportPublishProfile.targets"
          Condition="'$(PublishProfileImported)' != 'true'"/>

  <!-- 
    Expand TargetFramework to TargetFrameworkIdentifier and TargetFrameworkVersion,
    and adjust intermediate and output paths to include it.
  -->
  <Import Project="$(MSBuildThisFileDirectory)Microsoft.NET.TargetFrameworkInference.targets" />

  <!-- For .NET Framework, reference core assemblies -->
  <PropertyGroup>
    <_TargetFrameworkVersionWithoutV>$(TargetFrameworkVersion.TrimStart('vV'))</_TargetFrameworkVersionWithoutV>
  </PropertyGroup>

  <!--
    Use RuntimeIdentifier to determine PlatformTarget.
    Also, enforce that RuntimeIdentifier is always specified for .NETFramework executables.
  -->
  <Import Project="$(MSBuildThisFileDirectory)Microsoft.NET.RuntimeIdentifierInference.targets" />

  <PropertyGroup Condition="'$(_IsNETCoreOrNETStandard)' == ''">
    <_IsNETCoreOrNETStandard Condition="'$(TargetFrameworkIdentifier)' == '.NETCoreApp'">true</_IsNETCoreOrNETStandard>
    <_IsNETCoreOrNETStandard Condition="'$(TargetFrameworkIdentifier)' == '.NETStandard'">true</_IsNETCoreOrNETStandard>
  </PropertyGroup>

  <!-- Unification / automatic binding redirect logic -->
  <PropertyGroup>
    <DesignTimeAutoUnify Condition="'$(DesignTimeAutoUnify)' == ''">true</DesignTimeAutoUnify>
    <AutoUnifyAssemblyReferences Condition="'$(AutoUnifyAssemblyReferences)' == '' and $(OutputType) == 'Library'">true</AutoUnifyAssemblyReferences>
    <AutoUnifyAssemblyReferences Condition="'$(AutoUnifyAssemblyReferences)' == '' and '$(_IsNETCoreOrNETStandard)' == 'true'">true</AutoUnifyAssemblyReferences>
  </PropertyGroup>
  <PropertyGroup Condition="'$(TargetFrameworkIdentifier)' == '.NETFramework' and '$(HasRuntimeOutput)' == 'true'">
    <AutoGenerateBindingRedirects Condition="'$(AutoGenerateBindingRedirects)' == ''">true</AutoGenerateBindingRedirects>
  </PropertyGroup>

  <!-- Default settings for .NET Core and .NET Standard build logic -->
  <PropertyGroup Condition="'$(_IsNETCoreOrNETStandard)' == 'true'">
    <GenerateDependencyFile Condition=" '$(GenerateDependencyFile)' == '' ">true</GenerateDependencyFile>

    <!-- Assembly and file versions of runtime assets should be written to the deps.json by default, to support
         runtime minor version roll-forward: https://github.com/dotnet/core-setup/issues/3546 -->
    <IncludeFileVersionsInDependencyFile Condition="'$(IncludeFileVersionsInDependencyFile)' == ''">true</IncludeFileVersionsInDependencyFile>

    <!-- Force .dll extension for .NETCoreApp and .NETStandard projects even if output type is exe. -->
    <TargetExt Condition="'$(TargetExt)' == ''">.dll</TargetExt>

    <!-- Disable the use of FrameworkPathOverride in Microsoft.Common.CurrentVersion.targets which can slow down evaluation.  FrameworkPathOverride
    is not needed for NETStandard or NETCore since references come from NuGet packages-->
    <EnableFrameworkPathOverride Condition="'$(EnableFrameworkPathOverride)' == ''">false</EnableFrameworkPathOverride>
  </PropertyGroup>
  
  <!-- Regardless of platform, enable dependency file generation if PreserveCompilationContext is set. -->
  <PropertyGroup>
    <GenerateDependencyFile Condition="'$(GenerateDependencyFile)' == ''">$(PreserveCompilationContext)</GenerateDependencyFile>
  </PropertyGroup>

  <!-- Set PublishDir here, before Microsoft.Common.targets, to avoid a competing default there. -->
  <PropertyGroup>
    <PublishDirName Condition="'$(PublishDirName)' == ''">publish</PublishDirName>
    <!-- ensure the PublishDir is RID specific-->
    <PublishDir Condition="'$(PublishDir)' == '' and
                           '$(AppendRuntimeIdentifierToOutputPath)' != 'true' and
                           '$(RuntimeIdentifier)' != '' and
                           '$(_UsingDefaultRuntimeIdentifier)' != 'true'">$(OutputPath)$(RuntimeIdentifier)\$(PublishDirName)\</PublishDir>
    <PublishDir Condition="'$(PublishDir)' == ''">$(OutputPath)$(PublishDirName)\</PublishDir>
  </PropertyGroup>

  <!--
    Sets RestoreAdditionalProjectSources or RestoreAdditionalProjectFallbackFolders to the SDK Offline Cache based
    on the TargetFramework.
  -->
  <Import Project="$(MSBuildThisFileDirectory)Microsoft.NET.NuGetOfflineCache.targets" />
  
  <ItemGroup Condition=" '$(DisableImplicitFrameworkReferences)' != 'true' and '$(TargetFrameworkIdentifier)' == '.NETFramework'">

    <_SDKImplicitReference Include="System"/>
    <_SDKImplicitReference Include="System.Data"/>
    <_SDKImplicitReference Include="System.Drawing"/>
    <_SDKImplicitReference Include="System.Xml"/>

    <!-- When doing greater than/less than comparisons between strings, MSBuild will try to parse the strings as Version objects and compare them as
         such if the parse succeeds. -->
    
    <!-- Framework assemblies introduced in .NET 3.5 -->
    <_SDKImplicitReference Include="System.Core" Condition=" '$(_TargetFrameworkVersionWithoutV)' >= '3.5' "/>
    <_SDKImplicitReference Include="System.Runtime.Serialization" Condition=" '$(_TargetFrameworkVersionWithoutV)' >= '3.5' "/>
    <_SDKImplicitReference Include="System.Xml.Linq" Condition=" '$(_TargetFrameworkVersionWithoutV)' >= '3.5' "/>

    <!-- Framework assemblies introduced in .NET 4.0 -->
    <_SDKImplicitReference Include="System.Numerics" Condition=" '$(_TargetFrameworkVersionWithoutV)' >= '4.0' "/>

    <!-- Framework assemblies introduced in .NET 4.5 -->
    <_SDKImplicitReference Include="System.IO.Compression.FileSystem" Condition=" '$(_TargetFrameworkVersionWithoutV)' >= '4.5' "/>
    
    <!-- Don't automatically reference System.IO.Compression or System.Net.Http to help avoid hitting https://github.com/Microsoft/msbuild/issues/1329. -->
    <!--<Reference Include="System.IO.Compression" Condition=" '$(_TargetFrameworkVersionWithoutV)' >= '4.5' "/>
    <_SDKImplicitReference Include="System.Net.Http" Condition=" '$(_TargetFrameworkVersionWithoutV)' >= '4.5' "/>-->

    <_SDKImplicitReference Update="@(_SDKImplicitReference)"
                           Pack="false"
                           IsImplicitlyDefined="true" />

    <!-- Don't duplicate any references that are explicit in the project file.  This means that if you do want to include a framework assembly in your
         NuGet package, you can just add the Reference to your project file. -->
    <_SDKImplicitReference Remove="@(Reference)" />

    <!-- Add the implicit references to @(Reference) -->
    <Reference Include="@(_SDKImplicitReference)" />
  </ItemGroup>

  <PropertyGroup>
    <!-- Prevent System.Core reference from being added separately (see Microsoft.NETFramework.CurrentVersion.props) -->
    <AddAdditionalExplicitAssemblyReferences>false</AddAdditionalExplicitAssemblyReferences>
  </PropertyGroup>

  <PropertyGroup Condition="'$(DisableImplicitAssetTargetFallback)' != 'true' and '$(_IsNETCoreOrNETStandard)' == 'true' and '$(_TargetFrameworkVersionWithoutV)' >= '2.0'">
    <AssetTargetFallback>$(AssetTargetFallback);net461;net462;net47;net471;net472;net48</AssetTargetFallback>
  </PropertyGroup>

  <!-- Add conditional compilation symbols for the target framework (for example NET461, NETSTANDARD2_0, NETCOREAPP1_0) -->
  <PropertyGroup Condition=" '$(DisableImplicitFrameworkDefines)' != 'true' and '$(TargetFrameworkIdentifier)' != '.NETPortable' and '$(TargetFrameworkIdentifier)' != ''">
    <_FrameworkIdentifierForImplicitDefine>$(TargetFrameworkIdentifier.Replace('.', '').ToUpperInvariant())</_FrameworkIdentifierForImplicitDefine>
    <VersionlessImplicitFrameworkDefine>$(_FrameworkIdentifierForImplicitDefine)</VersionlessImplicitFrameworkDefine>
    <_FrameworkIdentifierForImplicitDefine Condition=" '$(TargetFrameworkIdentifier)' == '.NETFramework'">NET</_FrameworkIdentifierForImplicitDefine>

    <_FrameworkVersionForImplicitDefine>$(TargetFrameworkVersion.TrimStart('vV'))</_FrameworkVersionForImplicitDefine>

    <_FrameworkVersionForImplicitDefine>$(_FrameworkVersionForImplicitDefine.Replace('.', '_'))</_FrameworkVersionForImplicitDefine>
    
    <_FrameworkVersionForImplicitDefine Condition=" '$(TargetFrameworkIdentifier)' == '.NETFramework'">$(_FrameworkVersionForImplicitDefine.Replace('_', ''))</_FrameworkVersionForImplicitDefine>

    <ImplicitFrameworkDefine>$(_FrameworkIdentifierForImplicitDefine)$(_FrameworkVersionForImplicitDefine)</ImplicitFrameworkDefine>
  </PropertyGroup>

  <!-- Handle XML documentation file settings -->
  <PropertyGroup Condition="'$(GenerateDocumentationFile)' == ''">
    <GenerateDocumentationFile Condition="'$(DocumentationFile)' == ''">false</GenerateDocumentationFile>
    <GenerateDocumentationFile Condition="'$(DocumentationFile)' != ''">true</GenerateDocumentationFile>
  </PropertyGroup>

  <PropertyGroup Condition="'$(GenerateDocumentationFile)' == 'true' and '$(DocumentationFile)' == ''">
    <DocumentationFile Condition="'$(MSBuildProjectExtension)' == '.vbproj'">$(AssemblyName).xml</DocumentationFile>
    <DocumentationFile Condition="'$(MSBuildProjectExtension)' != '.vbproj'">$(IntermediateOutputPath)$(AssemblyName).xml</DocumentationFile>
  </PropertyGroup>

  <PropertyGroup Condition="'$(GenerateDocumentationFile)' != 'true'">
    <DocumentationFile />
  </PropertyGroup>

  <PropertyGroup>
    <PublishDocumentationFiles Condition="'$(PublishDocumentationFiles)' == ''">true</PublishDocumentationFiles>
    <PublishDocumentationFile Condition="'$(PublishDocumentationFile)' == '' and '$(PublishDocumentationFiles)' == 'true'">true</PublishDocumentationFile>
    <PublishReferencesDocumentationFiles Condition="'$(PublishReferencesDocumentationFiles)' == '' and '$(PublishDocumentationFiles)' == 'true'">true</PublishReferencesDocumentationFiles>
  </PropertyGroup>

  <!-- Add a project capability so that the project properties in the IDE can show the option to generate an XML documentation file without specifying the filename -->
  <ItemGroup>
    <ProjectCapability Include="GenerateDocumentationFile" />
  </ItemGroup>

</Project>
