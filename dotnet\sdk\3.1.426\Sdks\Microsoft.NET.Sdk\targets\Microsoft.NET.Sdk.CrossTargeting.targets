<!--
***********************************************************************************************
Microsoft.NET.Sdk.targets

WARNING:  DO NOT MODIFY this file unless you are knowledgeable about MSBuild and have
          created a backup copy.  Incorrect changes to this file will make it
          impossible to load or build your projects from the command-line or the IDE.

Copyright (c) .NET Foundation. All rights reserved.
***********************************************************************************************
-->
<Project ToolsVersion="14.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">

  <Import Project="Microsoft.NET.Sdk.Common.targets"/>

  <!--
  ============================================================
                              Publish

   This is the Publish target for cross-targeting.
   Currently it is unsupported to publish for multiple target frameworks
   because users can specify the $(PublishDir), and publish would put
   multiple published applications in a single directory.
  ============================================================
   -->
  <Target Name="Publish">
    <Error Text="The 'Publish' target is not supported without specifying a target framework. The current project targets multiple frameworks, please specify the framework for the published application." />
  </Target>

  <!--
  ============================================================
                                      GetAllRuntimeIdentifiers

  Outer build implementation of GetAllRuntimeIdentifiers returns
  a union of all runtime identifiers used across inner and outer
  build evaluations.

  It is further set to run before '_GenerateRestoreProjectSpec'
  (note that running only 'Restore' is too late and will not work
  with solution level restore). This ensures that any conditioning
  of runtime  identifiers against TargetFramework does not prevent
  restore from providing  the necessary RID-specific assets for all
  inner builds.

  It also brings parity to VS vs. command line behavior in this
  scenario because VS passes all of the information from each
  configured inner build to restore, whereas command-line restore
  without this target would only use the runtime identifiers that
  are statically set in the outer evaluation.
  ============================================================
  -->
  <Target Name="GetAllRuntimeIdentifiers"
          Returns="$(RuntimeIdentifiers)"
          BeforeTargets="_GenerateRestoreProjectSpec">

    <ItemGroup>
      <_GetAllRuntimeIdentifiersTargetFrameworks Include="$(TargetFrameworks)" />
      <_AllRuntimeIdentifiers Include="$(RuntimeIdentifiers);$(RuntimeIdentifier)" />
    </ItemGroup>

    <MSBuild Projects="$(MSBuildProjectFile)"
             Targets="GetAllRuntimeIdentifiers"
             Properties="TargetFramework=%(_GetAllRuntimeIdentifiersTargetFrameworks.Identity)">
      <Output ItemName="_AllRuntimeIdentifiers" TaskParameter="TargetOutputs" />
    </MSBuild>

    <PropertyGroup>
      <RuntimeIdentifiers>@(_AllRuntimeIdentifiers->Distinct())</RuntimeIdentifiers>
    </PropertyGroup>
  </Target>

  <!--
  ============================================================
                       GetPackagingOutputs

  Stub cross-targeting implementation of GetPackagingOutputs 
  to allow project references from from projects that pull in 
  Microsoft.AppxPackage.targets (UWP, PCL) to cross-targeted
  projects.

  Ultimately, the appx targets should be modified to use the
  same P2P TFM negotiation protocol as Microsoft.Common.targets
  so that they can forward to the TFM-specific GetPackagingOutputs
  of the appropriate inner build. This stub would not have any
  bad interaction with that change, which would happily bypass
  this implementation altogether.

  An empty GetPackagingOutputs is sufficient for the common
  case of a library with no special assets to contribute to
  the appx and is also equivalent to what is present in the
  single-targeted case unless WindowsAppContainer is not set 
  to true.

  Furthermore, the appx targets currently use continue-on-error
  such that even without this, clean builds succeed but log an 
  error and incremental builds silently succeed. As such, this 
  simply removes a confounding error from successful clean
  builds.

  ============================================================
  -->
  <Target Name="GetPackagingOutputs" />

  <!-- This exists as a workaround for https://github.com/Microsoft/msbuild/issues/3558 -->
  <PropertyGroup Condition="'$(DefaultProjectTypeGuid)' == ''">
    <DefaultProjectTypeGuid Condition="'$(MSBuildProjectExtension)' == '.csproj'">{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}</DefaultProjectTypeGuid>
    <DefaultProjectTypeGuid Condition="'$(MSBuildProjectExtension)' == '.vbproj'">{F184B08F-C81C-45F6-A57F-5ABD9991F28F}</DefaultProjectTypeGuid>
    <!-- Note: F# sets DefaultProjectTypeGuid in the F# SDK -->
  </PropertyGroup>

</Project>
