<!--
***********************************************************************************************
Microsoft.NET.Sdk.DefaultItems.props

WARNING:  DO NOT MODIFY this file unless you are knowledgeable about MSBuild and have
          created a backup copy.  Incorrect changes to this file will make it
          impossible to load or build your projects from the command-line or the IDE.

Copyright (c) .NET Foundation. All rights reserved. 
***********************************************************************************************
-->
<Project xmlns="http://schemas.microsoft.com/developer/msbuild/2003">

  <PropertyGroup Condition="'$(NETCoreSdkBundledVersionsProps)' == ''">
    <NETCoreSdkBundledVersionsProps>$(MSBuildThisFileDirectory)..\..\..\Microsoft.NETCoreSdk.BundledVersions.props</NETCoreSdkBundledVersionsProps>
  </PropertyGroup>

  <Import Project="$(NETCoreSdkBundledVersionsProps)" Condition="Exists('$(NETCoreSdkBundledVersionsProps)')" />

  <PropertyGroup>
    <!-- Set RuntimeIdentifier graph for NuGet (this needs to be after NETCoreSdkBundledVersionsProps is imported, as that's where
         BundledRuntimeIdentifierGraphFile is set. -->
    <RuntimeIdentifierGraphPath Condition="'$(RuntimeIdentifierGraphPath)' == ''">$(BundledRuntimeIdentifierGraphFile)</RuntimeIdentifierGraphPath>
  </PropertyGroup>
  
  <PropertyGroup>
    <!-- Disable web SDK implicit package versions for ASP.NET packages, since the .NET SDK now handles that -->
    <EnableWebSdkImplicitPackageVersions>false</EnableWebSdkImplicitPackageVersions>
  </PropertyGroup>


  <ItemGroup Condition=" '$(EnableDefaultItems)' == 'true' ">
    <Compile Include="**/*$(DefaultLanguageSourceExtension)" Exclude="$(DefaultItemExcludes);$(DefaultExcludesInProjectFolder)" Condition=" '$(EnableDefaultCompileItems)' == 'true' " />
    <EmbeddedResource Include="**/*.resx" Exclude="$(DefaultItemExcludes);$(DefaultExcludesInProjectFolder)" Condition=" '$(EnableDefaultEmbeddedResourceItems)' == 'true' " />
  </ItemGroup>
  <ItemGroup Condition=" '$(EnableDefaultItems)' == 'true' And '$(EnableDefaultNoneItems)' == 'true' ">
    <None Include="**/*" Exclude="$(DefaultItemExcludes);$(DefaultExcludesInProjectFolder)" />
    <None Remove="**/*$(DefaultLanguageSourceExtension)" />
    <None Remove="**/*.resx" />
  </ItemGroup>

  <!-- Automatically reference NETStandard.Library or Microsoft.NETCore.App package if targeting the corresponding target framework.
      We can refer here in the .props file to properties set in the .targets files because items and their conditions are
      evaluated in the second pass of evaluation, after all properties have been evaluated. -->
  <ItemGroup Condition=" '$(DisableImplicitFrameworkReferences)' != 'true' and '$(TargetFrameworkIdentifier)' == '.NETStandard' And '$(_TargetFrameworkVersionWithoutV)' &lt; '2.1'">
    <PackageReference Include="NETStandard.Library" Version="$(NETStandardImplicitPackageVersion)" IsImplicitlyDefined="true" />

    <!-- If targeting .NET Standard 2.0 or higher, then don't include a dependency on NETStandard.Library in the package produced by pack -->
    <PackageReference Update="NETStandard.Library"
                      Condition=" ('$(_TargetFrameworkVersionWithoutV)' != '') And ('$(_TargetFrameworkVersionWithoutV)' >= '2.0') "
                      PrivateAssets="All" 
                      Publish="true" />
  </ItemGroup>
  <ItemGroup Condition=" '$(DisableImplicitFrameworkReferences)' != 'true' and '$(TargetFrameworkIdentifier)' == '.NETStandard' And '$(_TargetFrameworkVersionWithoutV)' >= '2.1'">
    <FrameworkReference Include="NETStandard.Library" IsImplicitlyDefined="true" Pack="false" PrivateAssets="All"/>
  </ItemGroup>

  <ItemGroup Condition=" '$(DisableImplicitFrameworkReferences)' != 'true' and '$(TargetFrameworkIdentifier)' == '.NETCoreApp'">
    
    <!-- Use implicit PackageReference for Microsoft.NETCore.App on versions prior to 3.0.  For 3.0 or higher, use
         an implicit FrameworkReference -->
    
    <PackageReference Include="Microsoft.NETCore.App" IsImplicitlyDefined="true" 
                      Condition="('$(_TargetFrameworkVersionWithoutV)' != '') And ('$(_TargetFrameworkVersionWithoutV)' &lt; '3.0')"/>

    <!-- For targeting .NET Core 2.0 or higher, don't include a dependency on Microsoft.NETCore.App in the package produced by pack.
         Packing an DotnetCliTool should include the Microsoft.NETCore.App package dependency. -->
    <PackageReference Update="Microsoft.NETCore.App"
                      Condition="('$(_TargetFrameworkVersionWithoutV)' != '') And ('$(_TargetFrameworkVersionWithoutV)' >= '2.0') And ('$(PackageType)' != 'DotnetCliTool')"
                      PrivateAssets="All"
                      Publish="true" />

    <FrameworkReference Include="Microsoft.NETCore.App" IsImplicitlyDefined="true"
                        Pack="false"
                        PrivateAssets="All"
                    Condition="('$(_TargetFrameworkVersionWithoutV)' != '') And ('$(_TargetFrameworkVersionWithoutV)' >= '3.0')"/>

  </ItemGroup>

</Project>
