<!--
***********************************************************************************************
Microsoft.NET.Sdk.DefaultItems.targets

WARNING:  DO NOT MODIFY this file unless you are knowledgeable about MSBuild and have
          created a backup copy.  Incorrect changes to this file will make it
          impossible to load or build your projects from the command-line or the IDE.

Copyright (c) .NET Foundation. All rights reserved. 
***********************************************************************************************
-->
<Project xmlns="http://schemas.microsoft.com/developer/msbuild/2003">

  <!-- Don't get the default item globs by default when the flag is not set. -->
  <PropertyGroup Condition="'$(UsingNETSdkDefaults)' == 'true'">
   <EnableDefaultItems Condition=" '$(EnableDefaultItems)' == '' ">true</EnableDefaultItems>
   <EnableDefaultCompileItems Condition=" '$(EnableDefaultCompileItems)' == '' ">true</EnableDefaultCompileItems>
    <EnableDefaultEmbeddedResourceItems Condition=" '$(EnableDefaultEmbeddedResourceItems)' == '' ">true</EnableDefaultEmbeddedResourceItems>
    <EnableDefaultNoneItems Condition=" '$(EnableDefaultNoneItems)' == '' ">true</EnableDefaultNoneItems>
  </PropertyGroup>

  <PropertyGroup>    
    <!-- Set DefaultItemExcludes property for items that should be excluded from the default Compile, etc items.
         This is in the .targets because it needs to come after the final BaseOutputPath has been evaluated. -->
    
    <!-- bin folder, by default -->
    <DefaultItemExcludes>$(DefaultItemExcludes);$(BaseOutputPath)/**</DefaultItemExcludes>
    <!-- obj folder, by default -->
    <DefaultItemExcludes>$(DefaultItemExcludes);$(BaseIntermediateOutputPath)/**</DefaultItemExcludes>

    <!-- Various files that should generally always be ignored -->
    <DefaultItemExcludes>$(DefaultItemExcludes);**/*.user</DefaultItemExcludes>
    <DefaultItemExcludes>$(DefaultItemExcludes);**/*.*proj</DefaultItemExcludes>
    <DefaultItemExcludes>$(DefaultItemExcludes);**/*.sln</DefaultItemExcludes>
    <DefaultItemExcludes>$(DefaultItemExcludes);**/*.vssscc</DefaultItemExcludes>
    
    <!-- WARNING: This pattern is there to ignore folders such as .git and .vs, but it will also match items included with a
         relative path outside the project folder (for example "..\Shared\Shared.cs").  So be sure only to apply it to items
         that are in the project folder. -->
    <DefaultExcludesInProjectFolder>$(DefaultItemExcludesInProjectFolder);**/.*/**</DefaultExcludesInProjectFolder>
   
  </PropertyGroup>

  <!-- Set the default versions of the NETStandard.Library or Microsoft.NETCore.App packages to reference.
       The implicit package references themselves are defined in Microsoft.NET.Sdk.props, so that they can be overridden
       in the project file. -->
  <PropertyGroup Condition="'$(TargetFrameworkIdentifier)' == '.NETStandard' And '$(_TargetFrameworkVersionWithoutV)' &lt; '2.1'">
    <!-- If targeting .NET Standard 1.x, use version 1.6.1 of the package.  This is so that when projects are packed, the dependency on the package produced won't change when
         updating to the 2.0 or higher SDK.  When targeting .NET Standard 2.0 or higher, the NETStandard.Library reference won't show up as a dependency of the package
         produced, so we will roll forward to the latest version. -->
    <NETStandardImplicitPackageVersion Condition="'$(NETStandardImplicitPackageVersion)' =='' And '$(_TargetFrameworkVersionWithoutV)' &lt; '2.0'">1.6.1</NETStandardImplicitPackageVersion>
    
    <!-- Default to use the latest stable 2.0.x release. -->
    <NETStandardImplicitPackageVersion Condition="'$(NETStandardImplicitPackageVersion)' ==''">2.0.3</NETStandardImplicitPackageVersion>
  </PropertyGroup>

  <!--
    Determine the version (including patch) of .NET Core to target.
    
    When targeting .NET Core, the TargetFramework is used to specify the major and minor version of the runtime to use.  By default,
    the patch version is inferred.  The general logic is that self-contained apps will target the latest patch that the SDK
    knows about, while framework-dependent apps will target the ".0" patch (and roll forward to the latest patch installed at
    runtime).
    
    When targeting .NET Core 1.0 and 1.1, framework-dependent apps use 1.0.5 and 1.1.2, respectively.  This is done for compatibility
    with previous releases that bumped the self-contained and framework-dependent versions together.
    
    The TargetLatestRuntimePatch property can be set to true or false to explicitly opt in or out of the logic to roll forward
    to the latest patch, regardless of whether the app is self-contained or framework-dependent.
    
    The RuntimeFrameworkVersion is where the actual version of the .NET Core runtime to target can be set.  If set, it will be
    used in the implicit PackageReference to Microsoft.NETCore.App.  
    
    The framework version that is written to the runtimeconfig.json file is based on the actual resolved package version
    of Microsoft.NETCore.App.  This is to allow floating the verion number (ie the RuntimeFrameworkVersion could be set to
    "2.0-*".
  
  -->
  
  <PropertyGroup Condition="'$(TargetLatestRuntimePatch)' == ''">
    <TargetLatestRuntimePatch Condition="'$(SelfContained)' == 'true' ">true</TargetLatestRuntimePatch>
    <TargetLatestRuntimePatch Condition="'$(SelfContained)' != 'true' ">false</TargetLatestRuntimePatch>
    <_TargetLatestRuntimePatchIsDefault>true</_TargetLatestRuntimePatchIsDefault>
  </PropertyGroup>

  <PropertyGroup Condition="'$(TargetFrameworkIdentifier)' == '.NETCoreApp' And '$(DisableImplicitFrameworkReferences)' != 'true'">
    <VerifyMatchingImplicitPackageVersion Condition="'$(VerifyMatchingImplicitPackageVersion)' == ''">true</VerifyMatchingImplicitPackageVersion>
  </PropertyGroup>

  <ItemGroup>
    <!-- Set implicit metadata on ASP.NET package references -->
    <PackageReference Update="Microsoft.AspNetCore.App">
      <PrivateAssets Condition="'%(PackageReference.Version)' == ''">all</PrivateAssets>
      <Publish Condition="'%(PackageReference.Version)' == ''">true</Publish>
    </PackageReference>
  
    <PackageReference Update="Microsoft.AspNetCore.All">
      <PrivateAssets Condition="'%(PackageReference.Version)' == ''">all</PrivateAssets>
      <Publish Condition="'%(PackageReference.Version)' == ''">true</Publish>
    </PackageReference>
  
    <!-- Allow RuntimeFrameworkVersion to be used to explicitly specify the version of Microsoft.NETCore.App -->
    <PackageReference Update="Microsoft.NETCore.App"
                      Version="$(RuntimeFrameworkVersion)"
                      Condition="'$(RuntimeFrameworkVersion)' != ''" 
                      AllowExplicitVersion="true"/>
    
    <!-- If implicit PackageReferences are disabled, then don't warn about explicit versions at all -->
    <PackageReference Update="@(PackageReference)"
                      Condition="'$(DisableImplicitFrameworkReferences)' == 'true'"
                      AllowExplicitVersion="true"/>
  </ItemGroup>

  <UsingTask TaskName="ApplyImplicitVersions" AssemblyFile="$(MicrosoftNETBuildTasksAssembly)" />
  <UsingTask TaskName="CheckIfPackageReferenceShouldBeFrameworkReference" AssemblyFile="$(MicrosoftNETBuildTasksAssembly)" />

  <Target Name="UpdateAspNetToFrameworkReference"
          Condition="'$(TargetFrameworkIdentifier)' == '.NETCoreApp' And '$(_TargetFrameworkVersionWithoutV)' >= '3.0'">

    <CheckIfPackageReferenceShouldBeFrameworkReference
          PackageReferences="@(PackageReference)"
          FrameworkReferences="@(FrameworkReference)"
          PackageReferenceToReplace="Microsoft.AspNetCore.All"
          FrameworkReferenceToUse="Microsoft.AspNetCore.App">
      <Output TaskParameter="ShouldRemovePackageReference" PropertyName="_ShouldRemoveAspNetCoreAll" />
    </CheckIfPackageReferenceShouldBeFrameworkReference>

    <!-- NETSDK1079: The Microsoft.AspNetCore.All package is not supported when targeting .NET Core 3.0 or higher.
         A FrameworkReference to Microsoft.AspNetCore.App should be used instead, and will be implicitly included by Microsoft.NET.Sdk.Web. -->
    <NETSdkError Condition="'$(_ShouldRemoveAspNetCoreAll)' == 'true'"
                 ResourceName="AspNetCoreAllNotSupported" />

    <CheckIfPackageReferenceShouldBeFrameworkReference
      PackageReferences="@(PackageReference)"
      FrameworkReferences="@(FrameworkReference)"
      PackageReferenceToReplace="Microsoft.AspNetCore.App"
      FrameworkReferenceToUse="Microsoft.AspNetCore.App">
      <Output TaskParameter="ShouldRemovePackageReference" PropertyName="_ShouldRemoveAspNetCoreApp" />
      <Output TaskParameter="ShouldAddFrameworkReference" PropertyName="_ShouldAddAspNetCoreAppFrameworkReference" />
    </CheckIfPackageReferenceShouldBeFrameworkReference>

    <ItemGroup>
      <PackageReference Remove="Microsoft.AspNetCore.App" Condition="'$(_ShouldRemoveAspNetCoreApp)' == 'true'" />
      <FrameworkReference Include="Microsoft.AspNetCore.App" Condition="'$(_ShouldAddAspNetCoreAppFrameworkReference)' == 'true'" />
    </ItemGroup>
    
    <!-- NETSDK1080: A PackageReference to Microsoft.AspNetCore.App is not necessary when targeting .NET Core 3.0 or higher.
         If Microsoft.NET.Sdk.Web is used, the shared framework will be referenced automatically. Otherwise, the PackageReference
         should be replaced with a FrameworkReference. -->

    <NETSdkWarning Condition="'$(_ShouldRemoveAspNetCoreApp)' == 'true'"
                   ResourceName="AspNetCoreUsesFrameworkReference" />
    
  </Target>

  <Target Name="ApplyImplicitVersions" 
          BeforeTargets="_CheckForInvalidConfigurationAndPlatform;CollectPackageReferences;ProcessFrameworkReferences"
          DependsOnTargets="UpdateAspNetToFrameworkReference;CheckForImplicitPackageReferenceOverrides"
          Condition="'@(PackageReference)' != ''">
    
    <ApplyImplicitVersions TargetFrameworkVersion="$(_TargetFrameworkVersionWithoutV)"
                           TargetLatestRuntimePatch="$(TargetLatestRuntimePatch)"
                           PackageReferences="@(PackageReference)"
                           ImplicitPackageReferenceVersions="@(ImplicitPackageReferenceVersion)">
      <Output TaskParameter="PackageReferencesToUpdate" ItemName="PackageReferenceToUpdate" />
      <Output TaskParameter="SdkBuildWarnings" ItemName="SdkBuildWarning" />
    </ApplyImplicitVersions>

    <ItemGroup>
      <PackageReference Remove="@(PackageReferenceToUpdate)" />
      <PackageReference Include="@(PackageReferenceToUpdate)" />
    </ItemGroup>

    <ItemGroup>
      <!-- Support using a patch version in the TargetFramework, ie netcoreapp1.1.1
           Note that this isn't officially supported, but it worked in the past so
           this should prevent breaking it. -->
      <PackageReference Condition="'%(PackageReference.Identity)' == 'Microsoft.NETCore.App'">
        <Version Condition="'%(PackageReference.Version)' == ''">$(_TargetFrameworkVersionWithoutV)</Version>
      </PackageReference>
    </ItemGroup>
  </Target>
  
  <!-- This target runs before build but not before restore, to avoid duplicating these warnings
       if building with an implicit restore. -->
  <Target Name="WarnForExplicitVersions" BeforeTargets="_CheckForInvalidConfigurationAndPlatform"
          DependsOnTargets="ApplyImplicitVersions"
          Condition="'@(SdkBuildWarning)' != ''">
    <NetSdkWarning FormattedText="%(SdkBuildWarning.Identity)" />
  </Target>

  <!--
    Automatically add Link metadata to items of specific types if they are outside of the project folder and don't already have the Link metadata set.
    This will cause them to be shown in the Solution Explorer.  If an item has LinkBase metadata, the automatic Link will start with that value, and
    the items will appear in the Solution Explorer under the folder specified by LinkBase.
    -->
  <ItemGroup Condition="'$(SetLinkMetadataAutomatically)' != 'false'">
    <Compile Update="@(Compile)">
      <!-- First, add a trailing slash to the LinkBase metadata if necessary.  This allows us to use the same value
           for the Link metadata whether or not LinkBase metadata is set: %(LinkBase)%(RecursiveDir)%(Filename)%(Extension) 
           
           Note that RecursiveDir already includes the trailing slash.
      -->
      <LinkBase Condition="'%(LinkBase)' != ''">$([MSBuild]::EnsureTrailingSlash(%(LinkBase)))</LinkBase>
    
      <!-- Set the Link metadata if it's not already set, if the item wasn't defined in a shared project,  and the item is outside of the project directory.
           Check whether the item was defined in a shared project by checking whether the extension of the defining project was .projitems.
           Check whether an item is inside the project directory by seeing if the FullPath starts with EnsureTrailingSlash(MSBuildProjectDirectory)
           The FullPath and the MSBuildProjectDirectory will both already be normalized full paths.
           The call to [MSBuild]::ValueOrDefault() is there in order to allow calling StartsWith on the FullPath value, since it's
           not possible to call a string method on a metadata item directly.  The intrinsic ValueOrDefault() will be more
           performant than calling String.Copy(), which has been used for this in other contexts, but actually makes a copy
           of the string data.
      -->    
      <Link Condition="'%(Link)' == '' And '%(DefiningProjectExtension)' != '.projitems' And !$([MSBuild]::ValueOrDefault('%(FullPath)', '').StartsWith($([MSBuild]::EnsureTrailingSlash($(MSBuildProjectDirectory)))))">%(LinkBase)%(RecursiveDir)%(Filename)%(Extension)</Link>
    </Compile>

    <AdditionalFiles Update="@(AdditionalFiles)">
      <LinkBase Condition="'%(LinkBase)' != ''">$([MSBuild]::EnsureTrailingSlash(%(LinkBase)))</LinkBase>
      <Link Condition="'%(Link)' == '' And '%(DefiningProjectExtension)' != '.projitems' And !$([MSBuild]::ValueOrDefault('%(FullPath)', '').StartsWith($([MSBuild]::EnsureTrailingSlash($(MSBuildProjectDirectory)))))">%(LinkBase)%(RecursiveDir)%(Filename)%(Extension)</Link>
    </AdditionalFiles>
    
    <None Update="@(None)">
      <LinkBase Condition="'%(LinkBase)' != ''">$([MSBuild]::EnsureTrailingSlash(%(LinkBase)))</LinkBase>
      <Link Condition="'%(Link)' == '' And '%(DefiningProjectExtension)' != '.projitems' And !$([MSBuild]::ValueOrDefault('%(FullPath)', '').StartsWith($([MSBuild]::EnsureTrailingSlash($(MSBuildProjectDirectory)))))">%(LinkBase)%(RecursiveDir)%(Filename)%(Extension)</Link>
    </None>

    <Content Update="@(Content)">
      <LinkBase Condition="'%(LinkBase)' != ''">$([MSBuild]::EnsureTrailingSlash(%(LinkBase)))</LinkBase>
      <Link Condition="'%(Link)' == '' And '%(DefiningProjectExtension)' != '.projitems' And !$([MSBuild]::ValueOrDefault('%(FullPath)', '').StartsWith($([MSBuild]::EnsureTrailingSlash($(MSBuildProjectDirectory)))))">%(LinkBase)%(RecursiveDir)%(Filename)%(Extension)</Link>
    </Content>

    <EmbeddedResource Update="@(EmbeddedResource)">
      <LinkBase Condition="'%(LinkBase)' != ''">$([MSBuild]::EnsureTrailingSlash(%(LinkBase)))</LinkBase>
      <Link Condition="'%(Link)' == '' And '%(DefiningProjectExtension)' != '.projitems' And !$([MSBuild]::ValueOrDefault('%(FullPath)', '').StartsWith($([MSBuild]::EnsureTrailingSlash($(MSBuildProjectDirectory)))))">%(LinkBase)%(RecursiveDir)%(Filename)%(Extension)</Link>
    </EmbeddedResource>

    <Page Update="@(Page)">
      <LinkBase Condition="'%(LinkBase)' != ''">$([MSBuild]::EnsureTrailingSlash(%(LinkBase)))</LinkBase>
      <Link Condition="'%(Link)' == '' And '%(DefiningProjectExtension)' != '.projitems' And !$([MSBuild]::ValueOrDefault('%(FullPath)', '').StartsWith($([MSBuild]::EnsureTrailingSlash($(MSBuildProjectDirectory)))))">%(LinkBase)%(RecursiveDir)%(Filename)%(Extension)</Link>
    </Page>

    <Resource Update="@(Resource)">
      <LinkBase Condition="'%(LinkBase)' != ''">$([MSBuild]::EnsureTrailingSlash(%(LinkBase)))</LinkBase>
      <Link Condition="'%(Link)' == '' And '%(DefiningProjectExtension)' != '.projitems' And !$([MSBuild]::ValueOrDefault('%(FullPath)', '').StartsWith($([MSBuild]::EnsureTrailingSlash($(MSBuildProjectDirectory)))))">%(LinkBase)%(RecursiveDir)%(Filename)%(Extension)</Link>
    </Resource>
  </ItemGroup>

  <UsingTask TaskName="CheckForImplicitPackageReferenceOverrides" AssemblyFile="$(MicrosoftNETBuildTasksAssembly)" />

  <!-- Remove package references with metadata IsImplicitlyDefined = true, if there are other PackageReference items with the same identity -->
  <Target Name="CheckForImplicitPackageReferenceOverrides" BeforeTargets="_CheckForInvalidConfigurationAndPlatform;CollectPackageReferences">
    <PropertyGroup>
      <ImplicitPackageReferenceInformationLink>https://aka.ms/sdkimplicitrefs</ImplicitPackageReferenceInformationLink>
    </PropertyGroup>

    <CheckForImplicitPackageReferenceOverrides
        PackageReferenceItems="@(PackageReference)"
        MoreInformationLink="$(ImplicitPackageReferenceInformationLink)">
      <Output TaskParameter="ItemsToRemove" ItemName="_PackageReferenceToRemove" />
      <Output TaskParameter="ItemsToAdd" ItemName="_PackageReferenceToAdd" />
    </CheckForImplicitPackageReferenceOverrides>

    <ItemGroup>
      <!-- Remove and add the PackageReference items according to the output from the task -->
      <PackageReference Remove="@(_PackageReferenceToRemove)" />

      <PackageReference Include="@(_PackageReferenceToAdd)" />

      <!-- Clear items for other targets to use -->
      <_PackageReferenceToAdd Remove="@(_PackageReferenceToAdd)" />

    </ItemGroup>
    
    <!-- If any implicit package references were overridden, then don't check that RuntimeFrameworkVersion matches the package version -->
    <PropertyGroup Condition="'@(_PackageReferenceToRemove)' != ''">
      <VerifyMatchingImplicitPackageVersion>false</VerifyMatchingImplicitPackageVersion>
    </PropertyGroup>

  </Target>

  <UsingTask TaskName="CheckForDuplicateItems" AssemblyFile="$(MicrosoftNETBuildTasksAssembly)" />

  <Target Name="CheckForDuplicateItems" BeforeTargets="_CheckForInvalidConfigurationAndPlatform;CoreCompile">

    <PropertyGroup>
      <DefaultItemsMoreInformationLink>https://aka.ms/sdkimplicititems</DefaultItemsMoreInformationLink>
    </PropertyGroup>

    <!-- NOTE for design-time builds we continue on errors and remove the duplicate items.
         This is because otherwise there won't be any references to pass to the compiler, leading to design-time
         compilation errors for every API that is used in the project.  Amidst all the compile errors, it would
         be easy to miss the duplicate items error which is the real source of the problem. -->

    <CheckForDuplicateItems
      Items="@(Compile)"
      ItemName="Compile"
      DefaultItemsEnabled="$(EnableDefaultItems)"
      DefaultItemsOfThisTypeEnabled="$(EnableDefaultCompileItems)"
      PropertyNameToDisableDefaultItems="EnableDefaultCompileItems"
      MoreInformationLink="$(DefaultItemsMoreInformationLink)"
      ContinueOnError="$(ContinueOnError)">
      <Output TaskParameter="DeduplicatedItems" ItemName="DeduplicatedCompileItems" />
    </CheckForDuplicateItems>

    <CheckForDuplicateItems
      Items="@(EmbeddedResource)"
      ItemName="EmbeddedResource"
      DefaultItemsEnabled="$(EnableDefaultItems)"
      DefaultItemsOfThisTypeEnabled="$(EnableDefaultEmbeddedResourceItems)"
      PropertyNameToDisableDefaultItems="EnableDefaultEmbeddedResourceItems"
      MoreInformationLink="$(DefaultItemsMoreInformationLink)"
      ContinueOnError="$(ContinueOnError)">
      <Output TaskParameter="DeduplicatedItems" ItemName="DeduplicatedEmbeddedResourceItems" />
    </CheckForDuplicateItems>
    
    <!-- Default content items are enabled by the Web SDK, not the .NET SDK, but we check it here for simplicity -->
    <CheckForDuplicateItems
      Items="@(Content)"
      ItemName="Content"
      DefaultItemsEnabled="$(EnableDefaultItems)"
      DefaultItemsOfThisTypeEnabled="$(EnableDefaultContentItems)"
      PropertyNameToDisableDefaultItems="EnableDefaultContentItems"
      MoreInformationLink="$(DefaultItemsMoreInformationLink)"
      ContinueOnError="$(ContinueOnError)">
      <Output TaskParameter="DeduplicatedItems" ItemName="DeduplicatedContentItems" />
    </CheckForDuplicateItems>

    <ItemGroup Condition="'$(DesignTimeBuild)' == 'true' And '@(DeduplicatedCompileItems)' != ''">
      <Compile Remove="@(Compile)" />
      <Compile Include="@(DeduplicatedCompileItems)" />
    </ItemGroup>

    <ItemGroup Condition="'$(DesignTimeBuild)' == 'true' And '@(DeduplicatedEmbeddedResourceItems)' != ''">
      <EmbeddedResource Remove="@(EmbeddedResource)" />
      <EmbeddedResource Include="@(DeduplicatedEmbeddedResourceItems)" />
    </ItemGroup>

    <ItemGroup Condition="'$(DesignTimeBuild)' == 'true' And '@(DeduplicatedContentItems)' != ''">
      <Content Remove="@(Content)" />
      <Content Include="@(DeduplicatedContentItems)" />
    </ItemGroup>
    
  </Target>

  <Target Name="CheckWindowsDesktopSdkInUse" 
          BeforeTargets="_CheckForInvalidConfigurationAndPlatform" 
          Condition="'$(UseWpf)' == 'true' Or '$(UseWindowsForms)' == 'true'">
    <NetSdkWarning Condition="'$(_MicrosoftNetSdkWindowsDesktop)' != 'true'" 
                   ResourceName="UseWpfOrUseWindowsFormsRequiresWindowsDesktopFramework" />
  </Target>
</Project>
