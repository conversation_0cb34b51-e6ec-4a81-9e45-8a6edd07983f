<!--
***********************************************************************************************
Microsoft.NET.Sdk.FSharpTargetsShim.targets

WARNING:  DO NOT MODIFY this file unless you are knowledgeable about MSBuild and have
          created a backup copy.  Incorrect changes to this file will make it
          impossible to load or build your projects from the command-line or the IDE.

Copyright (c) .NET Foundation. All rights reserved. 
***********************************************************************************************
-->

<Project xmlns="http://schemas.microsoft.com/developer/msbuild/2003">

  <PropertyGroup Condition=" '$(UseBundledFSharpTargets)' == 'true' ">
    <AppDesignerFolder Condition="'$(AppDesignerFolder)' == ''">Properties</AppDesignerFolder>
  </PropertyGroup>

  <PropertyGroup Condition=" '$(DisableImplicitConfigurationDefines)' != 'true' and '$(UseBundledFSharpTargets)' == 'true' ">
    <ImplicitConfigurationDefine>$(Configuration.ToUpperInvariant())</ImplicitConfigurationDefine>

    <!-- Replace dashes and periods in the configuration with underscores.  This makes it more likely that
         the resulting compilation constant will be a valid C# conditional compilation symbol.  As the set
         of characters that aren't allowed is essentially open-ended, there's probably not a good way to
         fully sanitize the Configuration in MSBuild evaluation.  If the resulting string still isn't a
         valid conditional combilation symbol, then the compiler will generate the following error and
         the define will be ignored:
            warning MSB3052: The parameter to the compiler is invalid, '/define:0BAD_DEFINE' will be ignored.
         -->
    
    <ImplicitConfigurationDefine>$(ImplicitConfigurationDefine.Replace('-', '_'))</ImplicitConfigurationDefine>
    <ImplicitConfigurationDefine>$(ImplicitConfigurationDefine.Replace('.', '_'))</ImplicitConfigurationDefine>
    <DefineConstants>$(DefineConstants);$(ImplicitConfigurationDefine)</DefineConstants>
  </PropertyGroup>

  <!-- ***************************************************************************************************************
       Loads the cross-targeting targets if we are doing a cross-targeting build
       Import design time targets for Roslyn Project System. These are only available if Visual Studio is installed.
       Import design time targets before the common crosstargeting targets, which import targets from Nuget.
       *************************************************************************************************************** -->
  <PropertyGroup>
     <FSharpDesignTimeTargetsPath>$(MSBuildExtensionsPath)\Microsoft\VisualStudio\Managed\Microsoft.FSharp.DesignTime.targets</FSharpDesignTimeTargetsPath>
  </PropertyGroup>
  <Import Project="$(FSharpDesignTimeTargetsPath)" 
          Condition=" '$(UseBundledFSharpTargets)' == 'true' and '$(FSharpDesignTimeTargetsPath)' != '' and Exists('$(FSharpDesignTimeTargetsPath)') " />
  <Import Project="$(MSBuildToolsPath)\Microsoft.Common.CrossTargeting.targets"
          Condition=" '$(UseBundledFSharpTargets)' == 'true' and '$(IsCrossTargetingBuild)' == 'true' "/>

  <!-- ***************************************************************************************************************
       Shim to select the correct Microsoft.NET.Sdk.FSharp.targets file when not running 
       under Cross-targeting build and not under FSharp.Sdk
       If running under desktop select Microsoft.FSharp.targets file from VS deployment, 
       if running core msbuild select Microsoft.FSharp.targets from dotnet cli deployment
       *************************************************************************************************************** -->
  <PropertyGroup Condition=" '$(IsCrossTargetingBuild)' != 'true' " >
     <FSharpTargetsShim Condition = " '$(FSharpTargetsShim)' == '' and Exists('$(MSBuildToolsPath)\FSharp\Microsoft.FSharp.NetSdk.targets') ">$(MSBuildToolsPath)\FSharp\Microsoft.FSharp.NetSdk.targets</FSharpTargetsShim>
     <FSharpTargetsShim Condition = " '$(FSharpTargetsShim)' == '' and Exists('$(MSBuildExtensionsPath32)\Microsoft\VisualStudio\v$(VisualStudioVersion)\FSharp\Microsoft.FSharp.NetSdk.targets') ">$(MSBuildExtensionsPath32)\Microsoft\VisualStudio\v$(VisualStudioVersion)\FSharp\Microsoft.FSharp.NetSdk.targets</FSharpTargetsShim>
  </PropertyGroup>
  <Import Condition=" '$(UseBundledFSharpTargets)' == 'true' and  '$(IsCrossTargetingBuild)' != 'true' and Exists('$(FSharpTargetsShim)') " Project="$(FSharpTargetsShim)" />

</Project>
