<!--
***********************************************************************************************
Microsoft.NET.Sdk.ImportPublishProfile.targets

WARNING:  DO NOT MODIFY this file unless you are knowledgeable about MSBuild and have
          created a backup copy.  Incorrect changes to this file will make it
          impossible to load or build your projects from the command-line or the IDE.

Copyright (c) .NET Foundation. All rights reserved.
***********************************************************************************************
-->
<Project ToolsVersion="14.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <!-- Default to having imported the publish profile so the Web SDK doesn't also attempt to do so. -->
    <PublishProfileImported>true</PublishProfileImported>
    <_PublishProfileDesignerFolder Condition="'$(AppDesignerFolder)' != ''">$(AppDesignerFolder)</_PublishProfileDesignerFolder>
    <_PublishProfileDesignerFolder Condition="'$(_PublishProfileDesignerFolder)' == ''">Properties</_PublishProfileDesignerFolder>
    <_PublishProfileRootFolder Condition="'$(_PublishProfileRootFolder)' == ''">$(MSBuildProjectDirectory)\$(_PublishProfileDesignerFolder)\PublishProfiles\</_PublishProfileRootFolder>
    <PublishProfileName Condition="'$(PublishProfileName)' == '' and '$(PublishProfile)' != ''">$([System.IO.Path]::GetFileNameWithoutExtension($(PublishProfile)))</PublishProfileName>
    <PublishProfileFullPath Condition="'$(PublishProfileFullPath)' == '' and '$(PublishProfileName)' != ''">$(_PublishProfileRootFolder)$(PublishProfileName).pubxml</PublishProfileFullPath>
    <WebPublishProfileFile Condition="'$(WebPublishProfileFile)' == '' and Exists('$(PublishProfileFullPath)')">$(PublishProfileFullPath)</WebPublishProfileFile>

    <!-- If the publish profile doesn't exist, mark as not imported.
       This allows the Web SDK to import some default profiles that come with the Web SDK.
       Publishing in Visual Studio sets `WebPublishProfileFile` as a global property.
       Therefore, check that `ProjectToOverrideProjectExtensionsPath` is equal to `MSBuildProjectFullPath`
       to limit the import to the project being published. -->
    <PublishProfileImported Condition="('$(ProjectToOverrideProjectExtensionsPath)' != '' and
                                        '$(ProjectToOverrideProjectExtensionsPath)' != '$(MSBuildProjectFullPath)') or
                                       '$(WebPublishProfileFile)' == '' or
                                       !Exists('$(WebPublishProfileFile)')">false</PublishProfileImported>
  </PropertyGroup>

  <Import Project="$(WebPublishProfileFile)" Condition="'$(PublishProfileImported)' == 'true'" />
  <Import Project="$(WebPublishProfileFile).user" Condition="'$(PublishProfileImported)' == 'true' and Exists('$(WebPublishProfileFile).user')" />
</Project>
