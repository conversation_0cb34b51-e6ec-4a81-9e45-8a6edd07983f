<!--
***********************************************************************************************
Microsoft.NET.Sdk.VisualBasic.props

WARNING:  DO NOT MODIFY this file unless you are knowledgeable about MSBuild and have
          created a backup copy.  Incorrect changes to this file will make it
          impossible to load or build your projects from the command-line or the IDE.

Copyright (c) .NET Foundation. All rights reserved. 
***********************************************************************************************
-->
<Project xmlns="http://schemas.microsoft.com/developer/msbuild/2003">

  <PropertyGroup>
    <WarningLevel Condition=" '$(WarningLevel)' == '' ">1</WarningLevel>
    <NoWarn Condition=" '$(NoWarn)' == '' ">41999,42016,42017,42018,42019,42020,42021,42022,42032,42036</NoWarn>
    <OptionExplicit Condition=" '$(OptionExplicit)' == '' ">On</OptionExplicit>
    <OptionCompare Condition=" '$(OptionCompare)' == '' ">Binary</OptionCompare>
    <OptionStrict Condition=" '$(OptionStrict)' == '' ">Off</OptionStrict>
    <OptionInfer Condition=" '$(OptionInfer)' == '' ">On</OptionInfer>

    <!-- Remove the line below once https://github.com/Microsoft/visualfsharp/issues/3207 gets fixed -->
    <WarningsAsErrors Condition=" '$(WarningsAsErrors)' == '' ">NU1605</WarningsAsErrors>
  </PropertyGroup>

  <PropertyGroup Condition=" '$(Configuration)' == 'Debug' ">
    <DefineDebug Condition=" '$(DefineDebug)' == '' ">true</DefineDebug>
    <DefineTrace Condition=" '$(DefineTrace)' == '' ">true</DefineTrace>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)' == 'Release' ">
    <DefineTrace Condition=" '$(DefineTrace)' == '' ">true</DefineTrace>
  </PropertyGroup>

</Project>
