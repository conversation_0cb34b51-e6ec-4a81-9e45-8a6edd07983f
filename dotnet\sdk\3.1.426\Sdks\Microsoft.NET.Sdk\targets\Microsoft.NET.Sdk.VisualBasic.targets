<!--
***********************************************************************************************
Microsoft.NET.Sdk.VisualBasic.targets

WARNING:  DO NOT MODIFY this file unless you are knowledgeable about MSBuild and have
          created a backup copy.  Incorrect changes to this file will make it
          impossible to load or build your projects from the command-line or the IDE.

Copyright (c) .NET Foundation. All rights reserved. 
***********************************************************************************************
-->
<Project ToolsVersion="14.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <DisableImplicitNamespaceImports Condition="'$(DisableImplicitNamespaceImports)'==''">$(DisableImplicitFrameworkReferences)</DisableImplicitNamespaceImports>
  </PropertyGroup>
  <ItemGroup Condition=" '$(DisableImplicitNamespaceImports)' != 'true' and '$(TargetFrameworkIdentifier)' == '.NETFramework'">
    <!-- These namespaces are present in 2.0 Framework assemblies -->
    <Import Include="Microsoft.VisualBasic" />
    <Import Include="System" />
    <Import Include="System.Collections" />
    <Import Include="System.Collections.Generic" />
    <Import Include="System.Data" />
    <Import Include="System.Diagnostics" />
    <!-- These namespaces are introduced in 3.5 Framework assemblies -->
    <Import Include="System.Linq" Condition=" '$(_TargetFrameworkVersionWithoutV)' >= '3.5' "/>
    <Import Include="System.Xml.Linq" Condition=" '$(_TargetFrameworkVersionWithoutV)' >= '3.5' "/>
    <!-- This namespace is introduced in 4.0 Framework assemblies -->
    <Import Include="System.Threading.Tasks" Condition=" '$(_TargetFrameworkVersionWithoutV)' >= '4.0' "/>
  </ItemGroup>
  <ItemGroup Condition=" '$(DisableImplicitNamespaceImports)' != 'true' and '$(_IsNETCoreOrNETStandard)' == 'true'">
    <Import Include="Microsoft.VisualBasic" />
    <Import Include="System" />
    <Import Include="System.Collections" />
    <Import Include="System.Collections.Generic" />
    <Import Include="System.Diagnostics" />
    <Import Include="System.Linq" />
    <Import Include="System.Xml.Linq" />
    <Import Include="System.Threading.Tasks" />
  </ItemGroup>
  <PropertyGroup Condition="'$(DisableImplicitConfigurationDefines)' != 'true' and '$(Configuration)' != ''">
    <ImplicitConfigurationDefine>$(Configuration.ToUpperInvariant())</ImplicitConfigurationDefine>

    <!-- Replace dashes and periods in the configuration with underscores.  This makes it more likely that
         the resulting compilation constant will be a valid C# conditional compilation symbol.  As the set
         of characters that aren't allowed is essentially open-ended, there's probably not a good way to
         fully sanitize the Configuration in MSBuild evaluation.  If the resulting string still isn't a
         valid conditional combilation symbol, then the compiler will generate the following error and
         the define will be ignored:
            warning MSB3052: The parameter to the compiler is invalid, '/define:0BAD_DEFINE' will be ignored.
         -->
    
    <ImplicitConfigurationDefine>$(ImplicitConfigurationDefine.Replace('-', '_'))</ImplicitConfigurationDefine>
    <ImplicitConfigurationDefine>$(ImplicitConfigurationDefine.Replace('.', '_'))</ImplicitConfigurationDefine>
    <ImplicitConfigurationDefine>$(ImplicitConfigurationDefine.Replace(' ', '_'))</ImplicitConfigurationDefine>
    <!-- In F# and C# this is called DefineConstants, VB is idiosyncratic and calls it FinalDefineConstants -->
  </PropertyGroup>

  <PropertyGroup>
    <FinalDefineConstants Condition="'$(VersionlessImplicitFrameworkDefine)' != ''">$(FinalDefineConstants),$(VersionlessImplicitFrameworkDefine)=-1</FinalDefineConstants>
    <FinalDefineConstants Condition="'$(ImplicitFrameworkDefine)' != ''">$(FinalDefineConstants),$(ImplicitFrameworkDefine)=-1</FinalDefineConstants>
    <FinalDefineConstants Condition="'$(DefineDebug)' != 'true' and '$(ImplicitConfigurationDefine)' != ''">$(FinalDefineConstants),$(ImplicitConfigurationDefine)=-1</FinalDefineConstants>
  </PropertyGroup>

  <!--
    By default, VBRuntime is chosen as follows:

    .NET Framework:
      Same as classic vbproj. $(FrameworkPathOverride) is passed  as
      /sdkpath to compiler and Microsoft.VisualBasic.dll will be used
      from there even if there is no explicit reference to it. This is
      accomplished by leaving $(VBRuntime) unset.

    .NET Core 3.0+:
      Locate Microsoft.VisualBasic among references and pass it as
      /vbruntime to compiler.

    .NET Core < 3.0 or .NET Standard:
      Use embedded VB runtime as there is no Microsoft.VisualBasic.dll
      available that is complete enough to serve as /vbruntime.

   These defaults can be overridden as follows:

    $(UseReferencedVBRuntime) == true:
      Use referenced Microsoft.VisualBasic regardless of TFM.

      This is an escape hatch for a custom Microsoft.VisualBasic reference
      that can work as /vbruntime on a target framework other than .NET Core 3.0+

    $(VBRuntime) != '':
      Pass it through to compiler unchanged.
  -->

  <PropertyGroup Condition="'$(UseReferencedVBRuntime)' == ''
                        and '$(VBRuntime)' == ''
                        and '$(TargetFrameworkIdentifier)' == '.NETCoreApp'
                        and '$(_TargetFrameworkVersionWithoutV)' >= '3.0'">
    <UseReferencedVBRuntime>true</UseReferencedVBRuntime>
  </PropertyGroup>

  <PropertyGroup Condition="'$(VBRuntime)' == ''
                        and '$(UseReferencedVBRuntime)' != 'true'
                        and '$(TargetFrameworkIdentifier)' != '.NETFramework'">
    <VBRuntime>Embed</VBRuntime>
  </PropertyGroup>

  <PropertyGroup Condition="'$(TargetFrameworkIdentifier)' != '.NETFramework'">
    <!--
      Prevent the .NET Framework compiler from trying to locate assemblies
      in the .NET Framework directory when not targeting .NET Framework
    -->
    <DisableSdkPath Condition="'$(DisableSdkPath)' == ''">true</DisableSdkPath>

    <!--
      VB Runtime does not yet have enough support for My.* outside .NET Framework,
      so default MyType=Empty for non .NET Framework. Project templates will be
      responsible for setting MyType to non-Empty (Console, Windows, etc.) when 
      the VB Runtime can allow it and as appropriate for the project type.
     -->
    <FinalDefineConstants Condition="'$(MyType)' == ''">$(FinalDefineConstants),_MyType=&quot;Empty&quot;</FinalDefineConstants>
  </PropertyGroup>

  <!--
    NOTE: We must hook directly to CoreCompile for compatibility with two phase XAML
          build. We also must not pull in a dependency on ResolveAssemblyReferences
          as the generated temporary project for xaml compilation has a hard-coded list
          of items in ReferencePath, and does not need to run RAR.
  -->
  <Target Name="_UseReferencedVBRuntime" Condition="'$(UseReferencedVBRuntime)' == 'true'" BeforeTargets="CoreCompile">
    <PropertyGroup>
      <VBRuntime Condition="'%(ReferencePath.FileName)' == 'Microsoft.VisualBasic'">%(ReferencePath.Identity)</VBRuntime>
    </PropertyGroup>
  </Target>
</Project>
