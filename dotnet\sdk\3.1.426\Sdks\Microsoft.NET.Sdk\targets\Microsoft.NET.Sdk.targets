<!--
***********************************************************************************************
Microsoft.NET.Sdk.targets

WARNING:  DO NOT MODIFY this file unless you are knowledgeable about MSBuild and have
          created a backup copy.  Incorrect changes to this file will make it
          impossible to load or build your projects from the command-line or the IDE.

Copyright (c) .NET Foundation. All rights reserved.
***********************************************************************************************
-->
<Project ToolsVersion="14.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">

  <PropertyGroup Condition="'$(UsingNETSdkDefaults)' != 'true'">
    <!-- Skip import of Microsoft.NuGet.props and Microsoft.NuGet.targets -->
    <SkipImportNuGetProps>true</SkipImportNuGetProps>
    <SkipImportNuGetBuildTargets>true</SkipImportNuGetBuildTargets>
  </PropertyGroup>

  <!-- Workaround: https://github.com/Microsoft/msbuild/issues/1293 -->
  <PropertyGroup Condition="'$(MSBuildRuntimeType)' == 'Core' Or '$(TargetFrameworkIdentifier)' != '.NETFramework'">
    <GenerateResourceMSBuildArchitecture Condition=" '$(GenerateResourceMSBuildArchitecture)' == '' ">CurrentArchitecture</GenerateResourceMSBuildArchitecture>
    <GenerateResourceMSBuildRuntime Condition=" '$(GenerateResourceMSBuildRuntime)' == '' ">CurrentRuntime</GenerateResourceMSBuildRuntime>
  </PropertyGroup>

  <Import Project="Microsoft.NET.Sdk.Common.targets" />

  <ImportGroup>
    <Import Project="$(MSBuildThisFileDirectory)Microsoft.PackageDependencyResolution.targets" Condition="Exists('$(MSBuildThisFileDirectory)Microsoft.PackageDependencyResolution.targets') and '$(Language)' != 'C++'" />
    <Import Project="$(MSBuildThisFileDirectory)Microsoft.PackageDependencyResolutionStubs.targets" Condition="Exists('$(MSBuildThisFileDirectory)Microsoft.PackageDependencyResolutionStubs.targets') and '$(Language)' == 'C++'" />
  </ImportGroup>

  <Import Project="Microsoft.NET.Sdk.DefaultItems.targets" />

  <Import Project="Microsoft.NET.Sdk.FrameworkReferenceResolution.targets" />

  <UsingTask TaskName="GenerateDepsFile" AssemblyFile="$(MicrosoftNETBuildTasksAssembly)" />
  <UsingTask TaskName="GenerateRuntimeConfigurationFiles" AssemblyFile="$(MicrosoftNETBuildTasksAssembly)" />
  <UsingTask TaskName="GetAssemblyVersion" AssemblyFile="$(MicrosoftNETBuildTasksAssembly)" />
  <UsingTask TaskName="GenerateSatelliteAssemblies" AssemblyFile="$(MicrosoftNETBuildTasksAssembly)" />

  <PropertyGroup>
    <DisableStandardFrameworkResolution Condition="'$(DisableStandardFrameworkResolution)' == ''">$(_IsNETCoreOrNETStandard)</DisableStandardFrameworkResolution>
  </PropertyGroup>

  <PropertyGroup>
    <EnableDynamicLoading Condition="'$(EnableDynamicLoading)' == '' and '$(EnableComHosting)' == 'true'">true</EnableDynamicLoading>
    <GenerateRuntimeConfigurationFiles Condition=" '$(GenerateRuntimeConfigurationFiles)' == '' and '$(TargetFrameworkIdentifier)' == '.NETCoreApp' and ('$(HasRuntimeOutput)' == 'true' or '$(EnableComHosting)' == 'true' or '$(EnableDynamicLoading)' == 'true') ">true</GenerateRuntimeConfigurationFiles>
    <UserRuntimeConfig Condition=" '$(UserRuntimeConfig)' == '' ">$(MSBuildProjectDirectory)/runtimeconfig.template.json</UserRuntimeConfig>
    <GenerateSatelliteAssembliesForCore Condition=" '$(GenerateSatelliteAssembliesForCore)' == '' and '$(MSBuildRuntimeType)' == 'Core' ">true</GenerateSatelliteAssembliesForCore>
    <ComputeNETCoreBuildOutputFiles Condition=" '$(ComputeNETCoreBuildOutputFiles)' == '' and '$(TargetFrameworkIdentifier)' == '.NETCoreApp'">true</ComputeNETCoreBuildOutputFiles>
    <_GenerateRuntimeConfigurationPropertyInputsCache Condition="'$(_GenerateRuntimeConfigurationPropertyInputsCache)' == ''">$(IntermediateOutputPath)$(MSBuildProjectName).genruntimeconfig.cache</_GenerateRuntimeConfigurationPropertyInputsCache>
    <_GenerateRuntimeConfigurationPropertyInputsCache>$([MSBuild]::NormalizePath($(MSBuildProjectDirectory), $(_GenerateRuntimeConfigurationPropertyInputsCache)))</_GenerateRuntimeConfigurationPropertyInputsCache>
  </PropertyGroup>

  <ItemGroup>
    <GenerateRuntimeConfigurationFilesInputs Include="$(ProjectAssetsFile)" />
    <GenerateRuntimeConfigurationFilesInputs Include="$(ProjectAssetsCacheFile)" />
    <GenerateRuntimeConfigurationFilesInputs Include="$(UserRuntimeConfig)" Condition=" Exists($(UserRuntimeConfig)) " />
  </ItemGroup>

  <PropertyGroup>
    <ProjectDepsFileName Condition="'$(ProjectDepsFileName)' == ''">$(AssemblyName).deps.json</ProjectDepsFileName>
    <ProjectDepsFilePath Condition="'$(ProjectDepsFilePath)' == ''">$(TargetDir)$(ProjectDepsFileName)</ProjectDepsFilePath>
    <ProjectRuntimeConfigFileName Condition="'$(ProjectRuntimeConfigFileName)' == ''">$(AssemblyName).runtimeconfig.json</ProjectRuntimeConfigFileName>
    <ProjectRuntimeConfigFilePath Condition="'$(ProjectRuntimeConfigFilePath)' == ''">$(TargetDir)$(ProjectRuntimeConfigFileName)</ProjectRuntimeConfigFilePath>
    <ProjectRuntimeConfigDevFilePath Condition="'$(ProjectRuntimeConfigDevFilePath)' == ''">$(TargetDir)$(AssemblyName).runtimeconfig.dev.json</ProjectRuntimeConfigDevFilePath>
    <IncludeMainProjectInDepsFile Condition=" '$(IncludeMainProjectInDepsFile)' == '' ">true</IncludeMainProjectInDepsFile>
  </PropertyGroup>

  <PropertyGroup>
    <_NativeLibraryPrefix Condition="'$(_NativeLibraryPrefix)' == '' and !$(RuntimeIdentifier.StartsWith('win'))">lib</_NativeLibraryPrefix>

    <_NativeLibraryExtension Condition="'$(_NativeLibraryExtension)' == '' and $(RuntimeIdentifier.StartsWith('win'))">.dll</_NativeLibraryExtension>
    <_NativeLibraryExtension Condition="'$(_NativeLibraryExtension)' == '' and $(RuntimeIdentifier.StartsWith('osx'))">.dylib</_NativeLibraryExtension>
    <_NativeLibraryExtension Condition="'$(_NativeLibraryExtension)' == ''">.so</_NativeLibraryExtension>

    <_NativeExecutableExtension Condition="'$(_NativeExecutableExtension)' == '' and ($(RuntimeIdentifier.StartsWith('win')) or $(DefaultAppHostRuntimeIdentifier.StartsWith('win')))">.exe</_NativeExecutableExtension>
    <_ComHostLibraryExtension Condition="'$(_ComHostLibraryExtension)' == '' and ($(RuntimeIdentifier.StartsWith('win')) or $(DefaultAppHostRuntimeIdentifier.StartsWith('win')))">.dll</_ComHostLibraryExtension>
    <_IjwHostLibraryExtension Condition="'$(_IjwHostLibraryExtension)' == '' and ($(RuntimeIdentifier.StartsWith('win')) or $(DefaultAppHostRuntimeIdentifier.StartsWith('win')))">.dll</_IjwHostLibraryExtension>

    <_DotNetHostExecutableName>dotnet$(_NativeExecutableExtension)</_DotNetHostExecutableName>
    <_DotNetAppHostExecutableNameWithoutExtension>apphost</_DotNetAppHostExecutableNameWithoutExtension>
    <_DotNetAppHostExecutableName>$(_DotNetAppHostExecutableNameWithoutExtension)$(_NativeExecutableExtension)</_DotNetAppHostExecutableName>

    <_DotNetComHostLibraryNameWithoutExtension>comhost</_DotNetComHostLibraryNameWithoutExtension>
    <_DotNetComHostLibraryName>$(_DotNetComHostLibraryNameWithoutExtension)$(_ComHostLibraryExtension)</_DotNetComHostLibraryName>
    <_DotNetIjwHostLibraryNameWithoutExtension>Ijwhost</_DotNetIjwHostLibraryNameWithoutExtension>
    <_DotNetIjwHostLibraryName>$(_DotNetIjwHostLibraryNameWithoutExtension)$(_IjwHostLibraryExtension)</_DotNetIjwHostLibraryName>
    <_DotNetHostPolicyLibraryName>$(_NativeLibraryPrefix)hostpolicy$(_NativeLibraryExtension)</_DotNetHostPolicyLibraryName>
    <_DotNetHostFxrLibraryName>$(_NativeLibraryPrefix)hostfxr$(_NativeLibraryExtension)</_DotNetHostFxrLibraryName>
  </PropertyGroup>

  <PropertyGroup>
    <_DefaultUserProfileRuntimeStorePath>$(HOME)</_DefaultUserProfileRuntimeStorePath>
    <_DefaultUserProfileRuntimeStorePath Condition="'$(OS)' == 'Windows_NT'">$(USERPROFILE)</_DefaultUserProfileRuntimeStorePath>
    <_DefaultUserProfileRuntimeStorePath>$([System.IO.Path]::Combine($(_DefaultUserProfileRuntimeStorePath), '.dotnet', 'store'))</_DefaultUserProfileRuntimeStorePath>
    <UserProfileRuntimeStorePath Condition="'$(UserProfileRuntimeStorePath)' == ''">$(_DefaultUserProfileRuntimeStorePath)</UserProfileRuntimeStorePath>
  </PropertyGroup>

  <!-- Opt into .NET Core resource-serialization strategy by default when targeting frameworks
       that support it by default.
       -->
  <PropertyGroup>
    <GenerateResourceUsePreserializedResources
      Condition="'$(GenerateResourceUsePreserializedResources)' == '' and
                 ('$(TargetFrameworkIdentifier)' == '.NETCoreApp' and '$(_TargetFrameworkVersionWithoutV)' >= '3.0')">true</GenerateResourceUsePreserializedResources>
  </PropertyGroup>

  <PropertyGroup>
    <EmbeddedResourceUseDependentUponConvention 
      Condition="'$(EmbeddedResourceUseDependentUponConvention)' == '' and
                 (('$(TargetFrameworkIdentifier)' == '.NETCoreApp' and '$(_TargetFrameworkVersionWithoutV)' >= '3.0') or
                  ('$(TargetFrameworkIdentifier)' == '.NETStandard' and '$(_TargetFrameworkVersionWithoutV)' >= '2.1'))">true</EmbeddedResourceUseDependentUponConvention>
  </PropertyGroup>

  <PropertyGroup>
    <CoreBuildDependsOn>
      _CheckForBuildWithNoBuild;
      $(CoreBuildDependsOn);
      GenerateBuildDependencyFile;
      GenerateBuildRuntimeConfigurationFiles
    </CoreBuildDependsOn>
  </PropertyGroup>

  <PropertyGroup>
    <CoreCleanDependsOn>
      _SdkBeforeClean;
      $(CoreCleanDependsOn)
    </CoreCleanDependsOn>
  </PropertyGroup>

  <PropertyGroup>
    <RebuildDependsOn>
      _SdkBeforeRebuild;
      $(RebuildDependsOn)
    </RebuildDependsOn>
  </PropertyGroup>

  <!-- TODO: this target should not check GeneratePackageOnBuild.
             remove when https://github.com/NuGet/Home/issues/7801 is fixed.
    -->
  <Target Name="_CheckForBuildWithNoBuild"
          Condition="'$(NoBuild)' == 'true' and '$(GeneratePackageOnBuild)' != 'true'">
    <NETSdkError ResourceName="NoBuildRequested" />
  </Target>

  <Target Name="_ComputePackageReferencePublish">
    <ItemGroup>

      <!-- PrivateAssets="All" means exclude from publish, unless Publish metadata is specified separately -->
      <PackageReference Publish="false"
                        Condition="('%(PackageReference.PrivateAssets)' == 'All') And ('%(PackageReference.Publish)' == '')"/>

      <_ExcludeFromPublishPackageReference Include="@(PackageReference)"
                                           Condition="('%(PackageReference.Publish)' == 'false')" />
    </ItemGroup>
  </Target>

  <!--
    ============================================================
                                        GenerateBuildDependencyFile

    Generates the $(project).deps.json file during Build
    ============================================================
    -->

  <Target Name="GenerateBuildDependencyFile"
          DependsOnTargets="_DefaultMicrosoftNETPlatformLibrary;
                            _HandlePackageFileConflicts;
                            _ComputeReferenceAssemblies;
                            _ComputeUserRuntimeAssemblies;
                            ResolveRuntimePackAssets;
                            _ComputePackageReferencePublish"
          BeforeTargets="CopyFilesToOutputDirectory"
          Condition="'$(GenerateDependencyFile)' == 'true'"
          Inputs="$(ProjectAssetsFile);$(ProjectAssetsCacheFile);$(MSBuildAllProjects)"
          Outputs="$(ProjectDepsFilePath)">

    <!-- Set a dummy Version if it hasn't been set by DefaultAssemblyInfo.targets -->
    <PropertyGroup Condition="'$(UsingNETSdkDefaults)' != 'true'">
      <Version Condition="'$(Version)' == ''">1.0.0</Version>
    </PropertyGroup>

    <ItemGroup>
      <ResolvedCompileFileDefinitions Remove="@(_ConflictPackageFiles)" Condition="'%(_ConflictPackageFiles.ConflictItemType)' == 'Reference'" />
      <NativeCopyLocalItems Remove="@(_ConflictPackageFiles)" Condition="'%(_ConflictPackageFiles.ConflictItemType)' != 'Reference'" />
      <ResourceCopyLocalItems Remove="@(_ConflictPackageFiles)" Condition="'%(_ConflictPackageFiles.ConflictItemType)' != 'Reference'" />
      <RuntimeCopyLocalItems Remove="@(_ConflictPackageFiles)" Condition="'%(_ConflictPackageFiles.ConflictItemType)' != 'Reference'" />
      <RuntimeTargetsCopyLocalItems Remove="@(_ConflictPackageFiles)" Condition="'%(_ConflictPackageFiles.ConflictItemType)' != 'Reference'" />
      <RuntimePackAsset Remove="@(_ConflictPackageFiles)" Condition="'%(_ConflictPackageFiles.ConflictItemType)' != 'Reference'" />
    </ItemGroup>
    
    <GenerateDepsFile ProjectPath="$(MSBuildProjectFullPath)"
                      AssetsFilePath="$(ProjectAssetsFile)"
                      DepsFilePath="$(ProjectDepsFilePath)"
                      TargetFramework="$(TargetFrameworkMoniker)"
                      AssemblyName="$(AssemblyName)"
                      AssemblyExtension="$(TargetExt)"
                      AssemblyVersion="$(Version)"
                      AssemblySatelliteAssemblies="@(IntermediateSatelliteAssembliesWithTargetPath)"
                      ReferencePaths="@(ReferencePath)"
                      ReferenceDependencyPaths="@(ReferenceDependencyPaths)"
                      ReferenceSatellitePaths="@(ReferenceSatellitePaths)"
                      ReferenceAssemblies="@(_ReferenceAssemblies)"
                      RuntimePackAssets="@(RuntimePackAsset)"
                      IncludeMainProject="$(IncludeMainProjectInDepsFile)"
                      RuntimeIdentifier="$(RuntimeIdentifier)"
                      PlatformLibraryName="$(MicrosoftNETPlatformLibrary)"
                      RuntimeFrameworks="@(RuntimeFramework)"
                      CompilerOptions="@(DependencyFileCompilerOptions)"
                      CompileReferences="@(ResolvedCompileFileDefinitions)"
                      ResolvedNuGetFiles="@(NativeCopyLocalItems);@(ResourceCopyLocalItems);@(RuntimeCopyLocalItems)"
                      UserRuntimeAssemblies="@(UserRuntimeAssembly)"
                      ResolvedRuntimeTargetsFiles="@(RuntimeTargetsCopyLocalItems)"
                      IsSelfContained="$(SelfContained)"
                      IncludeRuntimeFileVersions="$(IncludeFileVersionsInDependencyFile)"
                      RuntimeGraphPath="$(BundledRuntimeIdentifierGraphFile)"/>

    <ItemGroup>
      <!-- Do this in an ItemGroup instead of as an output parameter of the GenerateDepsFile task so that it still gets added to the item set
           during incremental builds when the task is skipped -->
      <FileWrites Include="$(ProjectDepsFilePath)" Condition="Exists('$(ProjectDepsFilePath)')"/>
    </ItemGroup>
  </Target>

  <!-- To achieve incremental build with property change. When any property changes, WriteOnlyWhenDifferent will be triggered to write cache file.
  And the cache file's timestamp will be later, and it then triggers the incremental build.-->
  <Target Name="_GenerateRuntimeConfigurationFilesInputCache" DependsOnTargets="_DefaultMicrosoftNETPlatformLibrary">
    <ItemGroup>
      <GenerateRuntimeConfigurationFilesInputs Include="$(_GenerateRuntimeConfigurationPropertyInputsCache)" />

      <_GenerateRuntimeConfigurationPropertyInputsCacheToHash Include="$(TargetFramework)"/>
      <_GenerateRuntimeConfigurationPropertyInputsCacheToHash Include="$(RuntimeIdentifier)"/>
      <_GenerateRuntimeConfigurationPropertyInputsCacheToHash Include="$(RollForward)"/>
      <_GenerateRuntimeConfigurationPropertyInputsCacheToHash Include="$(AdditionalProbingPath)"/>
      <_GenerateRuntimeConfigurationPropertyInputsCacheToHash Include="$(SelfContained)"/>
      <_GenerateRuntimeConfigurationPropertyInputsCacheToHash Include="$(EnableDynamicLoading)"/>
    </ItemGroup>

    <Hash ItemsToHash="@(_GenerateRuntimeConfigurationPropertyInputsCacheToHash)">
      <Output TaskParameter="HashResult" PropertyName="_GenerateRuntimeConfigurationPropertyInputsCacheHash" />
    </Hash>

    <WriteLinesToFile
      Lines="$(_GenerateRuntimeConfigurationPropertyInputsCacheHash)"
      File="$(_GenerateRuntimeConfigurationPropertyInputsCache)"
      Overwrite="True"
      WriteOnlyWhenDifferent="True" />

    <ItemGroup>
      <FileWrites Include="$(_GenerateRuntimeConfigurationPropertyInputsCache)" />
    </ItemGroup>
  </Target>

  <!--
    ============================================================
                                        GenerateBuildRuntimeConfigurationFiles

    Generates the $(project).runtimeconfig.json and $(project).runtimeconfig.dev.json files during Build
    ============================================================
    -->

  <Target Name="GenerateBuildRuntimeConfigurationFiles"
          DependsOnTargets="_GenerateRuntimeConfigurationFilesInputCache"
          BeforeTargets="CopyFilesToOutputDirectory"
          Condition=" '$(GenerateRuntimeConfigurationFiles)' == 'true'"
          Inputs="@(GenerateRuntimeConfigurationFilesInputs)"
          Outputs="$(ProjectRuntimeConfigFilePath);$(ProjectRuntimeConfigDevFilePath)">

    <PropertyGroup>
      <_IsRollForwardSupported Condition="'$(_TargetFrameworkVersionWithoutV)' &gt;= '3.0'">true</_IsRollForwardSupported>
      <RollForward Condition="'$(RollForward)' == '' and '$(EnableDynamicLoading)' == 'true' and '$(_IsRollForwardSupported)' == 'true'">LatestMinor</RollForward>
    </PropertyGroup>

    <!-- RollForward is only supported since .NET Core 3.0, but we should allow limited usage when the app is targeting even lower versions
         This is to let 2.* apps specify that they are OK to run on 3.0 and above. So explicitly allow just Major and LatestMajor
         other values should still keep failing as they won't have any effect when run on 2.*. -->
    <NETSdkError Condition="'$(RollForward)' != '' and '$(RollForward)' != 'Major' and '$(RollForward)' != 'LatestMajor' and '$(_IsRollForwardSupported)' != 'true'"
                 ResourceName="RollForwardRequiresVersion30"/>

    <PropertyGroup>
      <_WriteIncludedFrameworks Condition="'$(SelfContained)' == 'true' and '$(_TargetFrameworkVersionWithoutV)' &gt;= '3.1'">true</_WriteIncludedFrameworks>
    </PropertyGroup>
    
    <GenerateRuntimeConfigurationFiles AssetsFilePath="$(ProjectAssetsFile)"
                                       TargetFrameworkMoniker="$(TargetFrameworkMoniker)"
                                       TargetFramework="$(TargetFramework)"
                                       RuntimeConfigPath="$(ProjectRuntimeConfigFilePath)"
                                       RuntimeConfigDevPath="$(ProjectRuntimeConfigDevFilePath)"
                                       RuntimeIdentifier="$(RuntimeIdentifier)"
                                       PlatformLibraryName="$(MicrosoftNETPlatformLibrary)"
                                       RuntimeFrameworks="@(RuntimeFramework)"
                                       RollForward="$(RollForward)"
                                       UserRuntimeConfig="$(UserRuntimeConfig)"
                                       HostConfigurationOptions="@(RuntimeHostConfigurationOption)"
                                       AdditionalProbingPaths="@(AdditionalProbingPath)"
                                       IsSelfContained="$(SelfContained)"
                                       WriteIncludedFrameworks="$(_WriteIncludedFrameworks)">

    </GenerateRuntimeConfigurationFiles>

    <ItemGroup>
      <!-- Do this in an ItemGroup instead of as an output parameter of the GenerateDepsFile task so that it still gets added to the item set
          during incremental builds when the task is skipped -->
      <FileWrites Include="$(ProjectRuntimeConfigFilePath)" Condition="Exists('$(ProjectRuntimeConfigFilePath)')"/>
      <FileWrites Include="$(ProjectRuntimeConfigDevFilePath)" Condition="Exists('$(ProjectRuntimeConfigDevFilePath)')"/>
    </ItemGroup>

  </Target>

  <!-- Add runtimeconfig.json file to BuiltProjectOutputGroupOutput, so that it will get included in the NuGet package by the Pack target -->
  <Target Name="AddRuntimeConfigFileToBuiltProjectOutputGroupOutput"
          Condition=" '$(GenerateRuntimeConfigurationFiles)' == 'true'"
          BeforeTargets="BuiltProjectOutputGroup">
    <ItemGroup>
      <BuiltProjectOutputGroupOutput Include="$(ProjectRuntimeConfigFilePath)" 
                                     TargetPath="$(ProjectRuntimeConfigFileName)"
                                     FinalOutputPath="$(ProjectRuntimeConfigFilePath)" />
    </ItemGroup>
  </Target>

  <Target Name="_SdkBeforeClean">
    <PropertyGroup Condition="'$(_CleaningWithoutRebuilding)' == ''">
      <_CleaningWithoutRebuilding>true</_CleaningWithoutRebuilding>
      <EmitAssetsLogMessages>false</EmitAssetsLogMessages>
    </PropertyGroup>
  </Target>

  <Target Name="_SdkBeforeRebuild">
    <PropertyGroup>
      <_CleaningWithoutRebuilding>false</_CleaningWithoutRebuilding>
    </PropertyGroup>
  </Target>

  <!--
    ============================================================
                                        DefaultRuntimeHostConfigurationOptions

    Defaults @(RuntimeHostConfigurationOption) items based on MSBuild properties.
    ============================================================
    -->

  <ItemGroup>
    <RuntimeHostConfigurationOption Include="System.GC.Concurrent"
                                    Condition="'$(ConcurrentGarbageCollection)' != ''"
                                    Value="$(ConcurrentGarbageCollection)" />

    <RuntimeHostConfigurationOption Include="System.GC.Server"
                                    Condition="'$(ServerGarbageCollection)' != ''"
                                    Value="$(ServerGarbageCollection)" />

    <RuntimeHostConfigurationOption Include="System.GC.RetainVM"
                                    Condition="'$(RetainVMGarbageCollection)' != ''"
                                    Value="$(RetainVMGarbageCollection)" />

    <RuntimeHostConfigurationOption Include="System.Runtime.TieredCompilation"
                                    Condition="'$(TieredCompilation)' != ''"
                                    Value="$(TieredCompilation)" />

    <RuntimeHostConfigurationOption Include="System.Runtime.TieredCompilation.QuickJit"
                                    Condition="'$(TieredCompilationQuickJit)' != ''"
                                    Value="$(TieredCompilationQuickJit)" />

    <RuntimeHostConfigurationOption Include="System.Runtime.TieredCompilation.QuickJitForLoops"
                                    Condition="'$(TieredCompilationQuickJitForLoops)' != ''"
                                    Value="$(TieredCompilationQuickJitForLoops)" />

    <RuntimeHostConfigurationOption Include="System.Threading.ThreadPool.MinThreads"
                                    Condition="'$(ThreadPoolMinThreads)' != ''"
                                    Value="$(ThreadPoolMinThreads)" />

    <RuntimeHostConfigurationOption Include="System.Threading.ThreadPool.MaxThreads"
                                    Condition="'$(ThreadPoolMaxThreads)' != ''"
                                    Value="$(ThreadPoolMaxThreads)" />
  
    <RuntimeHostConfigurationOption Include="System.Globalization.Invariant"
                                    Condition="'$(InvariantGlobalization)' != ''"
                                    Value="$(InvariantGlobalization)" />
  </ItemGroup>

  <!--
    ============================================================
                                        DefaultAdditionalProbingPaths

    Adds the default @(AdditionalProbingPath) items.
    ============================================================
    -->

  <ItemGroup Condition="'$(GenerateRuntimeConfigurationFiles)' == 'true' and '$(SkipDefaultAdditionalProbingPaths)' != 'true'">
    <!-- Note: can't use Path.Combine here since `|` is an illegal path character -->
    <AdditionalProbingPath Include="$(UserProfileRuntimeStorePath)$([System.IO.Path]::DirectorySeparatorChar)|arch|$([System.IO.Path]::DirectorySeparatorChar)|tfm|" />
  </ItemGroup>

  <PropertyGroup>
    <CompileDependsOn>
      $(CompileDependsOn);
      _CreateAppHost;
      _CreateComHost;
      _GetIjwHostPaths;
    </CompileDependsOn>
  </PropertyGroup>

  <!--
    ============================================================
                                        _CreateAppHost
    If we found a restored apphost, create the modified destination apphost
    with options from the project.
    ============================================================
     -->
  <UsingTask TaskName="CreateAppHost" AssemblyFile="$(MicrosoftNETBuildTasksAssembly)" />
  <Target Name="_CreateAppHost"
          Inputs="@(IntermediateAssembly);$(AppHostSourcePath)"
          Outputs="$(AppHostIntermediatePath)"
          DependsOnTargets="_GetAppHostPaths;CoreCompile"
          Condition="'$(ComputeNETCoreBuildOutputFiles)' == 'true' and
                     '$(AppHostSourcePath)' != '' and
                     Exists('@(IntermediateAssembly)') and
                     Exists('$(AppHostSourcePath)')">
    <PropertyGroup>
      <_UseWindowsGraphicalUserInterface Condition="($(RuntimeIdentifier.StartsWith('win')) or $(DefaultAppHostRuntimeIdentifier.StartsWith('win'))) and '$(OutputType)'=='WinExe'">true</_UseWindowsGraphicalUserInterface>
    </PropertyGroup>

    <CreateAppHost AppHostSourcePath="$(AppHostSourcePath)"
                   AppHostDestinationPath="$(AppHostIntermediatePath)"
                   AppBinaryName="$(AssemblyName)$(TargetExt)"
                   IntermediateAssembly="@(IntermediateAssembly->'%(FullPath)')"
                   WindowsGraphicalUserInterface="$(_UseWindowsGraphicalUserInterface)"
                   Retries="$(CopyRetryCount)"
                   RetryDelayMilliseconds="$(CopyRetryDelayMilliseconds)"
                   />
  </Target>

  <!--
    ============================================================
                                        _GetAppHostPaths

    Gets the path to apphost (restored via packages or in an apphost pack),
    and computes the path for the destination apphost.
    ============================================================
     -->
  <Target Name="_GetAppHostPaths"
          DependsOnTargets="ResolvePackageAssets;ResolveFrameworkReferences"
          Condition="'$(UseAppHost)' == 'true' and '$(_IsExecutable)' == 'true'">

    <NETSdkError Condition="'@(_NativeRestoredAppHostNETCore->Count())' > 1"
             ResourceName="MultipleFilesResolved"
             FormatArguments="$(_DotNetAppHostExecutableName)" />

    <PropertyGroup>
      <!-- AppHostSourcePath will be set from ProcessFrameworkReferences if not using the apphost from the assets file -->
      <AppHostSourcePath Condition="'$(UseAppHostFromAssetsFile)' == 'true'">@(_NativeRestoredAppHostNETCore)</AppHostSourcePath>
    </PropertyGroup>
    <PropertyGroup Condition="'$(UseAppHostFromAssetsFile)' == 'false' Or '@(_NativeRestoredAppHostNETCore)' != ''">
      <AppHostIntermediatePath>$([System.IO.Path]::GetFullPath('$(IntermediateOutputPath)$(AssemblyName)$(_NativeExecutableExtension)'))</AppHostIntermediatePath>
    </PropertyGroup>
    
  </Target>

  <ItemGroup>
    <ClsidMap Include="$(IntermediateOutputPath)$(AssemblyName).clsidmap" />
    <RegFreeComManifest Include="$(IntermediateOutputPath)$(AssemblyName).X.manifest" />
  </ItemGroup>
  
  <!--
      ============================================================
                                        _GenerateClsidMap

    Generates a *.clsidmap file from the built assembly.
    ============================================================
  -->
  <UsingTask TaskName="GenerateClsidMap" AssemblyFile="$(MicrosoftNETBuildTasksAssembly)" />
  <Target Name="_GenerateClsidMap"
          Inputs="@(IntermediateAssembly)"
          Outputs="@(ClsidMap)"
          DependsOnTargets="CoreCompile"
          Condition="'$(ComputeNETCoreBuildOutputFiles)' == 'true' and
                     '$(EnableComHosting)' == 'true'">
    <GenerateClsidMap
        IntermediateAssembly="@(IntermediateAssembly->'%(FullPath)')"
        CLsidMapDestinationPath="@(ClsidMap->'%(FullPath)')" />
  </Target>

  <UsingTask TaskName="CreateComHost" AssemblyFile="$(MicrosoftNETBuildTasksAssembly)" />

  <!--
    ============================================================
                                        _CreateComHost
    If we found a restored comhost, create the modified destination apphost
    with options from the project.
    ============================================================
     -->
  <Target Name="_CreateComHost"
          Inputs="@(IntermediateAssembly);$(ComHostSourcePath)"
          Outputs="$(ComHostIntermediatePath)"
          DependsOnTargets="_GetComHostPaths;CoreCompile;_GenerateClsidMap;_GenerateRegFreeComManifest"
          Condition="'$(ComputeNETCoreBuildOutputFiles)' == 'true' and
                     '$(ComHostIntermediatePath)' != '' and
                     Exists('@(IntermediateAssembly)') and
                     Exists('$(ComHostSourcePath)')">
    <CreateComHost
      ComHostSourcePath="$(ComHostSourcePath)"
      ComHostDestinationPath="$(ComHostIntermediatePath)"
      ClsidMapPath="@(ClsidMap)" />
  </Target>

  <UsingTask TaskName="GenerateRegFreeComManifest" AssemblyFile="$(MicrosoftNETBuildTasksAssembly)" />
  <!-- 
    ============================================================
                                        _GenerateRegFreeComManifest

    Gets the path to the restored comhost, and if the restored comhost
    was present, computes the path for the destination comhost.
    ============================================================
  -->
  <Target Name="_GenerateRegFreeComManifest"
          DependsOnTargets="_GenerateClsidMap;_GetComHostPaths"
          Inputs="@(ClsidMap);@(IntermediateAssembly)"
          Outputs="@(RegFreeComManifest)"
          Condition="'$(ComputeNETCoreBuildOutputFiles)' == 'true' and
                     '$(EnableComHosting)' == 'true' and
                     '$(EnableRegFreeCom)' == 'true'">
    <GenerateRegFreeComManifest
      IntermediateAssembly="@(IntermediateAssembly)"
      ComHostName="$(ComHostFileName)"
      ClsidMapPath="@(ClsidMap)"
      ComManifestPath="@(RegFreeComManifest)" />
  </Target>

  <!--
    ============================================================
                                        _GetComHostPaths

    Gets the path to the restored comhost, and if the restored comhost
    was present, computes the path for the destination comhost.
    ============================================================
     -->
  <Target Name="_GetComHostPaths"
          DependsOnTargets="ResolvePackageAssets;ResolveFrameworkReferences"
          Condition="'$(EnableComHosting)' == 'true' and '$(_IsExecutable)' != 'true'">
    <PropertyGroup>
      <ComHostFileName>$(AssemblyName).comhost$(_ComHostLibraryExtension)</ComHostFileName>
      <ComHostIntermediatePath>$([System.IO.Path]::GetFullPath('$(IntermediateOutputPath)$(ComHostFileName)'))</ComHostIntermediatePath>
    </PropertyGroup>

    <NETSdkError Condition="'$(ComHostSourcePath)' == '' or !Exists('$(ComHostSourcePath)')"
             ResourceName="CannotFindComhost" />
  </Target>

  <!--
    ============================================================
                                        _GetIjwHostPaths

    Gets the path to the restored Ijwhost, and if the restored Ijwhost
    was present, Computes the path for the destination Ijwhost.
    ============================================================
     -->
  <Target Name="_GetIjwHostPaths"
          DependsOnTargets="ResolvePackageAssets;ResolveFrameworkReferences"
          Condition="'$(UseIJWHost)' == 'true'">

    <NETSdkError Condition="'$(IjwHostSourcePath)' == '' or !Exists('$(IjwHostSourcePath)')"
             ResourceName="CannotFindIjwhost" />
  </Target>
  
  <!--
    ============================================================
                                        _ComputeNETCoreBuildOutputFiles

    Computes any files that need to be copied to the build output folder for .NET Core.
    ============================================================
    -->
  <Target Name="_ComputeNETCoreBuildOutputFiles"
          DependsOnTargets="_GetAppHostPaths;_GetComHostPaths;_GetIjwHostPaths"
          BeforeTargets="AssignTargetPaths"
          Condition="'$(ComputeNETCoreBuildOutputFiles)' == 'true'">

    <!-- Fallback to renaming the dotnet host if there is no apphost for self-contained builds. -->
    <PropertyGroup Condition="'$(AppHostIntermediatePath)' == '' and '$(SelfContained)' == 'true'">
      <_CopyAndRenameDotnetHost Condition="'$(_CopyAndRenameDotnetHost)' == ''">true</_CopyAndRenameDotnetHost>
    </PropertyGroup>
    <ItemGroup Condition="'$(_CopyAndRenameDotnetHost)' == 'true'">
      <None Include="@(NativeCopyLocalItems)"
            Condition="'%(NativeCopyLocalItems.FileName)%(NativeCopyLocalItems.Extension)' == '$(_DotNetHostExecutableName)'">
        <Link>$(AssemblyName)$(_NativeExecutableExtension)</Link>
        <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      </None>
    </ItemGroup>


    <ItemGroup Condition="'$(AppHostIntermediatePath)' != '' or '$(_CopyAndRenameDotnetHost)' == 'true'">
      <!--
        If not copying local lock file assemblies, copy the host policy and host fxr libraries for self-contained builds.
        This is required to allow the host to activate in self-contained mode.
      -->
      <None Include="@(NativeCopyLocalItems)"
            Condition="'$(SelfContained)' == 'true' and
                       '$(CopyLocalLockFileAssemblies)' != 'true' and
                       ('%(NativeCopyLocalItems.FileName)%(NativeCopyLocalItems.Extension)' == '$(_DotNetHostPolicyLibraryName)' or
                        '%(NativeCopyLocalItems.FileName)%(NativeCopyLocalItems.Extension)' == '$(_DotNetHostFxrLibraryName)')">
        <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        <CopyToPublishDirectory>Never</CopyToPublishDirectory>
      </None>
    </ItemGroup>
    <ItemGroup Condition="'$(AppHostIntermediatePath)' != ''">
      <None Include="$(AppHostIntermediatePath)">
        <Link>$(AssemblyName)$(_NativeExecutableExtension)</Link>
        <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        <!-- Always copy the AppHost because the contents of the apphost binary can change during the publish step (due to single-file bundling). 
             Always copying the apphost ensures that that the sequence
                 dotnet publish /p:PublishSingleFile=true 
                 dotnet publish /p:PublishSingleFile=false
             places the correct unbundled apphost in the publish directory. -->
        <CopyToPublishDirectory>Always</CopyToPublishDirectory>
      </None>
    </ItemGroup>
    
    <ItemGroup Condition="'$(ComHostIntermediatePath)' != ''">
      <None Include="$(ComHostIntermediatePath)">
        <Link>$(AssemblyName).$(_DotNetComHostLibraryName)</Link>
        <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      </None>
      <None Include="@(RegFreeComManifest)" Condition="'$(EnableRegFreeCom)' == 'true'">
        <Link>%(FileName)%(Extension)</Link>
        <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      </None>

    </ItemGroup>

    <ItemGroup Condition="'$(IjwHostSourcePath)' != '' and '$(UseIJWHost)' == 'true'">
      <None Include="$(IjwHostSourcePath)">
        <Link>$(_DotNetIjwHostLibraryName)</Link>
        <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      </None>
    </ItemGroup>
  </Target>

  <!--
    ============================================================
                                        _ComputeReferenceAssemblies

    Computes references that are only used at compile-time.
    ============================================================
    -->
  <Target Name="_ComputeReferenceAssemblies"
          DependsOnTargets="ResolveAssemblyReferences">

    <ItemGroup>
      <_FrameworkReferenceAssemblies Include="@(ReferencePath)"
                                     Condition="(%(ReferencePath.FrameworkFile) == 'true' or
                                                %(ReferencePath.ResolvedFrom) == 'ImplicitlyExpandDesignTimeFacades')
                                            and ('%(ReferencePath.NuGetSourceType)' == ''  or
                                                 '%(ReferencePath.NuGetIsFrameworkReference)' == 'true')" />

      <!--
      "ReferenceOnly" assemblies are assemblies that are only used at compile-time, and they can't be resolved
      by the normal compile-assembly resolvers at runtime:
      1. App local
      2. NuGet/Package layout
      3. ProgramFiles\Reference Assemblies
      These assemblies need to be copied to the 'refs' folder for both build and publish.
      -->
      <_ReferenceOnlyAssemblies Include="@(ReferencePath)"
                                Exclude="@(_FrameworkReferenceAssemblies)"
                                Condition="%(ReferencePath.CopyLocal) != 'true' and
                                           %(ReferencePath.NuGetSourceType) == ''" />

      <_ReferenceAssemblies Include="@(_FrameworkReferenceAssemblies)" />
      <_ReferenceAssemblies Include="@(_ReferenceOnlyAssemblies)" />
    </ItemGroup>

  </Target>
  <!--
    ============================================================
                                        _ComputeUserRuntimeAssemblies

    Computes references or reference dependencies that are copy local.

     NOTE: NuGet and framework references are never copy local so those are not included here.
           These will be project references and direct references and their copy local dependencies.
    ============================================================
    -->
  <Target Name="_ComputeUserRuntimeAssemblies">
    <ItemGroup>
      <!-- IncludeRuntimeDependency=true metadata is escape hatch to include a non-copy local reference in deps file as a runtime dependency -->
      <ReferencePath>
        <IncludeRuntimeDependency Condition="'%(ReferencePath.IncludeRuntimeDependency)' == '' and '%(ReferencePath.CopyLocal)' == 'true'">true</IncludeRuntimeDependency>
      </ReferencePath>

      <ReferenceDependencyPaths>
        <IncludeRuntimeDependency Condition="'%(ReferenceDependencyPaths.IncludeRuntimeDependency)' == '' and '%(ReferenceDependencyPaths.CopyLocal)' == 'true'">true</IncludeRuntimeDependency>
      </ReferenceDependencyPaths>

      <UserRuntimeAssembly Include="@(ReferencePath->WithMetadataValue('IncludeRuntimeDependency', 'true'))"  />
      <UserRuntimeAssembly Include="@(ReferenceDependencyPaths->WithMetadataValue('IncludeRuntimeDependency', 'true'))" />
    </ItemGroup>
  </Target>

  <!--
    ============================================================
                                        Run Information

    The ProcessStart information that can be used to run this project.
    ============================================================
    -->

  <PropertyGroup>
    <RunWorkingDirectory Condition="'$(RunWorkingDirectory)' == ''">$(StartWorkingDirectory)</RunWorkingDirectory>
  </PropertyGroup>

  <Choose>
    <When Condition="'$(StartAction)' == 'Program'">
      <PropertyGroup>
        <RunCommand Condition="'$(RunCommand)' == ''">$(StartProgram)</RunCommand>
        <RunArguments Condition="'$(RunArguments)' == ''">$(StartArguments)</RunArguments>
      </PropertyGroup>
    </When>

    <When Condition="'$(TargetFrameworkIdentifier)' == '.NETCoreApp' and '$(_IsExecutable)' == 'true'">
      <PropertyGroup Condition="'$(UseAppHost)' != 'true'">
        <!-- TODO: https://github.com/dotnet/sdk/issues/20 Need to get the DotNetHost path from MSBuild -->
        <RunCommand Condition="'$(RunCommand)' == ''">dotnet</RunCommand>

        <_NetCoreRunArguments>exec &quot;$(TargetPath)&quot;</_NetCoreRunArguments>
        <RunArguments Condition="'$(RunArguments)' == '' and '$(StartArguments)' != ''">$(_NetCoreRunArguments) $(StartArguments)</RunArguments>
        <RunArguments Condition="'$(RunArguments)' == ''">$(_NetCoreRunArguments)</RunArguments>
      </PropertyGroup>

      <PropertyGroup Condition="'$(UseAppHost)' == 'true'">
        <RunCommand Condition="'$(RunCommand)' == ''">$(TargetDir)$(AssemblyName)$(_NativeExecutableExtension)</RunCommand>
        <RunArguments Condition="'$(RunArguments)' == ''">$(StartArguments)</RunArguments>
      </PropertyGroup>
    </When>

    <When Condition="'$(TargetFrameworkIdentifier)' == '.NETFramework' and '$(_IsExecutable)' == 'true'">
      <PropertyGroup>
        <RunCommand Condition="'$(RunCommand)' == ''">$(TargetPath)</RunCommand>
        <RunArguments Condition="'$(RunArguments)' == ''">$(StartArguments)</RunArguments>
      </PropertyGroup>
    </When>
  </Choose>

  <PropertyGroup>
    <!-- Ensure $(RunWorkingDirectory) is a full path -->
    <RunWorkingDirectory Condition="'$(RunWorkingDirectory)' != '' and '$([System.IO.Path]::IsPathRooted($(RunWorkingDirectory)))' != 'true'">$([System.IO.Path]::GetFullPath($([System.IO.Path]::Combine('$(MSBuildProjectDirectory)', '$(RunWorkingDirectory)'))))</RunWorkingDirectory>
  </PropertyGroup>

  <!--
    ============================================================
                    CoreGenerateSatelliteAssemblies
    ============================================================
    -->
  <PropertyGroup>
    <CreateSatelliteAssembliesDependsOn>
      $(CreateSatelliteAssembliesDependsOn);
      CoreGenerateSatelliteAssemblies
    </CreateSatelliteAssembliesDependsOn>
  </PropertyGroup>

  <UsingTask TaskName="GetAssemblyAttributes" AssemblyFile="$(MicrosoftNETBuildTasksAssembly)" />

  <Target Name="CoreGenerateSatelliteAssemblies"
          DependsOnTargets="ResolveAssemblyReferences;_GetAssemblyInfoFromTemplateFile"
          Inputs="$(MSBuildAllProjects);@(_SatelliteAssemblyResourceInputs);$(IntermediateOutputPath)$(TargetName)$(TargetExt)"
          Outputs="$(IntermediateOutputPath)%(Culture)\$(TargetName).resources.dll"
          Condition="'@(_SatelliteAssemblyResourceInputs)' != ''  and '$(GenerateSatelliteAssembliesForCore)' == 'true' ">

    <MakeDir Directories="@(_SatelliteAssemblyResourceInputs->'$(IntermediateOutputPath)%(Culture)')" />

    <PropertyGroup>
      <_AssemblyInfoFile>$(IntermediateOutputPath)%(_SatelliteAssemblyResourceInputs.Culture)\$(TargetName).resources.cs</_AssemblyInfoFile>
      <_OutputAssembly>$(IntermediateOutputPath)%(_SatelliteAssemblyResourceInputs.Culture)\$(TargetName).resources.dll</_OutputAssembly>
    </PropertyGroup>

    <ItemGroup>
      <SatelliteAssemblyAttribute Include="System.Reflection.AssemblyCultureAttribute" Condition="'%(_SatelliteAssemblyResourceInputs.Culture)' != ''">
        <_Parameter1>%(_SatelliteAssemblyResourceInputs.Culture)</_Parameter1>
      </SatelliteAssemblyAttribute>
    </ItemGroup>

    <WriteCodeFragment AssemblyAttributes="@(SatelliteAssemblyAttribute)" Language="C#" OutputFile="$(_AssemblyInfoFile)">
      <Output TaskParameter="OutputFile" ItemName="FileWrites" />
    </WriteCodeFragment>

    <PropertyGroup Condition="'$(UseSharedCompilation)' == ''">
      <UseSharedCompilation>true</UseSharedCompilation>
    </PropertyGroup>

    <Csc Resources="@(_SatelliteAssemblyResourceInputs)"
         Sources="$(_AssemblyInfoFile)"
         OutputAssembly="$(_OutputAssembly)"
         References="@(ReferencePath)"
         KeyContainer="$(KeyContainerName)"
         KeyFile="$(KeyOriginatorFile)"
         NoConfig="true"
         NoLogo="$(NoLogo)"
         NoStandardLib="$(NoCompilerStandardLib)"
         PublicSign="$(PublicSign)"
         DelaySign="$(DelaySign)"
         Deterministic="$(Deterministic)"
         DisabledWarnings="$(DisabledWarnings)"
         WarningLevel="$(WarningLevel)"
         WarningsAsErrors="$(WarningsAsErrors)"
         WarningsNotAsErrors="$(WarningsNotAsErrors)"
         TargetType="Library"
         ToolExe="$(CscToolExe)"
         ToolPath="$(CscToolPath)"
         UseSharedCompilation="$(UseSharedCompilation)">

      <Output TaskParameter="OutputAssembly" ItemName="FileWrites"/>
    </Csc>
  </Target>

  <Target Name="_GetAssemblyInfoFromTemplateFile">
    <GetAssemblyAttributes PathToTemplateFile="$(IntermediateOutputPath)$(TargetName)$(TargetExt)" >
      <Output TaskParameter="AssemblyAttributes" ItemName="_AssemblyAttributesFromTemplateFile" />
    </GetAssemblyAttributes>

    <ItemGroup>
      <SatelliteAssemblyAttribute Include="@(_AssemblyAttributesFromTemplateFile)" />
    </ItemGroup>

  </Target>

  <!--
  ============================================================
                           _DefaultMicrosoftNETPlatformLibrary

  .NET Core apps can have shared frameworks that are pre-installed on the target machine, thus the app is "portable"
  to any machine that already has the shared framework installed. For .NET Core 1.x and 2.x, a "platform" library
  is declared. The platform library and its dependencies will be excluded from the publish output.
  
  For .NET Core 3 and up, targeting packs and runtime packs are used for shared framework assets instead of PackageReference
  ============================================================
  -->
  <Target Name="_DefaultMicrosoftNETPlatformLibrary">

    <PropertyGroup Condition="'$(MicrosoftNETPlatformLibrary)' == ''">
      <MicrosoftNETPlatformLibrary Condition="'$(TargetFrameworkIdentifier)' == '.NETCoreApp' And '$(_TargetFrameworkVersionWithoutV)' &lt; '3.0'">Microsoft.NETCore.App</MicrosoftNETPlatformLibrary>
    </PropertyGroup>

  </Target>

  <!--
  ============================================================
                                      GetAllRuntimeIdentifiers
  ============================================================
  -->
  <Target Name="GetAllRuntimeIdentifiers"
          Returns="$(RuntimeIdentifiers);$(RuntimeIdentifier)" />

  <!--
  ============================================================
                                      InjectTargetPathMetadata

  Update TargetPathWithTargetPlatformMoniker with target framework
  identifier and version metadata.  This is so that the
  ImplicitlyExpandNETStandardFacades target can determine if a
  referenced project needs the .NET Standard facades even if
  the project hasn't been compiled to disk yet.

  See https://github.com/dotnet/sdk/issues/1403 for more context
  ============================================================
  -->

  <ItemDefinitionGroup>
    <TargetPathWithTargetPlatformMoniker>
      <TargetFrameworkIdentifier>$(TargetFrameworkIdentifier)</TargetFrameworkIdentifier>
      <TargetFrameworkVersion>$(_TargetFrameworkVersionWithoutV)</TargetFrameworkVersion>
    </TargetPathWithTargetPlatformMoniker>
  </ItemDefinitionGroup>

  <!--
  ============================================================
                                         Project Capabilities
  ============================================================
  -->

  <ItemGroup Condition="'$(TargetFrameworkIdentifier)' == '.NETCoreApp' and '$(_IsExecutable)' == 'true'">
    <ProjectCapability Include="CrossPlatformExecutable" />
  </ItemGroup>

  <!-- Reference Manager capabilities -->
  <ItemGroup Condition="'$(TargetFrameworkIdentifier)' == '.NETCoreApp'">
    <ProjectCapability Remove="ReferenceManagerAssemblies" />
  </ItemGroup>

  <Import Project="$(MSBuildThisFileDirectory)Microsoft.NET.DisableStandardFrameworkResolution.targets" Condition="'$(DisableStandardFrameworkResolution)' == 'true'" />
  <Import Project="$(MSBuildThisFileDirectory)Microsoft.NET.DesignerSupport.targets" />
  <Import Project="$(MSBuildThisFileDirectory)Microsoft.NET.GenerateAssemblyInfo.targets" Condition="'$(UsingNETSdkDefaults)' == 'true'"/>
  <Import Project="$(MSBuildThisFileDirectory)Microsoft.NET.GenerateSupportedRuntime.targets" />
  <Import Project="$(MSBuildThisFileDirectory)Microsoft.NET.ComposeStore.targets" Condition="'$(UsingNETSdkDefaults)' == 'true'" />
  <Import Project="$(MSBuildThisFileDirectory)Microsoft.NET.CrossGen.targets" />
  <Import Project="$(MSBuildThisFileDirectory)Microsoft.NET.ObsoleteReferences.targets" />
  <Import Project="$(MSBuildThisFileDirectory)Microsoft.NET.Publish.targets" />
  <Import Project="$(MSBuildThisFileDirectory)Microsoft.NET.PackStubs.targets" Condition="'$(Language)' == 'C++'"/>
  <Import Project="$(MSBuildThisFileDirectory)Microsoft.NET.PackTool.targets" />
  <Import Project="$(MSBuildThisFileDirectory)Microsoft.NET.PackProjectTool.targets" />
  <Import Project="$(MSBuildThisFileDirectory)Microsoft.NET.PreserveCompilationContext.targets" />
  <Import Project="$(MSBuildThisFileDirectory)Microsoft.NET.ConflictResolution.targets" />
  <Import Project="$(MSBuildThisFileDirectory)Microsoft.NET.Sdk.CSharp.targets" Condition="'$(Language)' == 'C#'" />
  <Import Project="$(MSBuildThisFileDirectory)Microsoft.NET.Sdk.VisualBasic.targets" Condition="'$(Language)' == 'VB'" />
  <Import Project="$(MSBuildThisFileDirectory)Microsoft.NET.Sdk.FSharp.targets" Condition="'$(Language)' == 'F#'" />
  <Import Project="$(MSBuildThisFileDirectory)Microsoft.NET.ILLink.targets" />
</Project>
