<!--
***********************************************************************************************
Microsoft.NET.SupportedTargetFrameworks.targets

WARNING:  DO NOT MODIFY this file unless you are knowledgeable about MSBuild and have
          created a backup copy.  Incorrect changes to this file will make it
          impossible to load or build your projects from the command-line or the IDE.

Copyright (c) .NET Foundation. All rights reserved. 
***********************************************************************************************
-->

<!-- This file contains a list of the TFMs that are supported by this SDK for .NET Core, .NET Standard, and .NET Framework.
     This is used by VS to show the list of frameworks to which projects can be retargeted. -->
<Project>
    <!-- .NET Core App -->
    <ItemGroup>
        <SupportedNETCoreAppTargetFramework Include=".NETCoreApp,Version=v1.0" DisplayName=".NET Core 1.0" />
        <SupportedNETCoreAppTargetFramework Include=".NETCoreApp,Version=v1.1" DisplayName=".NET Core 1.1" />
        <SupportedNETCoreAppTargetFramework Include=".NETCoreApp,Version=v2.0" DisplayName=".NET Core 2.0" />
        <SupportedNETCoreAppTargetFramework Include=".NETCoreApp,Version=v2.1" DisplayName=".NET Core 2.1" />
        <SupportedNETCoreAppTargetFramework Include=".NETCoreApp,Version=v2.2" DisplayName=".NET Core 2.2" />
        <SupportedNETCoreAppTargetFramework Include=".NETCoreApp,Version=v3.0" DisplayName=".NET Core 3.0" />
        <SupportedNETCoreAppTargetFramework Include=".NETCoreApp,Version=v3.1" DisplayName=".NET Core 3.1" />
    </ItemGroup>

    <!-- .NET Standard -->
    <ItemGroup>
        <SupportedNETStandardTargetFramework Include=".NETStandard,Version=v1.0" DisplayName=".NET Standard 1.0" />
        <SupportedNETStandardTargetFramework Include=".NETStandard,Version=v1.1" DisplayName=".NET Standard 1.1" />
        <SupportedNETStandardTargetFramework Include=".NETStandard,Version=v1.2" DisplayName=".NET Standard 1.2" />
        <SupportedNETStandardTargetFramework Include=".NETStandard,Version=v1.3" DisplayName=".NET Standard 1.3" />
        <SupportedNETStandardTargetFramework Include=".NETStandard,Version=v1.4" DisplayName=".NET Standard 1.4" />
        <SupportedNETStandardTargetFramework Include=".NETStandard,Version=v1.5" DisplayName=".NET Standard 1.5" />
        <SupportedNETStandardTargetFramework Include=".NETStandard,Version=v1.6" DisplayName=".NET Standard 1.6" />
        <SupportedNETStandardTargetFramework Include=".NETStandard,Version=v2.0" DisplayName=".NET Standard 2.0" />
        <SupportedNETStandardTargetFramework Include=".NETStandard,Version=v2.1" DisplayName=".NET Standard 2.1" />
    </ItemGroup>

    <!-- .NET Framework -->
    <ItemGroup>
        <SupportedNETFrameworkTargetFramework Include=".NETFramework,Version=v2.0"   DisplayName=".NET Framework 2.0" />
        <SupportedNETFrameworkTargetFramework Include=".NETFramework,Version=v3.0"   DisplayName=".NET Framework 3.0" />
        <SupportedNETFrameworkTargetFramework Include=".NETFramework,Version=v3.5"   DisplayName=".NET Framework 3.5" />
        <SupportedNETFrameworkTargetFramework Include=".NETFramework,Version=v4.0"   DisplayName=".NET Framework 4.0" />
        <SupportedNETFrameworkTargetFramework Include=".NETFramework,Version=v4.5"   DisplayName=".NET Framework 4.5" />
        <SupportedNETFrameworkTargetFramework Include=".NETFramework,Version=v4.5.1" DisplayName=".NET Framework 4.5.1" />
        <SupportedNETFrameworkTargetFramework Include=".NETFramework,Version=v4.5.2" DisplayName=".NET Framework 4.5.2" />
        <SupportedNETFrameworkTargetFramework Include=".NETFramework,Version=v4.6"   DisplayName=".NET Framework 4.6" />
        <SupportedNETFrameworkTargetFramework Include=".NETFramework,Version=v4.6.1" DisplayName=".NET Framework 4.6.1" />
        <SupportedNETFrameworkTargetFramework Include=".NETFramework,Version=v4.6.2" DisplayName=".NET Framework 4.6.2" />
        <SupportedNETFrameworkTargetFramework Include=".NETFramework,Version=v4.7"   DisplayName=".NET Framework 4.7" />
        <SupportedNETFrameworkTargetFramework Include=".NETFramework,Version=v4.7.1" DisplayName=".NET Framework 4.7.1" />
        <SupportedNETFrameworkTargetFramework Include=".NETFramework,Version=v4.7.2" DisplayName=".NET Framework 4.7.2" />
        <SupportedNETFrameworkTargetFramework Include=".NETFramework,Version=v4.8"   DisplayName=".NET Framework 4.8" />
    </ItemGroup>

    <!-- All supported target frameworks -->
    <ItemGroup>
        <SupportedTargetFramework Include="@(SupportedNETCoreAppTargetFramework);@(SupportedNETStandardTargetFramework);@(SupportedNETFrameworkTargetFramework)" />
    </ItemGroup>
</Project>
