<!--
***********************************************************************************************
Microsoft.NET.TargetFrameworkInference.targets

WARNING:  DO NOT MODIFY this file unless you are knowledgeable about MSBuild and have
          created a backup copy.  Incorrect changes to this file will make it
          impossible to load or build your projects from the command-line or the IDE.

Copyright (c) .NET Foundation. All rights reserved. 
***********************************************************************************************
-->
<Project ToolsVersion="14.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">

  <!-- 
    Note that this file is only included when $(TargetFramework) is set and so we do not need to check that here.

    Common targets require that $(TargetFrameworkIdentifier) and $(TargetFrameworkVersion) are set by static evaluation
    before they are imported. In common cases (currently netstandard, netcoreapp, or net), we infer them from the short
    names given via TargetFramework to allow for terseness and lack of duplication in project files.

    For other cases, the user must supply them manually.

    For cases where inference is supported, the user need only specify the targets in TargetFrameworks, e.g:
      <PropertyGroup>
        <TargetFrameworks>net45;netstandard1.0</TargetFrameworks>
      </PropertyGroup>

    For cases where inference is not supported, identifier, version and profile can be specified explicitly as follows:
       <PropertyGroup>
         <TargetFrameworks>portable-net451+win81;xyz1.0</TargetFrameworks>
       <PropertyGroup>
       <PropertyGroup Condition="'$(TargetFramework)' == 'portable-net451+win81'">
         <TargetFrameworkIdentifier>.NETPortable</TargetFrameworkIdentifier>
         <TargetFrameworkVersion>v4.6</TargetFrameworkVersion>
         <TargetFrameworkProfile>Profile44</TargetFrameworkProfile>
       </PropertyGroup>
       <PropertyGroup Condition="'$(TargetFramework)' == 'xyz1.0'">
         <TargetFrameworkIdentifier>Xyz</TargetFrameworkVersion>
       <PropertyGroup>

    Note in the xyz1.0 case, which is meant to demonstrate a framework we don't yet recognize, we can still
    infer the version of 1.0. The user can also override it as always we honor a TargetFrameworkIdentifier
    or TargetFrameworkVersion that is already set.
   -->

  <!-- Split $(TargetFramework) (e.g. net45) into short identifier and short version (e.g. 'net' and '45'). -->
  <PropertyGroup Condition="'$(TargetFramework)' != '' and !$(TargetFramework.Contains(',')) and !$(TargetFramework.Contains('+'))">
   <_ShortFrameworkIdentifier>$(TargetFramework.TrimEnd('.0123456789'))</_ShortFrameworkIdentifier>
   <_ShortFrameworkVersion>$(TargetFramework.Substring($(_ShortFrameworkIdentifier.Length)))</_ShortFrameworkVersion>
  </PropertyGroup>

  <!-- Map short name to long name. See earlier comment for example of how to work with identifiers that are not recognized here. -->
  <PropertyGroup Condition="'$(TargetFrameworkIdentifier)' == ''">
    <TargetFrameworkIdentifier Condition="'$(_ShortFrameworkIdentifier)' == 'netstandard'">.NETStandard</TargetFrameworkIdentifier>
    <TargetFrameworkIdentifier Condition="'$(_ShortFrameworkIdentifier)' == 'netcoreapp'">.NETCoreApp</TargetFrameworkIdentifier>
    <TargetFrameworkIdentifier Condition="'$(_ShortFrameworkIdentifier)' == 'net'">.NETFramework</TargetFrameworkIdentifier>
  </PropertyGroup>

  <!-- Versions with dots are taken as is and just given leading 'v'. -->
  <PropertyGroup Condition="'$(TargetFrameworkVersion)' == '' and '$(_ShortFrameworkVersion)' != '' and $(_ShortFrameworkVersion.Contains('.'))">
    <TargetFrameworkVersion>v$(_ShortFrameworkVersion)</TargetFrameworkVersion>
  </PropertyGroup>

  <!-- Versions with no dots and up to 3 characters get leading 'v' and implicit dots between characters. -->
  <PropertyGroup Condition="'$(TargetFrameworkVersion)' == '' and '$(_ShortFrameworkVersion)' != ''">
    <TargetFrameworkVersion Condition="$(_ShortFrameworkVersion.Length) == 1">v$(_ShortFrameworkVersion[0]).0</TargetFrameworkVersion>
    <TargetFrameworkVersion Condition="$(_ShortFrameworkVersion.Length) == 2">v$(_ShortFrameworkVersion[0]).$(_ShortFrameworkVersion[1])</TargetFrameworkVersion>
    <TargetFrameworkVersion Condition="$(_ShortFrameworkVersion.Length) == 3">v$(_ShortFrameworkVersion[0]).$(_ShortFrameworkVersion[1]).$(_ShortFrameworkVersion[2])</TargetFrameworkVersion>
  </PropertyGroup>

  <!-- 
    Trigger an error if we're unable to infer the framework identifier and version. 

    We have to evaluate this here and not in the target because by the time the target runs,
    Microsoft.Common.targets will have defaulted to .NETFramework,Version=v4.0
  -->
  <PropertyGroup Condition="'$(TargetFrameworkIdentifier)' == '' or '$(TargetFrameworkVersion)' == ''">
    <_UnsupportedTargetFrameworkError>true</_UnsupportedTargetFrameworkError>
  </PropertyGroup>

  <!--
    NOTE: We must not validate the TFM before restore target runs as it prevents adding additional TFM 
          support from being provided by a nuget package such as MSBuild.Sdk.Extras.

          We run before RunResolvePackageDependencies and GetReferenceAssemblyPaths so that design-time builds
          which do not currently invoke _CheckForInvalidConfigurationAndPlatform, will not trigger spurious 
          errors that are only consequences of the root cause identified here.
  -->
  <Target Name="_CheckForUnsupportedTargetFramework"
          BeforeTargets="_CheckForInvalidConfigurationAndPlatform;RunResolvePackageDependencies;GetFrameworkPaths;GetReferenceAssemblyPaths"
          Condition="'$(_UnsupportedTargetFrameworkError)' == 'true'"
          >
    <NETSdkError Condition="!$(TargetFramework.Contains(';'))"
                 ResourceName="CannotInferTargetFrameworkIdentifierAndVersion"
                 FormatArguments="$([MSBuild]::Escape('$(TargetFramework)'))" />

    <NETSdkError Condition="$(TargetFramework.Contains(';'))"
                 ResourceName="TargetFrameworkWithSemicolon"
                 FormatArguments="$([MSBuild]::Escape('$(TargetFramework)'))" />
  </Target>

  <Target Name="_CollectTargetFrameworkForTelemetry" AfterTargets="_CheckForUnsupportedTargetFramework">
    <Telemetry EventName="targetframeworkeval" EventData="TargetFrameworkVersion=$([MSBuild]::Escape('$(TargetFrameworkMoniker)'))" />
  </Target>

  <!--
    Don't leave TargetFrameworkVersion empty if it still hasn't been determined. We will trigger the error above,
    but we need this to be a valid version so that our error message does not get pre-empted by failure to interpret
    version comparison expressions, which is currently unrecoverable in VS.

    Also don't leave TargetFrameworkIdentifier unset as it will be defaulted to .NETFramework by common targets, which
    can cause restore (which we cannot block, see above) to silently succeed for empty TargetFramework.
  -->
  <PropertyGroup Condition="'$(TargetFrameworkVersion)' == ''">
    <TargetFrameworkVersion >v0.0</TargetFrameworkVersion>
  </PropertyGroup>
  <PropertyGroup Condition="'$(TargetFrameworkIdentifier)' == ''">
    <TargetFrameworkIdentifier>_</TargetFrameworkIdentifier>
  </PropertyGroup>
  
  <!--
    Trigger an error if targeting a higher version of .NET Core or .NET Standard than is supported by the current SDK.
  -->
  <Target Name="_CheckForUnsupportedNETCoreVersion" BeforeTargets="_CheckForInvalidConfigurationAndPlatform;Restore;CollectPackageReferences"
          Condition="'$(TargetFrameworkIdentifier)' == '.NETCoreApp' And '$(NETCoreAppMaximumVersion)' != ''">

    <NETSdkError Condition="'$(_TargetFrameworkVersionWithoutV)' > '$(NETCoreAppMaximumVersion)'"
                 ResourceName="UnsupportedTargetFrameworkVersion"
                 FormatArguments=".NET Core;$(_TargetFrameworkVersionWithoutV);$(NETCoreAppMaximumVersion)"
      />
  </Target>

  <!--
    Trigger an error if C++ project targeting a lower version of .NET Core than 3.1.
  -->
  <Target Name="_CheckForUnsupportedCppNETCoreVersion" BeforeTargets="_CheckForInvalidConfigurationAndPlatform;Restore;CollectPackageReferences">
    <NETSdkError Condition="'$(TargetFrameworkIdentifier)' == '.NETCoreApp' and '$(_TargetFrameworkVersionWithoutV)' &lt; '3.1' and '$(Language)' == 'C++'"
                 ResourceName="CppRequiresTFMVersion31"
      />
  </Target>

  <PropertyGroup  Condition="'$(TargetFrameworkIdentifier)' == '.NETStandard' And '$(NETStandardMaximumVersion)' == ''">
    <NETStandardMaximumVersion>2.1</NETStandardMaximumVersion>
  </PropertyGroup>

  <Target Name="_CheckForUnsupportedNETStandardVersion" BeforeTargets="_CheckForInvalidConfigurationAndPlatform;Restore;CollectPackageReferences"
          Condition="'$(TargetFrameworkIdentifier)' == '.NETStandard' And '$(NETStandardMaximumVersion)' != ''">

    <NETSdkError Condition="'$(_TargetFrameworkVersionWithoutV)' > '$(NETStandardMaximumVersion)'"
                 ResourceName="UnsupportedTargetFrameworkVersion"
                 FormatArguments=".NET Standard;$(_TargetFrameworkVersionWithoutV);$(NETStandardMaximumVersion)"
      />    
  </Target>

  <Target Name="_CheckForUnsupportedTargetFrameworkAndFeatureCombination" BeforeTargets="_CheckForInvalidConfigurationAndPlatform;Restore;CollectPackageReferences"
        Condition="'$(TargetFrameworkIdentifier)' == '.NETFramework'">

    <NETSdkError Condition="'$(UsingNETSdkDefaults)' != 'true'"
                 ResourceName="NETFrameworkWithoutUsingNETSdkDefaults" />
  </Target>

  <!-- Exclude files from OutputPath and IntermediateOutputPath from default item globs.  Use the value
       of these properties before the TargetFramework is appended, so that if these values are specified
       in the project file, the specified value will be used for the exclude.
       
       We may be able to move this to Microsoft.NET.Sdk.DefaultItems.targets (where the other DefaultItemExcludes
       are defined) if we fix https://github.com/dotnet/sdk/issues/550
       -->
  <PropertyGroup>
    <DefaultItemExcludes>$(DefaultItemExcludes);$(OutputPath)/**</DefaultItemExcludes>
    <DefaultItemExcludes>$(DefaultItemExcludes);$(IntermediateOutputPath)/**</DefaultItemExcludes>
  </PropertyGroup>

  <!-- Projects which don't use Microsoft.NET.Sdk will typically define the OutputPath directly (usually in a
       Configuration-specific PropertyGroup), so in that case we won't append to it by default. -->
  <PropertyGroup Condition="'$(UsingNETSdkDefaults)' == 'true'">
    <AppendTargetFrameworkToOutputPath Condition="'$(AppendTargetFrameworkToOutputPath)' == ''">true</AppendTargetFrameworkToOutputPath>
  </PropertyGroup>

  <!--
    Append $(TargetFramework) directory to output and intermediate paths to prevent bin clashes between
    targets.
   -->
  <PropertyGroup Condition="'$(AppendTargetFrameworkToOutputPath)' == 'true' and '$(TargetFramework)' != '' and '$(_UnsupportedTargetFrameworkError)' != 'true'">
    <IntermediateOutputPath>$(IntermediateOutputPath)$(TargetFramework.ToLowerInvariant())\</IntermediateOutputPath>
    <OutputPath>$(OutputPath)$(TargetFramework.ToLowerInvariant())\</OutputPath>
  </PropertyGroup>

</Project>
