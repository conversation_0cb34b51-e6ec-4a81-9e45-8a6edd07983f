﻿using GainServicingAPI;
using GainServicingAPI.Controllers.Salesforce;
using GainServicingAPI.DAL.Salesforce.QueryDefinitions;
using GainServicingAPI.Database;
using GainServicingAPI.Helpers;
using GainServicingAPI.Helpers.Interfaces;
using GainServicingAPI.Helpers.Salesforce.Interfaces;
using GainServicingAPI.Model;
using GainServicingAPI.Model.HttpRequestBodies;
using GainServicingAPI.Model.Salesforce;
using GainServicingAPI.Model.ValueObjects;
using GainServicingAPIIntegrationTest.Mocks;
using GainServicingAPIIntegrationTest.TestHelpers;
using Microsoft.Extensions.DependencyInjection;
using Newtonsoft.Json;
using Salesforce.Common.Models.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Threading.Tasks;
using System.Web;
using VerifyXunit;
using Xunit;

namespace GainServicingAPIIntegrationTest.Controllers
{
    [UsesVerify]
    [Collection("Database tests")]
    public class PayoffControllerTest : IClassFixture<CustomWebApplicationFactory<Program>>
    {
        private readonly CustomWebApplicationFactory<Program> _factory;

        public PayoffControllerTest(CustomWebApplicationFactory<Program> factory)
        {
            _factory = factory;

            var migrator = factory.Services.GetRequiredService<DatabaseMigrator>();
            migrator.Migrate();
            migrator.SeedDatabase();
        }

        [Fact(Skip = "hitting salesforce")]
        public async Task ShouldSentOutDataTest()
        {
            var client = _factory.CreateClient();
            client.DefaultRequestHeaders.Add("Authorization", "Provider");

            // Arrange
            var query = HttpUtility.ParseQueryString(string.Empty);
            query["opportunityID"] = "006Ec00000Gv8arIAB";

            // Act
            var response = await client.GetAsync($"/api/payoff/sent-out-data?{query.ToString()}");

            var responseString = await response.Content.ReadAsStringAsync();

            // Assert
            Assert.True(HttpStatusCode.OK == response.StatusCode, responseString);
        }

        [Fact]
        public async Task ShouldUpdateReductionFieldAmount()
        {
            var payoffDAL = _factory.Services.GetRequiredService<IPayoffDAL>();
            var oppId = "OpportunityPayoff";
            await CreateReductionRequest(oppId, 10556, 7500);
            var payoff = (await payoffDAL.GetPayoffsByOpportunity(oppId)).First();

            var helper = new SalesforceTestHelper(_factory)
                .SetupCreateAsync()
                .SetupPayoffQueries();

            var client = helper.CreateClient("Provider");

            var professionalLienAmount = 13672.55f;

            ReductionUpdate requestBody = new ReductionUpdate()
            {
                PayoffLogID = payoff.PayoffLogID,
                SpecialsAmount = 16724.55f,
                MedicalAmount = 10556,
                MedicalInsuranceAmount = 0,
                ProfessionalLienAmount = professionalLienAmount,
                FacilityLienAmount = 3052,
                SettledAmount = 7500,
                OfferedAmount = 0,
                MedicalFacilityState = "IL"
            };

            HttpContent content = HttpTestHelper.ConvertObjectToHttpContent(requestBody);

            // Act
            var response = await client.PostAsync("/api/payoff/reduction-field-update", content);

            var responseString = await response.Content.ReadAsStringAsync();

            // Assert
            Assert.True(HttpStatusCode.OK == response.StatusCode, responseString);

            var updatedPayoff = await payoffDAL.GetReductionNegotiationData(payoff.PayoffLogID);
            Assert.Equal(1158.0899658203125f, updatedPayoff.Reduction.ReductionRequest.ProRata);
            Assert.Equal(13672.5f, updatedPayoff.Reduction.ReductionRequest.ProfessionalLienAmount);

        }

        [Fact(Skip = "hitting salesforce")]
        public async Task ShouldGetReductionRequest()
        {
            var client = _factory.CreateClient();
            client.DefaultRequestHeaders.Add("Authorization", "Provider");

            // Arrange
            var query = HttpUtility.ParseQueryString(string.Empty);
            query["opportunityID"] = "006Qj00000LXkKcIAL";

            // Act
            var response = await client.GetAsync($"/api/payoff/reduction-negotiation?{query.ToString()}");

            var responseString = await response.Content.ReadAsStringAsync();

            ReductionNegotiation reduction = JsonConvert.DeserializeObject<ReductionNegotiation>(responseString);

            // Assert
            Assert.True(HttpStatusCode.OK == response.StatusCode, responseString);

            var prorata = PayoffCalculation.ProRataCalculation(reduction.Reduction, "IL");
            Console.WriteLine(prorata);
        }

        [Theory]
        [InlineData("accept", "attorney", 1)]
        [InlineData("accept", "admin", 0)]
        [InlineData("counter", "attorney", 1)]
        [InlineData("counter", "admin", 0)]
        [InlineData("cancel", "admin", 0)]
        [InlineData("pending_cancel", "admin", 0)]
        [InlineData("restarted", "admin", 0)]
        public async Task ShouldSubmitReductionNegotiation(string decision, string counterFrom, int actingStatus)
        {
            // Arrange
            var payoffDAL = _factory.Services.GetRequiredService<IPayoffDAL>();
            var oppId = "006Ec00000LAnEhIAL";
            await CreateReductionRequest(oppId);
            var payoff = (await payoffDAL.GetPayoffsByOpportunity(oppId)).First();

            var helper = new SalesforceTestHelper(_factory)
                .SetupCreateAsync()
                .SetupPayoffQueries();

            var mockHttpMessageHandler = HttpMessageHandlerMocks.CreateCongaComposerMock();
            HttpMessageHandlerMocks.CreatePlaintiffPayoffMock(mockHttpMessageHandler);

            var client = helper.CreateClient(new TestHelperClientOptions
            {
                UserType = "Provider",
                ConfigureServices = (services) =>
                {
                    services.AddHttpClient<ISalesforceHttpClient, SalesforceHttpClient>()
                        .ConfigurePrimaryHttpMessageHandler(() => mockHttpMessageHandler.Object);
                }
            });

            ReductionNegotiationBody requestBody = new ReductionNegotiationBody()
            {
                PayoffLogID = payoff.PayoffLogID,
                Offer = 0,
                Notes = null,
                Decision = decision,
                CounterFrom = counterFrom,
                CashOut = 0,
                ActingRiskManagerID = 1,
                ActingUserID = 1,
                ActingStatus = actingStatus,
                OpportunityID = oppId,
                Reduction = new ReductionRequest() { },
                Flags = new ReductionRequestFlags()
                {
                    LessThanSentOutReason = "test",
                    LessThanFivePercentProRataReason = "test",
                    AcceptedReason = "test",
                    HighPayoffManagerName = "test",
                }
            };
            var query = HttpUtility.ParseQueryString(string.Empty);
            query["updateSettledAmount"] = "true";

            HttpContent content = HttpTestHelper.ConvertObjectToHttpContent(requestBody);

            // Act
            var response = await client.PostAsync($"/api/payoff/reduction-negotiation?{query.ToString()}", content);

            var responseString = await response.Content.ReadAsStringAsync();

            // Assert
            Assert.True(HttpStatusCode.OK == response.StatusCode, responseString);

            await Verifier.Verify(helper.Queries).UseParameters(decision, counterFrom, actingStatus);
        }

        [Fact]
        public async Task ShouldSubmitReductionPayoff()
        {
            // Arrange
            var helper = new SalesforceTestHelper(_factory)
                .SetupObjectAttributesToIgnore("Last_90_Day_Follow_up_Date__c")
                .SetupCreateAsync()
                .SetupUpdateAsync()
                .SetupPayoffSubmissionQueries();

            var client = helper.CreateClient("Provider");

            // Create the request body
            var notification = new GainNotification
            {
                OppId = "OpportunityPayoff",
                Username = "TestAdmin",
                OppFirstName = "Test",
                OppLastName = "Patient",
                OwnerName = "Test Law Firm",
                PayoffRequest = new PayoffRequest
                {
                    MedicalAmount = 10556,
                    IsReduction = true,
                    ReductionRequestAttachment = new Attachment { ID = "TestAttachmentId" },
                    ReductionRequest = new ReductionRequest
                    {
                        PersueUMClaim = true,
                        SpecialsAmount = 88232.73f,
                        ProfessionalLienAmount = 0,
                        FacilityLienAmount = 0,
                        MedicalInsuranceAmount = 0,
                        OfferedAmount = 0,
                        SettledAmount = 7500,
                        Notes = "Test reduction request notes"
                    }
                }
            };

            HttpContent content = HttpTestHelper.ConvertObjectToHttpContent(notification);

            // Act
            var response = await client.PutAsync("/api/payoff?sharerID=1", content);
            var responseString = await response.Content.ReadAsStringAsync();

            // Assert
            Assert.True(HttpStatusCode.OK == response.StatusCode, responseString);

            await Verifier.Verify(helper.Queries);
        }

        [Fact]
        public async Task ShouldSubmitStandardPayoff()
        {
            // Arrange
            var helper = new SalesforceTestHelper(_factory)
                .SetupCreateAsync()
                .SetupUpdateAsync()
                .SetupPayoffSubmissionQueries();

            var client = helper.CreateClient("Provider");

            // Create the request body
            var notification = new GainNotification
            {
                OppId = TestData.Ids.SalesforceId,
                Username = "TestAdmin",
                OppFirstName = "Test",
                OppLastName = "Patient",
                OwnerName = "Test Law Firm",
                PayoffRequest = new PayoffRequest
                {
                    MedicalAmount = 10556,
                    IsReduction = false,
                    GenerateDoc = true,
                    PayoffDocument = new PayoffDocument { documentId = "TestDocumentId" },
                    ShareLinkEmails = new List<string> { "<EMAIL>" },
                    ShareLinkMessage = "Test share link message",
                    ReductionRequest = new ReductionRequest
                    {
                        ProposedAmount = 15000.00f,
                        SpecialsAmount = 2000.00f,
                        MedicalInsuranceAmount = 3000.00f,
                        ProfessionalLienAmount = 1800.00f,
                        FacilityLienAmount = 4500.00f,
                        SettledAmount = 12500.00f,
                        OfferedAmount = 11000.00f,
                        Notes = "Negotiated reduction due to financial hardship. Documentation attached.",
                        isSettled = true,
                        medicalSpecialNotAvailable = false,
                        ProRata = 1000f,
                        PersueUMClaim = false
                    }
                }
            };

            HttpContent content = HttpTestHelper.ConvertObjectToHttpContent(notification);

            // Act
            var response = await client.PutAsync("/api/payoff?sharerID=1", content);
            var responseString = await response.Content.ReadAsStringAsync();

            // Assert
            Assert.Equal(HttpStatusCode.OK, response.StatusCode);

            await Verifier.Verify(helper.Queries);
        }


        [Theory]
        [InlineData("accept", "attorney", 1)]
        [InlineData("accept", "admin", 0)]
        public async Task ShouldBeAbleToAcceptNegotiationWithMultipleOps(string decision, string counterFrom, int actingStatus)
        {
            // Arrange
            var payoffDAL = _factory.Services.GetRequiredService<IPayoffDAL>();


            var primaryOppId = new SalesforceId("006Ec00000IxJtFIAV");
            var additionalOppIds = new List<SalesforceId> { new SalesforceId("006Ec00000IxG2DIAV"), new SalesforceId("006Ec00000J9TdwIAF") };
            var allOppIds = new List<SalesforceId> { primaryOppId }.Concat(additionalOppIds).ToList();

            // Create the combined test helper to track both Salesforce and Database operations
            var testHelper = new CombinedTestHelper(_factory);

            // Create initial single-opportunity reduction request
            await CreateReductionRequest(primaryOppId, 10556, 7500);
            var initialPayoff = (await payoffDAL.GetPayoffsByOpportunity(primaryOppId)).First();


            // Setup Salesforce mocks
            testHelper.Salesforce
                .SetupCreateAsync(randomId: true)
                .SetupObjectAttributesToIgnore("Date_of_Funding_Payoff_Request__c")
                .SetupObjectsToIgnoreId("Case")//Cases will generate new SF ids
                .SetupUpdateAsync()
                .SetupPayoffQueries()
                .SetupPayoffSubmissionQueries();

            //dynamic params to ignore
            var baseIgnoreParams = new List<string>()
            {
                "_timestamp",
                "_caseID",
            };
            testHelper.Database
                .SetupGeneralIgnoredParams(baseIgnoreParams)
                .SetupBaseActions()
                .SetupExecuteQueryProcedure<PortalAccount>()
                .SetupExecuteQuery<ReductionNegotiation>()
                .SetupExecuteQueryProcedure<ReductionNegotiation>()
                .SetupExecuteQueryProcedure<PayoffLogItem>()
                .SetupExecuteQueryProcedure<RiskManagementSupervisor>()
                .SetupExecuteQuery<string>()
                .SetupExecuteQueryProcedure<int>();

            var mockHttpMessageHandler = HttpMessageHandlerMocks.CreateCongaComposerMock();
            HttpMessageHandlerMocks.CreatePlaintiffPayoffMock(mockHttpMessageHandler);

            var client = testHelper.CreateClient(new TestHelperClientOptions
            {
                UserType = "Provider",
                ConfigureServices = (services) =>
                {
                    services.AddHttpClient<ISalesforceHttpClient, SalesforceHttpClient>()
                        .ConfigurePrimaryHttpMessageHandler(() => mockHttpMessageHandler.Object);
                }
            });

            var initialReductionData = await payoffDAL.GetReductionNegotiationData(initialPayoff.PayoffLogID);

            var query = HttpUtility.ParseQueryString(string.Empty);
            query["updateSettledAmount"] = "true";


            var request = new CancelAndRecreateReductionRequestBody()
            {
                PayoffLogID = initialPayoff.PayoffLogID,
                BaseOpportunityId = primaryOppId,
                OppIds = allOppIds
            };

            HttpContent multipleOppsContent = HttpTestHelper.ConvertObjectToHttpContent(request);
            var multipleOppsResponse = await client.PostAsync($"/api/payoff/cancel-and-recreate-reduction", multipleOppsContent);

            var multipleOppsresponseString = await multipleOppsResponse.Content.ReadAsStringAsync();

            // Assert
            Assert.True(HttpStatusCode.OK == multipleOppsResponse.StatusCode, multipleOppsresponseString);

            var newPayoffs = await payoffDAL.GetPayoffsByOpportunity(primaryOppId);
            var newPayoff = newPayoffs.OrderByDescending(p => p.PayoffLogID).First();

            ReductionNegotiationBody requestBody = new ReductionNegotiationBody()
            {
                PayoffLogID = newPayoff.PayoffLogID,
                Offer = 0,
                Notes = null,
                Decision = decision,
                CounterFrom = counterFrom,
                CashOut = 0,
                ActingRiskManagerID = 1,
                ActingUserID = 1,
                ActingStatus = actingStatus,
                OpportunityID = primaryOppId,
                Reduction = new ReductionRequest() { },
                Flags = new ReductionRequestFlags()
                {
                    LessThanSentOutReason = "test",
                    LessThanFivePercentProRataReason = "test",
                    AcceptedReason = "test"
                }
            };

            testHelper.ClearOperations();

            HttpContent content = HttpTestHelper.ConvertObjectToHttpContent(requestBody);

            // Act
            var response = await client.PostAsync($"/api/payoff/reduction-negotiation?{query.ToString()}", content);

            var responseString = await response.Content.ReadAsStringAsync();

            // Assert
            Assert.True(HttpStatusCode.OK == response.StatusCode, responseString);

            //The third item has dynamic ID, so we remove it and check on assert
            var queryIfDynamicId = testHelper.AllOperations[2];
            Assert.Contains("FROM Case", queryIfDynamicId);

            var verifier = new List<string>(testHelper.AllOperations);
            verifier.RemoveAt(2);

            await Verifier.Verify(verifier).UseParameters(decision, counterFrom, actingStatus);
        }

        [Fact]
        public async Task ShouldCancelAndRecreateMultiOpportunityReduction()
        {
            // Arrange
            var payoffDAL = _factory.Services.GetRequiredService<IPayoffDAL>();

            var primaryOppId = new SalesforceId("006Ec00000IxJtFIAV");
            var additionalOppIds = new List<SalesforceId> { new SalesforceId("006Ec00000IxG2DIAV"), new SalesforceId("006Ec00000J9TdwIAF") };
            var allOppIds = new List<SalesforceId> { primaryOppId }.Concat(additionalOppIds).ToList();

            // Create the combined test helper to track both Salesforce and Database operations
            var testHelper = new CombinedTestHelper(_factory);

            // Create initial single-opportunity reduction request
            await CreateReductionRequest(primaryOppId, 10556, 7500);
            var initialPayoff = (await payoffDAL.GetPayoffsByOpportunity(primaryOppId)).First();

            // Setup Salesforce mocks
            testHelper.Salesforce
                .SetupCreateAsync(randomId: true)
                .SetupObjectAttributesToIgnore("Date_of_Funding_Payoff_Request__c")
                .SetupObjectsToIgnoreId("Case")//Cases will generate new SF ids
                .SetupUpdateAsync()
                .SetupPayoffQueries()
                .SetupPayoffSubmissionQueries();

            //dynamic params to ignore
            var baseIgnoreParams = new List<string>()
            {
                "_timestamp",
                "_caseID",
            };
            testHelper.Database
                .SetupGeneralIgnoredParams(baseIgnoreParams)
                .SetupBaseActions()
                .SetupExecuteQueryProcedure<PortalAccount>()
                .SetupExecuteQuery<ReductionNegotiation>()
                .SetupExecuteQueryProcedure<ReductionNegotiation>()
                .SetupExecuteQueryProcedure<PayoffLogItem>()
                .SetupExecuteQuery<string>()
                .SetupExecuteQueryProcedure<int>();

            var mockHttpMessageHandler = HttpMessageHandlerMocks.CreateCongaComposerMock();
            HttpMessageHandlerMocks.CreatePlaintiffPayoffMock(mockHttpMessageHandler);

            var client = testHelper.CreateClient(new TestHelperClientOptions
            {
                UserType = "Provider",
                ConfigureServices = (services) =>
                {
                    services.AddHttpClient<ISalesforceHttpClient, SalesforceHttpClient>()
                        .ConfigurePrimaryHttpMessageHandler(() => mockHttpMessageHandler.Object);
                }
            });

            // Verify initial reduction request was created
            var initialReductionData = await payoffDAL.GetReductionNegotiationData(initialPayoff.PayoffLogID);
            Assert.NotNull(initialReductionData);
            Assert.True(initialReductionData.Reduction.ReductionRequest.PersueUMClaim);
            Assert.True(initialReductionData.Reduction.ReductionRequest.SpecialsAmount == 10000.0f,
                $"Expected SpecialsAmount to be 10000.0, but was {initialReductionData.Reduction.ReductionRequest.SpecialsAmount}");


            // Act - Cancel and recreate with multiple opportunities
            var request = new CancelAndRecreateReductionRequestBody()
            {
                PayoffLogID = initialPayoff.PayoffLogID,
                BaseOpportunityId = primaryOppId,
                OppIds = allOppIds
            };
            HttpContent content = HttpTestHelper.ConvertObjectToHttpContent(request);
            var response = await client.PostAsync($"/api/payoff/cancel-and-recreate-reduction", content);
            var responseString = await response.Content.ReadAsStringAsync();

            // Assert
            Assert.True(HttpStatusCode.OK == response.StatusCode, responseString);
            Assert.Equal("true", responseString.ToLower());

            // Verify new multi-opportunity reduction request was created
            var newPayoffs = await payoffDAL.GetPayoffsByOpportunity(primaryOppId);
            Assert.True(newPayoffs.Count >= 2, "Should have at least 2 payoff records (original + new)");

            var newPayoff = newPayoffs.OrderByDescending(p => p.PayoffLogID).First();
            var newReductionData = await payoffDAL.GetReductionNegotiationData(newPayoff.PayoffLogID);

            Assert.NotNull(newReductionData);
            Assert.True(newReductionData.Reduction.ReductionRequest.PersueUMClaim);
            Assert.Contains("Re-created from canceled reduction request for 3 opportunities", newReductionData.Reduction.ReductionRequest.Notes);


            // Verify that the new payoff request has links to all opportunities
            var linkedOpportunities = await payoffDAL.GetOpportunitiesByPayoffLogID(newPayoff.PayoffLogID);
            Assert.Equal(3, linkedOpportunities.Count);
            Assert.Contains(allOppIds[0].Value, linkedOpportunities);
            Assert.Contains(allOppIds[1].Value, linkedOpportunities);
            Assert.Contains(allOppIds[2].Value, linkedOpportunities);

            // Verify primary opportunity is correctly set
            var primaryOpportunity = await payoffDAL.GetPrimaryOpportunityByPayoffLogID(newPayoff.PayoffLogID);
            Assert.Equal(primaryOppId, primaryOpportunity);

            // Verify the original reduction request data was preserved (using tolerance for floating-point comparisons)
            Assert.True(Math.Abs(initialReductionData.Reduction.ReductionRequest.SpecialsAmount - newReductionData.Reduction.ReductionRequest.SpecialsAmount) < 0.01f,
                $"SpecialsAmount should be preserved. Expected: {initialReductionData.Reduction.ReductionRequest.SpecialsAmount}, Actual: {newReductionData.Reduction.ReductionRequest.SpecialsAmount}");
            Assert.True(Math.Abs(initialReductionData.Reduction.ReductionRequest.MedicalInsuranceAmount - newReductionData.Reduction.ReductionRequest.MedicalInsuranceAmount) < 0.01f,
                $"MedicalInsuranceAmount should be preserved. Expected: {initialReductionData.Reduction.ReductionRequest.MedicalInsuranceAmount}, Actual: {newReductionData.Reduction.ReductionRequest.MedicalInsuranceAmount}");
            Assert.True(Math.Abs(initialReductionData.Reduction.ReductionRequest.ProfessionalLienAmount - newReductionData.Reduction.ReductionRequest.ProfessionalLienAmount) < 0.01f,
                $"ProfessionalLienAmount should be preserved. Expected: {initialReductionData.Reduction.ReductionRequest.ProfessionalLienAmount}, Actual: {newReductionData.Reduction.ReductionRequest.ProfessionalLienAmount}");
            Assert.True(Math.Abs(initialReductionData.Reduction.ReductionRequest.FacilityLienAmount - newReductionData.Reduction.ReductionRequest.FacilityLienAmount) < 0.01f,
                $"FacilityLienAmount should be preserved. Expected: {initialReductionData.Reduction.ReductionRequest.FacilityLienAmount}, Actual: {newReductionData.Reduction.ReductionRequest.FacilityLienAmount}");
            Assert.True(Math.Abs(initialReductionData.Reduction.ReductionRequest.OfferedAmount - newReductionData.Reduction.ReductionRequest.OfferedAmount) < 0.01f,
                $"OfferedAmount should be preserved. Expected: {initialReductionData.Reduction.ReductionRequest.OfferedAmount}, Actual: {newReductionData.Reduction.ReductionRequest.OfferedAmount}");

            // Verify both Salesforce and Database operations using the combined helper
            await Verifier.Verify(testHelper.AllOperations);
        }

        [Fact]
        public async Task ShouldFailCancelAndRecreateWithEmptyOpportunityList()
        {
            // Arrange
            var payoffDAL = _factory.Services.GetRequiredService<IPayoffDAL>();
            var oppId = "OpportunityPayoff";
            await CreateReductionRequest(oppId);
            var payoff = (await payoffDAL.GetPayoffsByOpportunity(oppId)).First();

            var helper = new SalesforceTestHelper(_factory)
                .SetupCreateAsync()
                .SetupPayoffQueries();

            var client = helper.CreateClient("Provider");

            // Act - Try to cancel and recreate with empty opportunity list
            var emptyOppIds = new List<SalesforceId>();
            CancelAndRecreateReductionRequestBody request = new CancelAndRecreateReductionRequestBody()
            {
                OppIds = emptyOppIds,
                BaseOpportunityId = SalesforceIdGenerator.Generate(),
                PayoffLogID = payoff.PayoffLogID,

            };

            HttpContent content = HttpTestHelper.ConvertObjectToHttpContent(request);
            var response = await client.PostAsync($"/api/payoff/cancel-and-recreate-reduction", content);
            var responseString = await response.Content.ReadAsStringAsync();

            // Assert
            Assert.Equal(HttpStatusCode.BadRequest, response.StatusCode);
            Assert.Contains("At least one opportunity ID must be provided", responseString);
        }

        [Fact]
        public async Task ShouldFailCancelAndRecreateWithInvalidPayoffLogID()
        {
            // Arrange
            var helper = new SalesforceTestHelper(_factory)
                .SetupCreateAsync()
                .SetupPayoffQueries();

            var client = helper.CreateClient("Provider");

            // Act - Try to cancel and recreate with invalid payoff log ID
            var sfId = SalesforceIdGenerator.Generate();
            var oppIds = new List<SalesforceId> { sfId };
            CancelAndRecreateReductionRequestBody request = new CancelAndRecreateReductionRequestBody()
            {
                OppIds = oppIds,
                BaseOpportunityId = SalesforceIdGenerator.Generate(),
                PayoffLogID = 99999,

            };
            HttpContent content = HttpTestHelper.ConvertObjectToHttpContent(request);
            var response = await client.PostAsync("/api/payoff/cancel-and-recreate-reduction", content);
            var responseString = await response.Content.ReadAsStringAsync();

            // Assert
            Assert.Equal(HttpStatusCode.BadRequest, response.StatusCode);
            Assert.Contains("Reduction request with ID 99999 not found", responseString);
        }

        private async Task CreateReductionRequest(string oppId, float medicalAmount = 1000.0f, float settledAmount = 0)
        {
            // Arrange
            var helper = new SalesforceTestHelper(_factory)
                .SetupCreateAsync()
                .SetupPayoffQueries();

            var client = helper.CreateClient("Provider");

            GainNotification requestBody = new GainNotification()
            {
                Type = "AttorneyRequestPayoff",
                OppId = oppId,
                Username = "<EMAIL>",
                UserFirstName = "test",
                UserLastName = "test",
                UserType = "Attorney",
                OppFirstName = "Test",
                OppLastName = "Test",
                OppDateOfBirth = "11/11/2001",
                OwnerName = "Gain Law Firm",

                PayoffRequest = new PayoffRequest()
                {
                    GenerateDoc = true,
                    MedicalAmount = medicalAmount,
                    PCAAmount = 0,
                    TotalUncappedAmount = 0,
                    TotalStateCapPCA = 0,
                    TotalAmount = 0,
                    TotalUncappedPCA = 0,
                    ActualMedicalAmount = 1000,
                    MedicalFacilityState = "GA",
                    ErrorMessage = null,
                    ErrorCode = null,
                    IsReduction = true,
                    ReductionRequestAttachment = new Attachment(),

                    ReductionRequest = new ReductionRequest()
                    {
                        SpecialsAmount = 10000,
                        ProposedAmount = 9000,
                        MedicalInsuranceAmount = 0,
                        ProfessionalLienAmount = 0,
                        FacilityLienAmount = 0,
                        SettledAmount = settledAmount,
                        OfferedAmount = 10000,
                        Notes = "",
                        isSettled = false,
                        attachment = new Attachment(),
                        medicalSpecialNotAvailable = false,
                        ProRata = 0,
                        PersueUMClaim = true,
                    },
                }
            };

            HttpContent content = HttpTestHelper.ConvertObjectToHttpContent(requestBody);

            // Act
            var response = await client.PutAsync("/api/payoff", content);

            var responseString = await response.Content.ReadAsStringAsync();

            // Assert
            Assert.True(HttpStatusCode.OK == response.StatusCode, responseString);
        }

        [Fact]
        public async Task ShouldCreateReductionRequest()
        {
            var payoffDAL = _factory.Services.GetRequiredService<IPayoffDAL>();
            var oppId = "006Ec00000LAnEhIAL";
            await CreateReductionRequest(oppId);
            var payoffs = await payoffDAL.GetPayoffsByOpportunity(oppId);
            Assert.Single(payoffs);
            var lastPayoff = payoffs.LastOrDefault();
            var reductionNegotiation = await payoffDAL.GetReductionNegotiationData(lastPayoff.PayoffLogID);
            Assert.True(reductionNegotiation.Reduction.ReductionRequest.PersueUMClaim);
        }

        [Fact(Skip = "hitting salesforce")]
        public async Task ShouldGetReductionBreakdown()
        {
            var client = _factory.CreateClient();
            client.DefaultRequestHeaders.Add("Authorization", "Provider");

            // Arrange
            var query = HttpUtility.ParseQueryString(string.Empty);
            query["opportunityID"] = "006Qj00000B8cfiIAB";

            // Act
            var response = await client.GetAsync($"/api/payoff/reduction-breakdown?{query.ToString()}");

            var responseString = await response.Content.ReadAsStringAsync();

            // Assert
            Assert.True(HttpStatusCode.OK == response.StatusCode, responseString);
        }

        [Fact(Skip = "hitting salesforce")]
        public async Task ShouldGetMedicalBreakdown()
        {
            var client = _factory.CreateClient();
            client.DefaultRequestHeaders.Add("Authorization", "Provider");

            // Arrange
            var query = HttpUtility.ParseQueryString(string.Empty);
            query["opportunityID"] = "006Qj00000B8cfiIAB";

            // Act
            var response = await client.GetAsync($"/api/payoff/medical-breakdown?{query.ToString()}");

            var responseString = await response.Content.ReadAsStringAsync();

            // Assert
            Assert.True(HttpStatusCode.OK == response.StatusCode, responseString);
        }
    }
}
