using GainServicingAPI.DataAccess.Salesforce.QueryDefinitions;
using GainServicingAPI.Helpers.Interfaces;
using GainServicingAPI.Helpers.Salesforce.Interfaces;
using GainServicingAPI.Logging;
using GainServicingAPI.Model.HttpRequestBodies;
using GainServicingAPI.Model.Responses;
using GainServicingAPI.Model.Salesforce;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using Salesforce.Common.Models.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace GainServicingAPI.Controllers
{
    [Route("api/ar-books")]
    [ApiController]
    [Authorize]
    public class ARBookController : ControllerBase
    {
        private readonly ISalesforceClient _sfClient;
        private readonly IAPILogger _logger;
        private readonly IJwtTokenizer _tokenizer;

        public ARBookController(
            ISalesforceClient sfClient,
            IAPILogger logger,
            IJwtTokenizer tokenizer)
        {
            _sfClient = sfClient;
            _logger = logger;
            _tokenizer = tokenizer;
        }

        /// <summary>
        /// Search AR Books with filtering support
        /// </summary>
        /// <param name="filters">Search filters</param>
        /// <returns>List of AR Books matching the criteria</returns>
        [HttpGet]
        public async Task<ActionResult<ARBookSearchResponse>> SearchARBooks([FromQuery] ARBookFilters filters)
        {
            var authToken = Request.Headers["Authorization"];
            _logger.UserIP = Request.HttpContext.Connection.RemoteIpAddress.ToString();
            var user = _tokenizer.GetUserData(authToken);
            var userId = user.UserID;

            try
            {
                _logger.LogInfo(Request.Method, Request.Path + Request.QueryString, userId, null);

                var query = SalesForceQueries.SearchARBooks(filters);
                var searchResponse = await _sfClient.QueryAsync<SF_AR_Book>(query);

                var response = new ARBookSearchResponse
                {
                    Books = searchResponse.Records.Select(book => (ARBookResponse)book).ToList(),
                    TotalCount = searchResponse.Records.Count
                };

                return Ok(response);
            }
            catch (Exception e)
            {
                _logger.LogException(Request.Method, Request.Path + Request.QueryString, userId, null, e);
                return BadRequest(e.Message);
            }
        }

        /// <summary>
        /// Get AR Book details by ID
        /// </summary>
        /// <param name="id">AR Book Salesforce ID</param>
        /// <returns>AR Book details</returns>
        [HttpGet("{id}")]
        public async Task<ActionResult<ARBookResponse>> GetARBookDetails(string id)
        {
            var authToken = Request.Headers["Authorization"];
            _logger.UserIP = Request.HttpContext.Connection.RemoteIpAddress.ToString();
            var user = _tokenizer.GetUserData(authToken);
            var userId = user.UserID;

            try
            {
                _logger.LogInfo(Request.Method, Request.Path + Request.QueryString, userId, null);

                var query = SalesForceQueries.GetARBookDetails(id);
                var searchResponse = await _sfClient.QueryAsync<SF_AR_Book>(query);

                if (searchResponse.Records.Count == 0)
                {
                    return NotFound($"AR Book with ID {id} not found");
                }

                var arBook = (ARBookResponse)searchResponse.Records[0];
                return Ok(arBook);
            }
            catch (Exception e)
            {
                _logger.LogException(Request.Method, Request.Path + Request.QueryString, userId, null, e);
                return BadRequest(e.Message);
            }
        }

        /// <summary>
        /// Get fundings associated with an AR Book
        /// </summary>
        /// <param name="id">AR Book Salesforce ID</param>
        /// <returns>List of fundings and book summary</returns>
        [HttpGet("{id}/fundings")]
        public async Task<ActionResult<ARBookFundingsResponse>> GetARBookFundings(string id)
        {
            var authToken = Request.Headers["Authorization"];
            _logger.UserIP = Request.HttpContext.Connection.RemoteIpAddress.ToString();
            var user = _tokenizer.GetUserData(authToken);
            var userId = user.UserID;

            try
            {
                _logger.LogInfo(Request.Method, Request.Path + Request.QueryString, userId, null);

                // Get AR Book details for summary
                var bookQuery = SalesForceQueries.GetARBookDetails(id);
                var bookResponse = await _sfClient.QueryAsync<SF_AR_Book>(bookQuery);

                if (bookResponse.Records.Count == 0)
                {
                    return NotFound($"AR Book with ID {id} not found");
                }

                // Get fundings
                var fundingsQuery = SalesForceQueries.GetARBookFundings(id);
                var fundingsResponse = await _sfClient.QueryAsync<Funding__c>(fundingsQuery);

                var response = new ARBookFundingsResponse
                {
                    Fundings = fundingsResponse.Records.Select(funding => (ARBookFundingResponse)funding).ToList(),
                    TotalCount = fundingsResponse.Records.Count,
                    BookSummary = (ARBookSummaryResponse)bookResponse.Records[0]
                };

                return Ok(response);
            }
            catch (Exception e)
            {
                _logger.LogException(Request.Method, Request.Path + Request.QueryString, userId, null, e);
                return BadRequest(e.Message);
            }
        }

        /// <summary>
        /// Get recent AR Books
        /// </summary>
        /// <param name="limit">Number of recent books to return (default: 10)</param>
        /// <returns>List of recent AR Books</returns>
        [HttpGet("recent")]
        public async Task<ActionResult<List<ARBookResponse>>> GetRecentARBooks([FromQuery] int limit = 10)
        {
            var authToken = Request.Headers["Authorization"];
            _logger.UserIP = Request.HttpContext.Connection.RemoteIpAddress.ToString();
            var user = _tokenizer.GetUserData(authToken);
            var userId = user.UserID;

            try
            {
                _logger.LogInfo(Request.Method, Request.Path + Request.QueryString, userId, null);

                var query = SalesForceQueries.GetRecentARBooks(limit);
                var searchResponse = await _sfClient.QueryAsync<SF_AR_Book>(query);

                var response = searchResponse.Records.Select(book => (ARBookResponse)book).ToList();
                return Ok(response);
            }
            catch (Exception e)
            {
                _logger.LogException(Request.Method, Request.Path + Request.QueryString, userId, null, e);
                return BadRequest(e.Message);
            }
        }

        /// <summary>
        /// Get available AR Book types
        /// </summary>
        /// <returns>List of AR Book types</returns>
        [HttpGet("types")]
        public async Task<ActionResult<List<string>>> GetARBookTypes()
        {
            var authToken = Request.Headers["Authorization"];
            _logger.UserIP = Request.HttpContext.Connection.RemoteIpAddress.ToString();
            var user = _tokenizer.GetUserData(authToken);
            var userId = user.UserID;

            try
            {
                _logger.LogInfo(Request.Method, Request.Path + Request.QueryString, userId, null);

                var query = SalesForceQueries.GetARBookTypes();
                var searchResponse = await _sfClient.QueryAsync<object>(query);

                string result = JsonConvert.SerializeObject(searchResponse.Records);
                var typeObjects = JsonConvert.DeserializeObject<List<Dictionary<string, object>>>(result);

                var types = typeObjects
                    .Where(obj => obj.ContainsKey("AR_Type__c") && obj["AR_Type__c"] != null)
                    .Select(obj => obj["AR_Type__c"].ToString())
                    .Distinct()
                    .OrderBy(type => type)
                    .ToList();

                return Ok(types);
            }
            catch (Exception e)
            {
                _logger.LogException(Request.Method, Request.Path + Request.QueryString, userId, null, e);
                return BadRequest(e.Message);
            }
        }
    }
}
