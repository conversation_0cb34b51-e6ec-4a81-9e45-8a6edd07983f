using GainServicingAPI.Helpers;
using GainServicingAPI.Helpers.Salesforce.Interfaces;
using GainServicingAPI.Model.Salesforce;
using Salesforce.Common;
using Salesforce.Common.Models.Json;
using Salesforce.Force;
using System;
using System.Threading.Tasks;

namespace GainServicingAPI.Controllers.Salesforce
{

    public class SalesforceClient : ISalesforceClient
    {
        private SalesforceSettings _settings = null;
        private string _apiVersion = null;
        private ForceClient _client = null;

        public SalesforceClient()
        {
            _settings = new SalesforceSettings()
            {
                ConsumerKey = SecurePasswordManager.DecryptPasswordWithEnvironment(Startup.Configuration["Salesforce:ConsumerKey"]),
                ConsumerSecret = SecurePasswordManager.DecryptPasswordWithEnvironment(Startup.Configuration["Salesforce:ConsumerSecret"]),
                Domain = Startup.Configuration["Salesforce:Domain"],
                Password = SecurePasswordManager.DecryptPasswordWithEnvironment(Startup.Configuration["Salesforce:Password"]),
                SecurityToken = SecurePasswordManager.DecryptPasswordWithEnvironment(Startup.Configuration["Salesforce:SecurityToken"]),
                Username = Startup.Configuration["Salesforce:Username"]
            };

            var version = CacheManager.GetCacheOrNull("SalesForce:APIVersion");
            _apiVersion = (version != null) ? version.ToString() : "49";

            if (_apiVersion != "testEnvironment")
            {
                Task.Run(async () => await SetupNewClient()).Wait();
            }
        }

        /// <summary>
        /// Run Salesforce query
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="soql"></param>
        /// <returns></returns>
        public async Task<QueryResult<T>> QueryAsync<T>(string soql)
        {
            return await ExecWithRetry<QueryResult<T>>(
                async () => await _client.QueryAsync<T>(soql).ConfigureAwait(false)
            ).ConfigureAwait(false);
        }

        public async Task<SuccessResponse> CreateAsync<T>(string objectName, T obj)
        {
            return await ExecWithRetry<SuccessResponse>(
                async () => await _client.CreateAsync(objectName, obj).ConfigureAwait(false)
            ).ConfigureAwait(false);
        }

        public async Task<SuccessResponse> UpdateAsync<T>(string objectName, string id, T obj)
        {
            return await ExecWithRetry<SuccessResponse>(
                async () => await _client.UpdateAsync(objectName, id, obj).ConfigureAwait(false)
            ).ConfigureAwait(false);
        }

        public async Task<bool> DeleteAsync<T>(string objectName, string id)
        {
            return await ExecWithRetry<bool>(
                async () => await _client.DeleteAsync(objectName, id).ConfigureAwait(false)
            ).ConfigureAwait(false);
        }

        /// <summary>
        /// Create new SalesforceClient
        /// </summary>
        private async Task<bool> SetupNewClient()
        {
            AuthToken token = (AuthToken)CacheManager.GetCacheOrNull("SalesForce:AuthToken");
            if (token == null)
            {
                await Authenticate();
                token = (AuthToken)CacheManager.GetCacheOrNull("SalesForce:AuthToken");
            }

            _client = new ForceClient(token.InstanceUrl, token.AccessToken, _apiVersion);
            return true;
        }

        /// <summary>
        /// Authenticates Salesforce credentials
        /// </summary>
        /// <returns>Salesforce AuthToken</returns>
        public async Task<AuthToken> Authenticate()
        {
            using (var authClient = new AuthenticationClient())
            {
                authClient.ApiVersion = "49.0";
                authClient.InstanceUrl = _settings.Domain;
                await authClient.GetLatestVersionAsync();

                await authClient.UsernamePasswordAsync(
                    _settings.ConsumerKey,
                    _settings.ConsumerSecret,
                    _settings.Username,
                    _settings.Password + _settings.SecurityToken,
                    _settings.Domain + "/services/oauth2/token"
                ).ConfigureAwait(false);
                _apiVersion = authClient.ApiVersion;

                AuthToken token = new AuthToken
                {
                    InstanceUrl = authClient.InstanceUrl,
                    AccessToken = authClient.AccessToken
                };

                CacheManager.SetCacheItem("SalesForce:AuthToken", token);
                CacheManager.SetCacheItem("SalesForce:APIVersion", _apiVersion);
                return token;
            }
        }

        /// <summary>
        /// Try to execute query 3 times
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="op"></param>
        /// <returns></returns>
        private async Task<T> ExecWithRetry<T>(Func<Task<T>> op)
        {
            int tryCount = 1;
            try
            {
                return await op().ConfigureAwait(false);
            }
            catch (ForceException e)
            {
                tryCount++;
                if (e.Error is Error.InvalidSessionId && tryCount <= 3)
                {
                    await Authenticate();
                    AuthToken token = (AuthToken)CacheManager.GetCacheOrNull("SalesForce:AuthToken");
                    if (token == null)
                        throw;

                    _client = new ForceClient(token.InstanceUrl, token.AccessToken, _apiVersion);
                    return await op().ConfigureAwait(false);
                }
                throw;
            }
        }

    }
}
