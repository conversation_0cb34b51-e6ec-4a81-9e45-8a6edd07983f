﻿using GainServicingAPI.DAL.Salesforce.QueryDefinitions;
using GainServicingAPI.Helpers;
using GainServicingAPI.Model;
using GainServicingAPI.Model.HttpRequestBodies;
using GainServicingAPI.Model.Salesforce.StageNames;
using System;
using System.Collections.Generic;
using System.Linq;

namespace GainServicingAPI.DataAccess.Salesforce.QueryDefinitions
{
    public static class SalesForceQueries
    {
        #region Patients
        /// <summary>
        /// Searches for opportunity ids for patients of specified search criteria.
        /// </summary>
        /// <param name="patientName"></param>
        /// <param name="patientDOB"></param>
        /// <param name="patientDOI"></param>
        /// <param name="patientCaseStatus"></param>
        /// <param name="AccountId"></param>
        /// <param name="pageSize"></param>
        /// <param name="RollupNodeList">List of comma separated strings</param>
        /// <param name="RollupDepthList">List of distances from node to Leaf</param>
        /// <returns></returns>
        public static string PatientSearchIds(OpportunityFilters search, string parentAccountId)
        {
            var accountFieldString = $"Medical_Facility__c IN({parentAccountId}) OR Partner_Account__c IN({parentAccountId}) ";
            if (search.IsRollUp.HasValue && search.IsRollUp.Value && search.RollupDepthList != null)
            {
                accountFieldString = HierarchyManager.getSOQLHierarchyFundingClause(search.RollupDepthList, search.RollupNodeList);
            }

            string query = @$"SELECT
	                            Plaintiff__r.id,
	                            SUM(Invoice_Amount__c) Balance,
                                MAX(Plaintiff__r.LastModifiedDate)
                            FROM Funding__c
                            WHERE ({accountFieldString}) ";

            query += $"\nAND (NOT Funding_Stage__c IN ({StageNames.GetExemptFundingStageList()}))";

            search.GroupBy = "Plaintiff__r.id";
            search.OrderBy = "MAX(Plaintiff__r.LastModifiedDate) DESC";

            query = OpportunitySalesForceQueries.SearchByOpportunityFilters(search, query, new ProviderStageNames(), "Plaintiff__r.");

            return query;
        }

        public static string LookupPatientIDsByAccountID(string accountID)
        {
            string query = @$"SELECT
	                            Plaintiff__r.id
                            FROM Funding__c
                            WHERE (Medical_Facility__c = '{accountID}' OR Partner_Account__c = '{accountID}')";
            query += $@" AND Plaintiff__r.StageName NOT IN ({new ProviderStageNames().getExemptStageSalesforceList()})";

            return query;
        }

        public static string PatientSearchIdsLast90DayFilter(OpportunityFilters search, string parentAccountSfIDs)
        {
            var accountFieldString = $"Medical_Facility__c IN({parentAccountSfIDs}) OR Partner_Account__c IN({parentAccountSfIDs}) ";
            if (search.IsRollUp.HasValue && search.IsRollUp.Value && search.RollupDepthList != null)
            {
                accountFieldString = HierarchyManager.getSOQLHierarchyFundingClause(search.RollupDepthList, search.RollupNodeList);
            }

            string query = @$"SELECT
	                            Plaintiff__r.id,
                                Plaintiff__r.Last_90_Day_Follow_up_Date__c,
	                            SUM(Invoice_Amount__c) Balance,
                                MAX(Plaintiff__r.LastModifiedDate)
                            FROM Funding__c
                            WHERE ({accountFieldString})";

            search.GroupBy = "Plaintiff__r.id, Plaintiff__r.Last_90_Day_Follow_up_Date__c";

            if (search.HasCaseStatus)
            {
                query += $"\nAND Plaintiff__r.Last_90_Day_Follow_up_Date__c != LAST_N_DAYS:60";
                search.OrderBy = "Plaintiff__r.Last_90_Day_Follow_up_Date__c ASC NULLS FIRST";
            }
            else
            {
                query += $"\nAND Plaintiff__r.Last_90_Day_Follow_up_Date__c = LAST_N_DAYS:60";
                search.OrderBy = "MAX(Plaintiff__r.LastModifiedDate) DESC";
            }

            query = OpportunitySalesForceQueries.SearchByOpportunityFilters(search, query, new ProviderStageNames(), "Plaintiff__r.");

            return query;
        }

        /// <summary>
        /// Search for a patient by name only
        /// </summary>
        /// <param name="patientName"></param>
        /// <returns></returns>
        public static string PatientSearchByName(string patientName)
        {

            string query = SOQLQueryBuilder.BuildSelectQuery("Opportunity")
                                            .Param("Id")
                                            .Param("Date_of_Accident__c")
                                            .Param("Date_of_Birth__c")
                                            .Param("First_Name__c")
                                            .Param("Last_Name__c")
                                            .Param("Law_Firm_Account_Name__c")
                                            .Param("Name")
                                            .Param("Case_Status__c")
                                            .Param("Home_Phone__c")
                                            .Param("Cell_Phone__c")
                                            .Param("Medical_Funding_Amount__c")
                                            .Param("Attorney__r.Name").ToString();

            SOQLQueryBuilder.SearchBy(ref query, patientName, "Name");
            return query;
        }

        /// <summary>
        /// Returns list of patients with Ids
        /// </summary>
        /// <param name="patientIds"></param>
        /// <returns></returns>
        public static string GetPatientsByIdList(string patientIds, int pageSize)
        {
            string query = $@"SELECT
                    Id,
                    Date_of_Accident__c,
                    Date_of_Birth__c,
                    First_Name__c,
                    Last_Name__c
                    FROM Opportunity
                    WHERE Id IN ({patientIds})
                    LIMIT {pageSize}";
            return query;
        }

        /// <summary>
        /// Salesforce query to return list of patient detail for search grid
        /// </summary>
        /// <param name="patientIds">array of patientIds</param>
        /// <returns>String query to be executed</returns>
        public static string GetPatientSearchGridDetails(string patientIds, bool caseStatusSort)
        {
            string query = $@"SELECT
	                            id,
	                            Name,
	                            Plaintiff_Email__c,
	                            Date_of_Birth__c,
	                            Date_of_Accident__c,
                                State__c,
                                Law_Firm_Account_Name__c,
	                            StageName,
                                Case_Status__c,
	                            Home_Phone__c,
                                Cell_Phone__c,
	                            Medical_Funding_Amount__c,
                                Attorney__r.Id,
	                            Attorney__r.Name,
                                Attorney__r.Email,
                                Attorney__r.Phone,
	                            Description_of_Injuries__c,
                                LastActivityDate,
                                LastModifiedDate,
                                Last_90_Day_Follow_up_Date__c,
                                toLabel(What_type_of_case__c)
                            FROM Opportunity
                            WHERE Id IN ({patientIds})";
            if (caseStatusSort)
            {
                query += $"\nORDER BY Last_90_Day_Follow_up_Date__c ASC NULLS FIRST";
            }
            else
            {
                query += $"\nORDER BY LastModifiedDate DESC";
            }
            return query;
        }

        /// <summary>
        /// Salesforce query to return patient details
        /// </summary>
        /// <param name="patientId">patientId</param>
        /// <returns>String query to be executed</returns>
        public static string GetPatientDetails(string patientId)
        {
            string query = $@"SELECT
	                            id,
	                            Name,
                                Date_of_Birth__c,
                                Case_Status__c,
                                StageName,
	                            Address__c,
	                            Address_2__c,
	                            City__c,
	                            State__c,
	                            Zip__c,
	                            Home_Phone__c,
	                            Cell_Phone__c,
	                            Plaintiff_Email__c,
	                            Date_of_Accident__c,
	                            toLabel(What_type_of_case__c),
                                Description_of_Injuries__c,
                                Insurance_Company_I__r.Id,
	                            Insurance_Company_I__r.Name,
                                Insurance_Company_II__r.Id,
	                            Insurance_Company_II__r.Name,
                                Insurance_Limits__c,
                                Insurance_Limits_2__c,
                                Insurance_Policy_Type__c,
                                Insurance_Policy_Type_II__c,
                                Medical_Max_Limit__c,
	                            Claim__c,
	                            What_state_are_you_in__c,
	                            Accident_Report__c,
                                Account.Name,
                                Account.Id,
	                            Attorney__r.Name,
                                Attorney__r.Id,
	                            Attorneys_Phone__c,
	                            Attorney_s_Email__c,
                                Paralegal_or_Case_Manager__r.Id,
	                            Paralegal_or_Case_Manager__r.Name,
                                Paralegal_or_Case_Manager__r.Email,
	                            Paralegal_or_Case_Manager__r.Phone,
                                Co_counsel_s_Law_Firm__r.Name,
                                Co_counsel_s_Law_Firm__r.Id,
                                Cocounsel__r.Id,
                                Cocounsel__r.Name,
                                Provider_Portal_Alert_Message__c,
                                Attorney_Portal_Alert_Message__c,
                                Cherokee_Case_Manager__c,
                                Last_90_Day_Follow_up_Date__c,
                                Injury_Details__c,
                                Injury_Type__c,
                                Date_of_First_Treatment__c,
                                Treatment_Complete__c,
                                Capped_At_Minimum__c,
                                Jurisdiction_State__c,
                                Partner_Account__r.Name,
                                Medical_Funding_Amount__c,
                                (SELECT LastModifiedDate FROM Cases__r ORDER BY LastModifiedDate DESC LIMIT 1),
                                (SELECT LastModifiedDate FROM ActivityHistories ORDER BY ActivityDate DESC, LastModifiedDate DESC LIMIT 1),
                                (SELECT Case_Docket__c, Case_Jurisdiction__c FROM Case_Status_Updates__r ORDER BY LastModifiedDate DESC  LIMIT 1),
                                LastModifiedDate
                            FROM Opportunity
                            WHERE id = '{patientId}'";

            return query;
        }

        /// <summary>
        /// Salesforce query to return large payoff approval details
        /// </summary>
        /// <param name="patientId">patientId</param>
        /// <returns>String query to be executed</returns>
        public static string GetPayoffApproval(string patientId)
        {
            string query = $@"SELECT
	                            id,
	                            Payoff_Approved__c
                            FROM Opportunity
                            WHERE id = '{patientId}'";

            return query;
        }

        /// <summary>
        /// Returns query string for returning a patients law firm
        /// </summary>
        /// <param name="patientId"></param>
        /// <returns></returns>
        public static string GetPatientLawFirmId(string patientId)
        {
            string query = $"SELECT AccountId FROM Opportunity WHERE Id = '{patientId}'";
            return query;
        }

        public static string GetFundingIdOfPatientWithAccountIds(string patientId, List<string> accountIds, bool isRollUp)
        {
            string accountFieldString = "";
            string accountIdsString = string.Join(",", accountIds.Select(i => $"'{i}'"));
            if (isRollUp)
            {
                accountFieldString = $"Medical_Location__c IN ({accountIdsString}) ";
            }
            else
            {
                accountFieldString = $"Medical_Facility__c IN ({accountIdsString}) OR Partner_Account__c IN ({accountIdsString}) ";
            }
            string query = @$"SELECT
                                Id
                            FROM
                                Funding__c
                            WHERE
                                (Plaintiff__r.Id = '{patientId}')
                            AND
                                ({accountFieldString})
                            AND
                                (Plaintiff__r.StageName NOT IN ({new ProviderStageNames().getExemptStageSalesforceList()}))";
            return query;
        }

        public static string GetPatientAlertMessages(string patientId)
        {
            return $@"SELECT
                        id,
                        Alert_Message__c,
                        Account.Alert_Message__c,
                        Insurance_Policy_Type__c,
                        Insurance_Policy_Type_II__c
                      FROM Opportunity WHERE id = '{patientId}'";
        }

        /// <summary>
        /// Salesforce query to return attorney email associated with the opportunity
        /// </summary>
        /// <param name="patientId">patientId</param>
        /// <returns>String query to be executed</returns>
        public static string GetPatientAttorneyEmail(string patientId)
        {
            /*string query = $@"SELECT id, Alert_Message__c, Alert_Messages_Law_Firm__c FROM Opportunity WHERE id = '{patientId}'";*/
            string query = $@"SELECT Attorney_s_Email__c FROM Opportunity WHERE id = '{patientId}'";

            return query;
        }
        private static void SearchByFundingFilters(ref string query, FundingFilters filters)
        {
            SOQLQueryBuilder.SearchBy(ref query, filters.PlaintiffId, "Plaintiff__c");
            SOQLQueryBuilder.SearchByList(ref query, filters.Types, "RecordType.DeveloperName");
            if (filters.ExcludeWhiteLabel == true)
            {
                query += $@" AND Medical_Facility__r.White_Label__c != 'Yes'";
                query += $@" AND Partner_Account__r.White_Label__c != 'Yes'";
            }
            SOQLQueryBuilder.SearchByList(ref query, filters.NotSubStages, "Funding_Sub_Stage__c", notIn: true);
            SOQLQueryBuilder.SearchByList(ref query, filters.NotStages, "Funding_Stage__c", notIn: true);
            SOQLQueryBuilder.SearchByList(ref query, filters.NotPayoffStatus, "Payoff_Status__c", notIn: true);

            if (!string.IsNullOrEmpty(filters.OrderBy))
            {
                query += $"\nORDER BY {filters.OrderBy}";
            }

            if (filters.PageSize != null)
            {
                query += $"\nLIMIT {filters.PageSize}";
            }
        }

        #endregion

        #region Clients

        /// <summary>
        /// Returns query string for searching attorney clients
        /// </summary>
        /// <param name="clientName"></param>
        /// <param name="clientDOB"></param>
        /// <param name="clientDOI"></param>
        /// <param name="clientCaseStatus"></param>
        /// <param name="AccountId"></param>
        /// <param name="attorneyId"></param>
        /// <param name="paralegalId"></param>
        /// <param name="pageSize"></param>
        /// <returns></returns>
        public static string ClientSearch(OpportunityFilters searchBody, string accountSfIDs, bool isCaseStatus = false)
        {
            string query = @$"SELECT
	                            id,
                                Name,
	                            Date_of_Birth__c,
	                            Plaintiff_Email__c,
	                            Date_of_Accident__c,
                                State__c,
                                What_state_are_you_in__c,
	                            StageName,
                                Case_status__c,
                                Last_90_Day_Follow_up_Date__c,
	                            Home_Phone__c,
                                Cell_Phone__c,
	                            Non_White_Label_Total_Amount__c,
                                Total_Owed_for_Plaintiff_Fundings__c,
                                Attorney__r.Id,
	                            Attorney__r.Name,
                                Attorney__r.Email,
                                Attorney__r.Phone,
                                Paralegal_or_Case_Manager__r.Id,
                                Paralegal_or_Case_Manager__r.Name,
                                Paralegal_or_Case_Manager__r.Email,
                                Paralegal_or_Case_Manager__r.Phone,
	                            Description_of_Injuries__c,
                                LastActivityDate,
                                LastModifiedDate,
                                toLabel(What_type_of_case__c)
                            FROM Opportunity
                            WHERE AccountId IN ({accountSfIDs})";

            query += $"\nAND White_Label_Status__c != 'Full White Label'";

            if (isCaseStatus)
            {
                query += $"\nAND Last_90_Day_Follow_up_Date__c != LAST_N_DAYS:60";
                searchBody.OrderBy = "Last_90_Day_Follow_up_Date__c ASC NULLS FIRST";
            }
            else
            {
                query += $"\nAND Last_90_Day_Follow_up_Date__c = LAST_N_DAYS:60";
                searchBody.OrderBy = "LastModifiedDate DESC";
            }

            query = OpportunitySalesForceQueries.SearchByOpportunityFilters(searchBody, query, new AttorneyStageNames());

            return query;
        }

        public static string GetFundingsByOpportunity(FundingFilters filters)
        {
            string query = $@"SELECT
                                Id,
                                Name,
                                Date_of_Service__c,
                                RecordType.Id,
                                RecordType.Name,
                                Check_Number__c,
                                Date_Funding_Sent_Out__c,
                                Invoice_Amount__c,
                                Funding_Amount__c,
                                Medical_Facility__r.Id,
                                Medical_Facility__r.Name,
                                Funding_Stage__c
                            FROM Funding__c";

            SearchByFundingFilters(ref query, filters);

            return query;
        }

        public static string ClientFundingSearchById(string clientId, IEnumerable<string> types, int pageSize, bool? excludeWhiteLabel = true)
        {
            string query = $@"SELECT
	                            Id,
                                Plaintiff__c,
                                Name,
                                Type__c,
                                RecordType.Name,
                                Date_of_Service__c,
                                Invoice_Amount__c,
                                Medical_Facility__r.Id,
                                Medical_Facility__r.Name,
                                Medical_Facility__r.White_Label__c,
                                Date_Funding_Sent_Out__c,
                                Funding_Amount__c,
                                Buyout_Amount__c,
                                Partner_Account__c,
                                Partner_Account__r.Name,
                                Partner_Account__r.White_Label__c
                            FROM Funding__c";

            SearchByFundingFilters(ref query, new FundingFilters()
            {
                PlaintiffId = clientId,
                NotSubStages = StageNames.ExemptFundingSubStages,
                NotStages = StageNames.ExemptFundingStages,
                NotPayoffStatus = StageNames.ExemptPayOffStatus,
                Types = types,
                ExcludeWhiteLabel = excludeWhiteLabel,
                OrderBy = "CreatedDate DESC",
                PageSize = pageSize,
            });

            return query;
        }

        public static string FundingAmountMedicalFacilityById(List<string> opportunityIDs)
        {


            string query = SOQLQueryBuilder.BuildSelectQuery("Funding__c")
                                                .Param("Medical_Facility__c")
                                                .Param("Medical_Facility__r.Name", "MedicalFacilityName")
                                                .Param("Partner_Account__c")
                                                .Param("Partner_Account__r.Name", "PartnerAccountName")
                                                .Param("SUM(Invoice_Amount__c)", "TotalInvoiceAmount")
                                                .ToString();

            SOQLQueryBuilder.SearchByList(ref query, opportunityIDs, "Plaintiff__c");
            SOQLQueryBuilder.SearchByList(ref query, StageNames.ExemptFundingStages, "Funding_Stage__c", notIn: true);
            SOQLQueryBuilder.SearchLike(ref query, "Medical Funding%", "RecordType.Name");
            SOQLQueryBuilder.GroupBy(ref query, "Medical_Facility__c, Medical_Facility__r.Name, Partner_Account__c, Partner_Account__r.Name");

            return query;
        }


        public static string MedicalFacilityPayoffVerificationById(string opportunityID)
        {
            string query = $@"SELECT
	                            Id,
                                Medical_Facility__r.Verify_Portal_Balance__c,
                                Medical_Facility__r.Name,
                                Partner_Account__r.Name,
                                Partner_Account__r.Verify_Portal_Balance__c
                            FROM Funding__c
	                        WHERE  Plaintiff__r.Id = '{opportunityID}'"
                            ;
            return query;
        }

        public static string MedicalFacilityStateById(string opportunityID)
        {
            string query = $@"SELECT
                                Medical_Facility__r.Name,
                                Medical_Facility__r.BillingState,
                                Medical_Facility__r.BillingStateCode,
                                Partner_Account__r.Name,
                                Partner_Account__r.BillingState,
                                Partner_Account__r.BillingStateCode
                            FROM Funding__c
	                        WHERE  Plaintiff__r.Id = '{opportunityID}'"
                            ;
            return query;
        }

        public static string ClientFundingSearchByIdList(string clientIdList, IEnumerable<string> types)
        {
            string query = $@"SELECT
                                Plaintiff__c,
                                Invoice_Amount__c,
                                Funding_Amount__c,
                                Medical_Facility__c,
                                Medical_Facility__r.White_Label__c,
                                Partner_Account__c,
                                Partner_Account__r.White_Label__c,
                                Date_Funding_Sent_Out__c,
                                Amount_Sent_to_Provider__c
                            FROM Funding__c
	                        WHERE Plaintiff__c IN ({clientIdList})
                            AND (NOT Funding_Sub_Stage__c IN ({StageNames.GetExemptFundingSubStageList()}))
                            AND (NOT Funding_Stage__c IN ({StageNames.GetExemptFundingStageList()}))
                            AND (NOT Payoff_Status__c IN({StageNames.GetExemptPayOffStatusList()}))";

            SearchByFundingFilters(ref query, new FundingFilters
            {
                Types = types,
            });

            return query;
        }

        public static string ClientPaymentAmountAgreedById(string clientId)
        {
            string query = $@"SELECT
                                Id,
                                StageName,
                                Sub_Stage__c,
                                One_Payoff_Status__c,
                                Payment_Amount_Agreed_to_by_CF__c
                            FROM Opportunity
	                        WHERE Id = '{clientId}'
                            LIMIT 1
                        ";
            return query;
        }

        #endregion

        #region Attachments
        /// <summary>
        /// Salesforce query to return patient attachments
        /// </summary>
        /// <param name="entityId">patientId</param>
        /// <returns>String query to be executed</returns>
        public static string GetAttachmentListByLinkedEntityId(string entityId, bool showPPD = true)
        {
            string query = $@"SELECT
                    Id,
                    ContentDocumentId,
                    ShareType,
                    Visibility,
                    ContentDocument.LatestPublishedVersionId,
                    ContentDocument.Title,
                    ContentDocument.CreatedById,
                    ContentDocument.LastModifiedDate,
                    ContentDocument.FileType,
                    ContentDocument.LatestPublishedVersion.Document_Type__c,
                    ContentDocument.LatestPublishedVersion.Law_Firm__c
                FROM ContentDocumentLink
                WHERE LinkedEntityId = '{entityId}'
                AND ContentDocument.FileType IN ('PDF', 'WORD_X')";

            if (!showPPD)
                query += $"{Environment.NewLine}AND ContentDocument.LatestPublishedVersion.Document_Type__c != 'PPD'";

            return query;
        }

        /// <summary>
        /// Salesforce query to return patient attachments
        /// </summary>
        /// <param name="patientId">patientId</param>
        /// <returns>String query to be executed</returns>
        public static string GetPatientAttachmentList(PatientAttachmentQueryInfo queryInfo)
        {
            string query = $@"SELECT
                    Id,
                    ContentDocumentId,
                    ContentDocument.LatestPublishedVersionId,
                    ContentDocument.Title,
                    ContentDocument.CreatedById,
                    ContentDocument.LastModifiedDate,
                    ContentDocument.FileType,
                    Document_Type__c
                FROM ContentVersion
                WHERE Opportunity__c = '{queryInfo.entityId}'
                AND isLatest = true";

            switch (queryInfo.accountOwnerType)
            {
                case "Medical Facility":
                    if (queryInfo.isRollup.HasValue && queryInfo.isRollup.Value)
                    {
                        string addquery = HierarchyManager.getSOQLHierarchyAttachmentClause(queryInfo.rollupDepthList?.Select(d => d.ToString()).ToList() ?? new List<string>(), queryInfo.rollupNodeList ?? new List<string>());
                        if (!string.IsNullOrEmpty(addquery))
                        {
                            query += $"\nAND ({addquery})";
                        }
                        query += $"\nAND Document_Type__c IN ({queryInfo.docTypes})";
                    }
                    else
                    {
                        query += $"\nAND Medical_Facility__c IN ({queryInfo.accountOwnerIds})";
                        query += $"\nAND Document_Type__c IN ({queryInfo.docTypes})";
                    }
                    break;
                case "Law Firm":
                    query += $"\nAND Portal_Attorney_Share__c = true AND Document_Type__c IN ({queryInfo.docTypes})";
                    query += $"\nAND Medical_Facility__r.White_Label__c != 'Yes'";
                    break;
            }

            if (!queryInfo.showPPD)
                query += $"\nAND Document_Type__c != 'PPD'";

            query += $"\nLIMIT {queryInfo.pageSize}";
            return query;
        }

        /// <summary>
        /// Gets opportunity attachments based off opportunity id and specified types.
        /// </summary>
        /// <param name="entityId"></param>
        /// <param name="typeList"></param>
        /// <param name="pageSize"></param>
        /// <returns></returns>
        public static string GetOpportunityAttachmentListByTypes(string entityId, string typeList, int pageSize = 200)
        {
            string query = $@"SELECT
                    Id,
                    ContentDocumentId,
                    ContentDocument.LatestPublishedVersionId,
                    ContentDocument.Title,
                    ContentDocument.CreatedById,
                    ContentDocument.LastModifiedDate,
                    ContentDocument.FileType,
                    Document_Type__c
                FROM ContentVersion
                WHERE Opportunity__c = '{entityId}'
                AND isLatest = true
                AND Document_Type__c IN ({typeList})
                LIMIT {pageSize}";

            return query;
        }

        public static string filterPatientAttachmentsByAccount(string listOfDocuments, string accountOwnerId, int pageSize)
        {
            string query = $@"SELECT
                    Id,
                    ContentDocumentId,
                    ShareType,
                    Visibility,
                    ContentDocument.LatestPublishedVersionId,
                    ContentDocument.Title,
                    ContentDocument.CreatedById,
                    ContentDocument.LastModifiedDate,
                    ContentDocument.FileType
                FROM ContentDocumentLink
                WHERE ContentDocumentId IN ({listOfDocuments})
                AND LinkedEntityId = '{accountOwnerId}'
                LIMIT {pageSize}";

            return query;
        }

        /// <summary>
        /// Salesforce query to return an attachment
        /// </summary>
        /// <param name="documentId">patientId</param>
        /// <returns>String query to be executed</returns>
        public static string GetAttachmentById(string documentId)
        {
            string query = $@"SELECT
	                    Id,
	                    ContentDocumentId,
	                    FileExtension,
	                    Title,
	                    VersionNumber,
	                    IsLatest,
	                    VersionData,
                        Medical_Facility__c,
                        Law_Firm__c,
                        ContentDocument.LatestPublishedVersionId
	                FROM ContentVersion
	                WHERE Id = '{documentId}'";
            return query;
        }

        public static string GetLatestVersionId(string contentDocID)
        {
            string query = $@"SELECT
	                    LatestPublishedVersionId
	                FROM ContentDocument
	                WHERE Id = '{contentDocID}'";
            return query;
        }

        /// <summary>
        /// Salesforce query to return an attachment
        /// </summary>
        /// <param name="documentId">patientId</param>
        /// <returns>String query to be executed</returns>
        public static string GetAttachmentsByDocListAndType(string docList, string documentType = null)
        {
            string query = $@"SELECT
	                    Id,
	                    ContentDocumentId,
	                    FileExtension,
	                    Title,
	                    VersionNumber,
	                    IsLatest,
	                    VersionData,
                        Medical_Facility__c,
                        Law_Firm__c,
                        ContentDocument.LatestPublishedVersionId,
                        CreatedDate
	                FROM ContentVersion
	                WHERE Id IN ({docList})";
            if (!String.IsNullOrEmpty(documentType))
                query += $"AND Document_Type__c = '{documentType}'";
            return query;
        }

        /// <summary>
        /// Salesforce query to return documents of a certain type and entity owners
        /// </summary>
        /// <param name="entityIdList"></param>
        /// <param name="documentType"></param>
        /// <returns></returns>
        public static string FilterDocumentsByTypeAndEntities(string entityIdList, string documentType)
        {
            string query = $@"SELECT
                ContentDocument.CreatedDate,
                ContentDocument.FileType,
                ContentDocument.Id,
                ContentDocument.LatestPublishedVersionId
                FROM ContentVersion WHERE ContentDocument.Id IN ({entityIdList})
                AND Document_Type__c = '{documentType}'";
            return query;
        }

        /// <summary>
        /// Get list of documents by entityId
        /// </summary>
        /// <param name="entityId"></param>
        /// <returns></returns>
        public static string GetDocumentsByEntityId(string entityId)
        {
            string query = $@"SELECT ContentDocumentId FROM ContentDocumentLink WHERE linkedEntityId = '{entityId}'";
            return query;
        }

        /// <summary>
        /// Document List By patient list and document list
        /// </summary>
        /// <param name="entityList"></param>
        /// <param name="documentList"></param>
        /// <param name="pageSize"></param>
        /// <returns></returns>
        public static string GetAttachmentListByPatientListAndDocList(string entityList, string documentList)
        {
            string query = $@"SELECT
	                    Id,
                        ContentDocument.Title,
                        ContentDocument.Id,
                        LinkedEntityId,
                        ContentDocument.LatestPublishedVersionId,
                        ContentDocument.FileType,
                        ContentDocument.CreatedDate
	                FROM ContentDocumentLink
	                WHERE ContentDocumentId IN ({documentList})";
            if (!String.IsNullOrEmpty(entityList))
                query += $"AND LinkedEntityId IN ({entityList})";

            query += $@"ORDER BY ContentDocument.CreatedDate DESC";
            return query;
        }

        /// <summary>
        /// Returns query for search a patients documents by type and owner
        /// </summary>
        /// <param name="entityList"></param>
        /// <param name="documentType"></param>
        /// <param name="accountOwnerId"></param>
        /// <param name="accountOwnerType"></param>
        /// <param name="pageSize"></param>
        /// <returns></returns>
        public static string GetAttachmentListByPatientListAndDocType(string entityList, string documentType, string accountOwnerId, string accountOwnerType, int pageSize)
        {
            string query = $@"SELECT
	                    Id,
                        ContentDocument.Title,
                        ContentDocument.Id,
                        ContentDocument.LatestPublishedVersionId,
                        ContentDocument.FileType,
                        ContentDocument.CreatedDate,
                        Opportunity__c
	                FROM ContentVersion
	                WHERE IsLatest = true";
            if (!String.IsNullOrEmpty(entityList))
                query += $" AND Opportunity__c IN ({entityList})";
            if (!String.IsNullOrEmpty(documentType))
                query += $" AND Document_Type__c = '{documentType}'";

            switch (accountOwnerType)
            {
                case "Medical Facility":
                    query += $" AND Medical_Facility__c = '{accountOwnerId}'";
                    break;
                case "Law Firm": // Is the Law Firm case required?
                    query += $" AND Portal_Attorney_Share__c = true";
                    query += $" AND Medical_Facility__r.White_Label__c != 'Yes'";
                    break;
            }

            query += $@" ORDER BY ContentDocument.CreatedDate DESC
                        LIMIT {pageSize}";
            return query;
        }

        #endregion

        #region Billing
        /// <summary>
        /// Salesforce query to return list of patient claims
        /// </summary>
        /// <param name="patientIds">array of patientIds</param>
        /// <param name="PlaintiffID">Id of Opportunity</param>
        /// <param name="PlaintiffName">Name of Opportunity</param>
        /// <param name="Limit">Number of records to return</param>
        /// <param name="IsRollup">Does Account use Hierarchy</param>
        /// <param name="RollupNodeList">List of comma separated strings</param>
        /// <param name="RollupDepthList">List of distances from node to Leaf</param>
        /// <returns>String query to be executed</returns>
        public static string ClaimsSearch(ClaimSearchBody search, string parentAccountId)
        {
            var facilityString = $"Medical_Facility__c = '{parentAccountId}' ";

            if (search.IsRollUp && search.RollupDepthList != null)
            {
                facilityString = HierarchyManager.getSOQLHierarchyFundingClause(search.RollupDepthList, search.RollupNodeList);
            }

            string query = $@"SELECT
	                            Id,
                                RecordType.Name,
	                            Invoice_Amount__c,
	                            Plaintiff__r.Name,
	                            Plaintiff__r.Id,
	                            Date_of_Service__c,
	                            Plaintiff__r.Date_of_Accident__c,
                                Plaintiff__r.Date_of_Birth__c,
	                            LastModifiedDate,
                                CreatedDate,
                                Medical_Facility__r.Name,
                                Date_Funding_Sent_Out__c,
                                Amount_Sent_to_Provider__c
                            FROM Funding__c
                            WHERE ({facilityString})";

            // Apply either plaintiff filter OR stage filter, but not both
            if (!String.IsNullOrEmpty(search.PlaintiffID))
            {
                query += $"\nAND Plaintiff__c = '{search.PlaintiffID}'";
            }
            else
            {
                query += $"\nAND Plaintiff__r.StageName != 'Settled'";
                SOQLQueryBuilder.SearchByName(ref query, search.PlaintiffName, "Plaintiff__r.");
            }

            query += $"\nAND Invoice_Amount__c > 0";
            query += $"\nAND (NOT Funding_Sub_Stage__c IN ({StageNames.GetExemptFundingSubStageList()}))";
            query += $"\nAND (NOT Funding_Stage__c IN ({StageNames.GetExemptFundingStageList()}))";
            query += $"\nORDER BY CreatedDate DESC\nLIMIT {search.Limit}";

            return query;
        }
        #endregion

        #region Law Firm

        /// <summary>
        /// Salesforce query to search for a Law firm
        /// </summary>
        /// <param name="Law Firm Name">Name of attorney</param>
        /// <param name="pagesize">number of records to get</param>
        /// <param name="RollupDepthList">list of distances from given node to leaf</param>
        /// <param name="RollupNodeList">list of comma separated strings</param>
        /// <returns>String query to be executed</returns>
        public static string LawFirmSearchQuery(FirmSearchBody search)
        {
            string query = $@"SELECT
	            Account.Id,
	            Account.Name,
                Attorney__c,
                COUNT(Id)
            FROM Opportunity
            WHERE Account.Type = 'Law Firm'";

            if (search.IsRollup && search.RollupDepthList != null)
            {
                query += " AND Id IN ( SELECT Plaintiff__c FROM Funding__c WHERE ";
                query += HierarchyManager.getSOQLHierarchyFundingClause(search.RollupDepthList, search.RollupNodeList);
                query += ")";
            }

            if (!string.IsNullOrEmpty(search.LawFirmName))
            {
                query += $@" AND Account.Name LIKE '%{search.LawFirmName}%'";
            }
            query += $" GROUP BY Account.Id, Account.Name, Attorney__c";
            query += $" LIMIT {search.PageSize}";

            return query;
        }

        /// <summary>
        /// Salesforce query to search for a Law firm by name at the acccount level
        /// </summary>
        /// <param name="Law Firm Name">Name of attorney</param>
        /// <param name="pagesize">number of records to get</param>
        /// <returns>String query to be executed</returns>
        public static string SearchAllLawFirmsByName(FirmSearchBody search)
        {
            string query = $@"SELECT
	            Id,
                Name
            FROM Account
            WHERE Type = 'Law Firm'";

            if (!string.IsNullOrEmpty(search.LawFirmName))
            {
                query += $@" AND Name LIKE '%{search.LawFirmName}%'";
            }
            query += $" LIMIT {search.PageSize}";

            return query;
        }

        public static string GetLawFirmById(string id)
        {
            string query = $@"SELECT
	            Id,
	            Name
            FROM Account
            WHERE Type = 'Law Firm'
            AND Id = '{id}'";

            return query;
        }

        /// <summary>
        /// Salesforce query to search for an attorney associated with lawfirm
        /// </summary>
        /// <param name="attorneyName"></param>
        /// <param name="lawfirmId"></param>
        /// <param name="pageSize"></param>
        /// <returns>Query string to be executed</returns>
        public static string AttorneyFirmSearchQuery(string attorneyName, string lawfirmId, string recordTypeId, string contactType, List<string> acceptedTypes, int pageSize)
        {
            string query = $@"SELECT
	            Id,
	            Name,
                Phone,
                Email
            FROM Contact
            WHERE RecordTypeId = '{recordTypeId}'
            AND Name LIKE '%{attorneyName}%'
            AND (NOT Name LIKE '%no longer%')";

            SOQLQueryBuilder.SearchBy(ref query, lawfirmId, "AccountId");

            if (contactType == "Attorney")
            {
                foreach (var type in acceptedTypes)
                {
                    if (type != null)
                    {
                        if (acceptedTypes.IndexOf(type) == 0)
                        {
                            query += $"\nAND (Title Like '%{type}%'";
                        }
                        else
                        {
                            query += $"\nOR Title Like '%{type}%'";

                            if (acceptedTypes.IndexOf(type) == (acceptedTypes.Count - 1))
                            {
                                query += $")";
                            }
                        }
                    }
                }
            }
            else
            {
                foreach (var type in acceptedTypes)
                {
                    if (type != null)
                    {
                        if (acceptedTypes.IndexOf(type) == 0)
                        {
                            query += $"\nAND ((NOT Title Like '%{type}%')";
                        }
                        else
                        {
                            query += $"\nAND (NOT Title Like '%{type}%')";

                            if (acceptedTypes.IndexOf(type) == (acceptedTypes.Count - 1))
                            {
                                query += $")";
                            }
                        }
                    }
                }
            }

            SOQLQueryBuilder.OrderBy(ref query, "Name ASC NULLS LAST");
            SOQLQueryBuilder.PageSize(ref query, pageSize);

            return query;
        }

        public static string GetLawFirmContacts(string lawfirmId, List<string> acceptedTypes)
        {
            string query = $@"SELECT
	            Id,
	            Name,
                Phone,
                Email
            FROM Contact
            WHERE RecordTypeId in {ConvertListIntoSOQLParameter(acceptedTypes)}
            AND AccountId = '{lawfirmId}'";

            return query;
        }

        /// <summary>
        /// Salesforce query to search for all contacts associated with lawfirm
        /// </summary>
        /// <param name="attorneyName"></param>
        /// <param name="lawfirmId"></param>
        /// <param name="pageSize"></param>
        /// <returns>Query string to be executed</returns>
        public static string ContactFirmSearchQuery(string attorneyName, string lawfirmId, string recordTypeId, int pageSize)
        {
            string query = $@"SELECT
	            Id,
	            Name,
                Phone,
                Email
            FROM Contact
            WHERE RecordTypeId = '{recordTypeId}'
            AND AccountId = '{lawfirmId}'
            AND Name LIKE '%{attorneyName}%'
            AND (NOT Name LIKE '%no longer%')";

            query += $@" ORDER BY Name ASC NULLS LAST LIMIT {pageSize}";

            return query;
        }

        /// <summary>
        /// Salesforce query to search for all attorneys
        /// </summary>
        /// <param name="attorneyName"></param>
        /// <param name="pageSize"></param>
        /// <returns>Query string to be executed</returns>
        public static string AttorneySearchByIDsQuery(string attorneyName, string recordTypeId, string attorneyIDs, string contactType, List<string> acceptedTypes, int pageSize)
        {
            string query = $@"SELECT
	            Id,
	            Name,
                Phone,
                Email
            FROM Contact
            WHERE RecordTypeId = '{recordTypeId}'
            AND Id IN ({attorneyIDs})
            AND (NOT Name LIKE '%no longer%')";

            if (!string.IsNullOrEmpty(attorneyName))
            {
                query += $@"AND Name LIKE '%{attorneyName}%'";
            }

            query += $@" ORDER BY Name ASC NULLS LAST LIMIT {pageSize}";

            return query;
        }

        public static string GetProviderLawFirms(string medicalFacilityIDs)
        {
            string query =
                $@"SELECT
                    Plaintiff__r.AccountId
                FROM Funding__c
                WHERE Medical_Facility__c IN ({medicalFacilityIDs})
                    OR Partner_Account__c IN ({medicalFacilityIDs})";

            return query;
        }
        #endregion

        #region Account
        /// <summary>
        /// Salesforce query to lookup account by name
        /// </summary>
        /// <param name="name">Name of Account</param>
        /// <returns>String query to be executed</returns>
        public static string LookupAccountByName(string name)
        {
            string query = $@"SELECT
	            Id,
                RecordTypeId,
                Name,
                Email__c,
                Date_of_Birth__c,
                White_Label__c,
                ParentId
            FROM Account
            WHERE Name ='{name}'";

            return query;
        }

        /// <summary>
        /// Salesforce query to lookup account by email
        /// </summary>
        /// <param name="email"></param>
        /// <returns></returns>
        public static string LookupAccountByEmail(string email)
        {
            string query = $@"SELECT
	            Id,
                RecordTypeId,
                Name,
                Email__c,
                Phone,
                Date_of_Birth__c,
                White_Label__c,
                ParentId
            FROM Account
            WHERE Email__c ='{email}'";

            return query;
        }

        /// <summary>
        /// Salesforce query to lookup Account by Id
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public static string LookupAccountById(string id)
        {
            string query = $@"SELECT
	            Id,
                Name,
                RecordTypeId,
                RecordType.Name,
                White_Label__c,
                ParentId,
                Cherokee_Case_Manager__r.Portal_Contact_ID__c,
                Cherokee_Case_Manager__r.Name,
                Cherokee_Case_Manager__r.Email,
                Cherokee_Case_Manager__r.Phone,
                Cherokee_Risk_Manager__c,
                Cherokee_Risk_Manager__r.Portal_Contact_ID__c,
                Cherokee_Risk_Manager__r.Name,
                Cherokee_Risk_Manager__r.Email,
                Cherokee_Risk_Manager__r.Phone,
                Owner.Name,
                BillingStreet,
                BillingCity,
                BillingState,
                BillingStateCode,
                BillingPostalCode,
                Contract_Type__c,
                Portal_Rewards__c,
                Phone,
                Email__c
            FROM Account
            WHERE Id ='{id}'";

            return query;
        }

        /// <summary>
        /// Salesforce query to lookup Accounts by Ids
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public static string LookupAccountsByIds(string ids)
        {
            string query = $@"SELECT
	            Id,
                Name,
                RecordTypeId,
                RecordType.Name,
                White_Label__c,
                ParentId,
                Cherokee_Case_Manager__r.Portal_Contact_ID__c,
                Cherokee_Case_Manager__r.Name,
                Cherokee_Case_Manager__r.Email,
                Cherokee_Case_Manager__r.Phone,
                Cherokee_Risk_Manager__r.Portal_Contact_ID__c,
                Cherokee_Risk_Manager__r.Name,
                Cherokee_Risk_Manager__r.Email,
                Cherokee_Risk_Manager__r.Phone,
                Owner.Name,
                BillingState,
                BillingStateCode,
                Contract_Type__c,
                Portal_Rewards__c
            FROM Account
            WHERE Id IN ({ids})";

            return query;
        }

        /// <summary>
        /// Salesforce query to lookup recordTypeId
        /// </summary>
        /// <param name="recordTypeName">Name of the record Type IE: Medical_Facility</param>
        /// <param name="sObjectType">Subtype of the Record Type IE: Opportunity</param>
        /// <returns></returns>
        public static string GetAccountRecordTypeId(string recordTypeName, string sObjectType)
        {
            return $@"SELECT Id FROM RecordType WHERE DeveloperName = '{recordTypeName}' AND SobjectType = '{sObjectType}'";
        }

        public static string GetAccountRecordTypeIdsByList(string recordTypeList, string sObjectType)
        {
            string query = $@"SELECT Id, Name FROM RecordType WHERE DeveloperName IN ({recordTypeList}) AND SobjectType = '{sObjectType}'";

            return query;
        }

        /// <summary>
        /// Salesforce query to lookup recordTypeId
        /// </summary>
        /// <param name="recordTypeId"></param>
        /// <returns>MedicalFacility</returns>
        public static string GetAccountRecordTypeById(string recordTypeId)
        {
            string query = $@"SELECT Name FROM RecordType WHERE Id = '{recordTypeId}'";

            return query;
        }

        /// <summary>
        /// Salesforce query to search accounts of specified record type.
        /// </summary>
        /// <param name="accountName">Account name to search</param>
        /// <param name="recordTypeId">Record type filter</param>
        /// <returns></returns>
        public static string AccountSearchByRecordType(string accountName, string recordTypeId, int pageSize)
        {
            string query = $@"SELECT
	            Id,
                Name
            FROM Account
            WHERE Name LIKE '%{accountName}%'
                AND RecordTypeId = '{recordTypeId}'
                ORDER BY Name ASC NULLS LAST LIMIT {pageSize}";

            return query;
        }

        /// <summary>
        /// Salesforce query to search accounts of specified record type list.
        /// </summary>
        /// <param name="accountName">Account name to search</param>
        /// <param name="recordTypeIdList">Record types filter</param>
        /// <returns></returns>
        public static string AccountSearchByRecordTypeIdList(string accountName, string recordTypeIdList, int pageSize)
        {
            if (accountName != null)
            {
                accountName = accountName.Replace("'", "\\'");

            }
            string query = $@"SELECT
	            Id,
                Name,
                RecordTypeId
            FROM Account
            WHERE Name LIKE '%{accountName}%'
                AND RecordTypeId IN ({recordTypeIdList})
                ORDER BY Name ASC NULLS LAST LIMIT {pageSize}";

            return query;
        }

        public static string GetAccountContractType(string accountId)
        {
            string query = $@"SELECT
                Contract_Type__c
            FROM Account
            WHERE Id ='{accountId}'";

            return query;
        }

        public static string GetFundingSource(string oppId)
        {
            string query = $@"SELECT
                FUNDING_SOURCE__C
            FROM FUNDING__C
            WHERE PLAINTIFF__C='{oppId}' AND FUNDING_STAGE__C NOT IN ('Rejected', 'Refunded')";

            return query;
        }

        public static string GetUserRoleId(string sfUserID)
        {
            string query = $@"SELECT
	            Profile.Name
            FROM User
            WHERE Portal_Contact_ID__c = '{sfUserID}'";
            return query;
        }

        public static string GetAccountGeolocation(string accountId)
        {
            string query = $@"SELECT
                Id,
                Name,
                BillingLatitude,
                BillingLongitude
            FROM Account
            WHERE Id ='{accountId}'";

            return query;
        }

        public static string GetAccountAddress(string accountId)
        {
            string query = $@"SELECT
                Id,
                Name,
                BillingStreet,
                BillingCity,
                BillingState
            FROM Account
            WHERE Id ='{accountId}'";

            return query;
        }

        public static string GetOrganizationAccounts(string parentAccountId)
        {
            string query = $@"SELECT
	            Id,
                Name
            FROM Account
            WHERE ParentId ='{parentAccountId}'";

            return query;
        }

        public static string GetAccountInfoForComments(string accountId)
        {
            string query = $@"SELECT
                Name,
                Cherokee_Risk_Manager__c,
                Cherokee_Case_Manager__c
            FROM Account
            WHERE Id='{accountId}'";

            return query;
        }

        public static string GetRatesByState(string stateCode)
        {
            string query = $@"SELECT
	            Id,
                State_Code__c,
                Usage_Rate__c,
                Promo_Usage_Rate__c,
                Processing_Fee__c,
                Capped_At__c,
                Promo_Capped_At__c,
                Promo_Active__c,
                Medical_Funding_Allowed__c
            FROM Rate_by_State__c
            WHERE Name = '{stateCode}'";

            return query;
        }

        public static string GetStateFundingStatus()
        {
            string query = $@"SELECT
	            State_Code__c,
                Medical_Funding_Serviced_Allowed__c,
                Medical_Funding_Partial_Advance_Allowed__c,
                Medical_Funding_Allowed__c,
                PCA_for_Medical_Allowed__c,
                Can_we_fund_in_this_state__c
            FROM Rate_by_State__c";

            return query;
        }
        #endregion

        #region Opportunity

        /// <summary>
        /// Salesforce query to lookup OpportunityId
        /// </summary>
        /// <param name="name">Name of Opportunity</param>
        /// <returns>String query to be executed</returns>
        public static string LookupOpportunityByNameDOB(string name, string dob)
        {
            string query = $@"SELECT
	            Id,
                First_Name__c,
                Last_Name__c,
                Date_of_Birth__c
            FROM Opportunity
            WHERE Name LIKE '%{name}%' AND Date_of_Birth__c = {dob}";

            return query;
        }

        /// <summary>
        /// Salesforce query to lookup OpportunityId
        /// </summary>
        /// <param name="name">Name of Opportunity</param>
        /// <returns>String query to be executed</returns>
        public static string LookupOpportunitiesByName(string name)
        {
            if (name.Contains("'"))
            {
                name = name.Replace("'", "%");
            }
            string query = $@"SELECT
	            Id,
                First_Name__c,
                Last_Name__c,
                Date_of_Birth__c,
	            Address__c,
	            Address_2__c,
	            City__c,
	            State__c,
	            Zip__c,
	            Date_of_Accident__c,
                StageName,
                Plaintiff_Account__c,
                Attorney__c
            FROM Opportunity
            WHERE Name LIKE '%{name}%'";

            return query;
        }

        public static string GetOpportunitiesByIDList(string ids)
        {
            string query = $@"SELECT
	            Id,
                First_Name__c,
                Last_Name__c,
                Date_of_Birth__c,
	            Address__c,
	            Address_2__c,
	            City__c,
	            State__c,
	            Zip__c,
	            Date_of_Accident__c,
                StageName
            FROM Opportunity
            WHERE Id IN ({ids})";

            return query;
        }
        public static string GetReductionQueueOpportunitiesByIDList(string ids, string name = "")
        {
            string query = $@"SELECT
	            Id,
                First_Name__c,
                Last_Name__c
            FROM Opportunity
            WHERE Id IN ({ids})";

            SOQLQueryBuilder.SearchByName(ref query, name);

            return query;
        }

        public static string LookupOpportunityIDsByAccountID(string accountID)
        {
            string query = $@"SELECT
	            Id
            FROM Opportunity
            WHERE AccountId = '{accountID}'
            AND StageName NOT IN({new AttorneyStageNames().getExemptStageSalesforceList()})";

            return query;
        }

        public static string LookupAccountOppsByIds(string accountIDs)
        {
            string query = $@"
                SELECT
	                AccountId,
                    COUNT(Id)
                FROM Opportunity
                WHERE AccountId IN ({accountIDs})
                    AND StageName NOT IN({new AttorneyStageNames().getExemptStageSalesforceList()})
                GROUP BY AccountId";

            return query;
        }

        public static string ProvidersMapLookupOpportunityByAccountID(string accountID, string clientName)
        {
            string query = $@"SELECT
	            Id,
                First_Name__c,
                Last_Name__c,
                Name,
	            Address__c,
	            Address_2__c,
	            City__c,
	            State__c,
                Cherokee_Case_Manager__c
            FROM Opportunity
            WHERE AccountId = '{accountID}'
            AND White_Label_Status__c != 'Full White Label'
            AND StageName NOT IN({new AttorneyStageNames().getExemptStageSalesforceList()})";

            SOQLQueryBuilder.SearchBySpaceWildcard(ref query, clientName, "Name");

            return query;
        }

        public static string GetOpportunityProviderAccounts(string oppID)
        {
            string query = $@"SELECT
               Id,
	           Name,
               Account_Name__c,
               OpportunityId__c
            FROM AccountOpportunityRelation__c
            WHERE OpportunityId__c = '{oppID}'";

            return query;
        }

        public static string GetOpportunityProviderAccounts(string oppID, string accountOwnerID)
        {
            string query = $@"SELECT
               Id,
	           Name,
               Account_Name__c,
               OpportunityId__c,
               Treatment_Complete__c
            FROM AccountOpportunityRelation__c
            WHERE OpportunityId__c = '{oppID}'
            AND Account_Name__c = '{accountOwnerID}'";

            return query;
        }

        public static string GetOpportunityTreatmentComplete(string oppID)
        {
            string query = $@"SELECT
                Id,
                Treatment_Complete__c
            FROM Opportunity
            WHERE Id = '{oppID}'";

            return query;
        }

        public static string GetTailClaimStatus(string oppID)
        {
            string query = $@"SELECT
                Id,
                Treatment_Complete__c,
                Insurance_Vendor__c,
                ATI_Tail_Claim__c
            FROM Opportunity
            WHERE Id = '{oppID}'";

            return query;
        }

        public static string GetOpportunityCases(string oppID, string caseType = "Portal Message")
        {
            string query = $@"SELECT
                Id,
                Status,
                Description,
                CreatedDate
            FROM Case
            WHERE Opportunity_Name__c = '{oppID}'
            AND CaseType__c = '{caseType}'
            AND Status != 'Closed'";

            return query;
        }

        public static string GetAllOpportunityCases(string oppID, string caseType = "", string status = "")
        {
            string query = $@"SELECT
                Id,
                Status,
                Description,
                CaseType__c,
                CreatedDate,
                CaseNumber,
                Priority,
                Subject,
                Latest_Case_Comment__c
            FROM Case";

            SOQLQueryBuilder.SearchBy(ref query, oppID, "Opportunity_Name__c");
            SOQLQueryBuilder.SearchNotBy(ref query, status, "Status");
            SOQLQueryBuilder.SearchBy(ref query, caseType, "CaseType__c");

            return query;
        }
        public static string GetAccountIdAndLawFirmFromOpportunity(string oppID)
        {
            string query = $@"SELECT
                AccountId,
                Law_Firm_Account_Name__c
            FROM Opportunity
            WHERE Id = '{oppID}'";

            return query;
        }

        public static string GetOpportunityCase(string id)
        {
            string query = $@"SELECT
                Id,
                Subject,
                Status,
                Opportunity_Name__c,
                Description
            FROM Case
            WHERE Id = '{id}'
            ";
            return query;
        }



        public static string GetAccountIdAndPartnerAccount(string opportunityId)
        {
            string query = $@"SELECT
                AccountId,
                Partner_Account__c
            FROM Opportunity
            WHERE Id = '{opportunityId}'";

            return query;
        }

        public static string GetOpportunityFundingByMedicalFacility(string opportunityId)
        {

            string query = $@"SELECT
                Partner_Account__r.name partner_name,
                Medical_Facility__r.name medical_name,
                SUM(Invoice_Amount__c)
            FROM Funding__c
            WHERE Funding_Stage__c NOT IN ('Refunded', 'Rejected', 'Historic')
            AND Plaintiff__c = '{opportunityId}'
            AND RecordType.Name LIKE 'Medical Funding%'
            GROUP BY Partner_Account__r.name, Medical_Facility__r.name";
            return query;
        }
        public static string GetUnderwriterInfo(string opportunityId)
        {
            return $@"SELECT Underwriter__c
                      FROM Opportunity
	                  WHERE id = '{opportunityId}'";
        }

        public static string GetOpportunityFundings(string opportunityId)
        {
            return $@"SELECT
                        Buyout_Amount__c,
                        Funding_Amount__c,
                        Amount_Sent_to_Provider__c,
                        Invoice_Amount__c,
                        RecordType.Name,
                        RecordTypeId
                      FROM Funding__c
                      WHERE Plaintiff__c = '{opportunityId}'
                        AND (NOT Funding_Sub_Stage__c IN ({StageNames.GetExemptFundingSubStageList()}))
                        AND (NOT Funding_Stage__c IN ({StageNames.GetExemptFundingStageList()}))";
        }

        public static string GetOpportunityTotalSentOut(string opportunityId)
        {
            return $@"SELECT
                        Total_Out_for_Plaintiff__c,
                        Total_Amount_Sent_to_Provider__c,
                        Medical_Funding_Amount__c
                      FROM Opportunity
                      WHERE Id = '{opportunityId}'";
        }

        #endregion

        #region Contacts
        /// <summary>
        /// Salesforce query to search for contacts of a particular Account
        /// </summary>
        /// <param name="name"></param>
        /// <param name="accountSalesforceID"></param>
        /// <returns>Query string to be executed</returns>
        public static string SearchContactsByAccountSalesforceID(string name, string accountSalesforceID)
        {
            string query = $@"SELECT
	            Id
            FROM Contact
            WHERE AccountId = '{accountSalesforceID}'
            AND Name LIKE '{name}'";
            return query;
        }

        /// <summary>
        /// Salesforce query to search for one contact of a particular Account
        /// Works better for Law Firms Accounts as the ORDER BY chooses the most relevant contact
        /// </summary>
        /// <param name="name"></param>
        /// <param name="accountSalesforceID"></param>
        /// <returns>Query string to be executed</returns>
        public static string SearchContactByAccountSalesforceID(string name, string accountSalesforceID)
        {
            string query = $@"SELECT
	            Id
            FROM Contact
            WHERE AccountId = '{accountSalesforceID}'
            AND Name LIKE '{name}'
            LIMIT 1";
            return query;
        }

        /// <summary>
        /// Salesforce query to search for one contact (of type legal) of a particular Account
        /// Works better for Law Firms Accounts as the ORDER BY chooses the most relevant contact
        /// </summary>
        /// <param name="name"></param>
        /// <param name="accountSalesforceID"></param>
        /// <returns>Query string to be executed</returns>
        public static string SearchContactLegalByAccountSalesforceID(string name, string accountSalesforceID)
        {
            string query = $@"SELECT
	            Id
            FROM Contact
            WHERE AccountId = '{accountSalesforceID}'
            AND Name LIKE '{name}'
            LIMIT 1";
            return query;
        }

        /// <summary>
        /// Salesforce query to retrieve a contact with a particular Salesforce ID
        /// </summary>
        /// <param name="salesforceID"></param>
        /// <returns>Query string to be executed</returns>
        public static string RetrieveContactDetailsBySalesforceID(string salesforceID)
        {
            string query = $@"SELECT
                Id,
                RecordTypeId,
                Contact_Type__c,
                AccountId,
                Name,
                Email,
                Phone,
                Date_of_Birth__c,
                Last_Activity_Date__c,
                Title
            FROM Contact
            WHERE Id = '{salesforceID}'";
            return query;
        }

        /// <summary>
        /// Salesforce query to lookup account by email
        /// </summary>
        /// <param name="email"></param>
        /// <returns></returns>
        public static string LookupContactByEmail(string email)
        {
            string query = $@"SELECT
	            Id,
                RecordTypeId,
                Contact_Type__c,
                AccountId,
                Name,
                FirstName,
                LastName,
                Email,
                Phone,
                Date_of_Birth__c,
                Last_Activity_Date__c
            FROM Contact
            WHERE Email ='{email}'";

            return query;
        }

        public static string LookupFirmContactByNameEmail(string name, string email, string firmID)
        {
            string query = $@"SELECT
	            Id,
                RecordTypeId,
                Contact_Type__c,
                AccountId,
                Name,
                FirstName,
                LastName,
                Email,
                Phone,
                Date_of_Birth__c,
                Last_Activity_Date__c
            FROM Contact
            WHERE Email ='{email}'
            AND Name = '{name}'
            AND AccountId = '{firmID}'";

            return query;
        }

        public static string LookupFirmContactByNamePhone(string name, string phone, string firmID)
        {
            string query = $@"SELECT
	            Id,
                RecordTypeId,
                Contact_Type__c,
                AccountId,
                Name,
                FirstName,
                LastName,
                Email,
                Phone,
                Date_of_Birth__c,
                Last_Activity_Date__c
            FROM Contact
            WHERE Phone ='{phone}'
            AND Name = '{name}'
            AND AccountId = '{firmID}'";

            return query;
        }

        public static string LookupContactsByEmailList(string emails)
        {
            string query = $@"SELECT
	            Id,
                RecordTypeId,
                Contact_Type__c,
                AccountId,
                Name,
                FirstName,
                LastName,
                Email,
                Phone,
                Date_of_Birth__c,
                Last_Activity_Date__c
            FROM Contact
            WHERE Email IN({emails})";

            return query;
        }
        #endregion

        #region User
        public static string GetUserById(string sfUserID)
        {
            string query = $@"SELECT
	            Name
            FROM User
            WHERE Id = '{sfUserID}'";
            return query;
        }

        public static string GetUserByContact(string sfContactID)
        {
            string query = $@"SELECT
                Salesforce_User__c
            FROM Contact
            WHERE Id = '{sfContactID}'";
            return query;
        }
        #endregion

        #region AR_Book
        public static string SearchARBook(ARBookSearchBody search)
        {
            string query = "";
            if (search.MonthSent < 10)
            {
                query = $@"SELECT
	            Id,
                Name,
                Account_Name__c,
                AR_Type__c,
                Date_Funding_Sent_Out__c,
                Month_Sent_Out__c,
                Year_Sent_Out__c
                FROM AR_Book__c
                WHERE Modifier__c = 'Portal'
                AND Account_Name__c = '{search.AccountID}'
                AND AR_Type__c = '{search.ARType}'
                AND Month_Sent_Out__c = '0{search.MonthSent}'
                AND Year_Sent_Out__c = '{search.YearSent}'";
            }
            else
            {
                query = $@"SELECT
	            Id,
                Name,
                Account_Name__c,
                AR_Type__c,
                Date_Funding_Sent_Out__c,
                Month_Sent_Out__c,
                Year_Sent_Out__c
                FROM AR_Book__c
                WHERE Modifier__c = 'Portal'
                AND Account_Name__c = '{search.AccountID}'
                AND AR_Type__c = '{search.ARType}'
                AND Month_Sent_Out__c = '{search.MonthSent}'
                AND Year_Sent_Out__c = '{search.YearSent}'";
            }

            return query;
        }

        public static string GetPlaintiffRecordId(string oppId)
        {
            return $@"SELECT
	                    Plaintiff_Record_ID__c
                    FROM Opportunity
                    WHERE Id = '{oppId}'";
        }

        /// <summary>
        /// Progressive AR Book search with fallback support
        /// </summary>
        /// <param name="filters">AR Book search filters</param>
        /// <returns>SOQL query string</returns>
        public static string SearchARBooks(ARBookFilters filters)
        {
            // Start with minimal fields that should exist in any Salesforce object
            var query = $@"SELECT
                Id,
                Name,
                CreatedDate,
                LastModifiedDate
            FROM AR_Book__c";

            var whereConditions = new List<string>();

            // Only add basic WHERE clause to ensure query works
            whereConditions.Add("Id != null");

            // Add filters only if we're confident about field existence
            // These will be enabled after schema discovery
            /*
            if (filters.Month.HasValue)
                whereConditions.Add($"Month__c = {filters.Month}");

            if (filters.Year.HasValue)
                whereConditions.Add($"Year__c = {filters.Year}");

            if (!string.IsNullOrEmpty(filters.ArType))
                whereConditions.Add($"AR_Type__c = '{filters.ArType}'");

            if (!string.IsNullOrEmpty(filters.AccountId))
                whereConditions.Add($"Account__c = '{filters.AccountId}'");

            if (!string.IsNullOrEmpty(filters.Status))
                whereConditions.Add($"Status__c = '{filters.Status}'");
            */

            if (whereConditions.Any())
                query += " WHERE " + string.Join(" AND ", whereConditions);

            query += " ORDER BY CreatedDate DESC";

            if (filters.PageSize.HasValue)
                query += $" LIMIT {filters.PageSize}";

            return query;
        }

        /// <summary>
        /// Enhanced AR Book search (use after schema discovery)
        /// </summary>
        /// <param name="filters">AR Book search filters</param>
        /// <returns>SOQL query string</returns>
        public static string SearchARBooksEnhanced(ARBookFilters filters)
        {
            // This will be enabled after we confirm field existence
            var query = $@"SELECT
                Id,
                Name,
                CreatedDate,
                LastModifiedDate,
                -- Add these fields one by one after testing:
                -- Month__c,
                -- Year__c,
                -- AR_Type__c,
                -- Account__c,
                -- Account__r.Name,
                -- Total_Amount__c,
                -- Funding_Count__c,
                -- Status__c
            FROM AR_Book__c";

            var whereConditions = new List<string>();

            // Basic filter to ensure query works
            whereConditions.Add("Id != null");

            if (whereConditions.Any())
                query += " WHERE " + string.Join(" AND ", whereConditions);

            query += " ORDER BY CreatedDate DESC";

            if (filters.PageSize.HasValue)
                query += $" LIMIT {filters.PageSize}";

            return query;
        }

        /// <summary>
        /// Get AR Book details by ID (minimal version)
        /// </summary>
        /// <param name="arBookId">AR Book Salesforce ID</param>
        /// <returns>SOQL query string</returns>
        public static string GetARBookDetails(string arBookId)
        {
            return $@"SELECT
                Id,
                Name,
                CreatedDate,
                LastModifiedDate
            FROM AR_Book__c
            WHERE Id = '{arBookId}'";
        }

        /// <summary>
        /// Get fundings associated with an AR Book (minimal version)
        /// </summary>
        /// <param name="arBookId">AR Book Salesforce ID</param>
        /// <returns>SOQL query string</returns>
        public static string GetARBookFundings(string arBookId)
        {
            return $@"SELECT
                Id,
                Name,
                CreatedDate,
                LastModifiedDate
            FROM Funding__c
            WHERE AR_Book__c = '{arBookId}'
            ORDER BY CreatedDate DESC";
        }

        /// <summary>
        /// Get recent AR Books (minimal version)
        /// </summary>
        /// <param name="limit">Number of recent books to return</param>
        /// <returns>SOQL query string</returns>
        public static string GetRecentARBooks(int limit = 10)
        {
            return $@"SELECT
                Id,
                Name,
                CreatedDate,
                LastModifiedDate
            FROM AR_Book__c
            ORDER BY LastModifiedDate DESC
            LIMIT {limit}";
        }

        /// <summary>
        /// Get distinct AR Book types (fallback version)
        /// </summary>
        /// <returns>SOQL query string</returns>
        public static string GetARBookTypes()
        {
            // Fallback: return names to extract types from
            return @"SELECT Name
                FROM AR_Book__c
                WHERE Name != null
                ORDER BY Name
                LIMIT 100";
        }
        #endregion

        private static string ConvertListIntoSOQLParameter(List<string> list)
        {
            if (list != null && list.Any())
                return $"('{string.Join("','", list)}')";
            else
                return "()";
        }
    }
}
