﻿using GainServicingAPI.Model;
using GainServicingAPI.Model.HttpRequestBodies;
using GainServicingAPI.Model.Salesforce;
using GainServicingAPI.Model.StaticValues;
using System;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Text.RegularExpressions;

namespace GainServicingAPI.Helpers
{
    public class EmailFormatter
    {
        #region Private Helper Methods

        /// <summary>
        /// Gets document types from notification, first trying DocumentTypes property, then falling back to attachments
        /// </summary>
        /// <param name="notification">The notification object</param>
        /// <returns>Comma-separated string of document types</returns>
        public static string GetDocumentTypesString(GainNotification notification)
        {
            // First try to get from DocumentTypes property
            if (notification.DocumentTypes != null && notification.DocumentTypes.Any())
            {
                return string.Join(", ", notification.DocumentTypes.ToArray());
            }

            // Fallback to attachments if DocumentTypes is null or empty
            if (notification.Attachments != null && notification.Attachments.Any())
            {
                var documentTypes = notification.Attachments
                    .Select(a => a.DocumentType)
                    .Where(dt => !string.IsNullOrEmpty(dt))
                    .ToArray();

                if (documentTypes.Any())
                {
                    return string.Join(", ", documentTypes);
                }
            }

            // Return empty string if no document types found
            return string.Empty;
        }

        #endregion

        #region GainNotifications
        public static string getOpportunityCommentBody(string portalLink, GainNotification notification, string type)
        {
            var body = $@"<p>User {notification.UserFirstName} {notification.UserLastName} from {notification.OwnerName} has added a message to the <a href='{portalLink}'>{notification.OppFirstName} {notification.OppLastName}</a> case:</p>
                            <p>{Formatter.FormatDateTimeToGainServicing(notification.CommentTimestamp)}: {notification.CommentMessage}</p>
                            <table>
                                <tr>
                                    <td align = 'left'>
                                        Client:
                                    </td>
                                    <td>
                                        {notification.OppFirstName} {notification.OppLastName}
                                    </td>
                                </tr>
                                <tr>
                                    <td align = 'left'>
                                        Case Status:
                                    </td>
                                    <td>
                                        {notification.CaseStatus}
                                    </td>
                                </tr>
                                <tr>
                                    <td align = 'left'>
                                        {type} Office:
                                    </td>
                                    <td>
                                        {notification.OwnerName}
                                    </td>
                                </tr>
                                <tr>
                                    <td align = 'left' style='padding-right:50px;'>
                                        {type} User Name:
                                    </td>
                                    <td>
                                        {notification.UserFirstName} {notification.UserLastName}
                                    </td>
                                </tr>
                                <tr>
                                    <td align = 'left'>
                                        {type} User Email:
                                    </td>
                                    <td>
                                        {notification.Username}
                                    </td>
                                </tr>
                              </table>
                              <br>
                              <br>
                              <br>
                              <p>Portal Opportunity Link:<br><br>{portalLink}</p>
                              <p>Salesforce Opportunity Link:<br><br>{notification.SalesforceLink}</p>";

            return body;

        }

        public static string getTreatmentCompleteBody(string portalLink, GainNotification notification)
        {
            var body = $@"<p>User {notification.UserFirstName} {notification.UserLastName} from {notification.OwnerName} has indicated that all documents have been uploaded for <a href='{portalLink}'>{notification.OppFirstName} {notification.OppLastName}</a></p>
                            <br>
                            <br>
                            <br>
                            <p>Portal Opportunity Link:<br><br>{portalLink}</p>
                            <p>Salesforce Opportunity Link:<br><br>{notification.SalesforceLink}</p>";

            return body;
        }

        public static string getTreatmentResumedBody(string portalLink, GainNotification notification)
        {
            var body = $@"<p>User {notification.UserFirstName} {notification.UserLastName} from {notification.OwnerName} has indicating treatment resumed for <a href='{portalLink}'>{notification.OppFirstName} {notification.OppLastName}</a></p>
                            <br>
                            <br>
                            <br>
                            <p>Portal Opportunity Link:<br><br>{portalLink}</p>
                            <p>Salesforce Opportunity Link:<br><br>{notification.SalesforceLink}</p>";

            return body;
        }

        public static string getPayoffRequestBody(string portalLink, GainNotification notification)
        {
            var body = $@"";
            var totalAmount = notification.PayoffRequest.MedicalAmount + notification.PayoffRequest.PCAAmount;

            var title = "Payoff Amount";
            if (notification.PayoffRequest.ReductionRequest.ProposedAmount > 0)
            {
                title = "Reduction";
            }

            body = $"<p>User {notification.UserFirstName} {notification.UserLastName} from {notification.OwnerName} is requesting a {title} for <a href='{portalLink}'>{notification.OppFirstName} {notification.OppLastName}</a></p>";

            if (notification.PayoffRequest.ErrorCode != null)
            {
                body += $"<p>Error:</p>" +
                        $"<table>" +
                            $"<tr>" +
                                $"<td align = 'left'>" +
                                    $"{notification.PayoffRequest.ErrorCode}" +
                                $"</td>" +
                                $"<td>" +
                                    $"{notification.PayoffRequest.ErrorMessage}" +
                                $"</td>" +
                            $"</tr>" +
                        $"</table>";
            }

            else
            {

                if (notification.PayoffRequest.ReductionRequest.ProposedAmount > 0 && notification.PayoffRequest.ReductionRequest.Notes != null)
                {
                    body += getReductionRequestBody(notification)
                        + $"<p>** Payoff Details **</p>";
                }

                // If there are white label fundings, mention and add corrected values
                if (notification.PayoffRequest.MedicalAmount != notification.PayoffRequest.ActualMedicalAmount || notification.PayoffRequest.PCAAmount != notification.PayoffRequest.ActualPCAAmount)
                {
                    body += $"<p><b><font size='+2'>WARNING: The payoff summary sent to {notification.UserFirstName} {notification.UserLastName} does not include white label fundings.</font></b></p>" +
                            $"<p><b>Payoff Summary (with White Label fundings)</b></p>" +
                            $"<table>" +
                                $"<tr>" +
                                    $"<td align = 'left' style='padding-right:50px;'>" +
                                        $"Actual PCA Amount:" +
                                    $"</td>" +
                                    $"<td>" +
                                        $"{notification.PayoffRequest.ActualPCAAmount.ToString("C", new CultureInfo("en-US"))}" +
                                    $"</td>" +
                                $"</tr>" +
                                $"<tr>" +
                                    $"<td>" +
                                        $"Actual Medical Amount:" +
                                    $"</td>" +
                                    $"<td>" +
                                        $"{notification.PayoffRequest.ActualMedicalAmount.ToString("C", new CultureInfo("en-US"))}" +
                                    $"</td>" +
                                $"</tr>" +
                                $"<tr>" +
                                    $"<td>" +
                                        $"Actual Total Amount:" +
                                    $"</td>" +
                                    $"<td>" +
                                        $"{(notification.PayoffRequest.ActualPCAAmount + notification.PayoffRequest.ActualMedicalAmount).ToString("C", new CultureInfo("en-US"))}" +
                                    $"</td>" +
                                $"</tr>" +
                            $"</table>" +
                            $"<p><b><font size='+2'>WARNING: The above payoff summary includes white label fundings and is NOT what was sent to {notification.UserFirstName} {notification.UserLastName}.</font></b></p>" +
                            $"<p><b>Payoff Summary sent to {notification.UserFirstName} {notification.UserLastName} is shown below.</b></p>";
                }

                // Always add payoff values/table
                body += $"<table>" +
                            $"<tr>" +
                                $"<td align = 'left' style='padding-right:50px;'>" +
                                    $"PCA Amount Uncapped:" +
                                $"</td>" +
                                $"<td>" +
                                    $"{notification.PayoffRequest.TotalUncappedPCA.ToString("C", new CultureInfo("en-US"))}" +
                                $"</td>" +
                            $"</tr>" +
                            $"<tr>" +
                                $"<td align = 'left' style='padding-right:50px;'>" +
                                    $"PCA State Cap:" +
                                $"</td>" +
                                $"<td>" +
                                    $"{notification.PayoffRequest.TotalStateCapPCA.ToString("C", new CultureInfo("en-US"))}" +
                                $"</td>" +
                            $"</tr>" +
                            $"<tr>" +
                                $"<td align = 'left' style='padding-right:50px;'>" +
                                    $"PCA Amount w/All Caps:" +
                                $"</td>" +
                                $"<td>" +
                                    $"{notification.PayoffRequest.PCAAmount.ToString("C", new CultureInfo("en-US"))}" +
                                $"</td>" +
                            $"</tr>" +
                            $"<tr>" +
                                $"<td>" +
                                    $"Medical Amount:" +
                                $"</td>" +
                                $"<td>" +
                                    $"{notification.PayoffRequest.MedicalAmount.ToString("C", new CultureInfo("en-US"))}" +
                                $"</td>" +
                            $"</tr>" +
                            $"<tr>" +
                                $"<td>" +
                                    $"Total Amount Uncapped:" +
                                $"</td>" +
                                $"<td>" +
                                    $"{notification.PayoffRequest.TotalUncappedAmount.ToString("C", new CultureInfo("en-US"))}" +
                                $"</td>" +
                            $"</tr>" +
                            $"<tr>" +
                                $"<td>" +
                                    $"Total Amount w/All Caps:" +
                                $"</td>" +
                                $"<td>" +
                                    $"{totalAmount.ToString("C", new CultureInfo("en-US"))}" +
                                $"</td>" +
                            $"</tr>" +
                            $"</table>" +
                            $"<p>This quote is valid for 90 days from {DateTime.Now.ToString("MM-dd-yyyy")}</p>";

                // The commented code adds payoff values/table only if the amount is > 0
                //if (notification.PayoffRequest.PCAAmount > 0 || notification.PayoffRequest.MedicalAmount > 0)
                //{
                //    body += $"<table>" +
                //                $"<tr>" +
                //                    $"<td align = 'left' style='padding-right:50px;'>" +
                //                        $"PCA Amount:" +
                //                    $"</td>" +
                //                    $"<td>" +
                //                        $"{notification.PayoffRequest.PCAAmount.ToString("C", new CultureInfo("en-US"))}" +
                //                    $"</td>" +
                //                $"</tr>" +
                //                $"<tr>" +
                //                    $"<td>" +
                //                        $"Medical Amount:" +
                //                    $"</td>" +
                //                    $"<td>" +
                //                        $"{notification.PayoffRequest.MedicalAmount.ToString("C", new CultureInfo("en-US"))}" +
                //                    $"</td>" +
                //                $"</tr>" +
                //                $"<tr>" +
                //                    $"<td>" +
                //                        $"Total Amount:" +
                //                    $"</td>" +
                //                    $"<td>" +
                //                        $"{(notification.PayoffRequest.MedicalAmount + notification.PayoffRequest.PCAAmount).ToString("C", new CultureInfo("en-US"))}" +
                //                    $"</td>" +
                //                $"</tr>" +
                //            $"</table>" +
                //            $"<p>This quote is valid for 90 days from {DateTime.Now.ToString("MM-dd-yyyy")}</p>";
                //}
            }

            body += $"<br>" +
                    $"<table>" +
                        $"<tr>" +
                            $"<td align = 'left'>" +
                                $"Client:" +
                            $"</td>" +
                            $"<td>" +
                                $"{notification.OppFirstName} {notification.OppLastName}" +
                            $"</td>" +
                        $"</tr>" +
                        $"<tr>" +
                            $"<td style='padding-right:50px;'>" +
                                $"Attorney Office:" +
                            $"</td>" +
                            $"<td>" +
                                $"{notification.OwnerName}" +
                            $"</td>" +
                        $"</tr>" +
                        $"<tr>" +
                            $"<td>" +
                                $"Requestor Name:" +
                            $"</td>" +
                            $"<td>" +
                                $"{notification.UserFirstName} {notification.UserLastName}" +
                            $"</td>" +
                        $"</tr>" +
                        $"<tr>" +
                            $"<td>" +
                                $"Requestor Email:" +
                            $"</td>" +
                            $"<td>" +
                                $"{notification.Username}" +
                            $"</td>" +
                        $"</tr>" +
                    $"</table>" +
                    $"<br>" +
                    $"<p>Portal Opportunity Link:<br><br>{portalLink}</p>" +
                    $"<p>Salesforce Opportunity Link:<br><br>{notification.SalesforceLink}</p>";

            return body;
        }

        public static string getPatientIntakeBody(string portalLink, GainNotification notification)
        {
            var body = $"User {notification.UserFirstName} {notification.UserLastName} from {notification.OwnerName} has added a patient.";
            body += $"<br>"
                  + $"<table>" +
                        $"<tr>" +
                            $"<td align = 'left' style='padding-right:50px;'>" +
                                $"Patient:" +
                            $"</td>" +
                            $"<td>" +
                                $"<a href='{portalLink}'>{notification.OppFirstName} {notification.OppLastName}</a>" +
                            $"</td>" +
                        $"</tr>" +
                        $"<tr>" +
                            $"<td>" +
                                $"DOB:" +
                            $"</td>" +
                            $"<td>" +
                                $"{notification.OppDateOfBirth}" +
                            $"</td>" +
                        $"</tr>" +
                        $"<tr>" +
                            $"<td>" +
                                $"Provider:" +
                            $"</td>" +
                            $"<td>" +
                                $"{notification.OwnerName}" +
                            $"</td>" +
                        $"</tr>" +
                        $"<tr>" +
                            $"<td>" +
                                $"Provider User:" +
                            $"</td>" +
                            $"<td>" +
                                $"{notification.Username}" +
                            $"</td>" +
                        $"</tr>" +
                    $"</table>" +
                    $"<br>" +
                    $"<p>Portal Opportunity Link:<br><br>{portalLink}</p>" +
                    $"<p>Salesforce Opportunity Link:<br><br>{notification.SalesforceLink}</p>";

            return body;
        }

        public static string getClientIntakeBody(string portalLink, GainNotification notification)
        {
            var body = $"User {notification.UserFirstName} {notification.UserLastName} from {notification.OwnerName} has added a client.";
            body += $"<br>"
                  + $"<table>" +
                        $"<tr>" +
                            $"<td align = 'left' style='padding-right:50px;'>" +
                                $"Patient:" +
                            $"</td>" +
                            $"<td>" +
                                $"<a href='{portalLink}'>{notification.OppFirstName} {notification.OppLastName}</a>" +
                            $"</td>" +
                        $"</tr>" +
                        $"<tr>" +
                            $"<td>" +
                                $"DOB:" +
                            $"</td>" +
                            $"<td>" +
                                $"{notification.OppDateOfBirth}" +
                            $"</td>" +
                        $"</tr>" +
                        $"<tr>" +
                            $"<td>" +
                                $"Law Firm:" +
                            $"</td>" +
                            $"<td>" +
                                $"{notification.OwnerName}" +
                            $"</td>" +
                        $"</tr>" +
                        $"<tr>" +
                            $"<td>" +
                                $"Attorney/Paralegal:" +
                            $"</td>" +
                            $"<td>" +
                                $"{notification.Username}" +
                            $"</td>" +
                        $"</tr>" +
                    $"</table>" +
                    $"<br>" +
                    $"<p>Portal Opportunity Link:<br><br>{portalLink}</p>" +
                    $"<p>Salesforce Opportunity Link:<br><br>{notification.SalesforceLink}</p>";

            return body;
        }

        public static string GetAttorneyAddAttorneyBody(string portalLink, GainNotification notification)
        {
            var body = $"User {notification.UserFirstName} {notification.UserLastName} from {notification.OwnerName} has added an attorney.";

            body += $"<br><table>" +
                            $"<tr>" +
                                $"<td align = 'left' style='padding-right:50px;'>" +
                                    $"Client:" +
                                $"</td>" +
                                $"<td>" +
                                    $"<a href='{portalLink}'>{notification.OppFirstName} {notification.OppLastName}</a>" +
                                $"</td>" +
                            $"</tr>" +
                            $"<tr>" +
                                $"<td>" +
                                    $"Law Firm:" +
                                $"</td>" +
                                $"<td>" +
                                    $"{notification.FirmName}" +
                                $"</td>" +
                            $"</tr>" +
                            $"<tr>" +
                                $"<td>" +
                                    $"User:" +
                                $"</td>" +
                                $"<td>" +
                                    $"{notification.Username}" +
                                $"</td>" +
                            $"</tr>" +
                        $"</table>" +
                        $"<br>" +
                        $"<p>Portal Opportunity Link:<br><br>{portalLink}</p>" +
                        $"<p>Salesforce Opportunity Link:<br><br>{notification.SalesforceLink}</p>";

            return body;
        }

        public static string GetProviderAddAttorneyBody(string portalLink, GainNotification notification)
        {
            var body = $"User {notification.UserFirstName} {notification.UserLastName} from {notification.OwnerName} has added an attorney.";

            body += $"<br><table>" +
                            $"<tr>" +
                                $"<td align = 'left' style='padding-right:50px;'>" +
                                    $"Patient:" +
                                $"</td>" +
                                $"<td>" +
                                    $"<a href='{portalLink}'>{notification.OppFirstName} {notification.OppLastName}</a>" +
                                $"</td>" +
                            $"</tr>" +
                            $"<tr>" +
                                $"<td>" +
                                    $"Law Firm:" +
                                $"</td>" +
                                $"<td>" +
                                    $"{notification.FirmName}" +
                                $"</td>" +
                            $"</tr>" +
                            $"<tr>" +
                                $"<td>" +
                                    $"Provider:" +
                                $"</td>" +
                                $"<td>" +
                                    $"{notification.OwnerName}" +
                                $"</td>" +
                            $"</tr>" +
                            $"<tr>" +
                                $"<td>" +
                                    $"User:" +
                                $"</td>" +
                                $"<td>" +
                                    $"{notification.Username}" +
                                $"</td>" +
                            $"</tr>" +
                        $"</table>" +
                        $"<br>" +
                        $"<p>Portal Opportunity Link:<br><br>{portalLink}</p>" +
                        $"<p>Salesforce Opportunity Link:<br><br>{notification.SalesforceLink}</p>";

            return body;
        }

        public static string getAttorneyPCA(string portalLink, GainNotification notification)
        {
            var body = $@"<p>User {notification.UserFirstName} {notification.UserLastName} from {notification.OwnerName} is requesting a PCA for client <a href='{portalLink}'>{notification.OppFirstName} {notification.OppLastName}</a></p>";

            if (notification.ContactAttorney)
            {
                body += $"<p>** ATTORNEY REQUESTS DISCUSSION BEFORE FUNDING **</p>";
            }

            body += $"<table>" +
                            $"<tr>" +
                                $"<td align = 'left' style='padding-right:50px;'>" +
                                    $"Client:" +
                                $"</td>" +
                                $"<td>" +
                                    $"<a href='{portalLink}'>{notification.OppFirstName} {notification.OppLastName}</a>" +
                                $"</td>" +
                            $"</tr>" +
                            $"<tr>" +
                                $"<td>" +
                                    $"Attorney Office:" +
                                $"</td>" +
                                $"<td>" +
                                    $"{notification.OwnerName}" +
                                $"</td>" +
                            $"</tr>" +
                            $"<tr>" +
                                $"<td>" +
                                    $"Requestor Name:" +
                                $"</td>" +
                                $"<td>" +
                                    $"{notification.UserFirstName} {notification.UserLastName}" +
                                $"</td>" +
                            $"</tr>" +
                            $"<tr>" +
                                $"<td>" +
                                    $"Requestor Email:" +
                                $"</td>" +
                                $"<td>" +
                                    $"{notification.Username}" +
                                $"</td>" +
                            $"</tr>" +
                            $"<tr></tr>" +
                            $"<tr>" +
                                $"<td>" +
                                    $"Funding Type:" +
                                $"</td>" +
                                $"<td>" +
                                    $"PCA" +
                                $"</td>" +
                            $"</tr>" +
                            $"<tr>" +
                                $"<td>" +
                                    $"Amount:" +
                                $"</td>" +
                                $"<td>" +
                                    $"{notification.CashAdvanceAmount}" +
                                $"</td>" +
                            $"</tr>" +
                        $"</table>" +
                        $"<br>" +
                        $"<p>Portal Opportunity Link:<br><br>{portalLink}</p>" +
                        $"<p>Salesforce Opportunity Link:<br><br>{notification.SalesforceLink}</p>";

            return body;
        }

        public static string getAddAttorneyPCA(string portalLink, GainNotification notification)
        {
            var body = $@"<p>User {notification.UserFirstName} {notification.UserLastName} from {notification.OwnerName} is requesting a PCA for client <a href='{portalLink}'>{notification.OppFirstName} {notification.OppLastName}</a></p>";

            if (notification.ContactAttorney)
            {
                body += $"<p>** ATTORNEY REQUESTS DISCUSSION BEFORE FUNDING **</p>";
            }

            body += $"<table>" +
                            $"<tr>" +
                                $"<td align = 'left' style='padding-right:50px;'>" +
                                    $"Client:" +
                                $"</td>" +
                                $"<td>" +
                                    $"<a href='{portalLink}'>{notification.OppFirstName} {notification.OppLastName}</a>" +
                                $"</td>" +
                            $"</tr>" +
                            $"<tr>" +
                                $"<td>" +
                                    $"Attorney Office:" +
                                $"</td>" +
                                $"<td>" +
                                    $"{notification.OwnerName}" +
                                $"</td>" +
                            $"</tr>" +
                            $"<tr>" +
                                $"<td>" +
                                    $"Requestor Name:" +
                                $"</td>" +
                                $"<td>" +
                                    $"{notification.UserFirstName} {notification.UserLastName}" +
                                $"</td>" +
                            $"</tr>" +
                            $"<tr>" +
                                $"<td>" +
                                    $"Requestor Email:" +
                                $"</td>" +
                                $"<td>" +
                                    $"{notification.Username}" +
                                $"</td>" +
                            $"</tr>" +
                            $"<tr></tr>" +
                            $"<tr>" +
                                $"<td>" +
                                    $"Funding Type:" +
                                $"</td>" +
                                $"<td>" +
                                    $"PCA" +
                                $"</td>" +
                            $"</tr>" +
                            $"<tr>" +
                                $"<td>" +
                                    $"Amount:" +
                                $"</td>" +
                                $"<td>" +
                                    $"{notification.CashAdvanceAmount}" +
                                $"</td>" +
                            $"</tr>" +
                        $"</table>" +
                        $"<br>" +
                        $"<p>Portal Opportunity Link:<br><br>{portalLink}</p>" +
                        $"<p>Salesforce Opportunity Link:<br><br>{notification.SalesforceLink}</p>";

            return body;
        }

        public static string getAttorneyMedicalFundingBody(string portalLink, GainNotification notification)
        {
            var body = $@"<p>User {notification.UserFirstName} {notification.UserLastName} from {notification.OwnerName} is requesting a Medical Treatment for client <a href='{portalLink}'>{notification.OppFirstName} {notification.OppLastName}</a></p>";

            body += $"<table>" +
                $"<tr>" +
                    $"<td align = 'left' style='padding-right:50px;'>" +
                        $"Client:" +
                    $"</td>" +
                    $"<td>" +
                        $"<a href='{portalLink}'>{notification.OppFirstName} {notification.OppLastName}</a>" +
                    $"</td>" +
                $"</tr>" +
                $"<tr>" +
                    $"<td>" +
                        $"Attorney Office:" +
                    $"</td>" +
                    $"<td>" +
                        $"{notification.OwnerName}" +
                    $"</td>" +
                $"</tr>" +
                $"<tr>" +
                    $"<td>" +
                        $"Requestor Name:" +
                    $"</td>" +
                    $"<td>" +
                        $"{notification.UserFirstName} {notification.UserLastName}" +
                    $"</td>" +
                $"</tr>" +
                $"<tr>" +
                    $"<td>" +
                        $"Requestor Email:" +
                    $"</td>" +
                    $"<td>" +
                        $"{notification.Username}" +
                    $"</td>" +
                $"</tr>" +
                $"<tr></tr>" +
                $"<tr>" +
                    $"<td>" +
                        $"Funding Type:" +
                    $"</td>" +
                    $"<td>" +
                        $"Medical" +
                    $"</td>" +
                $"</tr>" +
                $"<tr>" +
                    $"<td>" +
                        $"Specialty:" +
                    $"</td>" +
                    $"<td>" +
                        $"{notification.SelectedProvider.ProviderSpecialty ?? "None"}" +
                    $"</td>" +
                $"</tr>" +
                $"<tr>" +
                    $"<td>" +
                        $"Comments:" +
                    $"</td>" +
                    $"<td>" +
                        $"{notification.SelectedProvider.AdditionalComments ?? "None"}" +
                    $"</td>" +
                $"</tr>" +
            $"</table>" +
            $"<br>" +
            $"<p>Portal Opportunity Link:<br><br>{portalLink}</p>" +
            $"<p>Salesforce Opportunity Link:<br><br>{notification.SalesforceLink}</p>";

            return body;
        }

        public static string getRequestDocumentsBody(string portalLink, GainNotification notification)
        {
            var body = $@"<p>User {notification.UserFirstName} {notification.UserLastName} from {notification.OwnerName} is requesting Documents for <a href='{portalLink}'>{notification.OppFirstName} {notification.OppLastName}</a></p>";

            body += $"<table>" +
                        $"<tr>" +
                            $"<td align = 'left' style='padding-right:50px;'>" +
                                $"Documents Requested:" +
                            $"</td>" +
                            $"<td>" +
                                $"{GetDocumentTypesString(notification)}" +
                            $"</td>" +
                        $"</tr>";

            if (!string.IsNullOrEmpty(notification.Notes))
            {
                notification.Notes = Regex.Replace(notification.Notes, "(.{" + 80 + "})", "$1" + Environment.NewLine + "\t");
                body += $"<tr>" +
                            $"<td>" +
                                $"Note:" +
                            $"</td>" +
                            $"<td>" +
                                $"{notification.Notes}" +
                            $"</td>" +
                        $"</tr>";
            }

            if (notification.UserType == "Attorney")
            {
                body += $"<tr>" +
                            $"<td>" +
                                $"Client:" +
                            $"</td>" +
                            $"<td>" +
                                $"<a href='{portalLink}'>{notification.OppFirstName} {notification.OppLastName}</a>" +
                            $"</td>" +
                        $"</tr>" +
                        $"<tr>" +
                            $"<td>" +
                                $"Attorney Office:" +
                            $"</td>" +
                            $"<td>" +
                                $"{notification.OwnerName}" +
                            $"</td>" +
                        $"</tr>";
            }

            if (notification.UserType == "Provider")
            {
                body += $"<tr>" +
                            $"<td>" +
                                $"Patient:" +
                            $"</td>" +
                            $"<td>" +
                                $"<a href='{portalLink}'>{notification.OppFirstName} {notification.OppLastName}</a>" +
                            $"</td>" +
                        $"</tr>" +
                        $"<tr>" +
                            $"<td>" +
                                $"Provider Office:" +
                            $"</td>" +
                            $"<td>" +
                                $"{notification.OwnerName}" +
                            $"</td>" +
                        $"</tr>";
            }

            body += $"<tr>" +
                        $"<td>" +
                            $"Requestor Name:" +
                        $"</td>" +
                        $"<td>" +
                            $"{notification.UserFirstName} {notification.UserLastName}" +
                        $"</td>" +
                    $"</tr>" +
                    $"<tr></tr>" +
                    $"<tr>" +
                        $"<td>" +
                            $"Requestor Email:" +
                        $"</td>" +
                        $"<td>" +
                            $"{notification.Username}" +
                        $"</td>" +
                    $"</tr>" +
                $"</table>" +
                $"<br>" +
                $"<p>Portal Opportunity Link:<br><br>{portalLink}</p>" +
                $"<p>Salesforce Opportunity Link:<br><br>{notification.SalesforceLink}</p>";

            return body;
        }

        public static string getNoLongerRepresentBody(string portalLink, GainNotification notification)
        {
            var body = $@"<p>User {notification.UserFirstName} {notification.UserLastName} from {notification.OwnerName} has updated <a href='{portalLink}'>{notification.OppFirstName} {notification.OppLastName}</a> status to No Longer Represent</p>";

            body += $"<table>";
            if (!string.IsNullOrEmpty(notification.Notes))
            {
                notification.Notes = Regex.Replace(notification.Notes, "(.{" + 80 + "})", "$1" + Environment.NewLine + "\t");
                body += $"<tr>" +
                            $"<td align = 'left' style='padding-right:50px;'>" +
                                $"Client is now represented by:" +
                            $"</td>" +
                            $"<td>" +
                                $"{notification.Notes}" +
                            $"</td>" +
                        $"</tr>";
            }

            body += $"<tr>" +
                        $"<td align = 'left' style='padding-right:50px;'>" +
                            $"Client:" +
                        $"</td>" +
                        $"<td>" +
                            $"<a href='{portalLink}'>{notification.OppFirstName} {notification.OppLastName}</a>" +
                        $"</td>" +
                    $"</tr>" +
                    $"<tr>" +
                        $"<td>" +
                            $"Attorney Office:" +
                        $"</td>" +
                        $"<td>" +
                            $"{notification.OwnerName}" +
                        $"</td>" +
                    $"</tr>" +
                    $"<tr>" +
                        $"<td>" +
                            $"Attorney User Name:" +
                        $"</td>" +
                        $"<td>" +
                            $"{notification.UserFirstName} {notification.UserLastName}" +
                        $"</td>" +
                    $"</tr>" +
                    $"<tr>" +
                        $"<td>" +
                            $"Attorney User Email:" +
                        $"</td>" +
                        $"<td>" +
                            $"{notification.Username}" +
                        $"</td>" +
                    $"</tr>" +
                $"</table>" +
                $"<br>" +
                $"<p>Portal Opportunity Link:<br><br>{portalLink}</p>" +
                $"<p>Salesforce Opportunity Link:<br><br>{notification.SalesforceLink}</p>";

            return body;
        }

        public static string getAttorneyRepresentBody(string portalLink, GainNotification notification)
        {
            var body = $@"<p>User {notification.UserFirstName} {notification.UserLastName} from {notification.OwnerName} has updated <a href='{portalLink}'>{notification.OppFirstName} {notification.OppLastName}</a> status to {notification.CaseStatus}</p>";

            body += $"<table>";
            if (!string.IsNullOrEmpty(notification.Notes))
            {
                notification.Notes = Regex.Replace(notification.Notes, "(.{" + 80 + "})", "$1" + Environment.NewLine + "\t");
                body += $"<tr>" +
                            $"<td align = 'left' style='padding-right:50px;'>" +
                                $"Client previously represented by:" +
                            $"</td>" +
                            $"<td>" +
                                $"{notification.Notes}" +
                            $"</td>" +
                        $"</tr>";
            }

            body += $"<tr>" +
                        $"<td align = 'left' style='padding-right:50px;'>" +
                            $"Old Status:" +
                        $"</td>" +
                        $"<td>" +
                            $"No Longer Represent" +
                        $"</td>" +
                    $"</tr>" +
                    $"<tr>" +
                        $"<td>" +
                            $"New Status:" +
                        $"</td>" +
                        $"<td>" +
                            $"{notification.CaseStatus}" +
                        $"</td>" +
                    $"</tr>" +
                    $"<tr></tr>";

            body += $"<tr>" +
                        $"<td align = 'left' style='padding-right:50px;'>" +
                            $"Client:" +
                        $"</td>" +
                        $"<td>" +
                            $"<a href='{portalLink}'>{notification.OppFirstName} {notification.OppLastName}</a>" +
                        $"</td>" +
                    $"</tr>" +
                    $"<tr>" +
                        $"<td>" +
                            $"Attorney Office:" +
                        $"</td>" +
                        $"<td>" +
                            $"{notification.OwnerName}" +
                        $"</td>" +
                    $"</tr>" +
                    $"<tr>" +
                        $"<td>" +
                            $"Attorney User Name:" +
                        $"</td>" +
                        $"<td>" +
                            $"{notification.UserFirstName} {notification.UserLastName}" +
                        $"</td>" +
                    $"</tr>" +
                    $"<tr>" +
                        $"<td>" +
                            $"Attorney User Email:" +
                        $"</td>" +
                        $"<td>" +
                            $"{notification.Username}" +
                        $"</td>" +
                    $"</tr>" +
                $"</table>" +
                $"<br>" +
                $"<p>Portal Opportunity Link:<br><br>{portalLink}</p>" +
                $"<p>Salesforce Opportunity Link:<br><br>{notification.SalesforceLink}</p>";

            return body;
        }

        public static string getCaseClosedBody(string portalLink, GainNotification notification)
        {
            var body = $@"<p>User {notification.UserFirstName} {notification.UserLastName} from {notification.OwnerName} has updated <a href='{portalLink}'>{notification.OppFirstName} {notification.OppLastName}</a> status to {notification.CaseStatus}</p>";

            body += $"<table>";
            if (!string.IsNullOrEmpty(notification.Notes))
            {
                notification.Notes = Regex.Replace(notification.Notes, "(.{" + 80 + "})", "$1" + Environment.NewLine + "\t");
                body += $"<tr>" +
                            $"<td align = 'left' style='padding-right:50px;'>" +
                                $"Payment was dispersed to:" +
                            $"</td>" +
                            $"<td>" +
                                $"{notification.Notes}" +
                            $"</td>" +
                        $"</tr>";
            }

            body += $"<tr>" +
                        $"<td>" +
                            $"New Status:" +
                        $"</td>" +
                        $"<td>" +
                            $"{notification.CaseStatus}" +
                        $"</td>" +
                    $"</tr>" +
                    $"<tr></tr>";

            body += $"<tr>" +
                        $"<td align = 'left' style='padding-right:50px;'>" +
                            $"Client:" +
                        $"</td>" +
                        $"<td>" +
                            $"<a href='{portalLink}'>{notification.OppFirstName} {notification.OppLastName}</a>" +
                        $"</td>" +
                    $"</tr>" +
                    $"<tr>" +
                        $"<td>" +
                            $"Attorney Office:" +
                        $"</td>" +
                        $"<td>" +
                            $"{notification.OwnerName}" +
                        $"</td>" +
                    $"</tr>" +
                    $"<tr>" +
                        $"<td>" +
                            $"Attorney User Name:" +
                        $"</td>" +
                        $"<td>" +
                            $"{notification.UserFirstName} {notification.UserLastName}" +
                        $"</td>" +
                    $"</tr>" +
                    $"<tr>" +
                        $"<td>" +
                            $"Attorney User Email:" +
                        $"</td>" +
                        $"<td>" +
                            $"{notification.Username}" +
                        $"</td>" +
                    $"</tr>" +
                $"</table>" +
                $"<br>" +
                $"<p>Portal Opportunity Link:<br><br>{portalLink}</p>" +
                $"<p>Salesforce Opportunity Link:<br><br>{notification.SalesforceLink}</p>";

            return body;
        }

        public static string getHCFAParseSuccessBody(ParseNotification claimNotification)
        {
            var body = $@"<p>User {claimNotification.UserFirstName} {claimNotification.UserLastName} successfully parsed a Claim for {claimNotification.OppName}.</p>";

            body += "<p><b>Please verify that the funding(s) created from the linked Claims file are correct.</b></p>";

            body += $"<p>Salesforce Opportunity Link:<br><br>{claimNotification.SalesforceLink}</p>" + $"<p>Salesforce Attachment Link:<br><br>{claimNotification.AttachmentLink}</p>";

            body += $"<p>Salesforce Funding Link(s):<br></p>";
            foreach (string link in claimNotification.FundingLinks)
            {
                body += $"<p>{link}<br></p>";
            }

            return body;
        }

        public static string getHCFAParseErrorBody(ParseNotification claimNotification)
        {
            var body = $@"<p>User {claimNotification.UserFirstName} {claimNotification.UserLastName} uploaded a Claim for {claimNotification.OppName} that failed to parse.</p>";

            body += "<p><b>Please verify the created funding and create any additional required fundings for the linked Claims file.</b></p>";

            body += $"<p>Salesforce Opportunity Link:<br><br>{claimNotification.SalesforceLink}</p>" + $"<p>Salesforce Attachment Link:<br><br>{claimNotification.AttachmentLink}</p>";

            body += $"<p>Salesforce Funding Link(s):</p>";
            foreach (string link in claimNotification.FundingLinks)
            {
                body += $"<p>{link}<br></p>";
            }

            return body;
        }

        public static string getClientIntakeParseSuccessBody(ParseNotification clientNotification)
        {
            var body = $@"<p>User {clientNotification.UserFirstName} {clientNotification.UserLastName} successfully parsed a Client Intake for {clientNotification.OppName}.</p>";

            body += "<p><b>Please verify the opportunity intake was correct, using Opportunity Link and Attachment Link.</b></p>";

            body += $"<p>Salesforce Opportunity Link:<br><br>{clientNotification.SalesforceLink}</p>" + $"<p>Salesforce Attachment Link:<br><br>{clientNotification.AttachmentLink}</p>";

            return body;
        }

        public static string getClientIntakeParseErrorBody(ParseNotification clientNotification)
        {
            var body = $@"<p>User {clientNotification.UserFirstName} {clientNotification.UserLastName} uploaded a Client Intake for {clientNotification.OppName} that failed to parse.</p>";

            body += "<p><b>Please verify the opportunity intake was correct, using Opportunity Link and Attachment Link.</b></p>";

            body += $"<p>Salesforce Opportunity Link:<br><br>{clientNotification.SalesforceLink}</p>" + $"<p>Salesforce Attachment Link:<br><br>{clientNotification.AttachmentLink}</p>";

            return body;
        }

        public static string getPcaIntakeValidationError(string opportunityName, string stage, User user)
        {
            var body = $@"<p>Client Intake validation failed. No open opportunity.</p>";

            body += $"<table>" +
                        $"<tr>" +
                            $"<td align = 'left' style='padding-right:50px;'>" +
                                $"Closed Opportuinty:" +
                            $"</td>" +
                            $"<td>" +
                                $"{opportunityName}" +
                            $"</td>" +
                        $"</tr>" +
                        $"<tr>" +
                            $"<td align = 'left' style='padding-right:50px;'>" +
                                $"Stage:" +
                            $"</td>" +
                            $"<td>" +
                                $"{stage}" +
                            $"</td>" +
                        $"</tr>" +
                        $"<tr>" +
                            $"<td align = 'left' style='padding-right:50px;'>" +
                                $"Requestor:" +
                            $"</td>" +
                            $"<td>" +
                                $"{user.Firstname} {user.Lastname}" +
                            $"</td>" +
                        $"</tr>" +
                    $"</table>";

            return body;
        }

        public static string getIntakeValidationError(string opportunityName, string type, User user)
        {
            var body = $@"<p>Something went wrong in the intake process.</p>";

            body += $"<table>" +
                        $"<tr>" +
                            $"<td align = 'left' style='padding-right:50px;'>" +
                                $"Plaintiff:" +
                            $"</td>" +
                            $"<td>" +
                                $"{opportunityName}" +
                            $"</td>" +
                        $"</tr>" +
                        $"<tr>" +
                            $"<td align = 'left' style='padding-right:50px;'>" +
                                $"Type:" +
                            $"</td>" +
                            $"<td>" +
                                $"{type}" +
                            $"</td>" +
                        $"</tr>" +
                        $"<tr>" +
                            $"<td align = 'left' style='padding-right:50px;'>" +
                                $"Requestor:" +
                            $"</td>" +
                            $"<td>" +
                                $"{user.Firstname} {user.Lastname}" +
                            $"</td>" +
                        $"</tr>" +
                    $"</table>";

            return body;
        }

        public static string getLawfirmChangedBody(string portalLink, GainNotification notification)
        {
            string[] splitFirms = notification.FirmName.Split("|");

            var body = $@"User {notification.UserFirstName} {notification.UserLastName} from {notification.OwnerName} has changed The Law Firm assigned to patient <a href='{portalLink}'>{notification.OppFirstName} {notification.OppLastName}</a>";

            body += $"<tr>" +
                        $"<td align = 'left' style='padding-right:50px;'>" +
                            $"New Law Firm:" +
                        $"</td>" +
                        $"<td>" +
                            $"{splitFirms[1]}" +
                        $"</td>" +
                    $"</tr>" +
                    $"<tr>" +
                        $"<td>" +
                            $"Previous Law Firm:" +
                        $"</td>" +
                        $"<td>" +
                            $"{splitFirms[0]}" +
                        $"</td>" +
                    $"</tr>" +
                    $"<tr>" +
                        $"<td>" +
                            $"Patient:" +
                        $"</td>" +
                        $"<td>" +
                            $"<a href='{portalLink}'>{notification.OppFirstName} {notification.OppLastName}</a>" +
                        $"</td>" +
                    $"</tr>" +
                    $"<tr>" +
                        $"<td>" +
                            $"Provider Office:" +
                        $"</td>" +
                        $"<td>" +
                            $"{notification.OwnerName}" +
                        $"</td>" +
                    $"</tr>" +
                    $"<tr>" +
                        $"<td>" +
                            $"User Name:" +
                        $"</td>" +
                        $"<td>" +
                            $"{notification.UserFirstName} {notification.UserLastName}" +
                        $"</td>" +
                    $"</tr>" +
                    $"<tr>" +
                        $"<td>" +
                            $"User Email:" +
                        $"</td>" +
                        $"<td>" +
                            $"{notification.Username}" +
                        $"</td>" +
                    $"</tr>" +
                $"</table>" +
                $"<br>" +
                $"<p>Portal Opportunity Link:<br><br>{portalLink}</p>" +
                $"<p>Salesforce Opportunity Link:<br><br>{notification.SalesforceLink}</p>";

            return body;
        }

        public static string getFileUploadBody(string portalLink, GainNotification notification)
        {
            var attachmentsPortalLink = $"{portalLink}#patient-attachments";
            var body = $"User {notification.UserFirstName} {notification.UserLastName} from {notification.OwnerName} uploaded a file.";
            body += $"<br><table>";

            var opportunityType = "Client";

            if (notification.UserType == "Provider")
            {
                opportunityType = "Patient";
            }

            body += $"<tr>" +
                        $"<td align = 'left' style='padding-right:75px;'>" +
                            $"{opportunityType}:" +
                        $"</td>" +
                        $"<td>" +
                            $"<a href='{portalLink}'>{notification.OppFirstName} {notification.OppLastName}</a>" +
                        $"</td>" +
                    $"</tr>" +
                    $"<tr>" +
                        $"<td>" +
                            $"File Type:" +
                        $"</td>" +
                        $"<td>" +
                            $"{GetDocumentTypesString(notification)}" +
                        $"</td>" +
                    $"</tr>";

            if (notification.Attachments != null)
            {
                foreach (var a in notification.Attachments)
                {
                    string attachmentLinks = $"<a href='{attachmentsPortalLink}'>{a.Title}</a>";
                    body += $"<tr>" +
                                $"<td>" +
                                    $"File Name:" +
                                $"</td>" +
                                $"<td>" +
                                    $"<a href='{attachmentsPortalLink}'>{attachmentLinks}</a>" +
                                $"</td>" +
                            $"</tr>";
                }
            }

            body += $"<tr></tr>" +
                    $"<tr>" +
                        $"<td>" +
                            $"{notification.UserType} Office:" +
                        $"</td>" +
                        $"<td>" +
                            $"{notification.OwnerName}" +
                        $"</td>" +
                    $"</tr>";

            if (!String.IsNullOrEmpty(notification.CashAdvanceAmount))
            {
                body += $"<tr>" +
                        $"<td>" +
                            $"Treatment Amount:" +
                        $"</td>" +
                        $"<td>" +
                            $"{notification.CashAdvanceAmount}" +
                        $"</td>";
            }

            body += $"<tr>" +
                        $"<td>" +
                            $"User Name:" +
                        $"</td>" +
                        $"<td>" +
                            $"{notification.UserFirstName} {notification.UserLastName}" +
                        $"</td>" +
                    $"</tr>" +
                    $"<tr></tr>" +
                    $"<tr>" +
                        $"<td>" +
                            $"User Email:" +
                        $"</td>" +
                        $"<td>" +
                            $"{notification.Username}" +
                        $"</td>" +
                    $"</tr>" +
                $"</table>" +
                $"<br>" +
                $"<p>Portal Opportunity Link:<br><br>{portalLink}</p>" +
                $"<p>Salesforce Opportunity Link:<br><br>{notification.SalesforceLink}</p>";

            return body;
        }

        public static string getProviderMapMedicalFundingBody(string portalLink, GainNotification notification)
        {
            var body = $@"<p>User {notification.UserFirstName} {notification.UserLastName} from {notification.OwnerName} is requesting a Medical Treatment for client <a href='{portalLink}'>{notification.OppFirstName} {notification.OppLastName}</a></p>";

            body += $"<table>" +
                $"<tr>" +
                    $"<td align = 'left' style='padding-right:50px;'>" +
                        $"Client:" +
                    $"</td>" +
                    $"<td>" +
                        $"<a href='{portalLink}'>{notification.OppFirstName} {notification.OppLastName}</a>" +
                    $"</td>" +
                $"</tr>" +
                $"<tr>" +
                    $"<td>" +
                        $"Attorney Office:" +
                    $"</td>" +
                    $"<td>" +
                        $"{notification.OwnerName}" +
                    $"</td>" +
                $"</tr>" +
                $"<tr>" +
                    $"<td>" +
                        $"Requestor Name:" +
                    $"</td>" +
                    $"<td>" +
                        $"{notification.UserFirstName} {notification.UserLastName}" +
                    $"</td>" +
                $"</tr>" +
                $"<tr>" +
                    $"<td>" +
                        $"Requestor Email:" +
                    $"</td>" +
                    $"<td>" +
                        $"{notification.Username}" +
                    $"</td>" +
                $"</tr>" +
                $"<tr></tr>" +
                $"<tr>" +
                    $"<td>" +
                        $"Funding Type:" +
                    $"</td>" +
                    $"<td>" +
                        $"Medical" +
                    $"</td>" +
                $"</tr>" +
                $"<tr>" +
                    $"<td>" +
                        $"Provider Name:" +
                    $"</td>" +
                    $"<td>" +
                        $"{notification.SelectedProvider.ProviderName}" +
                    $"</td>" +
                $"</tr>" +
                $"<tr>" +
                    $"<td>" +
                        $"Provider Specialty:" +
                    $"</td>" +
                    $"<td>" +
                        $"{notification.SelectedProvider.ProviderSpecialty}" +
                    $"</td>" +
                $"</tr>" +
                $"<tr>" +
                    $"<td>" +
                        $"Provider Location:" +
                    $"</td>" +
                    $"<td>" +
                        $"{notification.SelectedProvider.ProviderLocation}" +
                    $"</td>" +
                $"</tr>";

            if (!string.IsNullOrEmpty(notification.SelectedProvider.AdditionalComments))
            {
                body += $"<tr>" +
                            $"<td>" +
                                $"Additional Comments:" +
                            $"</td>" +
                            $"<td>" +
                                $"{notification.SelectedProvider.AdditionalComments}" +
                            $"</td>" +
                        $"</tr>";
            }

            body += $"</table>" +
            $"<br>" +
            $"<p>Portal Opportunity Link:<br><br>{portalLink}</p>" +
            $"<p>Salesforce Opportunity Link:<br><br>{notification.SalesforceLink}</p>";

            return body;
        }

        public static string getRequestTransportationBody(string portalLink, GainNotification notification)
        {
            var body = $@"<p>User {notification.UserFirstName} {notification.UserLastName} from {notification.OwnerName} is requesting medical transportation for client <a href='{portalLink}'>{notification.OppFirstName} {notification.OppLastName}</a></p>";

            body += $"<table>" +
                $"<tr>" +
                    $"<td align = 'left' style='padding-right:50px;'>" +
                        $"Client Name:" +
                    $"</td>" +
                    $"<td>" +
                        $"<a href='{portalLink}'>{notification.OppFirstName} {notification.OppLastName}</a>" +
                    $"</td>" +
                $"</tr>" +
                $"<tr>" +
                    $"<td>" +
                        $"Client Email:" +
                    $"</td>" +
                    $"<td>" +
                        $"{notification.RequestTransportation.Email}" +
                    $"</td>" +
                $"</tr>" +
                $"<tr>" +
                $"<tr>" +
                    $"<td>" +
                        $"Client Phone:" +
                    $"</td>" +
                    $"<td>" +
                        $"{notification.RequestTransportation.Phone}" +
                    $"</td>" +
                $"</tr>" +
                $"<tr>" +
                    $"<td>" +
                        $"Attorney Office:" +
                    $"</td>" +
                    $"<td>" +
                        $"{notification.OwnerName}" +
                    $"</td>" +
                $"</tr>" +
                $"<tr>" +
                    $"<td>" +
                        $"Requestor Name:" +
                    $"</td>" +
                    $"<td>" +
                        $"{notification.UserFirstName} {notification.UserLastName}" +
                    $"</td>" +
                $"</tr>" +
                $"<tr>" +
                    $"<td>" +
                        $"Requestor Email:" +
                    $"</td>" +
                    $"<td>" +
                        $"{notification.Username}" +
                    $"</td>" +
                $"</tr>" +
                $"<tr></tr>" +
                $"<tr>" +
                    $"<td>" +
                        $"Pickup Location:" +
                    $"</td>" +
                    $"<td>" +
                        $"{notification.RequestTransportation.PickupLocation}" +
                    $"</td>" +
                $"</tr>" +
                $"<tr>" +
                    $"<td>" +
                        $"Is Round Trip:" +
                    $"</td>" +
                    $"<td>" +
                        $"{(notification.RequestTransportation.IsRoundTrip == true ? "Yes" : "No")}" +
                    $"</td>" +
                $"</tr>";

            foreach (var item in notification.RequestTransportation.Appointments.Select((value, i) => new { i, value }))
            {
                body += $@"<tr>
                             <td>{item.i + 1} - Destination Location:</td>
                             <td>{item.value.Destination}</td>
                           </tr>
                           <tr>
                             <td>{item.i + 1} - Appointment Date:</td>
                             <td>{item.value.Date}</td>
                           </tr>
                           <tr>
                             <td>{item.i + 1} - Appointment Time:</td>
                             <td>{item.value.Time}</td>
                           </tr>";

            }

            body += $"<tr>" +
                    $"<td>" +
                        $"Medical Specials:" +
                    $"</td>" +
                    $"<td>" +
                        $"{notification.RequestTransportation.MedicalSpecialsAmount.ToString("C", new CultureInfo("en-US"))}" +
                    $"</td>" +
                $"</tr>";

            if (!string.IsNullOrEmpty(notification.RequestTransportation.PolicyLimit))
            {
                body += $"<tr>" +
                            $"<td>" +
                                $"Policy Limit:" +
                            $"</td>" +
                            $"<td>" +
                                $"{notification.RequestTransportation.PolicyLimit}" +
                            $"</td>" +
                        $"</tr>";
            }

            body += $"</table>" +
            $"<br>" +
            $"<p>Portal Opportunity Link:<br><br>{portalLink}</p>" +
            $"<p>Salesforce Opportunity Link:<br><br>{notification.SalesforceLink}</p>";

            return body;
        }

        public static string getRequestPharmacyCardBody(string portalLink, GainNotification notification)
        {
            var opplink = $"<a href='{portalLink}'>{notification.RequestPharmacyCard.FirstName} {notification.RequestPharmacyCard.LastName}</a>";
            var body = $@"<p>User {notification.UserFirstName} {notification.UserLastName} from {notification.OwnerName} is requesting a pharmacy card for client {opplink}</p>";

            body += $"<table>" +
                $"<tr>" +
                    $"<td align = 'left' style='padding-right:50px;'>" +
                        $"Client Name:" +
                    $"</td>" +
                    $"<td>" +
                        $"{opplink}" +
                    $"</td>" +
                $"</tr>" +
                $"<tr>" +
                    $"<td>" +
                        $"Client DOB:" +
                    $"</td>" +
                    $"<td>" +
                        $"{notification.RequestPharmacyCard.DateOfBirth}" +
                    $"</td>" +
                $"</tr>" +
                $"<tr>" +
                $"<tr>" +
                    $"<td>" +
                        $"Client Email:" +
                    $"</td>" +
                    $"<td>" +
                        $"{notification.RequestPharmacyCard.Email}" +
                    $"</td>" +
                $"</tr>" +
                $"<tr>" +
                $"<tr>" +
                    $"<td>" +
                        $"Client Phone:" +
                    $"</td>" +
                    $"<td>" +
                        $"{notification.RequestPharmacyCard.Phone}" +
                    $"</td>" +
                $"</tr>" +
                $"<tr>" +
                    $"<td>" +
                        $"Date of Accident:" +
                    $"</td>" +
                    $"<td>" +
                        $"{notification.RequestPharmacyCard.DateOfAccident}" +
                    $"</td>" +
                $"</tr>" +
                $"<tr>" +
                $"<tr>" +
                    $"<td>" +
                        $"Attorney Office:" +
                    $"</td>" +
                    $"<td>" +
                        $"{notification.OwnerName}" +
                    $"</td>" +
                $"</tr>" +
                $"<tr>" +
                    $"<td>" +
                        $"Assigned Attorney:" +
                    $"</td>" +
                    $"<td>" +
                        $"{notification.RequestPharmacyCard.Attorney.Name}" +
                    $"</td>" +
                $"</tr>" +
                $"<tr>" +
                $"<tr>" +
                    $"<td>" +
                        $"Requestor Name:" +
                    $"</td>" +
                    $"<td>" +
                        $"{notification.UserFirstName} {notification.UserLastName}" +
                    $"</td>" +
                $"</tr>" +
                $"<tr>" +
                    $"<td>" +
                        $"Requestor Email:" +
                    $"</td>" +
                    $"<td>" +
                        $"{notification.Username}" +
                    $"</td>" +
                $"</tr>";

            body += $"</table>" +
            $"<br>" +
            $"<p>Portal Opportunity Link:<br><br>{portalLink}</p>" +
            $"<p>Salesforce Opportunity Link:<br><br>{notification.SalesforceLink}</p>";

            return body;
        }
        #endregion

        #region UserNotifications
        public static string getUserClientIntakeBody(GainNotification notification, string portalLink, string notificationSettingsLink, SocialMediaInfo mediaInfo)
        {
            string body = string.Empty;
            var path = "EmailTemplates" + Path.DirectorySeparatorChar + "ClientIntake.html";
            using StreamReader reader = new StreamReader(path);
            body = reader.ReadToEnd();

            var header = $"{notification.UserFirstName} {notification.UserLastName} from {notification.OwnerName} has added a new Client {notification.OppFirstName} {notification.OppLastName}";
            var fundingType = "";
            var requestedType = "";
            var requestedValue = "";

            switch (notification.Type)
            {
                case "Cash Advance":
                    fundingType = "PCA";
                    requestedType = "Amount Requested";
                    requestedValue = notification.CashAdvanceAmount != null ? float.Parse(notification.CashAdvanceAmount).ToString("C2") : "N/A";
                    break;

                case "Medical Funding":
                    fundingType = "Medical";
                    requestedType = "Treatment Requested";
                    requestedValue = notification.SelectedProvider.AdditionalComments ?? "None";
                    break;

                default:
                    fundingType = "N/A";
                    requestedType = "N/A";
                    requestedValue = "N/A";
                    break;
            }

            body = body.Replace("{frontend-url}", mediaInfo.FrontendURL);
            body = body.Replace("{logo-url}", mediaInfo.LogoURL);
            body = body.Replace("{header}", header);
            body = body.Replace("{client}", notification.OppFirstName + " " + notification.OppLastName);
            body = body.Replace("{funding-type}", fundingType);
            body = body.Replace("{requested-type}", requestedType);
            body = body.Replace("{requested-value}", requestedValue);
            body = body.Replace("{user-name}", getUserInfo(notification));
            body = body.Replace("{portal-link}", portalLink);
            body = body.Replace("{copyright-year}", DateTime.Now.ToString("yyyy"));

            body = body.Replace("{notification-settings-link}", notificationSettingsLink);

            body = getSocialMediaBody(body, mediaInfo);

            return body;
        }

        public static string getUserPatientIntakeBody(GainNotification notification, string portalLink, string notificationSettingsLink, SocialMediaInfo mediaInfo)
        {
            string body = string.Empty;
            var path = "EmailTemplates" + Path.DirectorySeparatorChar + "PatientIntake.html";
            using StreamReader reader = new StreamReader(path);
            body = reader.ReadToEnd();

            var header = $"{notification.UserFirstName} {notification.UserLastName} from {notification.OwnerName} has added a new Patient {notification.OppFirstName} {notification.OppLastName}";
            var opportunityType = "Patient";

            if (notification.UserType == "Attorney")
            {
                header = $"{notification.OwnerName} has assigned a new Client {notification.OppFirstName} {notification.OppLastName}";
                opportunityType = "Client";
            }

            body = body.Replace("{frontend-url}", mediaInfo.FrontendURL);
            body = body.Replace("{logo-url}", mediaInfo.LogoURL);
            body = body.Replace("{header}", header);
            body = body.Replace("{opportunity-type}", opportunityType);
            body = body.Replace("{opportunity}", notification.OppFirstName + " " + notification.OppLastName);
            body = body.Replace("{dob}", notification.OppDateOfBirth);
            body = body.Replace("{user-name}", getUserInfo(notification));
            body = body.Replace("{portal-link}", portalLink);
            body = body.Replace("{copyright-year}", DateTime.Now.ToString("yyyy"));

            body = body.Replace("{notification-settings-link}", notificationSettingsLink);

            body = getSocialMediaBody(body, mediaInfo);

            return body;
        }

        public static string getUserFileUploadBody(GainNotification notification, string portalLink, string notificationSettingsLink, SocialMediaInfo mediaInfo)
        {
            string body = string.Empty;
            var path = "EmailTemplates" + Path.DirectorySeparatorChar + "FileUpload.html";
            using StreamReader reader = new StreamReader(path);
            body = reader.ReadToEnd();

            var opportunityType = "Client";

            if (notification.UserType == "Provider")
            {
                opportunityType = "Patient";
            }

            var header = $"{GetDocumentTypesString(notification)} file for {notification.OppFirstName} {notification.OppLastName} has been uploaded for {notification.OwnerName}";

            body = body.Replace("{frontend-url}", mediaInfo.FrontendURL);
            body = body.Replace("{logo-url}", mediaInfo.LogoURL);
            body = body.Replace("{header}", header);
            body = body.Replace("{opportunity-type}", opportunityType);
            body = body.Replace("{opportunity}", notification.OppFirstName + " " + notification.OppLastName);
            body = body.Replace("{case-status}", notification.CaseStatus);
            body = body.Replace("{file-type}", GetDocumentTypesString(notification));
            body = body.Replace("{file-name}", string.Join(", ", notification.Attachments.Select(a => string.Format("'{0}'", a.Title))));
            body = body.Replace("{user-name}", getUserInfo(notification));
            body = body.Replace("{portal-link}", portalLink);
            body = body.Replace("{section-link}", portalLink + "#patient-attachments");
            //body = body.Replace("{attachment-link}", notification.AttachmentLink);
            body = body.Replace("{copyright-year}", DateTime.Now.ToString("yyyy"));

            body = body.Replace("{notification-settings-link}", notificationSettingsLink);

            body = getSocialMediaBody(body, mediaInfo);

            return body;
        }

        public static string getUserCaseStatusBody(GainNotification notification, string portalLink, string notificationSettingsLink, SocialMediaInfo mediaInfo)
        {
            string body = string.Empty;
            var path = "EmailTemplates" + Path.DirectorySeparatorChar + "CaseStatus.html";
            using StreamReader reader = new StreamReader(path);
            body = reader.ReadToEnd();

            var opportunityType = "Client";

            if (notification.UserType == "Provider")
            {
                opportunityType = "Patient";
            }

            var header = $"Client {notification.OppFirstName} {notification.OppLastName} case status has changed from <b>{notification.CaseStatusUpdate.FromStatus}</b> to <b>{notification.CaseStatusUpdate.ToStatus}</b>";
            var additionalDetails = "";

            if (notification.CaseStatusUpdate.ToStatus == "Case Closed Payment Disbursed")
            {
                additionalDetails = $"** Payment was disbursed to: {notification.CaseStatusUpdate.Notes} **";
            }

            body = body.Replace("{frontend-url}", mediaInfo.FrontendURL);
            body = body.Replace("{logo-url}", mediaInfo.LogoURL);
            body = body.Replace("{header}", header);
            body = body.Replace("{opportunity-type}", opportunityType);
            body = body.Replace("{opportunity}", notification.OppFirstName + " " + notification.OppLastName);
            body = body.Replace("{previous-status}", notification.CaseStatusUpdate.FromStatus);
            body = body.Replace("{updated-status}", notification.CaseStatusUpdate.ToStatus);
            body = body.Replace("{portal-link}", portalLink);
            body = body.Replace("{additional-details}", additionalDetails);
            body = body.Replace("{copyright-year}", DateTime.Now.ToString("yyyy"));

            body = body.Replace("{notification-settings-link}", notificationSettingsLink);

            body = getSocialMediaBody(body, mediaInfo);

            return body;
        }

        public static string getUserCashAdvanceBody(GainNotification notification, string portalLink, string notificationSettingsLink, SocialMediaInfo mediaInfo)
        {
            string body = string.Empty;
            var path = "EmailTemplates" + Path.DirectorySeparatorChar + "CashAdvance.html";
            using StreamReader reader = new StreamReader(path);
            body = reader.ReadToEnd();

            var opportunityType = "Client";

            var header = $"Additional PCA has been Requested for Client {notification.OppFirstName} {notification.OppLastName} by {notification.OwnerName}";
            var cashAmount = notification.CashAdvanceAmount ?? "N/A";

            var contactAttorneyMessage = string.Empty;

            if (notification.ContactAttorney)
            {
                contactAttorneyMessage = $"** ATTORNEY REQUESTS DISCUSSION BEFORE FUNDING **";
            }

            body = body.Replace("{frontend-url}", mediaInfo.FrontendURL);
            body = body.Replace("{logo-url}", mediaInfo.LogoURL);
            body = body.Replace("{header}", header);
            body = body.Replace("{opportunity-type}", opportunityType);
            body = body.Replace("{opportunity}", notification.OppFirstName + " " + notification.OppLastName);
            body = body.Replace("{amount}", cashAmount);
            body = body.Replace("{contact-attorney}", contactAttorneyMessage);
            body = body.Replace("{user-name}", getUserInfo(notification));
            body = body.Replace("{portal-link}", portalLink);
            body = body.Replace("{copyright-year}", DateTime.Now.ToString("yyyy"));

            body = body.Replace("{notification-settings-link}", notificationSettingsLink);

            body = getSocialMediaBody(body, mediaInfo);

            return body;
        }

        public static string getUserRequestDocumentBody(GainNotification notification, string portalLink, string notificationSettingsLink, SocialMediaInfo mediaInfo)
        {
            string body = string.Empty;
            var path = "EmailTemplates" + Path.DirectorySeparatorChar + "RequestDocument.html";
            using StreamReader reader = new StreamReader(path);
            body = reader.ReadToEnd();

            var opportunityType = "Client";

            if (notification.UserType == "Provider")
            {
                opportunityType = "Patient";
            }

            var header = $"Documents Requested for {notification.OppFirstName} {notification.OppLastName} by {notification.OwnerName}";
            var notes = notification.Notes ?? "N/A";
            var docs = !string.IsNullOrEmpty(GetDocumentTypesString(notification)) ? GetDocumentTypesString(notification) : "N/A";

            body = body.Replace("{frontend-url}", mediaInfo.FrontendURL);
            body = body.Replace("{logo-url}", mediaInfo.LogoURL);
            body = body.Replace("{header}", header);
            body = body.Replace("{opportunity-type}", opportunityType);
            body = body.Replace("{opportunity}", notification.OppFirstName + " " + notification.OppLastName);
            body = body.Replace("{requested-documents}", docs);
            body = body.Replace("{notes}", notes);
            body = body.Replace("{user-name}", getUserInfo(notification));
            body = body.Replace("{portal-link}", portalLink);

            body = body.Replace("{notification-settings-link}", notificationSettingsLink);

            body = getSocialMediaBody(body, mediaInfo);

            return body;
        }

        public static string getUserPayoffRequestBody(GainNotification notification, string portalLink, string notificationSettingsLink, SocialMediaInfo mediaInfo)
        {
            string body = string.Empty;
            var path = "EmailTemplates" + Path.DirectorySeparatorChar + "RequestPayoff.html";
            var isReduction = notification.PayoffRequest.ReductionRequest.ProposedAmount > 0 ? true : false;
            var header = $"Payoff Amount for {notification.OppFirstName} {notification.OppLastName} Requested by {notification.OwnerName}";
            var userName = notification.UserFirstName + " " + notification.UserLastName;

            if (isReduction)
            {
                path = "EmailTemplates" + Path.DirectorySeparatorChar + "RequestPayoffReduction.html";
                header = $"Reduction Requested for {notification.OppFirstName} {notification.OppLastName} by {notification.OwnerName}";
            }
            using StreamReader reader = new StreamReader(path);
            body = reader.ReadToEnd();

            var opportunityType = "Client";

            if (notification.UserType == "Provider")
            {
                opportunityType = "Patient";
            }

            var pcaAmount = (notification.PayoffRequest.TotalStateCapPCA > 0 && notification.PayoffRequest.TotalStateCapPCA != notification.PayoffRequest.TotalUncappedPCA)
                ? $"{notification.PayoffRequest.TotalUncappedPCA.ToString("C", new CultureInfo("en-US"))} (State Cap: {(notification.PayoffRequest.TotalStateCapPCA).ToString("C", new CultureInfo("en-US"))})"
                : notification.PayoffRequest.PCAAmount.ToString("C", new CultureInfo("en-US"));

            var medicalAmount = notification.PayoffRequest.MedicalAmount.ToString("C", new CultureInfo("en-US"));

            var totalAmount = (notification.PayoffRequest.MedicalAmount + notification.PayoffRequest.PCAAmount).ToString("C", new CultureInfo("en-US"));
            var notes = "";

            const string quote = "\"";
            var validDate = $@"
                                <tr>
                                    <td align={quote}center{quote} colspan={quote}2{quote} style={quote}font-family: 'Roboto',Helvetica,Arial,sans-serif; padding:7px; line-height:20px; font-size: 13px; color: #444444;{quote}>
                                        This quote is valid for 90 days from {DateTime.Now.ToString("MM/dd/yyyy")}
                                    </td>
                                </tr>";

            if (notification.PayoffRequest.GenerateDoc)
            {
                validDate += $@"
                                <tr>
                                    <td align={quote}center{quote} colspan={quote}2{quote} style={quote}font-family: 'Roboto',Helvetica,Arial,sans-serif; padding:7px; line-height:20px; font-size: 13px; color: #444444;{quote}>
                                        A Payoff document was generated and attached for your convenience. Your Gain Risk Manager will be in touch with {userName} shortly to discuss this reduction request.
                                        Log in to the Gain portal at any time to view or manage additional details and reqests for this client.
                                    </td>
                                </tr>";
            }

            var specialsNotCovered = string.Empty;

            if (notification.PayoffRequest.ReductionRequest.MedicalInsuranceAmount >= 0)
            {
                var specialsNotCoveredAmount = notification.PayoffRequest.ReductionRequest.SpecialsAmount - notification.PayoffRequest.ReductionRequest.MedicalInsuranceAmount;
                specialsNotCovered += $@"
                                        <tr>
                                            <td align={quote}center{quote} colspan={quote}2{quote} style={quote}font-family: 'Roboto',Helvetica,Arial,sans-serif; padding:7px; line-height:20px; font-size: 13px; color: #444444;{quote}>
                                                Total specials not covered by medical insurance: {specialsNotCoveredAmount.ToString("C", new CultureInfo("en-US"))}
                                            </td>
                                        </tr>";
            }

            body = body.Replace("{frontend-url}", mediaInfo.FrontendURL);
            body = body.Replace("{logo-url}", mediaInfo.LogoURL);
            body = body.Replace("{header}", header);
            body = body.Replace("{opportunity-type}", opportunityType);
            body = body.Replace("{opportunity}", notification.OppFirstName + " " + notification.OppLastName);
            body = body.Replace("{pca-amount}", pcaAmount);
            body = body.Replace("{medical-amount}", medicalAmount);
            body = body.Replace("{total-amount}", totalAmount);
            body = body.Replace("{user-name}", getUserInfo(notification));
            body = body.Replace("{total-specials-not-covered}", specialsNotCovered);
            body = body.Replace("{valid-date}", validDate);
            body = body.Replace("{portal-link}", portalLink);
            body = body.Replace("{copyright-year}", DateTime.Now.ToString("yyyy"));

            body = body.Replace("{notification-settings-link}", notificationSettingsLink);

            var proposedAmount = "";
            var specialsAmount = "";
            var caseSettled = "No";
            var caseAmountType = "";
            var caseAmount = "N/A";
            var medicalInsurance = "No";
            var medicalInsuranceAmount = "N/A";
            if (isReduction)
            {
                proposedAmount = notification.PayoffRequest.ReductionRequest.ProposedAmount.ToString("C", new CultureInfo("en-US"));
                specialsAmount = notification.PayoffRequest.ReductionRequest.SpecialsAmount.ToString("C", new CultureInfo("en-US"));

                if (notification.PayoffRequest.ReductionRequest.isSettled)
                {
                    caseSettled = "Yes";
                    caseAmountType = "Case Settlement Amount:";
                    caseAmount = notification.PayoffRequest.ReductionRequest.SettledAmount.ToString("C", new CultureInfo("en-US"));
                }
                else
                {
                    caseSettled = "No";
                    caseAmountType = "Top Offer:";
                    caseAmount = notification.PayoffRequest.ReductionRequest.OfferedAmount.ToString("C", new CultureInfo("en-US"));
                }

                if (notification.PayoffRequest.ReductionRequest.MedicalInsuranceAmount > 0)
                {
                    medicalInsurance = "Yes";
                    medicalInsuranceAmount = notification.PayoffRequest.ReductionRequest.MedicalInsuranceAmount.ToString("C", new CultureInfo("en-US"));
                }

                notes = notification.PayoffRequest.ReductionRequest.Notes ?? "";

                body = body.Replace("{case-settled}", caseSettled);
                body = body.Replace("{proposed-amount}", proposedAmount);
                body = body.Replace("{specials-amount}", specialsAmount);
                body = body.Replace("{case-amount-type}", caseAmountType);
                body = body.Replace("{case-amount}", caseAmount);
                body = body.Replace("{medical-insurance}", medicalInsurance);
                body = body.Replace("{medical-insurance-amount}", medicalInsuranceAmount);
                body = body.Replace("{notes}", notes);
            }

            body = getSocialMediaBody(body, mediaInfo);

            return body;
        }

        public static string getUserMedicalFundingBody(GainNotification notification, string portalLink, string notificationSettingsLink, SocialMediaInfo mediaInfo)
        {
            string body = string.Empty;
            var path = "EmailTemplates" + Path.DirectorySeparatorChar + "MedicalFunding.html";
            using StreamReader reader = new StreamReader(path);
            body = reader.ReadToEnd();

            var opportunityType = "Client";

            var header = $"Additional Medical Funding has been Requested for Client {notification.OppFirstName} {notification.OppLastName} by {notification.OwnerName}";

            body = body.Replace("{frontend-url}", mediaInfo.FrontendURL);
            body = body.Replace("{logo-url}", mediaInfo.LogoURL);
            body = body.Replace("{header}", header);
            body = body.Replace("{opportunity-type}", opportunityType);
            body = body.Replace("{opportunity}", notification.OppFirstName + " " + notification.OppLastName);
            body = body.Replace("{additional-comments}", notification.SelectedProvider.AdditionalComments ?? "None");
            body = body.Replace("{specialty}", notification.SelectedProvider.ProviderSpecialty ?? "None");
            body = body.Replace("{user-name}", getUserInfo(notification));
            body = body.Replace("{portal-link}", portalLink);
            body = body.Replace("{copyright-year}", DateTime.Now.ToString("yyyy"));

            body = body.Replace("{notification-settings-link}", notificationSettingsLink);

            body = getSocialMediaBody(body, mediaInfo);

            return body;
        }

        public static string getUserTreatmentCompleteBody(GainNotification notification, string portalLink, string notificationSettingsLink, SocialMediaInfo mediaInfo)
        {
            string body = string.Empty;
            var path = "EmailTemplates" + Path.DirectorySeparatorChar + "TreatmentComplete.html";
            using StreamReader reader = new StreamReader(path);
            body = reader.ReadToEnd();

            var opportunityType = "Client";

            if (notification.UserType == "Provider")
            {
                opportunityType = "Patient";
            }

            var title = "Treatment Complete";
            var header = $"Treatment complete for {notification.OppFirstName} {notification.OppLastName} indicated by {notification.OwnerName}";
            if (notification.Type == "TreatmentResumed")
            {
                title = "Treatment Resumed";
                header = $"Treatment INCOMPLETE for {notification.OppFirstName} {notification.OppLastName} indicated by {notification.OwnerName}";
            }

            body = body.Replace("{frontend-url}", mediaInfo.FrontendURL);
            body = body.Replace("{logo-url}", mediaInfo.LogoURL);
            body = body.Replace("{title}", title);
            body = body.Replace("{header}", header);
            body = body.Replace("{opportunity-type}", opportunityType);
            body = body.Replace("{opportunity}", notification.OppFirstName + " " + notification.OppLastName);
            body = body.Replace("{user-name}", getUserInfo(notification));
            body = body.Replace("{portal-link}", portalLink);
            body = body.Replace("{copyright-year}", DateTime.Now.ToString("yyyy"));

            body = body.Replace("{notification-settings-link}", notificationSettingsLink);

            body = getSocialMediaBody(body, mediaInfo);

            return body;
        }

        public static string getUserClaimUploadBody(GainNotification notification, string portalLink, string notificationSettingsLink, SocialMediaInfo mediaInfo)
        {
            string body = string.Empty;
            var path = "EmailTemplates" + Path.DirectorySeparatorChar + "ClaimUpload.html";
            using StreamReader reader = new StreamReader(path);
            body = reader.ReadToEnd();

            var opportunityType = "Client";

            if (notification.UserType == "Provider")
            {
                opportunityType = "Patient";
            }

            var header = $"New Claim Uploaded for {notification.OppFirstName} {notification.OppLastName} by User {notification.UserFirstName} {notification.UserLastName} from {notification.OwnerName}";
            var invoiceAmount = notification.PayoffRequest.MedicalAmount.ToString("C", new CultureInfo("en-US"));
            var notes = notification.Notes ?? "";

            body = body.Replace("{frontend-url}", mediaInfo.FrontendURL);
            body = body.Replace("{logo-url}", mediaInfo.LogoURL);
            body = body.Replace("{header}", header);
            body = body.Replace("{opportunity-type}", opportunityType);
            body = body.Replace("{opportunity}", notification.OppFirstName + " " + notification.OppLastName);
            body = body.Replace("{user-name}", getUserInfo(notification));
            body = body.Replace("{invoice-amount}", invoiceAmount);
            body = body.Replace("{dos}", notification.CaseStatusUpdate.FromStatusDate);
            body = body.Replace("{notes}", notes);
            body = body.Replace("{portal-link}", portalLink);
            body = body.Replace("{section-link}", portalLink + "#patient-attachments");
            body = body.Replace("{copyright-year}", DateTime.Now.ToString("yyyy"));

            if (notification.PayoffRequest.GenerateDoc)
            {
                body = body.Replace("{payoff-doc}", "TODO");
            }

            body = body.Replace("{notification-settings-link}", notificationSettingsLink);

            body = getSocialMediaBody(body, mediaInfo);

            return body;
        }

        public static string getUserOpportunityCommentBody(GainNotification notification, string portalLink, string notificationSettingsLink, SocialMediaInfo mediaInfo)
        {
            string body = string.Empty;
            var path = "EmailTemplates" + Path.DirectorySeparatorChar + "OpportunityComment.html";
            using StreamReader reader = new StreamReader(path);
            body = reader.ReadToEnd();

            var opportunityType = "Client";

            if (notification.UserType == "Provider")
            {
                opportunityType = "Patient";
            }

            var header = $"New Message Added to {opportunityType} {notification.OppFirstName} {notification.OppLastName} by {notification.UserFirstName} {notification.UserLastName} from {notification.OwnerName}";
            var timestamp = Formatter.FormatDateTimeToGainServicing(notification.CommentTimestamp);

            body = body.Replace("{frontend-url}", mediaInfo.FrontendURL);
            body = body.Replace("{logo-url}", mediaInfo.LogoURL);
            body = body.Replace("{header}", header);
            body = body.Replace("{opportunity-type}", opportunityType);
            body = body.Replace("{opportunity}", notification.OppFirstName + " " + notification.OppLastName);
            body = body.Replace("{user-name}", getUserInfo(notification));
            body = body.Replace("{timestamp}", timestamp);
            string message = "To see this secure message, login to the Gain Portal.";
            body = body.Replace("{message}", message); // notification.CommentMessage); // Uncomment to send actual message content
            body = body.Replace("{portal-link}", portalLink);
            body = body.Replace("{section-link}", portalLink + "#opportunity-messages");
            body = body.Replace("{copyright-year}", DateTime.Now.ToString("yyyy"));

            body = body.Replace("{notification-settings-link}", notificationSettingsLink);

            body = getSocialMediaBody(body, mediaInfo);

            return body;
        }

        public static string getUserOpportunityUpdateBody(GainNotification notification, string portalLink, string notificationSettingsLink, SocialMediaInfo mediaInfo)
        {
            string body = string.Empty;
            var path = "EmailTemplates" + Path.DirectorySeparatorChar + "OpportunityUpdate.html";
            using StreamReader reader = new StreamReader(path);
            body = reader.ReadToEnd();

            var opportunityType = "Client";
            var title = notification.UpdateInfoSection;

            if (notification.UserType == "Provider")
            {
                opportunityType = "Patient";
            }

            if (notification.UpdateInfoSection == "Patient Information")
            {
                if (notification.UserType == "Attorney")
                {
                    title = "Client Information";
                }
            }

            var header = $"{notification.OppFirstName} {notification.OppLastName} Case Updated";

            body = body.Replace("{frontend-url}", mediaInfo.FrontendURL);
            body = body.Replace("{logo-url}", mediaInfo.LogoURL);
            body = body.Replace("{title}", title);
            body = body.Replace("{header}", header);
            body = body.Replace("{opportunity-type}", opportunityType);
            body = body.Replace("{opportunity}", notification.OppFirstName + " " + notification.OppLastName);
            body = body.Replace("{update-details}", notification.Notes);
            body = body.Replace("{portal-link}", portalLink);
            body = body.Replace("{copyright-year}", DateTime.Now.ToString("yyyy"));

            body = body.Replace("{notification-settings-link}", notificationSettingsLink);

            body = getSocialMediaBody(body, mediaInfo);

            return body;
        }

        public static string getUserProviderMapSubmitBody(GainNotification notification, string portalLink, string notificationSettingsLink, SocialMediaInfo mediaInfo)
        {
            string body = string.Empty;
            var path = "EmailTemplates" + Path.DirectorySeparatorChar + "ProviderMapSubmit.html";
            using StreamReader reader = new StreamReader(path);
            body = reader.ReadToEnd();

            var opportunityType = "Client";

            var header = $"New Provider Selected for Medical Treatment of Client {notification.OppFirstName} {notification.OppLastName}";

            body = body.Replace("{frontend-url}", mediaInfo.FrontendURL);
            body = body.Replace("{logo-url}", mediaInfo.LogoURL);
            body = body.Replace("{header}", header);
            body = body.Replace("{opportunity-type}", opportunityType);
            body = body.Replace("{opportunity}", notification.SelectedProvider.OpportunityName);
            body = body.Replace("{provider-name}", notification.SelectedProvider.ProviderName);
            body = body.Replace("{provider-specialty}", notification.SelectedProvider.ProviderSpecialty);
            body = body.Replace("{provider-location}", notification.SelectedProvider.ProviderLocation);
            body = body.Replace("{additional-comments}", notification.SelectedProvider.AdditionalComments ?? "None");
            body = body.Replace("{user-name}", getUserInfo(notification));
            body = body.Replace("{portal-link}", portalLink);
            body = body.Replace("{copyright-year}", DateTime.Now.ToString("yyyy"));

            body = body.Replace("{notification-settings-link}", notificationSettingsLink);

            body = getSocialMediaBody(body, mediaInfo);

            return body;
        }

        public static string getUserShareableLinkBody(ShareableLinkRequest request, User user, string notificationSettingsLink, SocialMediaInfo mediaInfo)
        {
            string body = string.Empty;
            var path = "EmailTemplates" + Path.DirectorySeparatorChar + "ShareableLink.html";
            using StreamReader reader = new StreamReader(path);
            body = reader.ReadToEnd();

            var userName = user.Firstname + " " + user.Lastname;
            var buttonLabel = "login";

            var header = request.Subject ?? "Please click the button below";

            if (request.TypeID == (int)ShareableLinkType.Types.PLAINTIFF_DETAIL)
            {
                buttonLabel = $"{request.PlaintiffName} Link";
            }

            if (request.TypeID == (int)ShareableLinkType.Types.FILE)
            {
                buttonLabel = $"{request.PlaintiffName}'s File Link";
            }

            if (request.TypeID == (int)ShareableLinkType.Types.PAYOFF)
            {
                buttonLabel = $"{request.PlaintiffName}'s File Link";
            }

            if (request.TypeID == (int)ShareableLinkType.Types.PLAINTIFF_LIST)
            {
                buttonLabel = $"{userName}'s Search Link";
            }

            if (request.TypeID == (int)ShareableLinkType.Types.PROVIDER_MAP)
            {
                buttonLabel = $"{userName}'s Provider Search Link";
            }

            if (request.TypeID == (int)ShareableLinkType.Types.DOWNLOAD_ALL_FILES)
            {
                buttonLabel = $"{request.PlaintiffName}'s Download Link";
            }

            if (request.TypeID == (int)ShareableLinkType.Types.REDUCTION_ACCEPTED)
            {
                buttonLabel = $"{request.PlaintiffName}'s Reduction Link";
            }

            body = body.Replace("{header}", header);

            var userMessage = string.Empty;

            if (!string.IsNullOrEmpty(request.Message))
            {
                const string quote = "\"";
                userMessage = $@"
                                <tr>
                                    <td align={quote}center{quote} style={quote}font-family: 'Roboto',Helvetica,Arial,sans-serif; padding:20px; line-height:20px; font-size: 13px; color: #444444;{quote}>
                                        {request.Message}
                                    </td>
                                </tr>";
            }

            if (!string.IsNullOrEmpty(request.Link))
            {
                request.Link = new UriBuilder(request.Link).ToString();
            }

            body = body.Replace("{user-message}", userMessage);

            body = body.Replace("{button-label}", buttonLabel);
            body = body.Replace("{user-name}", userName);
            body = body.Replace("{user-email}", user.Email);
            body = body.Replace("{user-phone}", user.Phone);
            body = body.Replace("{frontend-url}", mediaInfo.FrontendURL);
            body = body.Replace("{section-link}", request.Link);
            body = body.Replace("{logo-url}", mediaInfo.LogoURL);
            body = body.Replace("{copyright-year}", DateTime.Now.ToString("yyyy"));

            body = body.Replace("{notification-settings-link}", notificationSettingsLink);

            body = getSocialMediaBody(body, mediaInfo);

            return body;
        }

        public static string getUserRequestTransportationBody(GainNotification notification, string portalLink, string notificationSettingsLink, SocialMediaInfo mediaInfo)
        {
            string body = string.Empty;
            var path = "EmailTemplates" + Path.DirectorySeparatorChar + "RequestTransportation.html";
            using StreamReader reader = new StreamReader(path);
            body = reader.ReadToEnd();

            var opportunityType = "Client";

            var header = $"Medical Transportation has been requested for Client {notification.OppFirstName} {notification.OppLastName} by {notification.OwnerName}";

            body = body.Replace("{frontend-url}", mediaInfo.FrontendURL);
            body = body.Replace("{logo-url}", mediaInfo.LogoURL);
            body = body.Replace("{header}", header);
            body = body.Replace("{opportunity-type}", opportunityType);
            body = body.Replace("{opportunity}", notification.OppFirstName + " " + notification.OppLastName);
            body = body.Replace("{email}", notification.RequestTransportation.Email);
            body = body.Replace("{phone}", notification.RequestTransportation.Phone);
            body = body.Replace("{pickup-location}", notification.RequestTransportation.PickupLocation);
            body = body.Replace("{round-trip}", notification.RequestTransportation.IsRoundTrip == true ? "Yes" : "No");

            var appointmentsBody = string.Empty;
            foreach (var item in notification.RequestTransportation.Appointments.Select((value, i) => new { i, value }))
            {
                appointmentsBody += "<tr>";
                appointmentsBody += getBodyInformationRow("th", $"{item.i + 1} - Destination Location");
                appointmentsBody += getBodyInformationRow("td", item.value.Destination);
                appointmentsBody += "</tr>";
                appointmentsBody += "<tr>";
                appointmentsBody += getBodyInformationRow("th", $"{item.i + 1} - Appointment Date");
                appointmentsBody += getBodyInformationRow("td", item.value.Date);
                appointmentsBody += "</tr>";
                appointmentsBody += "<tr>";
                appointmentsBody += getBodyInformationRow("th", $"{item.i + 1} - Appointment Time");
                appointmentsBody += getBodyInformationRow("td", item.value.Time);
                appointmentsBody += "</tr>";
            }

            body = body.Replace("{appointments}", appointmentsBody);
            body = body.Replace("{policy-limit}", notification.RequestTransportation.PolicyLimit ?? "N/A");
            body = body.Replace("{medical-specials}", notification.RequestTransportation.MedicalSpecialsAmount.ToString("C", new CultureInfo("en-US")));
            body = body.Replace("{user-name}", getUserInfo(notification));
            body = body.Replace("{portal-link}", portalLink);
            body = body.Replace("{copyright-year}", DateTime.Now.ToString("yyyy"));

            body = body.Replace("{notification-settings-link}", notificationSettingsLink);

            body = getSocialMediaBody(body, mediaInfo);

            return body;
        }

        public static string getUserRequestPharmacyCardBody(GainNotification notification, string portalLink, string notificationSettingsLink, SocialMediaInfo mediaInfo)
        {
            string body = string.Empty;
            var path = "EmailTemplates" + Path.DirectorySeparatorChar + "RequestPharmacyCard.html";
            using StreamReader reader = new StreamReader(path);
            body = reader.ReadToEnd();

            var header = $"Pharmacy Card has been Requested for Client {notification.RequestPharmacyCard.FirstName} {notification.RequestPharmacyCard.LastName} by {notification.OwnerName}";

            body = body.Replace("{frontend-url}", mediaInfo.FrontendURL);
            body = body.Replace("{logo-url}", mediaInfo.LogoURL);
            body = body.Replace("{header}", header);
            body = body.Replace("{opportunity}", notification.RequestPharmacyCard.FirstName + " " + notification.RequestPharmacyCard.LastName);
            body = body.Replace("{dob}", notification.RequestPharmacyCard.DateOfBirth);
            body = body.Replace("{email}", notification.RequestPharmacyCard.Email);
            body = body.Replace("{phone}", notification.RequestPharmacyCard.Phone);
            body = body.Replace("{doa}", notification.RequestPharmacyCard.DateOfAccident);
            body = body.Replace("{attorney}", notification.RequestPharmacyCard.Attorney.Name);
            body = body.Replace("{user-name}", getUserInfo(notification));
            body = body.Replace("{portal-link}", portalLink);
            body = body.Replace("{copyright-year}", DateTime.Now.ToString("yyyy"));

            body = body.Replace("{notification-settings-link}", notificationSettingsLink);

            body = getSocialMediaBody(body, mediaInfo);

            return body;
        }

        private static string getBodyInformationRow(string tag, string text)
        {
            var body = string.Empty;
            body += $"<{tag} align=\"left\" style=\"font-family: 'Roboto',Helvetica,Arial,sans-serif; padding:7px; border-bottom:1px solid #F1F1F1; border-right:1px solid #F1F1F1; border-left: 1px solid #F1F1F1; line-height:20px; font-size: 13px; color: #444444;\">" +
                        text +
                     "</{tag}>";
            return body;
        }

        public static string getUserReductionNegotiationBody(ReductionHistory history, SF_PatientDetails opportunity, string portalLink, string notificationSettingsLink, SocialMediaInfo mediaInfo)
        {
            string body = string.Empty;
            var path = "EmailTemplates" + Path.DirectorySeparatorChar + "ReductionNegotiation.html";
            using StreamReader reader = new StreamReader(path);
            body = reader.ReadToEnd();

            var opportunityName = opportunity.Name;
            var requestorName = history.ActingUser.Firstname + " " + history.ActingUser.Lastname;
            var riskManagerName = history.ActingRiskManager.Firstname + " " + history.ActingRiskManager.Lastname;
            var stageName = (ReductionQueueStage.Stages)history.StageID;

            var header = $"Reduction Request Updated for {opportunityName}";

            var notes = string.Empty;

            if (!string.IsNullOrEmpty(history.Notes))
            {
                const string quote = "\"";
                notes = $@"
                                <tr>
                                    <th align={quote}left{quote} style={quote}font-family: 'Roboto',Helvetica,Arial,sans-serif; padding: 20px 7px; border-bottom:1px solid #F1F1F1; border-right:1px solid #F1F1F1; border-left: 1px solid #F1F1F1; line-height:20px; font-size: 13px; color: #444444;{quote}>
                                        Notes:
                                    </th>
                                    <td align={quote}left{quote} style={quote}font-family: 'Roboto',Helvetica,Arial,sans-serif; padding: 20px 7px; border-bottom:1px solid #F1F1F1; border-right:1px solid #F1F1F1; border-left: 1px solid #F1F1F1; line-height:20px; font-size: 13px; color: #444444;{quote}>
                                        {history.Notes}
                                    </td>
                                </tr>";
            }

            body = body.Replace("{header}", header);
            body = body.Replace("{opportunity-name}", opportunityName);
            body = body.Replace("{stage}", stageName.ToString());

            body = body.Replace("{requestor-name}", requestorName);
            body = body.Replace("{requestor-email}", history.ActingUser.Email);
            body = body.Replace("{requestor-phone}", history.ActingUser.Phone);

            body = body.Replace("{risk-manager-name}", riskManagerName);
            body = body.Replace("{risk-manager-email}", history.ActingRiskManager.Email);
            body = body.Replace("{risk-manager-phone}", history.ActingRiskManager.Phone);

            body = body.Replace("{offer-amount}", history.OfferAmount.ToString("C", new CultureInfo("en-US")));
            body = body.Replace("{notes}", notes);

            body = body.Replace("{frontend-url}", mediaInfo.FrontendURL);
            body = body.Replace("{portal-link}", portalLink);
            body = body.Replace("{logo-url}", mediaInfo.LogoURL);
            body = body.Replace("{copyright-year}", DateTime.Now.ToString("yyyy"));

            body = body.Replace("{notification-settings-link}", notificationSettingsLink);

            body = getSocialMediaBody(body, mediaInfo);

            return body;
        }

        private static string SetReductionNegotiationBaseBody(string body, ReductionHistory history, SF_PatientDetails opportunity, string portalLink, string notificationSettingsLink, SocialMediaInfo mediaInfo)
        {
            var opportunityName = opportunity.Name;
            var requestorName = history.ActingUser.Firstname + " " + history.ActingUser.Lastname;
            var riskManagerName = history.ActingRiskManager.Firstname + " " + history.ActingRiskManager.Lastname;
            var stageName = (ReductionQueueStage.Stages)history.StageID;

            body = body.Replace("{opportunity-name}", opportunityName);
            body = body.Replace("{stage}", stageName.ToString());

            body = body.Replace("{requestor-name}", requestorName);
            body = body.Replace("{requestor-email}", history.ActingUser.Email);
            body = body.Replace("{requestor-phone}", history.ActingUser.Phone);

            body = body.Replace("{risk-manager-name}", riskManagerName);
            body = body.Replace("{risk-manager-email}", history.ActingRiskManager.Email);
            body = body.Replace("{risk-manager-phone}", history.ActingRiskManager.Phone);

            body = body.Replace("{offer-amount}", history.OfferAmount.ToString("C", new CultureInfo("en-US")));

            body = body.Replace("{frontend-url}", mediaInfo.FrontendURL);
            body = body.Replace("{portal-link}", portalLink);
            body = body.Replace("{logo-url}", mediaInfo.LogoURL);
            body = body.Replace("{copyright-year}", DateTime.Now.ToString("yyyy"));

            body = body.Replace("{notification-settings-link}", notificationSettingsLink);

            body = getSocialMediaBody(body, mediaInfo);

            return body;
        }

        public static string getUserReductionNegotiationAcceptBody(ReductionHistory history, SF_PatientDetails opportunity, string portalLink, string notificationSettingsLink, SocialMediaInfo mediaInfo)
        {
            string body = string.Empty;
            var path = "EmailTemplates" + Path.DirectorySeparatorChar + "ReductionNegotiationAccept.html";
            using StreamReader reader = new StreamReader(path);
            body = reader.ReadToEnd();

            body = SetReductionNegotiationBaseBody(body, history, opportunity, portalLink, notificationSettingsLink, mediaInfo);

            var header = $"Reduction Request Accepted for {opportunity.Name}";

            body = body.Replace("{header}", header);

            return body;
        }

        public static string getUserReductionNegotiationLessThanSentOutBody(
            ReductionHistory history,
            SF_PatientDetails opportunity,
            string portalLink,
            string notificationSettingsLink,
            SocialMediaInfo mediaInfo,
            string header,
            string reason,
            float percentageNonServiced)
        {
            string body = string.Empty;
            var path = "EmailTemplates" + Path.DirectorySeparatorChar + "ReductionNegotiationLessThanSentOut.html";
            using StreamReader reader = new StreamReader(path);
            body = reader.ReadToEnd();

            body = SetReductionNegotiationBaseBody(body, history, opportunity, portalLink, notificationSettingsLink, mediaInfo);

            body = body.Replace("{header}", header);
            body = body.Replace("{cash-out}", history.CashOut.ToString("C", new CultureInfo("en-US")));
            body = body.Replace("{cash-out-reason}", reason);

            // Calculate the value (percentage * offer amount)
            float calculatedValue = percentageNonServiced * history.OfferAmount;

            // Format and add to the email
            body = body.Replace("{percentage-non-serviced}", (percentageNonServiced * 100).ToString("0.00") + "%");
            body = body.Replace("{calculated-value}", calculatedValue.ToString("C", new CultureInfo("en-US")));

            var offerAmmountLabel = "Accepted Amount";
            if (history.StageID == (int)ReductionQueueStage.Stages.Countered)
            {
                offerAmmountLabel = "Countered Amount";
            }
            body = body.Replace("{offer-amount-label}", offerAmmountLabel);

            return body;
        }

        public static string getUserReductionNegotiationLessThanFivePercentProRataBody(
            ReductionHistory history,
            SF_PatientDetails opportunity,
            string portalLink,
            string notificationSettingsLink,
            SocialMediaInfo mediaInfo,
            string header,
            string reason)
        {
            string body = string.Empty;
            var path = "EmailTemplates" + Path.DirectorySeparatorChar + "ReductionNegotiationLessThanFivePercentProRata.html";
            using StreamReader reader = new StreamReader(path);
            body = reader.ReadToEnd();

            body = SetReductionNegotiationBaseBody(body, history, opportunity, portalLink, notificationSettingsLink, mediaInfo);

            body = body.Replace("{header}", header);
            body = body.Replace("{pro-rata}", history.ProRata.ToString("C", new CultureInfo("en-US")));
            body = body.Replace("{pro-rata-reason}", reason);
            var offerAmmountLabel = "Accepted Amount";
            if (history.StageID == (int)ReductionQueueStage.Stages.Countered)
            {
                offerAmmountLabel = "Countered Amount";
            }
            body = body.Replace("{offer-amount-label}", offerAmmountLabel);

            return body;
        }

        public static string getHighPayoffManagerNameNotificationBody(
            ReductionHistory history,
            SF_PatientDetails opportunity,
            string portalLink,
            string notificationSettingsLink,
            SocialMediaInfo mediaInfo,
            string header,
            string managerName)
        {
            string body = string.Empty;
            var path = "EmailTemplates" + Path.DirectorySeparatorChar + "HighPayoffManagerNameNotification.html";
            using StreamReader reader = new StreamReader(path);
            body = reader.ReadToEnd();

            body = SetReductionNegotiationBaseBody(body, history, opportunity, portalLink, notificationSettingsLink, mediaInfo);

            body = body.Replace("{header}", header);
            body = body.Replace("{high-payoff-manager-name}", managerName);

            var offerAmmountLabel = "Accepted Amount";
            if (history.StageID == (int)ReductionQueueStage.Stages.Countered)
            {
                offerAmmountLabel = "Countered Amount";
            }

            body = body.Replace("{offer-amount-label}", offerAmmountLabel);

            return body;
        }

        public static string getUserReductionNegotiationPaymentBody(ReductionHistory history, SF_PatientDetails opportunity, string portalLink, string notificationSettingsLink, SocialMediaInfo mediaInfo)
        {
            string body = string.Empty;
            var path = "EmailTemplates" + Path.DirectorySeparatorChar + "ReductionNegotiationPayment.html";
            using StreamReader reader = new StreamReader(path);
            body = reader.ReadToEnd();

            var opportunityName = opportunity.Name;
            var requestorName = history.ActingUser.Firstname + " " + history.ActingUser.Lastname;
            var riskManagerName = history.ActingRiskManager.Firstname + " " + history.ActingRiskManager.Lastname;
            var stageName = (ReductionQueueStage.Stages)history.StageID;

            var header = $"Payment Information for {opportunityName}";

            body = body.Replace("{header}", header);
            body = body.Replace("{opportunity-name}", opportunityName);

            body = body.Replace("{requestor-name}", requestorName);
            body = body.Replace("{requestor-email}", history.ActingUser.Email);
            body = body.Replace("{requestor-phone}", history.ActingUser.Phone);

            body = body.Replace("{risk-manager-name}", riskManagerName);
            body = body.Replace("{risk-manager-email}", history.ActingRiskManager.Email);
            body = body.Replace("{risk-manager-phone}", history.ActingRiskManager.Phone);

            body = body.Replace("{offer-amount}", history.OfferAmount.ToString("C", new CultureInfo("en-US")));

            body = body.Replace("{frontend-url}", mediaInfo.FrontendURL);
            body = body.Replace("{portal-link}", portalLink);
            body = body.Replace("{logo-url}", mediaInfo.LogoURL);
            body = body.Replace("{copyright-year}", DateTime.Now.ToString("yyyy"));

            body = body.Replace("{notification-settings-link}", notificationSettingsLink);

            body = getSocialMediaBody(body, mediaInfo);

            return body;
        }

        public static string getPayoffApprovalBody(PayoffQueueItem payoffApprovalDetail, SF_PatientDetails opportunity, string portalLink, string notificationSettingsLink, SocialMediaInfo mediaInfo, string status = "reject")
        {
            string body = string.Empty;
            var path = status == "approve" ? "EmailTemplates" + Path.DirectorySeparatorChar + "PayoffApproval.html" : "EmailTemplates" + Path.DirectorySeparatorChar + "PayoffReject.html";
            using StreamReader reader = new StreamReader(path);
            body = reader.ReadToEnd();

            var opportunityName = opportunity.Name;
            var requestorName = payoffApprovalDetail.Requestor.Firstname + " " + payoffApprovalDetail.Requestor.Lastname;
            var riskManagerName = payoffApprovalDetail.RiskManager.Firstname + " " + payoffApprovalDetail.RiskManager.Lastname;

            var header = status == "approve" ? $"Payoff Amount for {opportunityName} is Ready to View" : $"Payoff Update for {opportunityName}";

            body = body.Replace("{header}", header);
            body = body.Replace("{opportunity-name}", opportunityName);

            body = body.Replace("{requestor-name}", requestorName);
            body = body.Replace("{requestor-email}", payoffApprovalDetail.Requestor.Email);
            body = body.Replace("{requestor-phone}", payoffApprovalDetail.Requestor.Phone);

            body = body.Replace("{risk-manager-name}", riskManagerName);
            body = body.Replace("{risk-manager-email}", payoffApprovalDetail.RiskManager.Email);
            body = body.Replace("{risk-manager-phone}", payoffApprovalDetail.RiskManager.Phone);
            body = body.Replace("{notes}", payoffApprovalDetail.Notes);

            body = body.Replace("{frontend-url}", mediaInfo.FrontendURL);
            body = body.Replace("{portal-link}", portalLink);
            body = body.Replace("{logo-url}", mediaInfo.LogoURL);
            body = body.Replace("{copyright-year}", DateTime.Now.ToString("yyyy"));

            body = body.Replace("{notification-settings-link}", notificationSettingsLink);

            body = getSocialMediaBody(body, mediaInfo);

            return body;
        }

        #endregion

        #region AuthNotifications
        public static string GetInviteUserBody(string inviteUsername, string actionUsername, string actionUserEmail, string userType, string inviteLink, SocialMediaInfo mediaInfo)
        {
            string body = string.Empty;

            var filename = "InviteUserAttorney.html";
            if (userType == "Provider")
            {
                filename = "InviteUserProvider.html";
            }

            var intro = $"{actionUsername} ({actionUserEmail}) has invited you to use the Gain Servicing portal! ";

            if (actionUsername.Equals(inviteUsername))
            {
                intro = string.Empty;
            }

            var path = "EmailTemplates" + Path.DirectorySeparatorChar + filename;
            using StreamReader reader = new StreamReader(path);
            body = reader.ReadToEnd();

            body = body.Replace("{frontend-url}", mediaInfo.FrontendURL);
            body = body.Replace("{portal-link}", mediaInfo.FrontendURL);
            body = body.Replace("{logo-url}", mediaInfo.LogoURL);
            body = body.Replace("{name}", inviteUsername);
            body = body.Replace("{intro}", intro);
            body = body.Replace("{create-link}", inviteLink);
            body = body.Replace("{copyright-year}", DateTime.Now.ToString("yyyy"));

            body = getSocialMediaBody(body, mediaInfo);

            return body;
        }

        #endregion

        #region Private
        private static string getReductionRequestBody(GainNotification notification)
        {
            float specialsNonMedicalAmount = 0;
            var hasInsuranceCoverage = "No";

            if (notification.PayoffRequest.ReductionRequest.MedicalInsuranceAmount >= 0)
            {
                specialsNonMedicalAmount = (notification.PayoffRequest.ReductionRequest.SpecialsAmount - notification.PayoffRequest.ReductionRequest.MedicalInsuranceAmount);
                hasInsuranceCoverage = "Yes";
            }

            var body = $"<table>" +
                            $"<tr>" +
                                $"<td align = 'left' style='padding-right:100px;'>" +
                                    $"Proposed Settlement Amount:" +
                                $"</td>" +
                                $"<td>" +
                                    $"{notification.PayoffRequest.ReductionRequest.ProposedAmount.ToString("C", new CultureInfo("en-US"))}" +
                                $"</td>" +
                            $"</tr>" +
                            $"<tr>" +
                                $"<td>" +
                                    $"Total Specials/Medicals:" +
                                $"</td>" +
                                $"<td>" +
                                    $"{notification.PayoffRequest.ReductionRequest.SpecialsAmount.ToString("C", new CultureInfo("en-US"))}" +
                                $"</td>" +
                            $"</tr>" +
                            $"<tr>" +
                                $"<td>" +
                                    $"Are Specials Covered by Insurance:" +
                                $"</td>" +
                                $"<td>" +
                                    $"{hasInsuranceCoverage}" +
                                $"</td>" +
                            $"</tr>";

            if (notification.PayoffRequest.ReductionRequest.MedicalInsuranceAmount >= 0)
            {
                body +=
                        $"<tr>" +
                            $"<td>" +
                                $"Medical Insurance Amount:" +
                            $"</td>" +
                            $"<td>" +
                                $"{notification.PayoffRequest.ReductionRequest.MedicalInsuranceAmount.ToString("C", new CultureInfo("en-US"))}" +
                            $"</td>" +
                        $"</tr>" +
                        $"<tr>" +
                            $"<td>" +
                                $"Total Specials not covered by Medical Insurance:" +
                            $"</td>" +
                            $"<td>" +
                                $"{specialsNonMedicalAmount.ToString("C", new CultureInfo("en-US"))}" +
                            $"</td>" +
                        $"</tr>";
            }

            if (notification.PayoffRequest.ReductionRequest.isSettled)
            {
                body += $"<tr>" +
                            $"<td>" +
                                $"Did the Case Settle?" +
                            $"</td>" +
                            $"<td>" +
                                $"Yes" +
                            $"</td>" +
                        $"</tr>" +
                        $"<tr>" +
                            $"<td>" +
                                $"Case Settlement Amount:" +
                            $"</td>" +
                            $"<td>" +
                                $"{notification.PayoffRequest.ReductionRequest.SettledAmount.ToString("C", new CultureInfo("en-US"))}" +
                            $"</td>" +
                        $"</tr>";
            }
            else
            {
                body += $"<tr>" +
                            $"<td>" +
                                $"Did the Case Settle?" +
                            $"</td>" +
                            $"<td>" +
                                $"No" +
                            $"</td>" +
                        $"</tr>" +
                        $"<tr>" +
                            $"<td>" +
                                $"Top Offer:" +
                            $"</td>" +
                            $"<td>" +
                                $"{notification.PayoffRequest.ReductionRequest.OfferedAmount.ToString("C", new CultureInfo("en-US"))}" +
                            $"</td>" +
                        $"</tr>";
            }

            body += $"<tr>" +
                            $"<td>" +
                                $"Notes:" +
                            $"</td>" +
                            $"<td>" +
                                $"{Regex.Replace(notification.PayoffRequest.ReductionRequest.Notes, "(.{" + 80 + "})", "$1" + Environment.NewLine)}" +
                            $"</td>" +
                    $"</tr>" +
                    $"</table>";

            return body;
        }

        private static string getSocialMediaBody(string previousBody, SocialMediaInfo mediaInfo)
        {
            var body = previousBody;
            body = body.Replace("{facebook-icon}", mediaInfo.FacebookIconURL);
            body = body.Replace("{facebook-url}", mediaInfo.FacebookURL);
            body = body.Replace("{twitter-icon}", mediaInfo.TwitterIconURL);
            body = body.Replace("{twitter-url}", mediaInfo.TwitterURL);
            body = body.Replace("{linkedin-icon}", mediaInfo.LinkedinIconURL);
            body = body.Replace("{linkedin-url}", mediaInfo.LinkedinURL);

            return body;
        }

        private static string getUserInfo(GainNotification notification)
        {
            return $"{notification.UserFirstName} {notification.UserLastName} ({notification.Username})";
        }
        #endregion
    }
}
