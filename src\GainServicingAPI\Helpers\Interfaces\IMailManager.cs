using GainServicingAPI.Model;
using GainServicingAPI.Model.HttpRequestBodies;
using GainServicingAPI.Model.Salesforce;
using System.Collections.Generic;

namespace GainServicingAPI.Helpers.Interfaces
{
    public interface IMailManager
    {
        void SendPasswordRestLink(string emailAddress, string resetLink);
        void SendVerificationLink(string emailAddress, string name, string link);
        void SendTwoFactorAuthCode(string emailAddress, string code, int expiryInMins, string fromEmail = null);
        void SendUserInvite(string emailAddress, string name, string userType, string link, User actionUser);
        void SendAccountRequestErrorToGain(string newUserEmail, string error);
        void SendLawFirmAddedToGain(AddFirmBody request, User user);
        void SendPcaIntakeVerificationErrorToGain(string opportunityName, string stage, User user);
        void SendIntakeErrorToGain(string opportunityName, string type, User user);
        void SendNotificationToGain(GainNotification notification, User user, int accountId = 0, int sharerID = 0, bool allowInternalNotification = true);
        void SendClaimNotificationToGain(ParseNotification claimNotification);
        void SendClientNotificationToGain(ParseNotification clientNotification);
        void SendEmailTemplate(User To, string type, GainNotification notification, int accountID = 0, List<Attachment> attachments = null);
        void SendShareableLinkEmails(ShareableLinkRequest request, User user);
        void SendReductionNegotiationNotification(string toEmail, ReductionHistory history, SF_PatientDetails opportunity, int accountOwnerID);
        void SendReductionNegotiationAcceptNotification(string toEmail, ReductionHistory history, SF_PatientDetails opportunity, int accountOwnerID, Attachment attachment = null, string riskManagerEmail = "");
        void SendReductionNegotiationLessThanSentOutNotification(string toEmail, ReductionHistory history, SF_PatientDetails opportunity, string reason);
        void SendReductionNegotiationLessThanFivePercentProRata(string toEmail, ReductionHistory history, SF_PatientDetails opportunity, string reason);
        void SendHighPayoffManagerNameNotification(string toEmail, ReductionHistory history, SF_PatientDetails opportunity, string managerName);
        void SendPayoffApprovalNotification(string toEmail, PayoffQueueItem largePayoffDetail, SF_PatientDetails opportunity, int accountOwnerID, string status = "reject");
        void SendReductionNegotiationPaymentNotification(string toEmail, ReductionHistory history, SF_PatientDetails opportunity, int accountOwnerID, Attachment attachment = null);
    }
}