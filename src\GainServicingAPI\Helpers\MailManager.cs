﻿using Amazon;
using Amazon.SimpleEmail;
using Amazon.SimpleEmail.Model;
using GainServicingAPI.DAL.Interfaces;
using GainServicingAPI.Database;
using GainServicingAPI.Helpers.Interfaces;
using GainServicingAPI.Logging;
using GainServicingAPI.Model;
using GainServicingAPI.Model.HttpRequestBodies;
using GainServicingAPI.Model.Salesforce;
using GainServicingAPI.Model.StaticValues;
using GainServicingAPI.Services.Interfaces;
using Microsoft.Extensions.Configuration;
using MimeKit;
using System;
using System.Collections.Generic;
using System.IO;
using System.Threading.Tasks;
using Attachment = GainServicingAPI.Model.Attachment;

namespace GainServicingAPI.Helpers
{
    public class MailManager : IMailManager
    {

        private readonly IConfiguration Configuration;
        private readonly IAPILogger Logger;
        private readonly DbConnection Connector;
        private readonly IUserDAL UserDAL;
        private readonly IUserNotificationDAL UserNotificationDAL;
        private readonly IPayoffService PayoffService;
        private readonly string smtp_user;
        private readonly string smtp_password;
        private readonly string providerNotificationEmail;
        private readonly string attorneyNotificationEmail;
        private readonly string reductionRequestEmail;
        private readonly string requestAccountErrorEmail;
        private readonly string frontEndUrl;
        private readonly SocialMediaInfo SocialMediaInfo;
        private readonly IAccountDAL AccountDAL;

        public MailManager(
            IConfiguration config,
            IAPILogger logger,
            DbConnection connector,
            IUserDAL userDAL,
            IUserNotificationDAL userNotificationDAL,
            IPayoffService payoffService,
            IAccountDAL accountDAL)
        {
            this.Configuration = config;
            this.Logger = logger;
            this.Connector = connector;
            this.UserDAL = userDAL;
            this.UserNotificationDAL = userNotificationDAL;
            this.PayoffService = payoffService;

            this.smtp_password = SecurePasswordManager.DecryptPasswordWithEnvironment(this.Configuration["SMTP:AWSSecret"]);
            this.smtp_user = SecurePasswordManager.DecryptPasswordWithEnvironment(this.Configuration["SMTP:AWSKey"]);
            this.providerNotificationEmail = this.Configuration["SMTP:ProviderNotificationEmail"];
            this.attorneyNotificationEmail = this.Configuration["SMTP:AttorneyNotificationEmail"];
            this.reductionRequestEmail = this.Configuration["SMTP:ReductionRequestEmail"];
            this.requestAccountErrorEmail = this.Configuration["SMTP:RequestAccountErrorEmail"];
            this.frontEndUrl = this.Configuration["URLS:FrontEndUrl"];

            this.SocialMediaInfo = new SocialMediaInfo();
            this.SocialMediaInfo.FrontendURL = this.frontEndUrl;
            this.SocialMediaInfo.FacebookIconURL = this.Configuration["Social:Facebook:Icon"];
            this.SocialMediaInfo.FacebookURL = this.Configuration["Social:Facebook:URL"];
            this.SocialMediaInfo.TwitterIconURL = this.Configuration["Social:Twitter:Icon"];
            this.SocialMediaInfo.TwitterURL = this.Configuration["Social:Twitter:URL"];
            this.SocialMediaInfo.LinkedinIconURL = this.Configuration["Social:Linkedin:Icon"];
            this.SocialMediaInfo.LinkedinURL = this.Configuration["Social:Linkedin:URL"];
            this.SocialMediaInfo.LogoURL = this.Configuration["Logo:URL"];
            this.AccountDAL = accountDAL;
        }

        /// <summary>
        /// Sends email with a password reset link
        /// </summary>
        /// <param name="emailAddress"></param>
        /// <param name="resetLink"></param>
        public void SendPasswordRestLink(string emailAddress, string resetLink)
        {
            var encodedLink = new UriBuilder(resetLink);
            string subject = "Gain Servicing Password Reset";
            string body = $"<p>Please use the link below to reset your Gain Servicing password.</p><a href='{encodedLink}'>Reset Password Here</a>";

            var task = Task.Run(async () => await this.SendEmail(emailAddress, subject, null, body));
            task.Wait();
        }

        public void SendVerificationLink(string emailAddress, string name, string link)
        {
            var encodedLink = new UriBuilder(link);
            string subject = "Gain Servicing Portal Account Verification";
            string body = $"Hi {name},\n" +
                $"Your requested gain portal user account has been created:\n\n" +
                $"Please verify your email by clicking the link below\n\n" +
                $"<a href='{encodedLink}'>Verify Account Here</a>\n\n" +
                $"Portal URL: {this.frontEndUrl}\n" +
                $"Username: {emailAddress}\n\n" +
                $"We greatly value your input as a client and user of our service.  If you have any\n" +
                $"questions or would like to provide feedback or suggestions to improve the\n" +
                $"portal, please contact gain technical <NAME_EMAIL>.\n\n\n" +
                $"Thanks,\n\n\n" +
                $"Gain Support Team\n" +
                $"<EMAIL>";

            var task = Task.Run(async () => await this.SendEmail(emailAddress, subject, null, body));
            task.Wait();
        }

        public void SendTwoFactorAuthCode(string emailAddress, string code, int expiryInMins, string fromEmail = null)
        {
            string subject = "Gain Servicing Portal Two Factor Authentication Code";
            string body = "Your Two-Factor Authentication Code is: " + code;
            var task = Task.Run(async () => await this.SendEmail(emailAddress, subject, null, body, "", fromEmail));
            task.Wait();
        }
        public void SendUserInvite(string emailAddress, string name, string userType, string link, User actionUser)
        {
            var encodedLink = new UriBuilder(link);
            var actionUserName = $"{actionUser.Firstname} {actionUser.Lastname}";
            string subject = "Gain Servicing Portal Invitation";
            string body = EmailFormatter.GetInviteUserBody(name, actionUserName, actionUser.Email, userType, encodedLink.ToString(), this.SocialMediaInfo);

            var task = Task.Run(async () => await this.SendEmail(emailAddress, subject, null, body));
            task.Wait();
        }

        public void SendAccountRequestErrorToGain(string newUserEmail, string error)
        {
            var subject = $"Account Request Action Needed: {error}";
            var body = $@"<p>Account Request Action Needed:</p>" +
                            $" <table> " +
                                $"<tr>" +
                                    $"<td align = 'left' style='padding-right:50px;'>" +
                                        $"Email:" +
                                    $"</td>" +
                                    $"<td>" +
                                        $"{newUserEmail}" +
                                    $"</td>" +
                                $"</tr>" +
                                $"<tr>" +
                                    $"<td>" +
                                        $"Error:" +
                                    $"</td>" +
                                    $"<td>" +
                                        $"{error}" +
                                    $"</td>" +
                                $"</tr>" +
                            $"</table>";

            var task = Task.Run(async () => await this.SendEmail(this.requestAccountErrorEmail, subject, null, body));
            task.Wait();

        }

        public void SendLawFirmAddedToGain(AddFirmBody request, User user)
        {
            var subject = $"New Law Firm {request.LawFirmName} has been added for {request.OpportunityName} by {user.AccountOwner}";
            var body = $@"<p>New Law Firm {request.LawFirmName} has been added for {request.OpportunityName} by {user.AccountOwner}." +
                       $" Please reach out to confirm details and add/correct in Salesforce as needed, and check to make sure this account is not a duplicate.</p>";

            var task = Task.Run(async () => await this.SendEmail(this.requestAccountErrorEmail, subject, null, body));
            task.Wait();
        }

        public void SendPcaIntakeVerificationErrorToGain(string opportunityName, string stage, User user)
        {
            var subject = $"Client Intake: PCA Opportunity Verification Error";
            var body = EmailFormatter.getPcaIntakeValidationError(opportunityName, stage, user);

            var task = Task.Run(async () => await this.SendEmail(this.attorneyNotificationEmail, subject, null, body));
            task.Wait();
        }

        public void SendIntakeErrorToGain(string opportunityName, string type, User user)
        {
            var subject = $"Plaintiff Intake Failed";
            var body = EmailFormatter.getPcaIntakeValidationError(opportunityName, type, user);

            var task = Task.Run(async () => await this.SendEmail(this.requestAccountErrorEmail, subject, null, body));
            task.Wait();
        }

        /// <summary>
        /// Sends email notification to gain servicing
        /// Types:
        ///     NewPatient       -> When a new patient is manually created
        ///     NewPatientIntake -> When a new patient is created with an intake form
        ///     PatientReferral  -> When a patient referral form is submitted
        /// </summary>
        /// <param name="notification"></param>
        public void SendNotificationToGain(GainNotification notification, User user, int accountId = 0, int sharerID = 0, bool allowInternalNotification = true)
        {
            // Not sending the email
            if (notification.Type == "NoLongerRepresent")
            {
                return;
            }

            string subject = "";
            string body = "";
            var recipient = "";

            var portalLink = $"{this.frontEndUrl}plaintiffs/plaintiff-detail/{notification.OppId}/{accountId}";

            switch (notification.Type)
            {
                case "NewPatient":

                    subject = $"New Patient {notification.OppFirstName} {notification.OppLastName} Entered for {notification.OwnerName}";
                    recipient = this.providerNotificationEmail;
                    body = EmailFormatter.getPatientIntakeBody(portalLink, notification);
                    break;

                case "NewPatientIntake":

                    subject = $"New Patient Intake {notification.OppFirstName} {notification.OppLastName} Uploaded for {notification.OwnerName}";
                    recipient = this.providerNotificationEmail;
                    body = EmailFormatter.getPatientIntakeBody(portalLink, notification);
                    break;

                case "NewClientIntake":

                    subject = $"New Client Intake {notification.OppFirstName} {notification.OppLastName} Uploaded for {notification.OwnerName}";
                    recipient = this.attorneyNotificationEmail;
                    body = EmailFormatter.getClientIntakeBody(portalLink, notification);
                    break;

                case "PatientReferral":

                    subject = $"New Referral {notification.OppFirstName} {notification.OppLastName} Uploaded by {notification.OwnerName}";
                    recipient = this.providerNotificationEmail;
                    body = EmailFormatter.getPatientIntakeBody(portalLink, notification);
                    break;

                case "AttorneyAddAttorney":

                    subject = $"New Attorney {notification.OppFirstName} {notification.OppLastName} Added for {notification.FirmName}";
                    recipient = this.attorneyNotificationEmail;

                    body = EmailFormatter.GetAttorneyAddAttorneyBody(portalLink, notification);
                    break;

                case "ProviderAddAttorney":

                    subject = $"New Attorney {notification.OppFirstName} {notification.OppLastName} Added for {notification.FirmName} by {notification.OwnerName}";
                    recipient = this.providerNotificationEmail;

                    body = EmailFormatter.GetProviderAddAttorneyBody(portalLink, notification);
                    break;

                case "AttorneyPCA":

                    subject = $"New PCA Requested for {notification.OppFirstName} {notification.OppLastName}";
                    recipient = this.requestAccountErrorEmail;

                    body = EmailFormatter.getAttorneyPCA(portalLink, notification);
                    break;

                case "AddAttorneyPCA":

                    subject = $"Additional PCA Requested for {notification.OppFirstName} {notification.OppLastName}";
                    recipient = this.requestAccountErrorEmail;
                    body = EmailFormatter.getAddAttorneyPCA(portalLink, notification);

                    var logPCA = Task.Run(async () =>
                    {
                        var logEventID = await this.Logger.LogSystemEvent(Actions.REQUEST_CASH_ADVANCE, notification.OppId, user?.UserID ?? 0, user?.AccountOwnerID ?? 0, sharerID);
                        await this.Logger.LogSystemEventDetails(logEventID, null, new
                        {
                            CashAdvanceAmount = notification.CashAdvanceAmount
                        }, new List<string>() { "CashAdvanceAmount" });
                    });
                    logPCA.Wait();

                    this.SendUserNotifications(notification, NotificationID.TYPE_CASH_ADVANCE, user, accountId);

                    break;

                case "ProviderOpportunityComment":
                    recipient = this.providerNotificationEmail;
                    subject = $"Message from {notification.OwnerName} about the {notification.OppFirstName} {notification.OppLastName} case";

                    body = EmailFormatter.getOpportunityCommentBody(portalLink, notification, "Provider");
                    break;

                case "AttorneyOpportunityComment":
                    recipient = this.attorneyNotificationEmail;
                    subject = $"Message from {notification.OwnerName} about the {notification.OppFirstName} {notification.OppLastName} case";

                    body = EmailFormatter.getOpportunityCommentBody(portalLink, notification, "Attorney");
                    break;

                case "TreatmentComplete":
                    recipient = this.providerNotificationEmail;
                    subject = $"Final Documents Uploaded & Treatment Complete for {notification.OppFirstName} {notification.OppLastName} indicated by {notification.OwnerName}";

                    body = EmailFormatter.getTreatmentCompleteBody(portalLink, notification);
                    break;

                case "TreatmentResumed":
                    recipient = this.providerNotificationEmail;
                    subject = $"Treatment INCOMPLETE for {notification.OppFirstName} {notification.OppLastName} indicated by {notification.OwnerName}";

                    body = EmailFormatter.getTreatmentResumedBody(portalLink, notification);
                    break;

                case "AttorneyMedicalFunding":
                    recipient = this.attorneyNotificationEmail;
                    subject = $"New Medical Treatment Requested for {notification.OppFirstName} {notification.OppLastName}";

                    body = EmailFormatter.getAttorneyMedicalFundingBody(portalLink, notification);

                    var logMedicalFunding = Task.Run(async () =>
                    {
                        var logEventID = await this.Logger.LogSystemEvent(Actions.REQUEST_MEDICAL_TREATMENT, notification.OppId, user?.UserID ?? 0, user?.AccountOwnerID ?? 0, sharerID);
                        await this.Logger.LogSystemEventDetails(logEventID, null, new
                        {
                            ProviderSpecialty = notification.SelectedProvider.ProviderSpecialty,
                        }, new List<string>() { "ProviderSpecialty" },
                            "Request for: " + notification.OppFirstName + " " + notification.OppLastName + "; Additional comments: " + notification.SelectedProvider.AdditionalComments
                            );
                    });
                    logMedicalFunding.Wait();

                    this.SendUserNotifications(notification, NotificationID.TYPE_MEDICAL_TREATMENT, user, accountId);
                    break;

                case "AttorneyRequestPayoff":
                    recipient = this.reductionRequestEmail;

                    subject = $"Payoff Amount Requested for {notification.OppFirstName} {notification.OppLastName} indicated by {notification.OwnerName}";
                    if (notification.PayoffRequest.ErrorCode != null)
                    {
                        subject = $"PAYOFF REQUEST FAILED for {notification.OppFirstName} {notification.OppLastName} indicated by {notification.OwnerName}";
                    }

                    if (notification.PayoffRequest.ReductionRequest.ProposedAmount > 0)
                    {
                        subject = $"Reduction Request for {notification.OppFirstName} {notification.OppLastName} from {notification.OwnerName}";
                    }

                    body = EmailFormatter.getPayoffRequestBody(portalLink, notification);
                    break;

                case "RequestDocuments":
                    if (notification.UserType == "Attorney")
                    {
                        recipient = this.attorneyNotificationEmail;
                    }

                    if (notification.UserType == "Provider")
                    {
                        recipient = this.providerNotificationEmail;
                    }

                    subject = $@"Documents Requested for {notification.OppFirstName} {notification.OppLastName} by {notification.OwnerName}";

                    body = EmailFormatter.getRequestDocumentsBody(portalLink, notification);

                    var logRequestDoc = Task.Run(async () =>
                    {
                        var logEventID = await this.Logger.LogSystemEvent(Actions.REQUEST_DOCUMENTS, notification.OppId, user?.UserID ?? 0, user?.AccountOwnerID ?? 0, sharerID);

                        await this.Logger.LogSystemEventDetails(logEventID, null, new
                        {
                            DocumentTypes = notification.DocumentTypes,
                            Notes = notification.Notes
                        }, new List<string>() { "DocumentTypes", "Notes" });
                    });
                    logRequestDoc.Wait();

                    this.SendUserNotifications(notification, NotificationID.TYPE_REQUEST_DOCUMENTS, user, accountId);
                    break;

                case "NoLongerRepresent":
                    recipient = this.attorneyNotificationEmail;
                    subject = $@"Client {notification.OppFirstName} {notification.OppLastName} is NO LONGER REPRESENTED by {notification.OwnerName}";

                    body = EmailFormatter.getNoLongerRepresentBody(portalLink, notification);
                    break;

                case "AttorneyRepresent":
                    recipient = this.attorneyNotificationEmail;
                    subject = $@"Client {notification.OppFirstName} {notification.OppLastName} is NOW REPRESENTED by {notification.OwnerName}";

                    body = EmailFormatter.getAttorneyRepresentBody(portalLink, notification);
                    break;

                case "CaseClosed":
                    recipient = this.attorneyNotificationEmail;
                    subject = $@"Client {notification.OppFirstName} {notification.OppLastName}'s case has been marked CLOSED";

                    body = EmailFormatter.getCaseClosedBody(portalLink, notification);
                    break;

                case "LawFirmChanged":
                    recipient = this.attorneyNotificationEmail;
                    subject = $"ATTENTION: Law Firm Info For {notification.OppFirstName} {notification.OppLastName} Changed By {notification.OwnerName}";

                    body = EmailFormatter.getLawfirmChangedBody(portalLink, notification);
                    break;

                case "FileUpload":
                    if (notification.UserType == "Attorney")
                    {
                        recipient = this.attorneyNotificationEmail;
                    }

                    if (notification.UserType == "Provider")
                    {
                        recipient = this.providerNotificationEmail;
                    }

                    subject = $"Gain Portal {EmailFormatter.GetDocumentTypesString(notification)} file for {notification.OppFirstName} {notification.OppLastName} has been uploaded for {notification.UserType} {notification.OwnerName}";
                    body = EmailFormatter.getFileUploadBody(portalLink, notification);
                    break;

                case "ProviderMapMedicalFunding":
                    recipient = this.attorneyNotificationEmail;
                    subject = $"New Provider Selected for Medical Treatment of {notification.OppFirstName} {notification.OppLastName}";

                    body = EmailFormatter.getProviderMapMedicalFundingBody(portalLink, notification);

                    var logProviderMapMedFunding = Task.Run(async () =>
                    {
                        var logEventID = await this.Logger.LogSystemEvent(Actions.PROVIDER_MAP_SUBMIT, notification.OppId, user?.UserID ?? 0, user?.AccountOwnerID ?? 0, sharerID);
                        await this.Logger.LogSystemEventDetails(logEventID, null, new
                        {
                            ProviderName = notification.SelectedProvider.ProviderName,
                            ProviderSpecialty = notification.SelectedProvider.ProviderSpecialty,
                            ProviderLocation = notification.SelectedProvider.ProviderLocation
                        }, new List<string>() { "ProviderName", "ProviderSpecialty", "ProviderLocation" },
                            "Request for: " + notification.SelectedProvider.OpportunityName + "; Additional comments: " + notification.SelectedProvider.AdditionalComments
                            );
                    });

                    logProviderMapMedFunding.Wait();

                    this.SendUserNotifications(notification, NotificationID.TYPE_MEDICAL_TREATMENT, user, accountId);
                    break;

                case "RequestAccountError":
                    recipient = this.Configuration["SMTP:FromEmail"];
                    subject = $"Request Account Action Required: {notification.ErrorMessage}";
                    break;

                case "RequestTransportation":
                    recipient = this.providerNotificationEmail;
                    subject = $"Medical Transportation Requested for {notification.OppFirstName} {notification.OppLastName} by {notification.OwnerName}";
                    body = EmailFormatter.getRequestTransportationBody(portalLink, notification);

                    var logTransportationRequest = Task.Run(async () =>
                    {
                        var logEventID = await this.Logger.LogSystemEvent(Actions.REQUEST_TRANSPORTATION, notification.OppId, user?.UserID ?? 0, user?.AccountOwnerID ?? 0, sharerID);
                        await this.Logger.LogSystemEventDetails(logEventID, null, notification.RequestTransportation, null);
                    });

                    logTransportationRequest.Wait();

                    this.SendUserNotifications(notification, NotificationID.TYPE_REQUEST_TRANSPORTATION, user, accountId);
                    break;

                case "RequestPharmacyCard":
                    recipient = this.providerNotificationEmail;
                    subject = $"Pharmacy Card Requested for {notification.OppFirstName} {notification.OppLastName} by {notification.OwnerName}";
                    body = EmailFormatter.getRequestPharmacyCardBody(portalLink, notification);

                    var logPCRequest = Task.Run(async () =>
                    {
                        var logEventID = await this.Logger.LogSystemEvent(Actions.REQUEST_PHARMACY_CARD, notification.OppId, user?.UserID ?? 0, user?.AccountOwnerID ?? 0, sharerID);
                        await this.Logger.LogSystemEventDetails(logEventID, null, notification.RequestPharmacyCard, null);
                    });

                    logPCRequest.Wait();

                    this.SendUserNotifications(notification, NotificationID.TYPE_REQUEST_PHARMACY_CARD, user, accountId);
                    break;

                default:
                    break;
            }

            // If Internal mails are not allowed then return.
            if (!allowInternalNotification)
            {
                return;
            }

            if (notification.Attachments != null && notification.Attachments.Count > 0)
            {
                foreach (var a in notification.Attachments)
                {
                    body += $"<p>Salesforce Attachment Link:<br><br>{a.AttachmentLink}</p>";
                }
            }

            var task = Task.Run(async () => await this.SendEmail(recipient, subject, null, body));
            task.Wait();
        }

        /// <summary>
        /// Sends claims email notification to gain servicing
        /// Types:
        ///     ParseSuccess -> When a claim was successfully parsed
        ///     ParseFail -> When a claim was not successfully parsed
        /// </summary>
        /// <param name="claimNotification"></param>
        public void SendClaimNotificationToGain(ParseNotification claimNotification)
        {
            string subject = "";
            string body = "";
            var recipient = this.attorneyNotificationEmail;

            switch (claimNotification.Type)
            {
                case "ParseSuccess":
                    subject = $@"HCFA Parsing SUCCEEDED for {claimNotification.OppName}";
                    body = EmailFormatter.getHCFAParseSuccessBody(claimNotification);
                    break;
                case "ParseFailure":
                    subject = $@"HCFA Parsing FAILED for {claimNotification.OppName}";
                    body = EmailFormatter.getHCFAParseErrorBody(claimNotification);
                    break;
                default:
                    break;
            }
            var task = Task.Run(async () => await this.SendEmail(recipient, subject, null, body));
            task.Wait();
        }

        /// <summary>
        /// Sends claims email notification to gain servicing
        /// Types:
        ///     ParseSuccess -> When a client intake was successfully parsed
        ///     ParseFail -> When a client intake was not successfully parsed
        /// </summary>
        /// <param name="clientNotification"></param>
        public void SendClientNotificationToGain(ParseNotification clientNotification)
        {
            string subject = "";
            string body = "";
            var recipient = this.attorneyNotificationEmail;

            switch (clientNotification.Type)
            {
                case "ParseSuccess":
                    subject = $@"Client Intake Parsing SUCCEEDED for {clientNotification.OppName}";
                    body = EmailFormatter.getClientIntakeParseSuccessBody(clientNotification);
                    break;
                case "ParseFailure":
                    subject = $@"Client Intake Parsing FAILED for {clientNotification.OppName}";
                    body = EmailFormatter.getClientIntakeParseErrorBody(clientNotification);
                    break;
                default:
                    break;
            }
            var task = Task.Run(async () => await this.SendEmail(recipient, subject, null, body));
            task.Wait();
        }

        /// <summary>
        /// Sends an email of specified type to the specified user.
        /// </summary>
        /// <param name="to"></param>
        /// <param name="type"></param>
        /// <param name="notification"></param>
        /// <param name="accountID"></param>
        public async void SendEmailTemplate(User to, string type, GainNotification notification, int accountID = 0, List<Model.Attachment> attachments = null)
        {
            if ((to.Email.Equals(notification.Username) && !notification.SendToActionUser) || !to.Enabled)
            {
                return;
            }

            var body = "";
            var subject = "";
            var account = await this.AccountDAL.GetAccountByIDAsync(accountID);

            var requestType = "";

            var portalLink = $"{this.frontEndUrl}plaintiffs/plaintiff-detail/{notification.OppId}/{account.SalesforceID}";

            var notificationSettingsLink = $"{this.frontEndUrl}notifications";

            switch (type)
            {
                case "ClientIntake":
                    if (to.UserTypeID == UserTypeID.ADMIN)
                    {
                        subject = $"Gain Portal - new {notification.Type} client {notification.OppFirstName} {notification.OppLastName} for {notification.OwnerName}";
                    }
                    else
                    {
                        subject = $"Gain Portal - new {notification.Type} client {notification.OppFirstName} {notification.OppLastName}";
                    }
                    body = EmailFormatter.getUserClientIntakeBody(notification, portalLink, notificationSettingsLink, this.SocialMediaInfo);

                    if (notification.Type == "Cash Advance")
                    {
                        requestType = "Intake";
                    }
                    else if (notification.Type == "Medical Funding")
                    {
                        requestType = "CaseManagement";

                    }
                    break;

                case "PatientIntake":
                    if (to.UserTypeID == UserTypeID.ADMIN)
                    {
                        subject = $"Gain Portal - new {notification.Type} patient {notification.OppFirstName} {notification.OppLastName} for {notification.OwnerName}";
                    }
                    else if (to.UserTypeID == UserTypeID.ATTORNEY)
                    {
                        notification.UserType = "Attorney";
                        subject = $"Gain Portal - {notification.OwnerName} has added a new Client {notification.OppFirstName} {notification.OppLastName}";

                        if (to.UserID == notification.AssignedAttorneyID)
                        {
                            subject = $"Gain Portal - {notification.OwnerName} has assigned a new Client {notification.OppFirstName} {notification.OppLastName} to you";
                        }
                    }
                    else
                    {
                        subject = $"Gain Portal - new {notification.Type} patient {notification.OppFirstName} {notification.OppLastName}";
                    }
                    body = EmailFormatter.getUserPatientIntakeBody(notification, portalLink, notificationSettingsLink, this.SocialMediaInfo);
                    requestType = "CaseManagement";
                    break;

                case "FileUpload":
                    subject = $"Gain Portal - {EmailFormatter.GetDocumentTypesString(notification)} file for {notification.OppFirstName} {notification.OppLastName} has been uploaded for {notification.OwnerName}";
                    body = EmailFormatter.getUserFileUploadBody(notification, portalLink, notificationSettingsLink, this.SocialMediaInfo);
                    requestType = "Intake";
                    break;

                case "CaseStatus":
                    subject = $"Gain Portal - client {notification.OppFirstName} {notification.OppLastName} case status has changed from {notification.CaseStatusUpdate.FromStatus} to {notification.CaseStatusUpdate.ToStatus}";
                    body = EmailFormatter.getUserCaseStatusBody(notification, portalLink, notificationSettingsLink, this.SocialMediaInfo);
                    requestType = "RiskManagement";
                    break;

                case "AddAttorneyPCA":
                    subject = $"Gain Portal - Additional PCA has been Requested for Client {notification.OppFirstName} {notification.OppLastName} by {notification.OwnerName}";
                    body = EmailFormatter.getUserCashAdvanceBody(notification, portalLink, notificationSettingsLink, this.SocialMediaInfo);
                    requestType = "Intake";
                    break;

                case "RequestDocuments":
                    subject = $"Documents Requested for {notification.OppFirstName} {notification.OppLastName} by {notification.OwnerName}";
                    body = EmailFormatter.getUserRequestDocumentBody(notification, portalLink, notificationSettingsLink, this.SocialMediaInfo);
                    requestType = "RiskManagement";
                    break;

                case "AttorneyRequestPayoff":
                    subject = $"Gain Portal - Payoff Amount for {notification.OppFirstName} {notification.OppLastName} Requested by {notification.OwnerName}";

                    if (notification.PayoffRequest.ReductionRequest.ProposedAmount > 0)
                    {
                        subject = $"Reduction Requested for {notification.OppFirstName} {notification.OppLastName} by {notification.OwnerName}";
                    }
                    body = EmailFormatter.getUserPayoffRequestBody(notification, portalLink, notificationSettingsLink, this.SocialMediaInfo);
                    requestType = "RiskManagement";
                    break;

                case "AttorneyMedicalFunding":
                    subject = $"New Medical Treatment Requested for {notification.OppFirstName} {notification.OppLastName}";
                    body = EmailFormatter.getUserMedicalFundingBody(notification, portalLink, notificationSettingsLink, this.SocialMediaInfo);
                    requestType = "CaseManagement";
                    break;

                case "TreatmentComplete":
                case "TreatmentResumed":
                    subject = $"Gain Portal - {notification.OwnerName} has indicated that all documents have been uploaded for {notification.OppFirstName} {notification.OppLastName}";
                    if (type == "TreatmentResumed")
                    {
                        subject = $"Gain Portal - {notification.OwnerName} has indicated treatment INCOMPLETE for {notification.OppFirstName} {notification.OppLastName}";
                    }
                    body = EmailFormatter.getUserTreatmentCompleteBody(notification, portalLink, notificationSettingsLink, this.SocialMediaInfo);
                    requestType = "RiskManagement";
                    break;

                case "ClaimUpload":
                    subject = $"Gain Portal - New Claim Uploaded for {notification.OppFirstName} {notification.OppLastName} by {notification.OwnerName}";
                    body = EmailFormatter.getUserClaimUploadBody(notification, portalLink, notificationSettingsLink, this.SocialMediaInfo);
                    requestType = "CaseManagement";
                    break;

                case "OpportunityComment":
                    subject = $"Gain Portal - New Message Added to the {notification.OppFirstName} {notification.OppLastName} Case by {notification.UserFirstName} {notification.UserLastName} from {notification.OwnerName}";
                    body = EmailFormatter.getUserOpportunityCommentBody(notification, portalLink, notificationSettingsLink, this.SocialMediaInfo);
                    requestType = "RiskManagement";
                    break;

                case "OpportunityUpdate":
                    subject = $"Gain Portal - {notification.OppFirstName} {notification.OppLastName} Case Updated";
                    body = EmailFormatter.getUserOpportunityUpdateBody(notification, portalLink, notificationSettingsLink, this.SocialMediaInfo);
                    requestType = "RiskManagement";
                    break;

                case "ProviderMapMedicalFunding":
                    subject = $"New Provider Selected for Medical Treatment of {notification.OppFirstName} {notification.OppLastName}";
                    body = EmailFormatter.getUserProviderMapSubmitBody(notification, portalLink, notificationSettingsLink, this.SocialMediaInfo);
                    requestType = "CaseManagement";
                    break;

                case "RequestTransportation":
                    subject = $"Medical Transportation Requested for {notification.OppFirstName} {notification.OppLastName}";
                    body = EmailFormatter.getUserRequestTransportationBody(notification, portalLink, notificationSettingsLink, this.SocialMediaInfo);
                    requestType = "CaseManagement";
                    break;

                case "RequestPharmacyCard":
                    subject = $"Pharmacy Card Requested for {notification.OppFirstName} {notification.OppLastName}";
                    body = EmailFormatter.getUserRequestPharmacyCardBody(notification, portalLink, notificationSettingsLink, this.SocialMediaInfo);
                    requestType = "CaseManagement";
                    break;

                default:
                    break;
            }

            if (attachments != null)
            {
                string message = "Login to the Gain Portal to see this document.";

                if (attachments.Count > 1)
                {
                    message = "Login to the Gain Portal to see these documents.";
                }

                body = body.Replace("{attachment-message}", message);
            }

            var task = Task.Run(async () => await this.SendRawEmail(to.Email, subject, body, attachments, requestType));
        }

        public void SendShareableLinkEmails(ShareableLinkRequest request, User user)
        {
            var subject = request.Subject ?? "Gain Portal Access Link";
            var notificationSettingsLink = $"{this.frontEndUrl}notifications";

            var body = EmailFormatter.getUserShareableLinkBody(request, user, notificationSettingsLink, this.SocialMediaInfo);

            if (!request.ToEmails.Exists(e => e.Equals(user.Email)))
            {
                request.ToEmails.Add(user.Email);
            }

            foreach (var email in request.ToEmails)
            {
                var task = Task.Run(async () => await this.SendRawEmail(email, subject, body));
            }
        }

        public void SendReductionNegotiationNotification(string toEmail, ReductionHistory history, SF_PatientDetails opportunity, int accountOwnerID)
        {
            var portalLink = $"{this.frontEndUrl}plaintiffs/plaintiff-detail/{opportunity.id}/{opportunity.AccountId}";
            var notificationSettingsLink = $"{this.frontEndUrl}notifications";
            var subject = "Gain Portal - Reduction Request Update";

            var body = EmailFormatter.getUserReductionNegotiationBody(history, opportunity, portalLink, notificationSettingsLink, this.SocialMediaInfo);

            var task = Task.Run(async () => await this.SendRawEmail(toEmail, subject, body, null, "RiskManagement"));
        }

        private string getPortalLink(SF_PatientDetails opportunity)
        {
            return $"{this.frontEndUrl}plaintiffs/plaintiff-detail/{opportunity.id}/{opportunity.AccountId}";
        }

        private string getNotificationSettings()
        {
            return $"{this.frontEndUrl}notifications";
        }

        public void SendReductionNegotiationAcceptNotification(string toEmail, ReductionHistory history, SF_PatientDetails opportunity, int accountOwnerID, Model.Attachment attachment = null, string riskManagerEmail = "")
        {
            var portalLink = this.getPortalLink(opportunity);
            var notificationSettingsLink = this.getNotificationSettings();
            var subject = "Gain Portal - Reduction Request Accepted";

            var body = EmailFormatter.getUserReductionNegotiationAcceptBody(history, opportunity, portalLink, notificationSettingsLink, this.SocialMediaInfo);
            if (attachment != null)
            {
                List<Model.Attachment> attachments = new List<Attachment> { attachment };
                var task = Task.Run(async () => await this.SendRawEmail(toEmail, subject, body, attachments, "RiskManagement", true, riskManagerEmail));
            }
            else
            {
                var task = Task.Run(async () => await this.SendRawEmail(toEmail, subject, body, null, "RiskManagement", true, riskManagerEmail));
            }

        }

        public void SendReductionNegotiationLessThanSentOutNotification(string toEmail, ReductionHistory history, SF_PatientDetails opportunity, string reason)
        {
            var portalLink = this.getPortalLink(opportunity);
            var notificationSettingsLink = this.getNotificationSettings();
            var header = "Reduction Request Accepted Less Than Cash Out";
            if (history.StageID == (int)ReductionQueueStage.Stages.Countered)
            {
                header = "Reduction Request Countered Less Than Cash Out";
            }
            var subject = $"Gain Portal - {header}";

            // Get percentage non serviced from PayoffService
            var percentageNonServiced = 0.0f;
            var task = Task.Run(async () =>
            {
                percentageNonServiced = await this.PayoffService.CalculatePercentageNonServiced(opportunity.id);
            });
            task.Wait();

            var body = EmailFormatter.getUserReductionNegotiationLessThanSentOutBody(
                history,
                opportunity,
                portalLink,
                notificationSettingsLink,
                this.SocialMediaInfo,
                header,
                reason,
                percentageNonServiced);

            Task.Run(async () => await this.SendRawEmail(toEmail, subject, body));
        }

        public void SendReductionNegotiationLessThanFivePercentProRata(string toEmail, ReductionHistory history, SF_PatientDetails opportunity, string reason)
        {
            var portalLink = this.getPortalLink(opportunity);
            var notificationSettingsLink = this.getNotificationSettings();
            var header = "Reduction Request Accepted Less Than Pro Rata";
            if (history.StageID == (int)ReductionQueueStage.Stages.Countered)
            {
                header = "Reduction Request Countered Less Than Pro Rata";
            }
            var subject = $"Gain Portal - {header}";

            var body = EmailFormatter.getUserReductionNegotiationLessThanFivePercentProRataBody(history, opportunity, portalLink, notificationSettingsLink, this.SocialMediaInfo, header, reason);

            var task = Task.Run(async () => await this.SendRawEmail(toEmail, subject, body));
        }

        public void SendHighPayoffManagerNameNotification(string toEmail, ReductionHistory history, SF_PatientDetails opportunity, string managerName)
        {
            var portalLink = this.getPortalLink(opportunity);
            var notificationSettingsLink = this.getNotificationSettings();
            var header = "High Payoff Manager Name Notification";
            if (history.StageID == (int)ReductionQueueStage.Stages.Countered)
            {
                header = "High Payoff Manager Name - Reduction Request Countered";
            }
            else if (history.StageID == (int)ReductionQueueStage.Stages.Accepted)
            {
                header = "High Payoff Manager Name - Reduction Request Accepted";
            }
            var subject = $"Gain Portal - {header}";

            var body = EmailFormatter.getHighPayoffManagerNameNotificationBody(history, opportunity, portalLink, notificationSettingsLink, this.SocialMediaInfo, header, managerName);

            var task = Task.Run(async () => await this.SendRawEmail(toEmail, subject, body));
        }

        public void SendPayoffApprovalNotification(string toEmail, PayoffQueueItem largePayoffDetail, SF_PatientDetails opportunity, int accountOwnerID, string status = "reject")
        {
            var portalLink = $"{this.frontEndUrl}plaintiffs/plaintiff-detail/{opportunity.id}/{opportunity.AccountId}";
            var notificationSettingsLink = $"{this.frontEndUrl}notifications";
            var subject = status == "approve" ? "Gain Portal - Payoff Amount Is Ready to View" : "Gain Portal - Payoff Request Update";
            var body = EmailFormatter.getPayoffApprovalBody(largePayoffDetail, opportunity, portalLink, notificationSettingsLink, this.SocialMediaInfo, status);

            var task = Task.Run(async () => await this.SendRawEmail(toEmail, subject, body, null, "RiskManagement"));
        }

        public void SendReductionNegotiationPaymentNotification(string toEmail, ReductionHistory history, SF_PatientDetails opportunity, int accountOwnerID, Model.Attachment attachment = null)
        {
            var portalLink = $"{this.frontEndUrl}plaintiffs/plaintiff-detail/{opportunity.id}/{opportunity.AccountId}/#reduction-request";
            var notificationSettingsLink = $"{this.frontEndUrl}notifications";
            var subject = "Gain Portal - Payment Information";

            var body = EmailFormatter.getUserReductionNegotiationPaymentBody(history, opportunity, portalLink, notificationSettingsLink, this.SocialMediaInfo);

            List<Model.Attachment> attachments = new List<Attachment> { attachment };
            if (attachment == null)
            {
                var task = Task.Run(async () => await this.SendRawEmail(toEmail, subject, body, null, "RiskManagement"));
            }
            else
            {
                var task = Task.Run(async () => await this.SendRawEmail(toEmail, subject, body, attachments, "RiskManagement"));
            }
        }

        #region Private Methods
        /// <summary>
        /// Sends generic email
        /// </summary>
        /// <param name="to"></param>
        /// <param name="subject"></param>
        /// <param name="textBody"></param>
        /// <param name="htmlBody"></param>
        private async Task SendEmail(string to, string subject, string textBody, string htmlBody, string type = "", string fromEmail = null)
        {
            var emailSource = "";
            switch (type)
            {
                case "Intake":
                    emailSource = this.requestAccountErrorEmail;
                    break;
                case "Info":
                    emailSource = this.attorneyNotificationEmail;
                    break;
                case "RiskManagement":
                    emailSource = this.reductionRequestEmail;
                    break;
                case "CaseManagement":
                    emailSource = this.providerNotificationEmail;
                    break;
                default:
                    emailSource = fromEmail ?? this.Configuration["SMTP:FromEmail"];
                    break;
            }
            using (var client = new AmazonSimpleEmailServiceClient(this.smtp_user, this.smtp_password, RegionEndpoint.USEast1))
            {
                var sendRequest = new SendEmailRequest
                {
                    Source = emailSource,
                    Destination = new Destination
                    {
                        ToAddresses = new List<string> { to }
                    },
                    Message = new Message
                    {
                        Subject = new Content(subject),
                        Body = new Body
                        {
                            Html = htmlBody != null ? new Content
                            {
                                Charset = "UTF-8",
                                Data = htmlBody
                            } : null,

                            Text = textBody != null ? new Content
                            {
                                Charset = "UTF-8",
                                Data = textBody
                            } : null
                        }
                    },
                };
                try
                {
                    var response = await client.SendEmailAsync(sendRequest);
                }
                catch (Exception ex)
                {
                    Console.WriteLine(ex);
                    //TODO: log exception to error log
                    throw new Exception("Error sending email", ex);
                }
            }

        }

        private async Task SendRawEmail(string to, string subject, string htmlBody, List<Model.Attachment> attachments = null, string type = "", bool isCustomizedSender = false, string customizedSender = "", string fromEmail = null)
        {
            var emailSource = "";
            switch (type)
            {
                case "Intake":
                    emailSource = this.requestAccountErrorEmail;
                    break;
                case "Info":
                    emailSource = this.attorneyNotificationEmail;
                    break;
                case "RiskManagement":
                    emailSource = this.reductionRequestEmail;
                    break;
                case "CaseManagement":
                    emailSource = this.providerNotificationEmail;
                    break;
                default:
                    emailSource = fromEmail ?? this.Configuration["SMTP:FromEmail"];
                    break;
            }

            if (isCustomizedSender && customizedSender != "")
            {
                emailSource = customizedSender;
            }

            using (var client = new AmazonSimpleEmailServiceClient(this.smtp_user, this.smtp_password, RegionEndpoint.USEast1))
            {

                var stream = new MemoryStream();

                var message = new MimeMessage();

                message.From.Add(new MailboxAddress("", emailSource));

                message.To.Add(new MailboxAddress("", to));
                message.Subject = subject;

                var builder = new BodyBuilder();
                builder.HtmlBody = htmlBody;

                if (attachments != null)
                {
                    foreach (var a in attachments)
                    {
                        if (a.Title.Contains("payoff_document") || a.Title.Contains("reduction_request"))
                            builder.Attachments.Add($"{a.Title}.{a.FileExtension}", Convert.FromBase64String(a.File));
                    }
                }

                message.Body = builder.ToMessageBody();
                message.WriteTo(stream);

                var sendRawRequest = new SendRawEmailRequest
                {
                    Source = emailSource,
                    Destinations = new List<string> { to },
                    RawMessage = new RawMessage
                    {
                        Data = stream
                    }
                };

                try
                {
                    var response = await client.SendRawEmailAsync(sendRawRequest);
                }
                catch (Exception ex)
                {
                    Console.WriteLine(ex);
                    //TODO: log exception to error log
                    throw new Exception("Error sending email", ex);
                }
            }
        }

        /// <summary>
        /// Returns the email body for the patient intake process.
        /// </summary>
        /// <param name="notification"></param>
        /// <returns></returns>
        private string getPatientIntakeBody(GainNotification notification)
        {
            return $"Patient:\t\t\t{notification.OppFirstName} {notification.OppLastName}\n"
                        + $"DOB:\t\t\t{notification.OppDateOfBirth}\n"
                        + $"Provider:\t\t{notification.OwnerName}\n"
                        + $"Provider User:\t\t{notification.Username}\n\n"
                        + $"Salesforce Opportunity Link: \n{notification.SalesforceLink}";
        }

        private void SendUserNotifications(GainNotification notification, int notificationTypeID, User user, int accountID = 0)
        {
            var userWatchingList = this.UserDAL.GetOpportunityWatchingUsers(notification.OppId, accountID, notificationTypeID);

            if (notification.SendToActionUser && user != null)
            {
                if (!userWatchingList.Exists(u => user.UserID == u.UserID))
                {
                    userWatchingList.Add(user);
                }
            }

            foreach (var u in userWatchingList)
            {
                if (notification.Attachments == null)
                {
                    this.SendEmailTemplate(u, notification.Type, notification, accountID);
                }
                else
                {
                    this.SendEmailTemplate(u, notification.Type, notification, accountID, notification.Attachments);
                }
            }
        }
        #endregion

    }
}
