using GainServicingAPI.DAL;
using GainServicingAPI.Helpers.Interfaces;
using GainServicingAPI.Model;
using GainServicingAPI.Model.Salesforce;
using GainServicingAPI.Model.StaticValues;
using System.Threading.Tasks;

namespace GainServicingAPI.Helpers
{
    public class ReductionNotificationService : IReductionNotificationService
    {
        private readonly IMailManager _mailManager;
        private readonly IPayoffDAL _payoffDAL;

        public ReductionNotificationService(IMailManager mailManager, IPayoffDAL payoffDAL)
        {
            _mailManager = mailManager;
            _payoffDAL = payoffDAL;
        }

        /// <summary>
        /// Sends notifications to risk management supervisors based on reduction flags
        /// </summary>
        /// <param name="history">Reduction history</param>
        /// <param name="opportunity">Patient details</param>
        /// <param name="flags">Reduction request flags</param>
        public async Task SendReductionFlagsNotifications(ReductionHistory history, SF_PatientDetails opportunity, ReductionRequestFlags flags)
        {
            if (flags == null)
            {
                return;
            }

            var riskManagementSupervisors = await _payoffDAL.GetRiskManagementSupervisors();
            if (riskManagementSupervisors == null || riskManagementSupervisors.Count == 0)
            {
                return;
            }

            foreach (var riskManagementSupervisor in riskManagementSupervisors)
            {
                if (flags.LessThanSentOutReason != null)
                {
                    _mailManager.SendReductionNegotiationLessThanSentOutNotification(
                        riskManagementSupervisor.Email,
                        history,
                        opportunity,
                        flags.LessThanSentOutReason);
                }

                if (flags.LessThanFivePercentProRataReason != null)
                {
                    float medicalFundingAmount = opportunity.Medical_Funding_Amount__c ?? 0;

                    // Get the countered or accepted amount
                    float acceptedAmount = history.OfferAmount;

                    // Only send email if the amount is less than 89.5% of the total balance
                    if (medicalFundingAmount <= 0 || acceptedAmount < (medicalFundingAmount * 0.895))
                    {
                        _mailManager.SendReductionNegotiationLessThanFivePercentProRata(
                            riskManagementSupervisor.Email,
                            history,
                            opportunity,
                            flags.LessThanFivePercentProRataReason);
                    }
                }

                if (flags.HighPayoffManagerName != null)
                {
                    _mailManager.SendHighPayoffManagerNameNotification(
                        riskManagementSupervisor.Email,
                        history,
                        opportunity,
                        flags.HighPayoffManagerName);
                }
            }
        }
    }
}