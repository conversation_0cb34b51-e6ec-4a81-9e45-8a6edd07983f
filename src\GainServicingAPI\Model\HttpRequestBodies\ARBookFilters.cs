using System.ComponentModel.DataAnnotations;

namespace GainServicingAPI.Model.HttpRequestBodies
{
    public class ARBookFilters : CommonFilters
    {
        public int? Month { get; set; }
        public int? Year { get; set; }
        public string ArType { get; set; }
        public string AccountId { get; set; }
        public string Status { get; set; }
        
        [Range(1, 250)]
        public override int? PageSize { get; set; } = 50;
    }
}
