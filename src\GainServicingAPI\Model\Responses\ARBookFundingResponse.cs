using System;
using System.Linq;
using GainServicingAPI.Model.Salesforce;
using GainServicingAPI.Helpers;

namespace GainServicingAPI.Model.Responses
{
    public class ARBookFundingResponse
    {
        public string Id { get; set; }
        public string Name { get; set; }
        public string ArBookId { get; set; }
        public string OpportunityId { get; set; }
        public string OpportunityName { get; set; }
        public decimal Amount { get; set; }
        public string Type { get; set; }
        public string Status { get; set; }
        public DateTime CreatedDate { get; set; }
        public DateTime LastModifiedDate { get; set; }
        public string PlaintiffName { get; set; }
        public string LawFirm { get; set; }
        public string Attorney { get; set; }
        public DateTime? DateOfInjury { get; set; }

        public static explicit operator ARBookFundingResponse(Funding__c funding)
        {
            if (funding == null) return null;

            // Construct plaintiff name from opportunity (with fallbacks)
            string plaintiffName = "Unknown Patient";
            if (funding.Opportunity__r != null)
            {
                var firstName = funding.Opportunity__r?.First_Name__c ?? "";
                var lastName = funding.Opportunity__r?.Last_Name__c ?? "";
                plaintiffName = $"{firstName} {lastName}".Trim();

                // Fallback to opportunity name if first/last name not available
                if (string.IsNullOrWhiteSpace(plaintiffName))
                {
                    plaintiffName = funding.Opportunity__r?.Name ?? "Unknown Patient";
                }
            }
            else
            {
                // Extract from funding name if no opportunity relationship
                plaintiffName = ExtractPlaintiffFromName(funding.Name);
            }

            // Parse date of injury (with fallback)
            DateTime? dateOfInjury = null;
            if (!string.IsNullOrEmpty(funding.Opportunity__r?.Date_of_Accident__c))
            {
                if (DateTime.TryParse(funding.Opportunity__r.Date_of_Accident__c, out DateTime parsedDate))
                {
                    dateOfInjury = parsedDate;
                }
            }

            return new ARBookFundingResponse
            {
                Id = funding.Id,
                Name = funding.Name,
                ArBookId = funding.AR_Book__c ?? "",
                OpportunityId = funding.Opportunity__c ?? "",
                OpportunityName = funding.Opportunity__r?.Name ?? funding.Name,
                Amount = funding.Amount__c ?? (decimal)(funding.Invoice_Amount__c ?? 0),
                Type = funding.RecordType?.Name ?? funding.Type__c ?? "Medical",
                Status = funding.Status__c ?? funding.Funding_Stage__c ?? "Active",
                CreatedDate = funding.CreatedDate,
                LastModifiedDate = funding.LastModifiedDate,
                PlaintiffName = plaintiffName,
                LawFirm = funding.Opportunity__r?.Account?.Name ?? "Unknown Firm",
                Attorney = funding.Opportunity__r?.Attorney__r?.Name ?? "Unknown Attorney",
                DateOfInjury = dateOfInjury
            };
        }

        private static string ExtractPlaintiffFromName(string fundingName)
        {
            if (string.IsNullOrEmpty(fundingName)) return "Unknown Patient";

            // Try to extract patient name from funding name
            // Common patterns: "John Doe - Medical Funding", "Funding-001 - Jane Smith"
            var parts = fundingName.Split('-');
            if (parts.Length > 1)
            {
                var potentialName = parts[0].Trim();
                // Check if it looks like a name (contains space and no numbers)
                if (potentialName.Contains(" ") && !potentialName.Any(char.IsDigit))
                {
                    return potentialName;
                }
            }

            return "Unknown Patient";
        }
    }
}
