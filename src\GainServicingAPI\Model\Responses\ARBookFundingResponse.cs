using System;
using GainServicingAPI.Model.Salesforce;
using GainServicingAPI.Helpers;

namespace GainServicingAPI.Model.Responses
{
    public class ARBookFundingResponse
    {
        public string Id { get; set; }
        public string Name { get; set; }
        public string ArBookId { get; set; }
        public string OpportunityId { get; set; }
        public string OpportunityName { get; set; }
        public decimal Amount { get; set; }
        public string Type { get; set; }
        public string Status { get; set; }
        public DateTime CreatedDate { get; set; }
        public DateTime LastModifiedDate { get; set; }
        public string PlaintiffName { get; set; }
        public string LawFirm { get; set; }
        public string Attorney { get; set; }
        public DateTime? DateOfInjury { get; set; }

        public static explicit operator ARBookFundingResponse(Funding__c funding)
        {
            if (funding == null) return null;

            // Construct plaintiff name from opportunity
            string plaintiffName = null;
            if (funding.Opportunity__r != null)
            {
                var firstName = funding.Opportunity__r.First_Name__c ?? "";
                var lastName = funding.Opportunity__r.Last_Name__c ?? "";
                plaintiffName = $"{firstName} {lastName}".Trim();
                
                // Fallback to opportunity name if first/last name not available
                if (string.IsNullOrWhiteSpace(plaintiffName))
                {
                    plaintiffName = funding.Opportunity__r.Name;
                }
            }

            // Parse date of injury
            DateTime? dateOfInjury = null;
            if (!string.IsNullOrEmpty(funding.Opportunity__r?.Date_of_Accident__c))
            {
                if (DateTime.TryParse(funding.Opportunity__r.Date_of_Accident__c, out DateTime parsedDate))
                {
                    dateOfInjury = parsedDate;
                }
            }

            return new ARBookFundingResponse
            {
                Id = funding.Id,
                Name = funding.Name,
                ArBookId = funding.AR_Book__c,
                OpportunityId = funding.Opportunity__c,
                OpportunityName = funding.Opportunity__r?.Name,
                Amount = funding.Amount__c ?? (decimal)(funding.Invoice_Amount__c ?? 0),
                Type = funding.RecordType?.Name ?? funding.Type__c,
                Status = funding.Status__c ?? funding.Funding_Stage__c,
                CreatedDate = funding.CreatedDate,
                LastModifiedDate = funding.LastModifiedDate,
                PlaintiffName = plaintiffName,
                LawFirm = funding.Opportunity__r?.Account?.Name,
                Attorney = funding.Opportunity__r?.Attorney__r?.Name,
                DateOfInjury = dateOfInjury
            };
        }
    }
}
