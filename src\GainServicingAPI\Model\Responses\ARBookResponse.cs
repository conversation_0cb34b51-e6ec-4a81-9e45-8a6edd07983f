using System;
using GainServicingAPI.Model.Salesforce;

namespace GainServicingAPI.Model.Responses
{
    public class ARBookResponse
    {
        public string Id { get; set; }
        public string Name { get; set; }
        public int Month { get; set; }
        public int Year { get; set; }
        public string ArType { get; set; }
        public string AccountId { get; set; }
        public string AccountName { get; set; }
        public decimal TotalAmount { get; set; }
        public int FundingCount { get; set; }
        public DateTime CreatedDate { get; set; }
        public DateTime LastModifiedDate { get; set; }
        public string Status { get; set; }

        public static explicit operator ARBookResponse(SF_AR_Book sfArBook)
        {
            if (sfArBook == null) return null;

            return new ARBookResponse
            {
                Id = sfArBook.Id,
                Name = sfArBook.Name,
                // Smart defaults for missing fields
                Month = sfArBook.Month__c ?? sfArBook.CreatedDate.Month,
                Year = sfArBook.Year__c ?? sfArBook.CreatedDate.Year,
                ArType = sfArBook.AR_Type__c ?? DetermineArTypeFromName(sfArBook.Name),
                AccountId = sfArBook.Account__c ?? "",
                AccountName = sfArBook.Account__r?.Name ?? ExtractAccountFromName(sfArBook.Name),
                TotalAmount = sfArBook.Total_Amount__c ?? 0,
                FundingCount = sfArBook.Funding_Count__c ?? 0,
                CreatedDate = sfArBook.CreatedDate,
                LastModifiedDate = sfArBook.LastModifiedDate,
                Status = sfArBook.Status__c ?? "Open"
            };
        }

        private static string DetermineArTypeFromName(string name)
        {
            if (string.IsNullOrEmpty(name)) return "General";

            var lowerName = name.ToLower();
            if (lowerName.Contains("medical")) return "Medical";
            if (lowerName.Contains("pca")) return "PCA";
            if (lowerName.Contains("advance")) return "Advance";
            if (lowerName.Contains("partial")) return "Partial Advance";
            if (lowerName.Contains("serviced")) return "Serviced";
            if (lowerName.Contains("purchased")) return "Purchased";

            return "General";
        }

        private static string ExtractAccountFromName(string name)
        {
            if (string.IsNullOrEmpty(name)) return "Unknown Firm";

            // Try to extract law firm name from AR Book name
            // Common patterns: "Medical Funding - ABC Law Firm - January 2024"
            var parts = name.Split('-');
            if (parts.Length > 1)
            {
                return parts[1].Trim();
            }

            return "Unknown Firm";
        }
    }
}
