using System;
using GainServicingAPI.Model.Salesforce;

namespace GainServicingAPI.Model.Responses
{
    public class ARBookResponse
    {
        public string Id { get; set; }
        public string Name { get; set; }
        public int Month { get; set; }
        public int Year { get; set; }
        public string ArType { get; set; }
        public string AccountId { get; set; }
        public string AccountName { get; set; }
        public decimal TotalAmount { get; set; }
        public int FundingCount { get; set; }
        public DateTime CreatedDate { get; set; }
        public DateTime LastModifiedDate { get; set; }
        public string Status { get; set; }

        public static explicit operator ARBookResponse(SF_AR_Book sfArBook)
        {
            if (sfArBook == null) return null;

            return new ARBookResponse
            {
                Id = sfArBook.Id,
                Name = sfArBook.Name,
                Month = sfArBook.Month__c ?? 0,
                Year = sfArBook.Year__c ?? 0,
                ArType = sfArBook.AR_Type__c,
                AccountId = sfArBook.Account__c,
                AccountName = sfArBook.Account__r?.Name,
                TotalAmount = sfArBook.Total_Amount__c ?? 0,
                FundingCount = sfArBook.Funding_Count__c ?? 0,
                CreatedDate = sfArBook.CreatedDate,
                LastModifiedDate = sfArBook.LastModifiedDate,
                Status = sfArBook.Status__c
            };
        }
    }
}
