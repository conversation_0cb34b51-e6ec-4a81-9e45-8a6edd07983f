using GainServicingAPI.Model.Salesforce;

namespace GainServicingAPI.Model.Responses
{
    public class ARBookSummaryResponse
    {
        public string Id { get; set; }
        public string Name { get; set; }
        public decimal TotalAmount { get; set; }
        public int FundingCount { get; set; }

        public static explicit operator ARBookSummaryResponse(SF_AR_Book sfArBook)
        {
            if (sfArBook == null) return null;

            return new ARBookSummaryResponse
            {
                Id = sfArBook.Id,
                Name = sfArBook.Name,
                TotalAmount = sfArBook.Total_Amount__c ?? 0,
                FundingCount = sfArBook.Funding_Count__c ?? 0
            };
        }
    }
}
