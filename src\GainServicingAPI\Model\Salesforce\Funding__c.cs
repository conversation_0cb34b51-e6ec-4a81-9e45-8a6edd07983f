﻿using System;

namespace GainServicingAPI.Model.Salesforce
{
    public class Funding__c
    {
        public string Id { get; set; }
        public string Plaintiff__c { get; set; }
        public Plaintiff__R Plaintiff__r { get; set; }
        public float? Invoice_Amount__c { get; set; }
        public float? Amount_Sent_to_Provider__c { get; set; }
        public string RecordTypeId { get; set; }
        public SF_RecordType RecordType { get; set; }
        public string Type__c { get; set; }
        public string Name { get; set; }
        public string Medical_Facility__c { get; set; }
        public string Medical_Location__c { get; set; }
        public string Medical_Claim_Number__c { get; set; }
        public string Partner_Account__c { get; set; }
        public SF_Account Partner_Account__r { get; set; }
        public SF_Account Medical_Facility__r { get; set; }
        public SF_Account Medical_Location__r { get; set; }
        public string Funding_Stage__c { get; set; }
        public string Date_of_Service__c { get; set; }
        public string Usage_Rate__c { get; set; }
        public string Processing_Fee__c { get; set; }
        public string Date_Funding_Sent_Out__c { get; set; }
        public float? Funding_Amount__c { get; set; }
        public string Buyout_Amount__c { get; set; }
        public string Created_By_Portal_User__c { get; set; }
        public string Is_this_Organic__c { get; set; }
        public string AR_Book__c { get; set; }
        public float? Non_Rollup_Charge_Amounts_Total__c { get; set; }
        public string Medical_Chart__c { get; set; }
        public string Check_Number__c { get; set; }

        // Additional fields for AR Book funding relationships
        public SF_AR_Book AR_Book__r { get; set; }
        public string Opportunity__c { get; set; }
        public SF_Opportunity Opportunity__r { get; set; }
        public decimal? Amount__c { get; set; }
        public string Status__c { get; set; }
        public DateTime CreatedDate { get; set; }
        public DateTime LastModifiedDate { get; set; }
    }
}
