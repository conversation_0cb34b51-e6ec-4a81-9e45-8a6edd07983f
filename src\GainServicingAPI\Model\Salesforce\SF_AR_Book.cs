﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace GainServicingAPI.Model.Salesforce
{
    public class SF_AR_Book
    {
        public string Id { get; set; }
        public string Name { get; set; }
        public string Account_Name__c { get; set; }
        public string AR_Type__c { get; set; }
        public DateTime Date_Funding_Sent_Out__c { get; set; }
        public string Modifier__c { get; set; }

        // Additional fields for comprehensive AR Book support
        public int? Month__c { get; set; }
        public int? Year__c { get; set; }
        public string Account__c { get; set; }
        public SF_Account Account__r { get; set; }
        public decimal? Total_Amount__c { get; set; }
        public int? Funding_Count__c { get; set; }
        public DateTime CreatedDate { get; set; }
        public DateTime LastModifiedDate { get; set; }
        public string Status__c { get; set; }

        // Legacy fields for backward compatibility
        public string Month_Sent_Out__c { get; set; }
        public string Year_Sent_Out__c { get; set; }
    }
}
