{"iisSettings": {"windowsAuthentication": false, "anonymousAuthentication": true, "iisExpress": {"applicationUrl": "http://localhost:58979", "sslPort": 44310}}, "$schema": "http://json.schemastore.org/launchsettings.json", "profiles": {"IIS Express": {"commandName": "IISExpress", "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Develop"}}, "GainServicingAPI": {"commandName": "Project", "launchBrowser": false, "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Staging"}, "applicationUrl": "http://localhost:8080"}, "GainServicingAPI-Local": {"commandName": "Project", "launchBrowser": false, "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Staging"}, "applicationUrl": "http://localhost:8080"}, "GainServicingAPI-Staging": {"commandName": "Project", "launchBrowser": false, "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Staging"}, "applicationUrl": "http://localhost:8080"}, "GainServicingAPI-Prod": {"commandName": "Project", "launchBrowser": false, "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "PlainProduction"}, "applicationUrl": "http://localhost:8080"}}}