using BetterStack.Logs;
using GainServicingAPI.Controllers.Shared;
using GainServicingAPI.Controllers.Shared.Interfaces;
using GainServicingAPI.DAL;
using GainServicingAPI.DAL.Interfaces;
using GainServicingAPI.Database;
using GainServicingAPI.Helpers.Interfaces;
using GainServicingAPI.Helpers.Salesforce;
using GainServicingAPI.Helpers.Salesforce.Interfaces;
using GainServicingAPI.Logging;
using GainServicingAPI.Model;
using GainServicingAPI.Model.HttpRequestBodies;
using GainServicingAPI.Model.Salesforce;
using GainServicingAPI.Model.StaticValues;
using GainServicingAPI.Services.Interfaces;
using Microsoft.AspNetCore.Mvc;
using Salesforce.Common.Models.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Cryptography;
using System.Threading.Tasks;

namespace GainServicingAPI.Services
{
    public class ReductionNegotiationService : IReductionNegotiationService
    {
        private readonly IAPILogger _logger;
        private readonly IUserDAL _userDAL;
        private readonly IPayoffDAL _payoffDAL;
        private readonly NoteDAL _noteDAL;
        private readonly IOpportunitySalesforce _opportunitySalesforce;
        private readonly ISharedOpportunity _sharedOpportunity;
        private readonly RequestInfoService _requestInfoService;
        private readonly ISharedContacts _sharedContacts;
        private readonly ISharedCases _sharedCases;
        private readonly ICaseSalesforce _caseSalesforce;
        private readonly IPayoffService _payoffService;
        private readonly ISalesforceClient _salesforceClient;

        public ReductionNegotiationService(
            DbConnection connector,
            IAPILogger logger,
            IUserDAL userDAL,
            IPayoffDAL payoffDAL,
            IOpportunitySalesforce opportunitySalesforce,
            ISharedOpportunity sharedOpportunity,
            RequestInfoService requestInfoService,
            ISharedContacts sharedContacts,
            IDALClient dalClient,
            ISharedCases sharedCases,
            ICaseSalesforce caseSalesforce,
            IPayoffService payoffService,
            ISalesforceClient salesforceClient)
        {
            _logger = logger;
            _userDAL = userDAL;
            _payoffDAL = payoffDAL;
            _noteDAL = new NoteDAL(connector, dalClient);
            _opportunitySalesforce = opportunitySalesforce;
            _sharedOpportunity = sharedOpportunity;
            _requestInfoService = requestInfoService;
            _sharedContacts = sharedContacts;
            _sharedCases = sharedCases;
            _caseSalesforce = caseSalesforce;
            _payoffService = payoffService;
            _salesforceClient = salesforceClient;
        }

        /// <summary>
        /// Submits a reduction negotiation request
        /// </summary>
        /// <param name="request">The reduction negotiation request data</param>
        /// <param name="updateSettledAmount">Whether to update the settled amount in the payoff log</param>
        /// <param name="user">The current user</param>
        /// <returns>Success response</returns>
        public async Task<SuccessResponse> SubmitReductionNegotiation(ReductionNegotiationBody request, bool updateSettledAmount, User user)
        {


            var stageID = (int)ReductionQueueStage.Stages.Accepted;
            var timestamp = DateTime.Now;

            var historyLog = new ReductionHistory
            {
                PayoffLogID = request.PayoffLogID,
                Notes = request.Notes,
                Timestamp = timestamp,
                OfferAmount = request.Offer,
                ActingRiskManager = _userDAL.GetUserData(request.ActingRiskManagerID),
                ActingUser = _userDAL.GetUserData(request.ActingUserID),
                ProRata = request.Reduction.ProRata,
                CashOut = request.CashOut
            };

            var caseIds = await _payoffDAL.GetReductionCaseIDByPayoff(historyLog.PayoffLogID);
            var cases = await _sharedCases.GetCasesByIds(caseIds);
            List<string> oppIds = cases.Select(c => c.Opportunity_Name__c).Distinct().ToList();

            // Determine stage ID based on request parameters
            if (request.Decision == "accept" && request.ActingStatus == 0)
            {
                // Change field in sf 'Payment Amount Agreed To by CF' and opp stage to Pending Payment from Law Firm
                var contact = await _sharedContacts.GetUserByContact(historyLog.ActingRiskManager.SalesforceID);

                foreach (var oppId in oppIds)
                    await _sharedOpportunity.UpdateReductionNegotiationData(oppId, request.Offer, request.Reduction, contact.Salesforce_User__c);

                stageID = (int)ReductionQueueStage.Stages.Accepted;

                // Payoff log -> edit settle amount
                if (updateSettledAmount == true)
                {
                    await _payoffDAL.UpdateReductionSettlementAmount(request.PayoffLogID, request.Reduction.SettledAmount);
                }
            }
            else if (request.Decision == "accept" && request.ActingStatus == 1)
            {
                stageID = (int)ReductionQueueStage.Stages.PendingAccept;
            }
            else if (request.Decision == "counter" && request.CounterFrom == "admin")
            {
                stageID = (int)ReductionQueueStage.Stages.Countered;
            }
            else if (request.Decision == "counter" && request.CounterFrom == "attorney")
            {
                stageID = (int)ReductionQueueStage.Stages.Pending;
            }
            else if (request.Decision == "cancel")
            {
                stageID = (int)ReductionQueueStage.Stages.Canceled;
            }
            else if (request.Decision == "pending_cancel")
            {
                stageID = (int)ReductionQueueStage.Stages.PendingCancel;
            }
            else if (request.Decision == "restarted")
            {
                stageID = (int)ReductionQueueStage.Stages.Restarted;
            }

            // Extra Handling in case acting user/risk manager changes
            if (request.CounterFrom == "admin")
            {
                historyLog.ActingRiskManager.UserID = user.UserID;
            }
            else if (request.CounterFrom == "attorney")
            {
                historyLog.ActingUser.UserID = user.UserID;
            }

            historyLog.ActingStatus = request.ActingStatus;
            historyLog.LastAction = user;
            historyLog.StageID = stageID;


            foreach (var sfCase in cases)
            {
                // Update Salesforce case
                var comment = new OpportunityComment
                {
                    UserID = user.UserID,
                    Message = request.Notes,
                    OpportunityID = sfCase.Opportunity_Name__c,
                    TimeStamp = timestamp.ToString(),
                    CaseCaseSalesforceID = sfCase.Id
                };

                var stageMappings = new Dictionary<int, string>
                {
                    { (int)ReductionQueueStage.Stages.Pending, "Pending" },
                    { (int)ReductionQueueStage.Stages.PendingAccept, "Pending Accept" },
                    { (int)ReductionQueueStage.Stages.Countered, "Countered" },
                    { (int)ReductionQueueStage.Stages.Canceled, "Cancelled" },
                    { (int)ReductionQueueStage.Stages.Restarted, "Restarted" }
                };

                if (stageMappings.TryGetValue(stageID, out string stageName))
                {
                    comment.ReductionRequestStage = stageName;
                    await this.SetReductionRequestCase(stageName, comment, user, sfCase: sfCase);
                }

                if (stageID == (int)ReductionQueueStage.Stages.Accepted)
                {
                    comment.ReductionRequestStage = "Accepted";

                    var reductionData = await _payoffDAL.GetReductionNegotiationData(request.PayoffLogID);
                    if (updateSettledAmount == true)
                    {
                        reductionData.Reduction.ReductionRequest.SettledAmount = request.Reduction.SettledAmount;
                    }
                    await this.SetReductionRequestCase("Accepted", comment, user, request.Offer, reductionData, request.Flags, sfCase: sfCase);
                }

                // Update reduction queue and add history
                await _payoffDAL.UpdateReductionQueue(request.PayoffLogID, stageID, timestamp);
                var historyId = await _payoffDAL.AddReductionHistory(historyLog);

                // Add notes if flags are provided
                if (request.Flags != null)
                {
                    if (request.Flags.AcceptedReason != null)
                    {
                        var noteId = await _noteDAL.AddNote(NotesType.AcceptedReason, request.Flags.AcceptedReason);
                        await _payoffDAL.AddReductionHistoryNote(historyId, noteId);
                    }
                    if (request.Flags.LessThanSentOutReason != null)
                    {
                        var noteId = await _noteDAL.AddNote(NotesType.AcceptedLessThanSentOut, request.Flags.LessThanSentOutReason);
                        await _payoffDAL.AddReductionHistoryNote(historyId, noteId);
                    }
                    if (request.Flags.LessThanFivePercentProRataReason != null)
                    {
                        var noteId = await _noteDAL.AddNote(NotesType.AcceptedProRata, request.Flags.LessThanFivePercentProRataReason);
                        await _payoffDAL.AddReductionHistoryNote(historyId, noteId);
                    }
                    if (request.Flags.HighPayoffManagerName != null)
                    {
                        var noteId = await _noteDAL.AddNote(NotesType.HighPayoffManagerName, request.Flags.HighPayoffManagerName);
                        await _payoffDAL.AddReductionHistoryNote(historyId, noteId);
                    }
                }
            }



            return new SuccessResponse { Success = true };

        }


        public async Task<string> SetReductionRequestCase(string status, OpportunityComment comment, User user, float offerAmount = 0, ReductionNegotiation reductionData = null, ReductionRequestFlags flags = null, SF_Case sfCase = null)
        {

            SF_Case messageCase = sfCase ?? await _sharedCases.GetCaseById(comment.CaseCaseSalesforceID);

            // Closing already closed cases is redundant
            // Only modify payoff / reduction request case
            // Only modify case with matching salesforceID of the payoff
            string[] subjects = messageCase.Subject.Split('[');
            string newSubject = subjects[0];
            string sfID = messageCase.Opportunity_Name__c;
            var newNote = messageCase.Description + "\n" + user.Firstname + " " + user.Lastname + ": " + comment.Message;
            var sfComment = flags?.AcceptedReason != null ? flags.AcceptedReason : "";
            if (messageCase.Status != "Closed" && messageCase.Status != "Cancelled")
            {
                if (status == "Pending")
                {
                    await _caseSalesforce.Update(messageCase.Id, new SF_Case
                    {
                        Subject = newSubject + " [Pending]",
                        Status = "New",
                        Description = newNote
                    });
                }
                else if (status == "Pending Accept")
                {
                    await _caseSalesforce.Update(messageCase.Id, new SF_Case
                    {
                        Subject = newSubject + " [Pending Accept]",
                        Status = "Pending Accept",
                        Description = newNote
                    });
                }
                else if (status == "Countered")
                {
                    await _caseSalesforce.Update(messageCase.Id, new SF_Case
                    {
                        Subject = newSubject + " [Countered]",
                        Status = "Pending Law Firm",
                        Description = newNote
                    });
                }
                else if (status == "Cancelled" || status == "Restarted")
                {
                    await _caseSalesforce.Update(messageCase.Id, new SF_Case
                    {
                        Subject = newSubject + " [Canceled]",
                        Status = "Cancelled",
                        Description = newNote
                    });
                }
                else if (status == "Accepted")
                {
                    var amount = await _payoffService.GetPayoffAmount(sfID, true);

                    float medicalSpecial = 0;
                    float proRataAmount = 0;
                    if (reductionData.Reduction.ReductionRequest.ProRata > 0)
                    {
                        proRataAmount = reductionData.Reduction.ReductionRequest.ProRata;
                        if (reductionData != null && reductionData.Reduction != null && reductionData.Reduction.ReductionRequest != null)
                        {
                            medicalSpecial = reductionData.Reduction.ReductionRequest.SpecialsAmount;
                        }
                        float opportunityParticipationPercentage = amount.totalPayoffAmount / reductionData.Reduction.TotalAmount;
                        proRataAmount *= opportunityParticipationPercentage;
                    }
                    else
                    {
                        if (reductionData != null && reductionData.Reduction != null && reductionData.Reduction.ReductionRequest != null)
                        {
                            float settledAmount = reductionData.Reduction.ReductionRequest.SettledAmount;
                            float offeredAmount = reductionData.Reduction.ReductionRequest.OfferedAmount;
                            float medicalAmount = reductionData.Reduction.MedicalAmount;
                            float specialAmount = reductionData.Reduction.ReductionRequest.SpecialsAmount;
                            float medicalInsuranceAmount = reductionData.Reduction.ReductionRequest.MedicalInsuranceAmount;
                            float totalAmount = reductionData.Reduction.TotalAmount;

                            float opportunityParticipationPercentage = amount.totalMedicalAmount / totalAmount;

                            medicalSpecial = specialAmount;
                            proRataAmount = (((settledAmount != 0 ? settledAmount : offeredAmount) / (float)3.0) * (medicalAmount / (specialAmount - medicalInsuranceAmount)));
                            proRataAmount = proRataAmount < medicalAmount ? proRataAmount : medicalAmount;
                            proRataAmount *= opportunityParticipationPercentage;
                        }
                    }

                    if (medicalSpecial >= 0)
                    {
                        await _caseSalesforce.Update(messageCase.Id, new SF_Case
                        {
                            Subject = newSubject + " [Accepted]",
                            Reduction_Accepted_Amount__c = offerAmount,
                            Status = "Closed",
                            Description = newNote,
                            Pro_Rata_Amount__c = proRataAmount,
                            Comments = sfComment,
                        });

                        await _salesforceClient.UpdateAsync<object>("Opportunity", sfID, new
                        {
                            Final_Medical_Specials__c = medicalSpecial,
                            Pro_Rata_Amount__c = proRataAmount,

                        });
                    }
                    else
                    {
                        await _caseSalesforce.Update(messageCase.Id, new SF_Case
                        {
                            Subject = newSubject + " [Accepted]",
                            Reduction_Accepted_Amount__c = offerAmount,
                            Status = "Closed",
                            Description = newNote,
                            Comments = sfComment,
                        });

                        await _salesforceClient.UpdateAsync<object>("Opportunity", comment.OpportunityID, new
                        {
                            Final_Medical_Specials__c = medicalSpecial,
                        });
                    }
                    var contact = await _sharedContacts.GetUserByContact(user.SalesforceID);
                    await _caseSalesforce.UpdateOwner(messageCase.Id, contact.Salesforce_User__c);
                }
            }

            return sfID;
        }
    }
}